<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">

<plist>
  <array>
    <dict>
      <key>CustomParamDict</key>
      <dict>
        <key>EyelidRealtimeModelType</key>
        <integer>-1</integer>
      </dict>
      <key>SpecialFacelift</key>
      <integer>1</integer>
      <key>FacePart</key>
      <array>
        <dict>
          <key>Type</key>
          <string>FaceliftV2</string>
          <key>SwapPoint</key>
          <string>1</string>
          <key>MaskPath</key>
          <array>
            <!--脸型  Start-->
            <!--瘦脸  Start-->
            <dict>
              <key>Path</key>
              <string>face/shoulian/shoulianzheng1.png</string>
              <key>Rectangle</key>
              <string>103,165,77,46;</string>
              <key>GenerateType</key>
              <string>0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/shoulian/shoulianzheng2.png</string>
              <key>Rectangle</key>
              <string>19,26,243,243;</string>
              <key>GenerateType</key>
              <string>0</string>
            </dict>
            <!--瘦脸  End  -->
            <!--脸宽 正向 窄  Start-->
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z1/l.png</string>
              <key>Rectangle</key>
              <string>22,78,120,176;</string>
              <key>ChildSlider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z1/r.png</string>
              <key>Rectangle</key>
              <string>142,78,119,176;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z2/l.png</string>
              <key>Rectangle</key>
              <string>22,81,120,71;</string>
              <key>ChildSlider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z2/r.png</string>
              <key>Rectangle</key>
              <string>142,81,119,71;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--脸宽 正向 窄  End  -->
            <!--颌骨 正向 小 Start-->
            <dict>
              <key>Path</key>
              <string>face/hegu/Z/l.png</string>
              <key>Rectangle</key>
              <string>48,154,93,63;</string>
              <key>ChildSlider</key>
              <string>113</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/hegu/Z/r.png</string>
              <key>Rectangle</key>
              <string>141,154,93,63;</string>
              <key>ChildSlider</key>
              <string>114</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--颌骨 正向 小 End  -->
            <!--下巴 正向  短 Start-->
            <dict>
              <key>Path</key>
              <string>face/xiaba/0_1_140659887590832.png</string>
              <key>Rectangle</key>
              <string>111,188,64,52;</string>
            </dict>
            <!--下巴 正向  短 End  -->
            <!--脸型  End-->
            <!--嘴巴 Start-->
            <!--丰唇 反向 变薄  Start-->
            <dict>
              <key>Path</key>
              <string>mouth/fengchun/F/fengshangchunf.png</string>
              <key>Rectangle</key>
              <string>111,168,62,19;</string>
              <key>ChildSlider</key>
              <string>20</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>mouth/fengchun/F/fengxiachunf.png</string>
              <key>Rectangle</key>
              <string>118,175,47,24;</string>
              <key>ChildSlider</key>
              <string>21</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--丰唇 正向 变厚  Start-->
            <!--嘴巴 End-->
            <!--鼻子 Start  -->
            <!--鼻翼 正向 小  Start-->
            <dict>
              <key>Path</key>
              <string>nose/biyi/Z/lr.png</string>
              <key>Rectangle</key>
              <string>107,108,69,68;</string>
              <key>GenerateType</key>
              <string>0</string>
            </dict>
            <!--鼻翼 正向 小  End  -->
            <!--鼻梁 正向 细  Start-->
            <dict>
              <key>Path</key>
              <string>nose/biliang/0_1_140260981872552.png</string>
              <key>Rectangle</key>
              <string>117,106,49,70;</string>
            </dict>
            <!--鼻梁 正向 细  End  -->
            <!--鼻子 End  -->
            <!--缩人中 正向 缩  Start  -->
            <dict>
              <key>Path</key>
              <string>face/suorenzhong/renzhongzheng.png</string>
              <key>Rectangle</key>
              <string>19,148,247,109;</string>
            </dict>
            <!--缩人中 正向 缩  End  -->
          </array>
          <key>Configure</key>
          <array>
            <!--脸型  End-->
            <!--瘦脸 正向  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>0</integer>
              <key>LiftControlType</key>
              <integer>1</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>1</integer>
              <key>LiftControlType</key>
              <integer>1</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--瘦脸 正向  End  -->
            <!--脸宽 正向 窄  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>2</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>3</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>4</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>5</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--脸宽 正向 窄  End  -->
            <!--颌骨 正向 小 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>6</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>7</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--颌骨 正向 小 End  -->
            <!--下巴 正向  短 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>8</integer>
              <key>LiftControlType</key>
              <integer>2</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--下巴 正向  短 End  -->
            <!--脸型  End-->
            <!--嘴巴 Start-->
            <!--丰唇 反向 变薄  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>9</integer>
              <key>LiftControlType</key>
              <integer>30</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>10</integer>
              <key>LiftControlType</key>
              <integer>30</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--丰唇 反向 变薄  End  -->
            <!--嘴巴 End-->
            <!--鼻翼 正向 小  Start-->
            <dict>
              <key>MaskIndex</key>
              <string>11</string>
              <key>LiftControlType</key>
              <string>22</string>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>1.00</string>
            </dict>
            <!--鼻翼 正向 小  End  -->
            <!--鼻梁 正向 细  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>12</integer>
              <key>LiftControlType</key>
              <integer>51</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--鼻梁 正向 细  End  -->
            <!--鼻子 End  -->
            <!--缩人中 正向 缩  Start  -->
            <dict>
              <key>MaskIndex</key>
              <integer>13</integer>
              <key>LiftControlType</key>
              <integer>59</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.00</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--缩人中 正向 缩  End  -->
          </array>
        </dict>
      </array>
    </dict>
  </array>
</plist>
