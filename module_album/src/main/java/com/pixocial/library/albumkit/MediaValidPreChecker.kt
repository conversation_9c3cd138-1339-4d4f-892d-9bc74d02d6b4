package com.pixocial.library.albumkit

import android.graphics.Bitmap
import com.pixocial.library.albumkit.media.MediaInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 媒体点击拦截处理工具
 */
open class MediaValidPreChecker {

    /**
     * 相册配置
     */
    lateinit var config: AlbumConfig

    /**
     * 相册picker
     */
    lateinit var picker: AlbumPicker

    /**
     * 媒体素材点击检测拦截
     */
    open suspend fun dispatchMediaClick(mediaInfo: MediaInfo, inputBitmap: Bitmap): Boolean =
        withContext(Dispatchers.IO) {
            false
        }

    open suspend fun dispatchMediaClick(mediaInfo: MediaInfo): Boolean =
        withContext(Dispatchers.IO) {
            false
        }

}