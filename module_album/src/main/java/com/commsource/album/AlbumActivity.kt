package com.commsource.album

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.transition.Slide
import android.view.Gravity
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.SharedElementCallback
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.commsource.album.cover.MediaPreviewBottomCover.Companion.KEY_RESULT_FORM
import com.commsource.album.cover.MediaPreviewBottomCover.Companion.KEY_RESULT_FORM_IMPORT
import com.commsource.album.cover.MediaPreviewBottomCover.Companion.KEY_RESULT_FORM_MOCKUP
import com.commsource.album.databinding.ActivityAlbumBinding
import com.commsource.album.def.AlbumSource
import com.commsource.album.ui.MediaTransition
import com.commsource.beautyplus.activity.BaseActivity
import com.commsource.util.PermissionUitl
import com.commsource.util.UIHelper
import com.commsource.util.ipermission.EnsureAllPermissionCallBack
import com.commsource.util.ipermission.IPermission
import com.commsource.widget.analyse.AnalyseAgent
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.library.albumkit.AlbumConfirmResult
import com.pixocial.library.albumkit.AlbumCore
import com.pixocial.library.albumkit.AlbumPickerHolder
import com.pixocial.library.albumkit.ui.AlbumUICoverGroup

/**
 * 相册
 */
class AlbumActivity : BaseActivity(), AlbumPickerHolder {
    companion object {
        const val KEY_BACK_FROM = "back_form"
        const val BACK_FORM_MOCKUP_EDIT_NEW = 1 //塑封页，再来一张
        const val BACK_FORM_MOCKUP_RETURN_TO_RETRO_CAM = 2 // 塑封，返回复古相机
    }

    /**
     * 页面内ViewBinding
     */
    private val viewBinding by lazy { ActivityAlbumBinding.inflate(layoutInflater, null, false) }

    /**
     * ViewModelProvider
     */
    val viewModelProvider by lazy { ViewModelProvider(this) }

    /**
     * 相册ViewModel
     */
    val albumViewModel by lazy { viewModelProvider[AlbumViewModel::class.java] }

    /**
     * 当前相册选择器
     */
    private var picker: XAlbumPicker? = null

    /**
     * 当前SessionId
     */
    private var sessionId: String? = null

    private var needLogPerformance = true
    private var albumCreateTime = 0L

    val activityResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.apply {
                val backForm = getIntExtra(KEY_BACK_FROM, 0)
                when (backForm) {
                    BACK_FORM_MOCKUP_EDIT_NEW -> {
                        // 此时在预览页，需要选中塑封页刚保存的最新的一张图片
                        // 因为是保存到了Camera相册，那么选择Camera相册即可
                        fromMockupEditNew = true
                    }

                    BACK_FORM_MOCKUP_RETURN_TO_RETRO_CAM -> {
                        // 关闭所有相册页
                        picker?.onAlbumConfirmResult?.onAlbumConfirmResult(
                            this@AlbumActivity,
                            AlbumConfirmResult(listOf(), null, null, Bundle().apply {
                                putString(KEY_RESULT_FORM, "BACK_FORM_MOCKUP_RETURN_TO_RETRO_CAM")
                            })
                        )

                        finish()
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        if (PermissionUitl.hasMediaPermission()) {
            albumCreateTime = System.currentTimeMillis()
        }
        super.onCreate(savedInstanceState)
        setContentView(viewBinding.root)
        viewBinding.root.post { DeviceUtils.setsHomeHeight(viewBinding.root.height) }
        //当前的sessionId
        val sessionId = intent.getStringExtra(AlbumCore.KEY_SESSION_ID)
        this.sessionId = sessionId
        //销毁重建处理
        if (savedInstanceState != null) {
            onBackPressed()
            AlbumCore.clearSession(sessionId)
            return
        }
        val picker = AlbumCore.getSessionPicker(sessionId)
        picker ?: return onBackPressed()
        this.picker = picker as XAlbumPicker

        val albumConfig = picker.config
        val albumUiMode = albumConfig.uiMode
        window.setBackgroundDrawable(ColorDrawable(albumUiMode.primaryColor))
        albumUiMode.navigationBarColor?.let {
            window.navigationBarColor = it
        }

        overridePendingTransition(albumConfig.albumEnterAnimation, albumConfig.contentExitAnimation)
        //没有权限也发起初始化
        albumViewModel.init(picker = picker)
        var isRequestAllPermissions = false
        if (PermissionUitl.isNeedRequestAllPermissions) {
            isRequestAllPermissions = true
        }
        //绑定 pickerHolder
        picker.bindPickerHolder(this@AlbumActivity)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {//Android 14
            IPermission(this)
                .request(isRequestAllPermissions, *PermissionUitl.getAlbumMediaPermissions())
                .execute { result ->
                    PermissionUitl.isNeedRequestAllPermissions = false
                    var hasPermission = false
                    setMediaPermissionsTips()
                    if (result.isNotEmpty()) {
                        //Android 14
                        var hasReadMediaVisualUserSelected =
                            result.find { curPermission -> curPermission.permission == Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED }
                        if (hasReadMediaVisualUserSelected?.isSuccess == true) {
                            hasPermission = true
                        }
                    }
                    if (hasPermission) {
                        //通过配置 初始化数据
                        albumViewModel.loadAllBucket()
                        //一些独立数据配置直接解析
                        if (albumConfig.enablePortraitOrScreenShotBucket) {
                            //加载人像数据
                            albumViewModel.loadPortraitImage()
                        }
                        if (albumConfig.enableVideoBucket) {
                            //加载视频专属页面
                            albumViewModel.loadVideo()
                        }
                    }
                }
        } else {
            IPermission(this)
                .request(*PermissionUitl.getMediaPermissions())
                .execute(object : EnsureAllPermissionCallBack() {
                    override fun onAllPermissionEnable(isEnable: Boolean) {
                        if (isEnable) {
                            //通过配置 初始化数据
                            albumViewModel.loadAllBucket()
                            //一些独立数据配置直接解析
                            if (albumConfig.enablePortraitOrScreenShotBucket) {
                                //加载人像数据
                                albumViewModel.loadPortraitImage()
                            }
                            if (albumConfig.enableVideoBucket) {
                                //加载视频专属页面
                                albumViewModel.loadVideo()
                            }
                        }
                    }
                })
        }
        // 首次进入带入之前选中的媒体数据
        albumConfig.inputSelectedMedias?.let {
            picker.adjustSelectMedias(it)

            if (albumConfig.uiMode == XAlbumConfig.UiMode.Retro && albumConfig.directDisplayPreview) {
                // case：复古-启动相册后-打开预览页-定位到指定的Item
                // 这个场景只能单Media
                albumViewModel.mediaTransition.selectMedia(
                    // 此场景下，position不是必须的，里面会通过 MediaInfo 的信息来定位
                    MediaTransition.MediaPosition(-1, it[0])
                )
            }
        }

        setExitSharedElementCallback(object : SharedElementCallback() {
            override fun onMapSharedElements(
                names: MutableList<String>?,
                sharedElements: MutableMap<String, View>?
            ) {
                picker.onTargetMapSharedElements(names, sharedElements)
            }
        })

        if (albumConfig.showAlbumThenDisplayPreview) {
            Handler(Looper.getMainLooper()).postDelayed({
                XAlbum.toAlbum(this,
                    XAlbumConfig(videoEnable = true, gifEnable = true).apply {
                        directDisplayPreview = true
                        enableSelectGif = true
                        previewEnterAnimationType = XAlbumConfig.PREVIEW_ENTER_ANIMATION_TYPE_RTL
                        keepAlbum = true
                        source = AlbumSource.RetroCamera
                        enableMoreMenu = false
                        showPreviewIcon = false
                        albumEnterAnimation = R.anim.slide_right_in
                        albumExitAnimation = R.anim.slide_right_out
                        uiMode = XAlbumConfig.UiMode.Retro
                        retroMediaCount = albumConfig.retroMediaCount
                        retroFeature = albumConfig.retroFeature
                        beforeMockupMediaProcessor = albumConfig.beforeMockupMediaProcessor
                        onRetroImportMediaProcessor = albumConfig.onRetroImportMediaProcessor
                        mockupAndImportMediaChecker = albumConfig.mockupAndImportMediaChecker
                        needRequestImageMaterial = false
                        needRequestVideoMaterial = false
                    },
                    onResultWithContext = { activity, currentActivity, medias, data ->
                        val backForm = data?.getString("back_from")
                        when (backForm) {
                            "mockup" -> {
                                picker.onAlbumConfirmResult?.onAlbumConfirmResult(
                                    currentActivity,
                                    AlbumConfirmResult(medias, null, null, Bundle().apply {
                                        putString(KEY_RESULT_FORM, KEY_RESULT_FORM_MOCKUP)
                                    })
                                )
                            }

                            "import" -> {
                                picker.onAlbumConfirmResult?.onAlbumConfirmResult(
                                    currentActivity,
                                    AlbumConfirmResult(medias, null, null, Bundle().apply {
                                        putString(KEY_RESULT_FORM, KEY_RESULT_FORM_IMPORT)
                                    })
                                )
                            }

                            "BACK_FORM_MOCKUP_RETURN_TO_RETRO_CAM" -> {
                                finish()
                            }
                        }
                    }
                )
            }, XAlbum.AnimationDuration)
        }
    }

    override fun onResume() {
        super.onResume()
        if (fromPause) {
            fromPause = false
            //通过配置 初始化数据
            if (fromMockupEditNew) {
                fromMockupEditNew = false
                albumViewModel.albumUiStateEvent.value?.buckets?.find { it.dirName == AlbumViewModel.BucketCamera }
                    ?.let {
                        albumViewModel.loadAllBucket(it.dirID, onSelectedBucket = {
                            albumViewModel.backFromMockupEditNew.postValue(true)
                        })
                    }
            } else {
                if (albumViewModel.isDeletingSingleMedia
                    || picker?.config?.reSelectBucketInfoWhenTabChanged == true
                ) {
                    // 成功删除媒体后也会触发loadAllBucket，避免重复调用
                    // 选择Tab后会也会加载，resume的时候不用再加载了
                } else {
                    albumViewModel.loadAllBucket()
                }
            }
            //一些独立数据配置直接解析
            picker?.let {
                if (it.config.enablePortraitOrScreenShotBucket) {
                    //加载人像数据
                    albumViewModel.loadPortraitImage()
                }
                if (it.config.enableVideoBucket) {
                    //加载视频专属页面
                    albumViewModel.loadVideo()
                }
            }
        }
        setMediaPermissionsTips()
        albumViewModel.selectBucketEvent.observe(this@AlbumActivity, logObserver)
    }

    /**
     * 设置顶部获取完全的图片/视频权限view是否显示，Android 14以及以上可能会显示
     */
    private fun setMediaPermissionsTips() {
        if (PermissionUitl.hasOnlyReadMediaVisualUserSelected()) {
            albumViewModel.showOpenPermissionTipsEvent.postValue(true)
        } else {
            albumViewModel.showOpenPermissionTipsEvent.postValue(false)
        }
    }

    private var fromPause = false
    private var fromMockupEditNew = false

    private val logObserver = object : Observer<BucketUiState?> {
        override fun onChanged(t: BucketUiState?) {
            if (needLogPerformance && albumCreateTime > 0L && t != null) {
                val createTime = System.currentTimeMillis() - albumCreateTime
                UIHelper.runOnUiThread {
                    val hashMap = HashMap<String, String>(4)
                    hashMap["duration"] = createTime.toString()
                    hashMap["photos"] = (t.medias.size).toString()
                    hashMap["photoFetchTime"] = t.duration.toString()
                    hashMap["source"] = getFromSource()
                    AnalyseAgent.logEvent("performance_time_album", hashMap)
                    albumCreateTime = 0L
                }
                needLogPerformance = false
                albumViewModel.selectBucketEvent.removeObserver(this)
            }
        }
    }

    private fun getFromSource(): String {
        picker?.config?.source?.let {
            when (it) {
                AlbumSource.VideoStudio -> {
                    return "首页视频编辑按钮"
                }

                AlbumSource.H5 -> {
                    return "H5"
                }

                AlbumSource.BeautyCamera,
                AlbumSource.MovieCamera,
                AlbumSource.MontageSelectBg,
                AlbumSource.MontageTakePicture -> {
                    return "自拍"
                }

                AlbumSource.Album -> {
                    return "首页编辑按钮"
                }

                else -> {}
            }
        }
        return "其他"
    }

    override fun onPause() {
        super.onPause()
        fromPause = true
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        if (albumViewModel.noProgressLoadingEvent.value?.show == true) {
            // loading过程中不支持back
            return
        }
        if (picker?.onBackPressed() == true) {
            return
        }
        super.onBackPressed()
    }

    override fun getAlbumCoverGroup(): AlbumUICoverGroup {
        return viewBinding.coverGroup
    }

    override fun getHolderLifecycle(): Lifecycle {
        return lifecycle
    }

    override fun getLifecycleOwner(): LifecycleOwner {
        return this
    }

    override fun getLifecycleScope(): LifecycleCoroutineScope {
        return lifecycleScope
    }

    override fun getOwnerFragmentManager(): FragmentManager {
        return supportFragmentManager
    }

    override fun getOwnerScopeViewModelProvider(): ViewModelProvider {
        return viewModelProvider
    }

    override fun onExit() {
        finish()
    }

    private var hasPop = false

    override fun finish() {
        super.finish()

        picker?.let {
            overridePendingTransition(it.config.contentEnterAnimation, it.config.albumExitAnimation)
        }
        if (!hasPop) { // 有空研究下这里为啥走两次
            hasPop = true
            if (picker?.config?.keepSPM == true) {
                return
            }
            SPMManager.instance.popSpm()
        }
    }

    override fun onDestroy() {
        sessionId?.let {
            AlbumCore.clearSession(it)
        }
        // 从塑封页回到复古相机时，使用了Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        // finish()不会执行，所以在onDestroy增加SPM出栈处理
        if (!hasPop) {
            hasPop = true
            if (picker?.config?.keepSPM != true) {
                SPMManager.instance.popSpm()
            }
        }
        super.onDestroy()
    }

    override fun onActivityReenter(resultCode: Int, data: Intent?) {
        super.onActivityReenter(resultCode, data)
        if (resultCode == 102) {
            picker?.onSharedElementTransitionBack(resultCode, data)
            window.reenterTransition = Slide(Gravity.START)
        }
    }

}