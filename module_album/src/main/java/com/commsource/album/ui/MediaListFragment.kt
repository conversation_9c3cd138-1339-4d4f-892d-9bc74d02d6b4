package com.commsource.album.ui

import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.commsource.album.AlbumViewModel
import com.commsource.album.LOGW
import com.commsource.album.MediaUiState
import com.commsource.album.MediaViewHolder
import com.commsource.album.R
import com.commsource.album.XAlbum
import com.commsource.album.XAlbumConfig
import com.commsource.album.cover.MediaPreviewBottomCover.Companion.KEY_RESULT_FORM
import com.commsource.album.cover.MediaPreviewBottomCover.Companion.KEY_RESULT_FORM_IMPORT
import com.commsource.album.cover.MediaPreviewBottomCover.Companion.KEY_RESULT_FORM_MOCKUP
import com.commsource.album.databinding.FragmentMediaListBinding
import com.commsource.album.def.AlbumSource
import com.commsource.album.def.AlbumTab
import com.commsource.album.widget.AlbumItemDecoration
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.isVisible
import com.commsource.util.visible
import com.commsource.widget.analyse.AnalyseAgent
import com.commsource.widget.list.OnEntityClickListener
import com.commsource.widget.list.OnItemChildClickListener
import com.commsource.widget.list.XItem
import com.commsource.widget.list.XRecyclerViewAdapter
import com.pixocial.library.albumkit.AlbumConfirmResult
import com.pixocial.library.albumkit.media.MediaInfo


/**
 * 媒体列表Fragment
 */
open class MediaListFragment : BaseFragment(), MediaTransition.TransitionHolder {

    val viewBinding by lazy { FragmentMediaListBinding.inflate(layoutInflater, null, false) }

    /**
     * 相册ViewModel
     */
    val albumViewModel by lazy { ViewModelProvider(ownerActivity)[AlbumViewModel::class.java] }

    /**
     * 相册ItemAdapter
     */
    val adapter by lazy { XRecyclerViewAdapter(requireContext(), lifecycleScope) }

    /**
     * 分割装饰
     */
    private val albumItemDecoration by lazy { AlbumItemDecoration(viewBinding.rv) }

    /**
     * 相册布局
     */
    private val layoutManager: GridLayoutManager by lazy {
        object : GridLayoutManager(requireContext(), 3) {

            override fun supportsPredictiveItemAnimations(): Boolean {
                //Bugfix:java.lang.IndexOutOfBoundsException: Inconsistency detected. Invalid view holder adapter position
                return false
            }
        }.apply {
            initialPrefetchItemCount = 10
            requestSimpleAnimationsInNextLayout()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (savedInstanceState != null) {
            return
        }
        //相册网格显示
        viewBinding.rv.adapter = adapter
        viewBinding.rv.layoutManager = layoutManager
        viewBinding.rv.setHasFixedSize(true)
        viewBinding.rv.setRecycledViewPool(albumViewModel.recyclerPool)
        viewBinding.rv.addItemDecoration(albumItemDecoration)
        viewBinding.sbAlbum.setRecyclerView(viewBinding.rv)
        //插入是否可预览
        if (!albumViewModel.picker.config.showPreviewIcon) {
            adapter.addTag(MediaViewHolder.TAG_can_preview, false)
        } else {
            adapter.addTag(
                MediaViewHolder.TAG_can_preview,
                albumViewModel.config.enablePreview || albumViewModel.config.multiSelectSetting != null
            )
        }
        //基础顶部
        //距离顶部的高度albumViewModel.config.getDefaultTop()在AlbumMediaListCover中统一处理了
        viewBinding.rv.setPadding(
            albumItemDecoration.getDefaultPadding(),
            albumItemDecoration.getDefaultPadding(),
            albumItemDecoration.getDefaultPadding(),
            180.dp + albumItemDecoration.getDefaultPadding()
        )

        albumViewModel.tabSwitchEvent.observe(viewLifecycleOwner) {
            when(it) {
                AlbumTab.Video -> {
                    adapter.addTag(MediaViewHolder.TAG_display_video, true)
                }
                else -> {
                    adapter.addTag(MediaViewHolder.TAG_display_video, true)
                }
            }
//            "切换tab $it，修正相册高度".LOGV_Album()
            viewBinding.rv.setPadding(
                albumItemDecoration.getDefaultPadding(),
                albumItemDecoration.getDefaultPadding(),
                albumItemDecoration.getDefaultPadding(),
                180.dp + albumItemDecoration.getDefaultPadding()
            )
        }

        //动态分割
        albumViewModel.albumGridCountEvent.observe(viewLifecycleOwner) {
            it?.let {
                val count = if (albumViewModel.config.uiMode == XAlbumConfig.UiMode.Retro) {
                    3
                } else {
                    it
                }

                albumItemDecoration.count = count
                viewBinding.rv.setPadding(
                    albumItemDecoration.getDefaultPadding(),
                    albumItemDecoration.getDefaultPadding(),
                    albumItemDecoration.getDefaultPadding(),
                    180.dp + albumItemDecoration.getDefaultPadding()
                )
                adapter.addTag(
                    MediaViewHolder.TAG_size,
                    albumItemDecoration.calculateAlbumItemSize()
                )
                adapter.addTag(MediaViewHolder.TAG_count, count)
                layoutManager.requestSimpleAnimationsInNextLayout()
                layoutManager.spanCount = count
                adapter.notifyAllItemChange(MediaViewHolder.PAYLOAD_count_change)
            }
        }

        //相册内Item点击
        adapter.onItemChildClickListener = object : OnItemChildClickListener {
            override fun onItemChildClick(position: Int, item: XItem<*>, view: View) {
                when (view.id) {
                    R.id.cb_select -> {
                        item.entity.takeIf { it is MediaUiState }?.let {
                            it as MediaUiState
                            if (albumViewModel.inDeleteState()) {
                                albumViewModel.selectDelete(it)
                            }
                        }
                    }

                    R.id.iv_preview -> {
                        item.entity?.takeIf { it is MediaUiState }?.let {
                            onClickToPreview(position, it as MediaUiState)
                        }
                    }
                }
            }
        }
        adapter.setOnEntityClickListener(
            MediaUiState::class.java,
            object : OnEntityClickListener<MediaUiState> {

                override fun onClick(position: Int, entity: MediaUiState): Boolean {
                    if (!albumViewModel.picker.config.showPreviewIcon
                        && albumViewModel.picker.config.autoPreviewWhenSelect
                    ) {
                        onClickToPreview(position, entity)
                    } else {
                        entity.mediaInfo.let {
                            if (albumViewModel.inDeleteState()) {
                                //删除状态 优先
                                albumViewModel.selectDelete(entity)
                                return false
                            }
                            if (!albumViewModel.canUseMediaInfo(it)) {
                                //gif 视频跳转预览
                                //展示目标相册
                                when {
                                    albumViewModel.displayAlbumPageEvent.value == 0 -> albumViewModel.displayBucketEvent.value =
                                        albumViewModel.selectBucketEvent.value

                                    albumViewModel.config.enableVideoBucket -> albumViewModel.displayBucketEvent.value =
                                        albumViewModel.videoUiStateEvent.value

                                    albumViewModel.config.enablePortraitOrScreenShotBucket -> albumViewModel.displayBucketEvent.value =
                                        if (albumViewModel.isScreenShotTab()) {
                                            albumViewModel.screenShotUiStateEvent.value
                                        } else {
                                            albumViewModel.portraitUiStateEvent.value
                                        }

                                }
                                albumViewModel.mediaTransition.selectMedia(
                                    MediaTransition.MediaPosition(
                                        position,
                                        it,
                                        fromFirstEnterMediaPreview = true
                                    )
                                )
                                return@let
                            }
                            albumViewModel.picker.pickMediaInfo(
                                entity.mediaInfo,
                                null
                            )
                        }
                    }
                    return false
                }
            })

        //刷新可视范围数据变动
        albumViewModel.refreshMediaList.observe(viewLifecycleOwner) {
            adapter.notifyAllItemChange()
        }
        //是否处于多选删除状态
        albumViewModel.deleteStateEvent.observe(viewLifecycleOwner) {
            adapter.addTag(MediaViewHolder.TAG_select, it)
            adapter.notifyAllItemChange()
        }
    }

    internal fun onClickToPreview(position: Int, mediaUiState: MediaUiState) {
        AnalyseAgent.logEvent(MTAnalyticsConstant.album_clk_amplify)

        val albumConfig = albumViewModel.config
        val picker = albumViewModel.picker
        if (albumConfig.showAlbumThenDisplayPreview) {
            Handler(Looper.getMainLooper()).postDelayed({
                XAlbum.toAlbum(this.requireActivity(),
                    XAlbumConfig(videoEnable = true, gifEnable = true).apply {
                        directDisplayPreview = true
                        enableSelectGif = true
                        previewEnterAnimationType = XAlbumConfig.PREVIEW_ENTER_ANIMATION_TYPE_RTL
                        keepAlbum = true
                        source = AlbumSource.RetroCamera
                        enableMoreMenu = false
                        showPreviewIcon = false
                        albumEnterAnimation = R.anim.slide_right_in
                        albumExitAnimation = R.anim.slide_right_out
                        uiMode = XAlbumConfig.UiMode.Retro
                        beforeMockupMediaProcessor = albumConfig.beforeMockupMediaProcessor
                        mockupAndImportMediaChecker = albumConfig.mockupAndImportMediaChecker
                        onRetroImportMediaProcessor = albumConfig.onRetroImportMediaProcessor
                        retroMediaCount = albumConfig.retroMediaCount
                        retroFeature = albumConfig.retroFeature
                        // 当前选中Media带到下个页面去
                        inputSelectedMedias = listOf(mediaUiState.mediaInfo)
                        selectedMedia = albumViewModel.selectBucketEvent.value?.apply {
                            medias = listOf(mediaUiState)
                        }
                        needRequestImageMaterial = false
                        needRequestVideoMaterial = false
                    },
                    onResultWithContext = { activity, currentActivity, medias, data ->
                        val backForm = data?.getString("back_from")
                        when (backForm) {
                            "mockup" -> {
                                picker.onAlbumConfirmResult?.onAlbumConfirmResult(
                                    currentActivity,
                                    AlbumConfirmResult(medias, null, null, Bundle().apply {
                                        putString(KEY_RESULT_FORM, KEY_RESULT_FORM_MOCKUP)
                                    })
                                )
                            }

                            "import" -> {
                                picker.onAlbumConfirmResult?.onAlbumConfirmResult(
                                    currentActivity,
                                    AlbumConfirmResult(medias, null, null, Bundle().apply {
                                        putString(KEY_RESULT_FORM, KEY_RESULT_FORM_IMPORT)
                                    })
                                )
                            }
                        }
                    }
                )
            }, XAlbum.AnimationDuration)
        } else {
            mediaUiState.let {
                it.mediaInfo.let {
                    //展示目标相册
                    when {
                        albumViewModel.displayAlbumPageEvent.value == 0 -> albumViewModel.displayBucketEvent.value =
                            albumViewModel.selectBucketEvent.value

                        albumViewModel.config.enableVideoBucket -> albumViewModel.displayBucketEvent.value =
                            albumViewModel.videoUiStateEvent.value

                        albumViewModel.config.enablePortraitOrScreenShotBucket -> albumViewModel.displayBucketEvent.value =
                            if (albumViewModel.isScreenShotTab()) {
                                albumViewModel.screenShotUiStateEvent.value
                            } else {
                                albumViewModel.portraitUiStateEvent.value
                            }
                    }
                    albumViewModel.mediaTransition.selectMedia(
                        MediaTransition.MediaPosition(
                            position, it, fromFirstEnterMediaPreview = true
                        )
                    )
                }
            }
        }
    }

    /**
     * 更新相册数据
     */
    fun updateBucketUiState(medias: List<MediaUiState>, maskType: String? = null, withAnim: Boolean = true) {
        if (maskType == null) {
            viewBinding.maskContainer.hideAll()
        } else {
            viewBinding.maskContainer.showMask(maskType)
        }
        //设置相册展示
        adapter.beginTransaction()
            .addItems(medias, MediaViewHolder::class.java)
            .commit(withAnim) {
                if (albumViewModel.config.uiMode == XAlbumConfig.UiMode.Retro && viewBinding.rv.isVisible) {
                    viewBinding.rv.viewTreeObserver.addOnGlobalLayoutListener(object :
                        OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            viewBinding.rv.viewTreeObserver.removeOnGlobalLayoutListener(this)
                            viewBinding.rv.post {
                                // 定位到上个页面（预览页）选中的图片
                                albumViewModel.config.inputSelectedMedias?.let { inputMedias ->
                                    val targetMedia = inputMedias[0]
                                    val mediaUiState =
                                        medias.find { it.mediaInfo.mediaPath == targetMedia.mediaPath }
                                    val targetPosition = medias.indexOf(mediaUiState)
                                    "targetPosition=$targetPosition".LOGW()
                                    viewBinding.rv.scrollToPosition(targetPosition)
                                }
                            }
                        }
                    })
                }
            }
        adapter.notifyAllItemChange()
    }

    override fun getReuseDrawable(
        isEnter: Boolean,
        position: Int,
        mediaInfo: MediaInfo
    ): Drawable? {
        //无复用状态
        return null
    }

    override fun getDisplayVisibleRectF(
        isEnter: Boolean,
        position: Int,
        mediaInfo: MediaInfo,
        width: Int,
        height: Int
    ): MediaTransition.MediaTransitionPackage {
        val holder = viewBinding.rv.findViewHolderForAdapterPosition(position)
        return MediaTransition.MediaTransitionPackage(visibleRectF = Rect().apply {
            val leftTop = IntArray(2)
            //直接给到itemView宽高位置占用即可
            holder?.itemView?.getLocationInWindow(leftTop)
            val width =
                holder?.itemView?.measuredWidth ?: albumItemDecoration.calculateAlbumItemSize()
            val height =
                holder?.itemView?.measuredHeight ?: albumItemDecoration.calculateAlbumItemSize()
            set(
                Rect(
                    leftTop[0],
                    leftTop[1],
                    leftTop[0] + width,
                    leftTop[1] + height
                )
            )
        }, corner = 4.dpf)
    }

    override fun onPreTransitionStart(position: Int, mediaInfo: MediaInfo, isEnter: Boolean) {
        //退出需要显示RV
        if (!isEnter) {
            viewBinding.root.visible()
        }
        var holder = viewBinding.rv.findViewHolderForAdapterPosition(position)
        if (holder == null) {
            viewBinding.rv.scrollToPosition(position)
        }
    }

    override fun getDisplayMediaPosition(): MediaTransition.MediaPosition {
        return MediaTransition.MediaPosition()
    }

    override fun onTransitionStart(isEnter: Boolean) {
    }

    override fun onTransitionEnd(isEnter: Boolean) {
        if (isEnter && mActivity != null) {
            if (albumViewModel.mediaTransition.selectMediaPositionEvent.value != null) {
                //渲染优化 GPU过渡绘制 会渲染底层
                viewBinding.root.gone()
            }
        }
    }

    override fun onScreenSizeConfigurationChanged() {
        super.onScreenSizeConfigurationChanged()
        //重新计算宽度和高度
        adapter.itemCount.takeIf { it > 0 }?.apply {
            adapter.addTag(
                MediaViewHolder.TAG_size,
                albumItemDecoration.calculateAlbumItemSize()
            )
            adapter.notifyDataSetChanged()
        }
    }
}