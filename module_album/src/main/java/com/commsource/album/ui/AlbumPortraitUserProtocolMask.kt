package com.commsource.album.ui

import com.commsource.album.AlbumMaskType
import com.commsource.album.R
import com.commsource.widget.mask.BaseMask

/**
 * 相册权限 mask
 */
class AlbumPortraitUserProtocolMask : BaseMask() {

    override fun getLayoutId(): Int {
        return R.layout.mask_album_portrait_user_protocol
    }

    override fun getTag(): String {
        return AlbumMaskType.AlbumPortraitUserProtocol
    }
}