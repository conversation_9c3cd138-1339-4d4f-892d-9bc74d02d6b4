package com.commsource.album

import com.commsource.util.common.SPConfig
import com.commsource.util.common.SpTableName
import com.meitu.common.AppContext

/**
 * 相册Config
 */
object AlbumConfig {

    private val spConfig: SPConfig by lazy { SPConfig(AppContext.context, SpTableName.MY_PAGE) }

    /**
     * 相册列数。
     */
    private const val ALBUM_COL_COUNT = "ALBUM_COL_COUNT"
    fun setAlbumColCount(count: Int) {
        spConfig.putValue(ALBUM_COL_COUNT, count)
    }

    fun getAlbumColCount(): Int {
        return spConfig.getInt(ALBUM_COL_COUNT, XAlbumConfig.DEFAULT_COL_COUNT)
    }

    /**
     * 相册检测人脸协议
     */
    private const val ALBUM_PORTRAIT_USER_AGREE = "ALBUM_PORTRAIT_USER_AGREE"

    fun isAlbumPortraitUserAgree(): Boolean {
        return spConfig.getBoolean(ALBUM_PORTRAIT_USER_AGREE, false)
    }

    fun setAlbumPortraitUserAgree(agree: Boolean) {
        spConfig.putValue(ALBUM_PORTRAIT_USER_AGREE, agree)
    }

    /**
     * 相册打开次数
     */
    private const val ALBUM_ENTER_COUNT = "ALBUM_ENTER_COUNT"

    fun getAlbumEnterCount(): Int {
        return spConfig.getInt(ALBUM_ENTER_COUNT, 0)
    }

    fun setAlbumEnterCount(count: Int) {
        spConfig.putValue(ALBUM_ENTER_COUNT, count)
    }
}