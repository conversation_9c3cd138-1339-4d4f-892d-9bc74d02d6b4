<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <View
            android:id="@+id/vDiv"
            android:layout_width="16dp"
            android:layout_height="match_parent"/>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/llItem"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!--icon-->
            <com.commsource.widget.IconFrontView
                android:id="@+id/icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:textSize="16dp" />

            <!--text-->
            <com.commsource.album.widget.ExcludeFontPaddingTextView
                android:id="@+id/tvName"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="10dp"
                android:gravity="center_vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:lineSpacingExtra="0dp"
                android:textSize="14sp"
                tools:text="背景123"/>


        </androidx.appcompat.widget.LinearLayoutCompat>

        <View
            android:layout_width="10dp"
            android:layout_height="match_parent"/>
    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>