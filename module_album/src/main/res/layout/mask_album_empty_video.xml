<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/Gray_Background"
        android:clickable="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/all_img_warning_no_content" />

            <com.commsource.widget.BoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:text="@string/t_no_content"
                android:textSize="18dp" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/t_to_beautyplus_shoot"
                android:textColor="@color/Gray_D"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/tv_shoot"
                radius="@{22}"
                strokeColor="@{@color/Gray_A}"
                strokeWidth="@{1}"
                android:layout_width="102dp"
                android:layout_height="44dp"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:text="@string/t_to_video_shoot"
                android:visibility="visible"
                android:textColor="@color/Gray_A"
                android:textSize="15dp" />

        </LinearLayout>

    </FrameLayout>
</layout>