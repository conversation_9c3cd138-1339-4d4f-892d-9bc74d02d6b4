/*****************************************************************
 * 手机全局配置类
 *
 * @version: 1.0
 * @date: 2015-01-21
 * @author: <PERSON><PERSON><PERSON>
 * @note:
 *
 * @change:
 *
 ******************************************************************/
#ifndef _MT_NATIVECONTROL_SYSTEM_CONFIG_H_
#define _MT_NATIVECONTROL_SYSTEM_CONFIG_H_

#include "PlatformDefine.h"

#include <stdio.h>
#include <string.h>
#if defined (PLATFORM_ANDROID)
#include <android/asset_manager.h>
#endif

namespace mtlaboversea {

//低端摄像头的像素定义，中断摄像头的像素定义
#define MT_DEFINE_CAMERA_PIXELS_MIDDLE    2000000
#define MT_DEFINE_CAMERA_PIXELS            1000000

//路径最大长度
#ifndef MAX_FILE_PATH
#define MAX_FILE_PATH 256
#endif

//版本号信息
// 主版本号
#define NATIVE_MAJOR_AND_MINOR_VERSION          "1.1"

//小版本号 主要用于 bugfix及小幅度更新，跟打包的次数匹配。
#define NATIVE_REVISION_VERSION 1

//打包库的Git提交记录信息，打包前同步修改
#define NATIVE_BUILD_VERSION                    "479edbf"

// 这个类存储手机端全局配置参数
    class CSysConfig {
    private:
        CSysConfig(void);

        // 初始化，执行配置初始（读数据或默认值设定）
        void Initialize();

    public:
        // 获得系统配置单件实例
        static CSysConfig *getInstance();

        virtual ~CSysConfig(void);

        //设置相机像素
        void setCameraPixelCount(int count);

        //获取像素
        int getCameraPixelCount();

        //设置apk是否合法
        void setApkLegal(bool islegal = false);

        //apk是否合法
        bool isApkLegal();

        // 设置是否是非法包
        void setGL3Support(bool isSupport = true);

        // 是否是合法包
        bool isGL3Support();

        //设置当前是否为Debug版本的app
        void setDebugMode(bool isDebugMode = false);

        //获取当前是正式包还是调试包
        bool isDebugMode();

        //设置AssetsManager
        void setAssetsManager(AAssetManager *assets);

        //获取AssetsManger
        AAssetManager *getAssetsManager();

        //设置app包名
        void setPackageName(const char *packageName);

        //获得包名
        const char *getPackageName();

        //设置apk路径
        void setApkPath(const char *apkPath);

        //获取app的apk路径
        const char *getApkPath();

        //设置App 的系统目录
        void setDataDir(const char *dataDir);

        //获取App 的系统目录
        const char *getDataDir();

        //设置 缓存文件夹
        void setTempDir(const char *temDir);

        //获取 缓存文件夹
        const char *getTempDir();

        //设置素材在Sdcard目录的位置
        void setMaterialDir(const char *dir);

        //获取素材目录
        const char *getMaterialDir();

        //设置sdk版本
        void setSDKVersion(int sdkVersion);

        //获得sdk版本
        int getSDKVersion();

        //获取瘦脸配置
        bool needAutoSlimFace();

        //设置手机型号
        void setPhoneMode(const char *phoneMode);

        //获取手机型号
        const char *getPhoneMode();

    private:
        //当前机型的摄像头像素
        int m_cameraMaxPixels;

        //当前使用JNI库的App是否合法
        bool m_apkLegal;

        //是否为Debug模式
        bool m_isDebug;

        // 迷惑命名，实际上是记录是否破解包的变量，上层传入，默认值为true
        bool m_isGL30Support;

        //Asset manager
        AAssetManager *m_AssetManager;

        //当前App的包名
        char m_packageName[MAX_FILE_PATH];

        //当前app的apk路径
        char m_apkPath[MAX_FILE_PATH];

        //当前app的dataDir路径
        char m_dataDir[MAX_FILE_PATH];

        //缓存文件夹
        char m_tempDir[MAX_FILE_PATH];

        //素材文件目录
        char m_materiaDir[MAX_FILE_PATH];

        //sdk version
        int m_sdkVersion;

        //机型
        char m_phoneMode[MAX_FILE_PATH];
    };
}

#endif // _MT_CONTROL_SYSTEM_CONFIG_H_
