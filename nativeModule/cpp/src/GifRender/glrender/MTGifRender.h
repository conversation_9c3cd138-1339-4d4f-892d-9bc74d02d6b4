//
// Created by meitu on 2018/11/2.
//

#ifndef SELFIECITY_ANDROID_MTGIFRENDER_H
#define SELFIECITY_ANDROID_MTGIFRENDER_H

#include "MTGifFilter.hpp"
#include <functional>

using namespace std;

class MTGifRender {
public:
    MTGifRender();
    virtual ~MTGifRender();
    /**
     初始化

     */
    virtual bool Initlize();
    /**
     获取脚本里面的字段handle
     */
    virtual void FindGLHandle();

    /*
     * 渲染方法
     * @param srcTexture    原图数据
     * @param toFBO         输出缓冲区
     * @param toFBOTexture  输出纹理
     * @param resultWidth   宽
     * @param resultHeight  高
     */
    void RenderToFBO(GLuint srcTexture, GLuint toFBO, GLuint toFBOTexture, int resultWidth,int resultHeight);

    /**
     ios的接口
     
     @param texIn   输入纹理
     @param texOut  输出纹理
     @param width   纹理宽
     @param height  纹理高
     */
    int renderToTexture(GLuint texIn, GLuint texOut, int width, int height);
    
    void addGifFilter(CMTGifFilter* filter);

    void addGifFile(string path);

    /*
 * 点击事件
 */
    void onToucheBegin(float x, float y);

    /*
     * 移动事件
     */
    void onTouchMove(float x, float y);

    /**
     * 两支手指点击事件
     * @param x1 第一根手指的x
     * @param y1 第一根手指的y
     * @param x2 第二根手指的x
     * @param y2 第二根手指的y
     * @return 是否有触发到，需要两根手指同时触摸到当前顶点坐标区域内
     */
    void onTouchBeginTwoFinger(float x1, float y1, float x2, float y2);

    /**
     * 两支手指移动事件
     * @param x1 第一根手指的x
     * @param y1 第一根手指的y
     * @param x2 第二根手指的x
     * @param y2 第二根手指的y
     * @return 是否有触发到，需要两根手指同时触摸到当前顶点坐标区域内
     */
    void onTouchMoveTwoFinger(float x1, float y1, float x2, float y2);
    
    /**
     * 触摸事件结束，所有手指都立刻了屏幕
     */
    void onTouchEnd(){current_filter = nullptr;}
    
    /**
     * 是否有GIF对象被选中
     * @return 是否有选中
     */
    bool isSelectGif(){return current_filter? true : false;}
    
    /**
     * 删除当前选中的GIF对象
     */
    bool deleteCurrentGif();
    
    void setDisplayView(float left, float top, float scaleWidth, float scaleHeight) {
        m_left = left;
        m_top = top;
        m_scaleWidth = scaleWidth;
        m_scaleHeight = scaleHeight;
    }

    void setOrientation(int nOrientation){m_orientation = nOrientation;}
    
    /**
     * 设置当前选中的变为当前镜像
     */
    void setGIfImage();

    /**
     * 情况所有GIF
     */
    void clearAllGif();

    /**
     * 获取当前有多少个GIF对象
     * @return 当前GIF对象数量
     */
    int getCountOfGif(){ return (int)m_gifArray.size();}

    /**
     * 只释放gl相关
     */
    void releaseGLResource();

    /**
     * 重新初始化gl相关，纹理根据纹理地址列表
     */
    void reInitGLResource();
    
    /**
     * 设置动画开启状态
     * @param bStatus 动画状态
     */
    void setAnimationStatus(bool bStatus);
    
    /**
     * 设置动画顶点限制区域
     * @param x 左上角坐标x
     * @param y 左上角坐标y
     * @param width 区域宽
     * @param height 区域高
     */
    void setAnimationPositionsArea(float x, float y, float width, float height);
    
    /**
     * 获取当前显示区域
     * @param x 左上角坐标x
     * @param y 左上角坐标y
     * @param width 区域宽
     * @param height 区域高
     */
    void getPositionsArea(float &x, float &y, float &width, float &height);

public:
    function<void(void)> finishGifCallback;

private:
    vector<CMTGifFilter *> m_gifArray;

    vector<string> m_gifPathArray;  // 存GIF文件地址

    //特效Progma
    GLuint m_ProgramHandle;

    //顶点句柄
    GLint m_VertexHandle;

    //纹理坐标句柄
    int m_TexcoordHandle;

    //mvp矩阵句柄
    GLint m_MvpHandle;
    GLint inputTextureHandle; // 纹理句柄
    //渲染纹理的大小
    int m_TextureWidth;
    int m_TextureHeight;
    
    GLuint m_AsFilterFrameBuffer;

    CMTGifFilter* current_filter;
    
    float m_left;
    float m_top;
    float m_scaleWidth;
    float m_scaleHeight;
    
    int m_orientation;

private:
    void render(GLuint gifTextureID, const void* positions, const void *texcoords);

    void BindFBO(GLuint texture, GLuint fbo);
    
    void deleteGifFilter(int nIndex);
};


#endif //SELFIECITY_ANDROID_MTGIFRENDER_H
