#! /bin/bash

# 仓库的数量。
REPOSITORIES_NUM=8

MODULE_DES_DIR=("cpp/3rdparty/")
# 每一个仓库对应的模块名称。
MODULE_NAME=(
    "libMTDLGeneralImageEnhance"
    "libmttypes"
    "libmtcvlite"
    "libMTDLComponent"
    "libMTBeautyKernel"
    "libmtlab-base"
    "libyuv"
)

# SSH协议的仓库地址。
SSH_REPOSITORIES=(
    "*********************:zmk/libMTDLGeneralImageEnhance.git"
    "*********************:iOSModules_public/libmttypes.git"
    "*********************:MTlabBinarys/libmtcvlite.git"
    "*********************:MTlabBinarys/libMTDLComponent.git"
    "*********************:MTlabBinarys/libMTBeautyKernel.git"
    "*********************:MTlabBinarys/libmtlab-base.git"
    "*********************:MTlabBinarys/3rdpartyBinary/libyuv.git"
)

# 每个模块的目标分支
TARGET_BRANCHES=(
	"0.0.3.3"
	"3.1.3.0" 
	"0.2.2.1"
	"0.0.2.3"
    "0.0.9.7-PurryEyeCleaner-Opt"
    "0.0.24.0"
    "1.7.2.7"
)

# 采用SSH协议克隆仓库到本地。
function clone_with_ssh() {
	# 遍历所有仓库进行克隆。
	for((i=0; i<$REPOSITORIES_NUM; i++))
	do
		repository=${SSH_REPOSITORIES[i]}
		module_name=${MODULE_NAME[i]}
		if [ ! -d ${module_name} ]; then
			echo -e "\033[32m""<---------------------------Start clone [" ${module_name} "]--------------------------->""\033[0m"
			git clone ${repository} ${module_name}
			echo -e "\033[32m""<---------------------------Finish clone [" ${module_name} "]--------------------------->""\033[0m"
			echo
		else
			echo -e "\033[32m""The repository [" ${module_name} "] is already exist. Start checkout tag and pull updates ""\033[0m"
			cd ${module_name}
			git checkout ${TARGET_BRANCHES[i]}
			git pull
			echo -e "\033[32m""The repository [" ${module_name} "] was updated to latest version. ""\033[0m"
			cd ..	
			
		fi
	done
}

function update_with_ssh() {
	for((i=0; i<$REPOSITORIES_NUM; i++))
	do
		module_name=${MODULE_NAME[i]}
		cd ${module_name}
		if [ -d ".git" ]; then
			echo -e "\033[32m""<---------------------------Start update  "${module_name}"--------------------------->""\033[0m"
			git checkout ${TARGET_BRANCHES[i]}
			git pull
			echo -e "\033[32m""<---------------------------Finish update "${module_name}"--------------------------->""\033[0m"
			echo
		fi
		cd ..
	done

	echo `dirname $0`
}


cd ${MODULE_DES_DIR[0]}

while true
do
echo "请输入要执行的操作\n 1:  初始化仓库(通常第一次更新版本操作之前需要执行一次) \n 2:  更新仓库 \n 3:  退出 \n"

read -t 3600 -p "请输入:" platform
if [ $platform -eq 1 ]; then
clone_with_ssh
elif [ $platform -eq 2 ]; then
update_with_ssh
elif [ $platform -eq 3 ]; then
exit -1
fi

done



