{"formatVersion": 1, "database": {"version": 93, "identityHash": "12c04f26aab1b60b6d46e469ebf4abc6", "entities": [{"tableName": "AR_MATERIAL", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER, `NUMBER` INTEGER NOT NULL, `SORT` INTEGER NOT NULL, `VERSION_CONTROL` INTEGER NOT NULL, `MIN_VERSION` TEXT, `MAX_VERSION` TEXT, `FILE_URL` TEXT, `FILE_SIZE` TEXT, `THUMBNAIL` TEXT, `PREVIEW_URL` TEXT, `IS_HOT` INTEGER NOT NULL, `HOT_SORT` INTEGER NOT NULL, `HOT_END_TIME` INTEGER NOT NULL, `INTERACTIVE` INTEGER NOT NULL, `AUTO_DOWNLOAD` INTEGER NOT NULL, `IS_NEW` INTEGER NOT NULL, `NEW_SORT` INTEGER NOT NULL, `RED_TIME` INTEGER NOT NULL, `NEW_END_TIME` INTEGER NOT NULL, `END_TIME` INTEGER NOT NULL, `WEIGHT` INTEGER NOT NULL, `AFTER_WEIGHT` INTEGER NOT NULL, `TITLE` TEXT, `BGM_FLAG` INTEGER NOT NULL, `DBG_ENABLE` INTEGER NOT NULL, `DBG_URL` TEXT, `DBG_NUMBER` TEXT, `IS_3D` INTEGER NOT NULL, `FEATURED_SORT` INTEGER NOT NULL, `IS_FEATURED` INTEGER NOT NULL, `IS_COLLECTED` INTEGER NOT NULL, `IS_DOWNLOAD` INTEGER NOT NULL, `IS_DOWNLOADING` INTEGER NOT NULL, `DOWNLOAD_TIME` INTEGER NOT NULL, `IS_HIDE_RED` INTEGER NOT NULL, `GROUP_NUMBER` INTEGER NOT NULL, `IS_BG_DOWNLOAD` INTEGER NOT NULL, `IS_BG_DOWNLOADING` INTEGER NOT NULL, `IS_3D_DOWNLOAD` INTEGER NOT NULL, `IS_3D_DOWNLOADING` INTEGER NOT NULL, `END_USE_TIME` INTEGER NOT NULL, `IS_DYE_HAIR` INTEGER NOT NULL, `IS_DYE_HAIR_DOWNLOAD` INTEGER NOT NULL, `IS_DYE_HAIR_DOWNLOADING` INTEGER NOT NULL, `COLLECTION_TIME` INTEGER NOT NULL, `IS_HUMAN_POSTURE` INTEGER NOT NULL, `IS_HUMAN_DOWNLOAD` INTEGER NOT NULL, `IS_HUMAN_DOWNLOADING` INTEGER NOT NULL, `AR_ONLINE_TEXT` TEXT, `IS_SWITCHING` INTEGER NOT NULL, `IS_PHYSICAL` INTEGER NOT NULL, `IS_TOUCH` INTEGER NOT NULL, `REGION_HOT_SORT` INTEGER NOT NULL, `IS_ANIMAL_DOWNLOAD` INTEGER NOT NULL, `IS_PET` INTEGER NOT NULL, `REDIRECT_TO` TEXT, `ENABLE_TEXT` INTEGER NOT NULL, `IS_AFTER_SHARE` INTEGER NOT NULL, `IS_SHARE_LOCK` INTEGER NOT NULL, `IS_SKELETAL` INTEGER NOT NULL, `IS_SKELETON_DOWNLOAD` INTEGER NOT NULL, `IS_FACE_3D_V2` INTEGER NOT NULL, `IS_NECK_LOCK_POINT` INTEGER NOT NULL, `IS_NECK_LOCK_DOWNLOAD` INTEGER NOT NULL, `IS_FACE_3D_V2_DOWNLOAD` INTEGER NOT NULL, `AR_CORE_TYPE` INTEGER NOT NULL, `HASH_TAG` TEXT, `MAKE_LEVEL` INTEGER NOT NULL, `BEAUTY_LEVEL` INTEGER NOT NULL, `AR_HELP_TITLE` TEXT, `AR_HELP_RULE` INTEGER NOT NULL, `AR_HELP_URL` TEXT, `AR_HELP_IS_DOWNLOAD` INTEGER NOT NULL, `AR_HELP_TIP_TYPE` INTEGER NOT NULL, `IP_COLOR_CODE` TEXT, `LOCATIONS` INTEGER NOT NULL, `IS_HAND_POSE` INTEGER NOT NULL, `IS_NEED_WATER_MARK` INTEGER NOT NULL, `IP_STROE_ID` INTEGER NOT NULL, `LOCK_CAMERA` INTEGER NOT NULL, `AR_CORE_ICON` TEXT, `ALLOW_MODELS` INTEGER NOT NULL, `LIMIT_SPECIFIC_MODELS` TEXT, `SHOW_AR_CORE_ICON` INTEGER NOT NULL, `PAID_TYPE` TEXT, `NEW_AR` TEXT, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "number", "columnName": "NUMBER", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sort", "columnName": "SORT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "versionControl", "columnName": "VERSION_CONTROL", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "minVersion", "columnName": "MIN_VERSION", "affinity": "TEXT", "notNull": false}, {"fieldPath": "maxVersion", "columnName": "MAX_VERSION", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fileUrl", "columnName": "FILE_URL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fileSize", "columnName": "FILE_SIZE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thumbnail", "columnName": "THUMBNAIL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "previewUrl", "columnName": "PREVIEW_URL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isHot", "columnName": "IS_HOT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hotSort", "columnName": "HOT_SORT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hotEndTime", "columnName": "HOT_END_TIME", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "interactive", "columnName": "INTERACTIVE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "autoDownload", "columnName": "AUTO_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isNew", "columnName": "IS_NEW", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newSort", "columnName": "NEW_SORT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "redTime", "columnName": "RED_TIME", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newEndTime", "columnName": "NEW_END_TIME", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endTime", "columnName": "END_TIME", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "weight", "columnName": "WEIGHT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "afterWeight", "columnName": "AFTER_WEIGHT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bgmFlag", "columnName": "BGM_FLAG", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dbgEnable", "columnName": "DBG_ENABLE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dbgUrl", "columnName": "DBG_URL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dbgNumber", "columnName": "DBG_NUMBER", "affinity": "TEXT", "notNull": false}, {"fieldPath": "is3D", "columnName": "IS_3D", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "featuredSort", "columnName": "FEATURED_SORT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFeatured", "columnName": "IS_FEATURED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isCollected", "columnName": "IS_COLLECTED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDownload", "columnName": "IS_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDownloading", "columnName": "IS_DOWNLOADING", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadTime", "columnName": "DOWNLOAD_TIME", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isHideRed", "columnName": "IS_HIDE_RED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupNumber", "columnName": "GROUP_NUMBER", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isBgDownload", "columnName": "IS_BG_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isBgDownloading", "columnName": "IS_BG_DOWNLOADING", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is3DDownload", "columnName": "IS_3D_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is3DDownloading", "columnName": "IS_3D_DOWNLOADING", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endUseTime", "columnName": "END_USE_TIME", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDyeHair", "columnName": "IS_DYE_HAIR", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDyeHairDownload", "columnName": "IS_DYE_HAIR_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDyeHairDownloading", "columnName": "IS_DYE_HAIR_DOWNLOADING", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collectionTime", "columnName": "COLLECTION_TIME", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isHumanPosture", "columnName": "IS_HUMAN_POSTURE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isHumanDownload", "columnName": "IS_HUMAN_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isHumanDownloading", "columnName": "IS_HUMAN_DOWNLOADING", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "arOnlineText", "columnName": "AR_ONLINE_TEXT", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isSwitching", "columnName": "IS_SWITCHING", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPhysical", "columnName": "IS_PHYSICAL", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "is<PERSON><PERSON>ch", "columnName": "IS_TOUCH", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "regionHotSort", "columnName": "REGION_HOT_SORT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isAnimalDownload", "columnName": "IS_ANIMAL_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPet", "columnName": "IS_PET", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "redirectTo", "columnName": "REDIRECT_TO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enableText", "columnName": "ENABLE_TEXT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isAfterShare", "columnName": "IS_AFTER_SHARE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isShareLock", "columnName": "IS_SHARE_LOCK", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSkeletal", "columnName": "IS_SKELETAL", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSkeletalDownload", "columnName": "IS_SKELETON_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFace3dV2", "columnName": "IS_FACE_3D_V2", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isNeckLockPoint", "columnName": "IS_NECK_LOCK_POINT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isNeckLockDownloaded", "columnName": "IS_NECK_LOCK_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFace3dV2Download", "columnName": "IS_FACE_3D_V2_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "arCoreType", "columnName": "AR_CORE_TYPE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hashTags", "columnName": "HASH_TAG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "makeLevel", "columnName": "MAKE_LEVEL", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "beautyLevel", "columnName": "BEAUTY_LEVEL", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "arHelpTitle", "columnName": "AR_HELP_TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "arHelpRule", "columnName": "AR_HELP_RULE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "arHelpUrl", "columnName": "AR_HELP_URL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "arHelpIsDown", "columnName": "AR_HELP_IS_DOWNLOAD", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "arTipType", "columnName": "AR_HELP_TIP_TYPE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ipColorCode", "columnName": "IP_COLOR_CODE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "locations", "columnName": "LOCATIONS", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isHandPose", "columnName": "IS_HAND_POSE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isNeedWaterMark", "columnName": "IS_NEED_WATER_MARK", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ipStoreId", "columnName": "IP_STROE_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isLockCamera", "columnName": "LOCK_CAMERA", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "arCoreIcon", "columnName": "AR_CORE_ICON", "affinity": "TEXT", "notNull": false}, {"fieldPath": "allowModelLevel", "columnName": "ALLOW_MODELS", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "limitSpecificModelString", "columnName": "LIMIT_SPECIFIC_MODELS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "showArCoreIcon", "columnName": "SHOW_AR_CORE_ICON", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "PAID_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newAr", "columnName": "NEW_AR", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "AR_MATERIAL_GROUP", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER, `NUMBER` INTEGER NOT NULL, `VERSION_CONTROL` INTEGER NOT NULL, `MIN_VERSION` TEXT, `MAX_VERSION` TEXT, `END_TIME` INTEGER NOT NULL, `ICON` TEXT, `TITLE` TEXT, `SORT` INTEGER NOT NULL, `G<PERSON><PERSON>_RED` INTEGER NOT NULL, `ONLINE_AT` INTEGER NOT NULL, `SEQUENCE` TEXT, `IS_RED` INTEGER NOT NULL, `IS_IP` INTEGER NOT NULL, `AD_SLOT_ID` TEXT, `IP_LOGO` TEXT, `IP_GROUP_SORT` INTEGER NOT NULL, `LITERAL_ICON` INTEGER NOT NULL, `LITERAL` TEXT, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "number", "columnName": "NUMBER", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "versionControl", "columnName": "VERSION_CONTROL", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "minVersion", "columnName": "MIN_VERSION", "affinity": "TEXT", "notNull": false}, {"fieldPath": "maxVersion", "columnName": "MAX_VERSION", "affinity": "TEXT", "notNull": false}, {"fieldPath": "endTime", "columnName": "END_TIME", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "icon", "columnName": "ICON", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "SORT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupRed", "columnName": "GROUP_RED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineAt", "columnName": "ONLINE_AT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sequence", "columnName": "SEQUENCE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isRed", "columnName": "IS_RED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isIp", "columnName": "IS_IP", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "adSlotId", "columnName": "AD_SLOT_ID", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ipLogo", "columnName": "IP_LOGO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ipGroupSort", "columnName": "IP_GROUP_SORT", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "literalIcon", "columnName": "LITERAL_ICON", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "literal", "columnName": "LITERAL", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "AR_MATERIAL_PAID_INFO", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER, `NUMBER` INTEGER NOT NULL, `CATEGORY_NUMBER` INTEGER NOT NULL, `GOODS_ID` TEXT, `ITEMS` TEXT, `IS_PAID` INTEGER NOT NULL, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "number", "columnName": "NUMBER", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryNumber", "columnName": "CATEGORY_NUMBER", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "goodsId", "columnName": "GOODS_ID", "affinity": "TEXT", "notNull": false}, {"fieldPath": "items", "columnName": "ITEMS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isPaid", "columnName": "IS_PAID", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "CHAT_FILED", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER, `UID` TEXT, `TOKEN` TEXT, `IMAGE_PATH` TEXT, `UPLOAD_STATE` INTEGER, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "uid", "columnName": "UID", "affinity": "TEXT", "notNull": false}, {"fieldPath": "token", "columnName": "TOKEN", "affinity": "TEXT", "notNull": false}, {"fieldPath": "imagePath", "columnName": "IMAGE_PATH", "affinity": "TEXT", "notNull": false}, {"fieldPath": "uploadState", "columnName": "UPLOAD_STATE", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "CHAT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` REAL NOT NULL, `UID` TEXT, `CONTENT` TEXT, `ROLE` INTEGER, `TIME` TEXT, `CHAT_FAIL` INTEGER, `IMAGE` TEXT, `HASIMG` INTEGER, `UPLOAD_STATE` INTEGER, PRIMARY KEY(`ID`))", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "REAL", "notNull": true}, {"fieldPath": "uid", "columnName": "UID", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content", "columnName": "CONTENT", "affinity": "TEXT", "notNull": false}, {"fieldPath": "role", "columnName": "ROLE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "time", "columnName": "TIME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatFail", "columnName": "CHAT_FAIL", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "image", "columnName": "IMAGE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "hasimg", "columnName": "HASIMG", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "uploadState", "columnName": "UPLOAD_STATE", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"columnNames": ["ID"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "MONTAGE_GROUP_ENTITY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `category_id` TEXT, `group_name` TEXT, `icon_url` TEXT, `group_type` INTEGER NOT NULL, `group_sort` INTEGER NOT NULL, `group_is_available` INTEGER NOT NULL, `group_gender` INTEGER NOT NULL, `group_is_new_girl` INTEGER NOT NULL, `group_is_new_man` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "categoryId", "columnName": "category_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupName", "columnName": "group_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iconUrl", "columnName": "icon_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupType", "columnName": "group_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupSort", "columnName": "group_sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupIsAvailable", "columnName": "group_is_available", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupGender", "columnName": "group_gender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupIsNewForGirl", "columnName": "group_is_new_girl", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupIsNewForMan", "columnName": "group_is_new_man", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "MONTAGE_MATERIAL_ENTITY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `material_id` TEXT, `category_id` TEXT, `material_name` TEXT, `icon_url` TEXT, `file_url` TEXT, `md5` TEXT, `material_type` INTEGER NOT NULL, `material_sort` INTEGER NOT NULL, `material_download_type` INTEGER NOT NULL, `status` INTEGER NOT NULL, `material_is_available` INTEGER NOT NULL, `material_gender` INTEGER NOT NULL, `material_is_new_girl` INTEGER NOT NULL, `material_is_new_man` INTEGER NOT NULL, `material_is_paid` INTEGER NOT NULL, `material_goods_id` TEXT, `material_sample_url` TEXT, `material_limit_version` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "materialId", "columnName": "material_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categoryId", "columnName": "category_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "materialName", "columnName": "material_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iconUrl", "columnName": "icon_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "materialFileUrl", "columnName": "file_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fileMD5", "columnName": "md5", "affinity": "TEXT", "notNull": false}, {"fieldPath": "materialType", "columnName": "material_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialSort", "columnName": "material_sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialDownloadType", "columnName": "material_download_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialStatus", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isAvailable", "columnName": "material_is_available", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialGender", "columnName": "material_gender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialIsNewForGirl", "columnName": "material_is_new_girl", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialIsNewForMan", "columnName": "material_is_new_man", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialIsPaid", "columnName": "material_is_paid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialGoodsId", "columnName": "material_goods_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "materialSampleUrl", "columnName": "material_sample_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "materialLimitVersion", "columnName": "material_limit_version", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "NEW_FILTER", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`FilterId` INTEGER NOT NULL, `FilterThumbnail` TEXT, `FilterNewName` TEXT, `FilterOldName` TEXT, `FilterShopThumbnail` TEXT, `<PERSON><PERSON><PERSON><PERSON>Recommend` INTEGER NOT NULL, `FilterFileUrl` TEXT, `DisplayInList` INTEGER NOT NULL, `FilterSort` INTEGER NOT NULL, `CollectedState` INTEGER NOT NULL, `CollectedTime` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, `DownloadState` INTEGER NOT NULL, `GroupId` INTEGER NOT NULL, `FilterDefaultAlpha` INTEGER NOT NULL, `AlphaInCamera` INTEGER NOT NULL, `NeedNewMode` INTEGER NOT NULL, `NeedBodyMask` INTEGER NOT NULL, `NeedHairMode` INTEGER NOT NULL, `ShowState` INTEGER NOT NULL, `tag` INTEGER NOT NULL, PRIMARY KEY(`FilterId`))", "fields": [{"fieldPath": "filterId", "columnName": "FilterId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "filterThumbnail", "columnName": "FilterThumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "filterNewName", "columnName": "FilterNewName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "filterOldName", "columnName": "FilterOldName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "filterShopThumbnail", "columnName": "FilterShopThumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "filterRecommend", "columnName": "IsFilterRecommend", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "filterFileUrl", "columnName": "FilterFileUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "displayInList", "columnName": "DisplayInList", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "filterSort", "columnName": "FilterSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collectedState", "columnName": "CollectedState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collectedTime", "columnName": "CollectedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "DownloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupId", "columnName": "GroupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "filterDefaultAlpha", "columnName": "FilterDefaultAlpha", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "alphaInCamera", "columnName": "AlphaInCamera", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needNewMode", "columnName": "NeedNewMode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needBodyMask", "columnName": "NeedBodyMask", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needHairMask", "columnName": "NeedHairMode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needShow", "columnName": "ShowState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tag", "columnName": "tag", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["FilterId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "FILTER_CATEGORY_INFO", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`CategoryId` INTEGER NOT NULL, `CategoryName` TEXT, `CategorySort` INTEGER NOT NULL, `LockLocalState` INTEGER NOT NULL, PRIMARY KEY(`CategoryId`))", "fields": [{"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryName", "columnName": "CategoryName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categorySort", "columnName": "CategorySort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lockLocalState", "columnName": "LockLocalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["CategoryId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "FILTER_GROUP_INFO", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`GroupId` INTEGER NOT NULL, `FilterCount` INTEGER NOT NULL, `GroupName` TEXT, `GroupDesc` TEXT, `GroupPaidState` INTEGER NOT NULL, `GroupThumbnail` TEXT, `GroupPaidInfo` TEXT, `isAvailable` INTEGER NOT NULL, `GroupSort` INTEGER NOT NULL, `GroupTag` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, `DownloadType` INTEGER NOT NULL, `GroupPrice` TEXT, `CategoryId` INTEGER NOT NULL, `GroupColor` TEXT, `updateAt` INTEGER NOT NULL, `localInsertTime` INTEGER NOT NULL, PRIMARY KEY(`GroupId`))", "fields": [{"fieldPath": "groupId", "columnName": "GroupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "filterCount", "columnName": "FilterCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupName", "columnName": "GroupName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupDesc", "columnName": "GroupDesc", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupPaidState", "columnName": "GroupPaidState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupThumbnail", "columnName": "GroupThumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupPaidInfo", "columnName": "GroupPaidInfo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "available", "columnName": "isAvailable", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupSort", "columnName": "GroupSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupTag", "columnName": "GroupTag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "DownloadType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupPrice", "columnName": "GroupPrice", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupColor", "columnName": "GroupColor", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updateAt", "columnName": "updateAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "localInsertTime", "columnName": "localInsertTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["GroupId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "LOOK_MATERIAL_DUFFLE", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`m_id` TEXT NOT NULL, `sort` INTEGER NOT NULL, `name` TEXT, `isInside` INTEGER NOT NULL, `file` TEXT, `icon` TEXT, `color` TEXT, `download_type` INTEGER NOT NULL, `sex` INTEGER NOT NULL, `isDownloaded` INTEGER NOT NULL, `isNew` INTEGER NOT NULL, `isNewTime` INTEGER NOT NULL, `isGl3` INTEGER NOT NULL, `endedAt` INTEGER NOT NULL, `relationicons` TEXT, `paidType` INTEGER NOT NULL, `RecommendState` INTEGER NOT NULL, `RecommendSort` INTEGER NOT NULL, `categoryId` TEXT NOT NULL, PRIMARY KEY(`m_id`))", "fields": [{"fieldPath": "onlineId", "columnName": "m_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "insideState", "columnName": "isInside", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "url", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "uiColor", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "downloadMode", "columnName": "download_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sex", "columnName": "sex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "isDownloaded", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newState", "columnName": "isNew", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "isNewTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "limitOnlyGl3", "columnName": "isGl3", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endedAt", "columnName": "endedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "singleMakeup", "columnName": "relationicons", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paidType", "columnName": "paidType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendState", "columnName": "RecommendState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendSort", "columnName": "RecommendSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryId", "columnName": "categoryId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["m_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "MAKEUP_MATERIAL", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `onlineId` TEXT NOT NULL, `isInside` INTEGER NOT NULL, `makeupType` INTEGER NOT NULL, `name` TEXT, `icon` TEXT, `configType` INTEGER NOT NULL, `styleConfig` TEXT, `url` TEXT, `color` TEXT, `paidType` INTEGER NOT NULL, `defaultAlpha` INTEGER NOT NULL, `downloadType` INTEGER NOT NULL, `isDownloaded` INTEGER NOT NULL, `sort` INTEGER NOT NULL, `isSupportGl3` INTEGER NOT NULL, `isNew` INTEGER NOT NULL, `isNewTime` INTEGER NOT NULL, `endedAt` INTEGER NOT NULL, `recommends` TEXT, `picture_editor` INTEGER NOT NULL DEFAULT 1, `video_editor` INTEGER NOT NULL DEFAULT 1)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "onlineId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isInside", "columnName": "isInside", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "makeupType", "columnName": "makeupType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "configType", "columnName": "configType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "styleConfig", "columnName": "styleConfig", "affinity": "TEXT", "notNull": false}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "color", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paidType", "columnName": "paidType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "defaultAlpha", "columnName": "defaultAlpha", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "downloadType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isDownloaded", "columnName": "isDownloaded", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSupportGl3", "columnName": "isSupportGl3", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isNew", "columnName": "isNew", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isNewTime", "columnName": "isNewTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endedAt", "columnName": "endedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommends", "columnName": "recommends", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pictureEditor", "columnName": "picture_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "videoEditor", "columnName": "video_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "DOODLE_MATERIAL", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`DoodleId` INTEGER NOT NULL, `DoodleThumbnail` TEXT, `DoodleFile` TEXT, `PaidState` INTEGER NOT NULL, `DoodleTag` INTEGER NOT NULL, `DoodleRecommendState` INTEGER NOT NULL, `DownloadType` INTEGER NOT NULL, `isAvailable` INTEGER NOT NULL, `CanEditColor` INTEGER NOT NULL, `ProductId` TEXT, `DoodleAmount` INTEGER NOT NULL, `ListDisplay` INTEGER NOT NULL, `NeedShow` INTEGER NOT NULL, `CategoryId` INTEGER NOT NULL, `DoodleSort` INTEGER NOT NULL, `DoodleCollectState` INTEGER NOT NULL, `DoodleCollectTime` INTEGER NOT NULL, `updateAt` INTEGER NOT NULL, `localInsertTime` INTEGER NOT NULL, `DownloadState` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, PRIMARY KEY(`DoodleId`))", "fields": [{"fieldPath": "doodleId", "columnName": "<PERSON>dleId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "doodle<PERSON><PERSON><PERSON>nail", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "doodleFile", "columnName": "DoodleFile", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paidState", "columnName": "PaidState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "doodleTag", "columnName": "DoodleTag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendState", "columnName": "DoodleRecommendState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "DownloadType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "available", "columnName": "isAvailable", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "canEditColor", "columnName": "CanEditColor", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "doodleGoodId", "columnName": "ProductId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "doodleAmount", "columnName": "DoodleAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "listDisplay", "columnName": "ListDisplay", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needShow", "columnName": "NeedShow", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "doodleSort", "columnName": "DoodleSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collectState", "columnName": "DoodleCollectState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collectTime", "columnName": "DoodleCollectTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateAt", "columnName": "updateAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "localInsertTime", "columnName": "localInsertTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "DownloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["<PERSON>dleId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "DOODLE_CATEGORY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`CategoryId` INTEGER NOT NULL, `CategoryName` TEXT, `CategorySort` INTEGER NOT NULL, `LockLocalState` INTEGER NOT NULL, PRIMARY KEY(`CategoryId`))", "fields": [{"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryName", "columnName": "CategoryName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categorySort", "columnName": "CategorySort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lockLocalState", "columnName": "LockLocalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["CategoryId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "TEXTURE_MATERIAL", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`m_id` TEXT NOT NULL, `icon` TEXT, `name` TEXT, `file` TEXT, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `ended_at` INTEGER NOT NULL, `download_type` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `RecommendState` INTEGER NOT NULL, `RecommendSort` INTEGER NOT NULL, `CategoryIds` TEXT, `sort` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `CollectAt` INTEGER NOT NULL, `config` TEXT, `internalState` INTEGER NOT NULL, PRIMARY KEY(`m_id`))", "fields": [{"fieldPath": "id", "columnName": "m_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "download_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendState", "columnName": "RecommendState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendSort", "columnName": "RecommendSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryIds", "columnName": "CategoryIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collectAt", "columnName": "CollectAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "config", "columnName": "config", "affinity": "TEXT", "notNull": false}, {"fieldPath": "internalState", "columnName": "internalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["m_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "TEXT_TEMPLATE_MATERIAL", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`m_id` TEXT NOT NULL, `icon` TEXT, `name` TEXT, `file` TEXT, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `ended_at` INTEGER NOT NULL, `download_type` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `sort` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `config` TEXT, `internalState` INTEGER NOT NULL, `RecommendState` INTEGER NOT NULL, `RecommendSort` INTEGER NOT NULL, `CollectAt` INTEGER NOT NULL, `CategoryIds` TEXT, `assetVersion` TEXT, `new_picture_editor` INTEGER NOT NULL DEFAULT -1, `new_video_editor` INTEGER NOT NULL DEFAULT -1, PRIMARY KEY(`m_id`))", "fields": [{"fieldPath": "id", "columnName": "m_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "download_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "config", "columnName": "config", "affinity": "TEXT", "notNull": false}, {"fieldPath": "internalState", "columnName": "internalState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendState", "columnName": "RecommendState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendSort", "columnName": "RecommendSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collectAt", "columnName": "CollectAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryIds", "columnName": "CategoryIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "assetVersion", "columnName": "assetVersion", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pictureEditor", "columnName": "new_picture_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "-1"}, {"fieldPath": "videoEditor", "columnName": "new_video_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "-1"}], "primaryKey": {"columnNames": ["m_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "GRADIENT_MATERIAL", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`m_id` TEXT NOT NULL, `icon` TEXT, `name` TEXT, `file` TEXT, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `ended_at` INTEGER NOT NULL, `download_type` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `sort` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `config` TEXT, `internalState` INTEGER NOT NULL, PRIMARY KEY(`m_id`))", "fields": [{"fieldPath": "id", "columnName": "m_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "download_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "config", "columnName": "config", "affinity": "TEXT", "notNull": false}, {"fieldPath": "internalState", "columnName": "internalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["m_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "TEXT_FRONT_MATERIAL", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`m_id` TEXT NOT NULL, `icon` TEXT, `name` TEXT, `file` TEXT, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `ended_at` INTEGER NOT NULL, `download_type` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `bg_color_model` INTEGER NOT NULL DEFAULT 0, `bgColor` TEXT, `sort` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `category` TEXT, `internalState` INTEGER NOT NULL, PRIMARY KEY(`m_id`))", "fields": [{"fieldPath": "id", "columnName": "m_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "download_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bgColorModel", "columnName": "bg_color_model", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "bgColor", "columnName": "bgColor", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": false}, {"fieldPath": "internalState", "columnName": "internalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["m_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "DOODLE_MATERIAL_DUFFLE", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`m_id` TEXT NOT NULL, `icon` TEXT, `name` TEXT, `file` TEXT, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `ended_at` INTEGER NOT NULL, `download_type` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `PaidSort` INTEGER NOT NULL, `sort` INTEGER NOT NULL, `OnlineSort` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `CanEditColor` INTEGER NOT NULL, `ListDisplay` INTEGER NOT NULL, `Hot` INTEGER NOT NULL, `HotSort` INTEGER NOT NULL, `internalState` INTEGER NOT NULL, `CategoryId` TEXT, `DoodleCollectState` INTEGER NOT NULL, `DoodleCollectTime` INTEGER NOT NULL, `NeedShow` INTEGER NOT NULL, `localInsertTime` INTEGER NOT NULL, PRIMARY KEY(`m_id`))", "fields": [{"fieldPath": "id", "columnName": "m_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "download_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidSort", "columnName": "PaidSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "localSort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineSort", "columnName": "OnlineSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "canEditColor", "columnName": "CanEditColor", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "listDisplay", "columnName": "ListDisplay", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hot", "columnName": "Hot", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hotSort", "columnName": "HotSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "internalState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "collectState", "columnName": "DoodleCollectState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collectTime", "columnName": "DoodleCollectTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needShow", "columnName": "NeedShow", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "localInsertTime", "columnName": "localInsertTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["m_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "FORMULA_MATERIAL_DUFFLE", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`FormulaId` TEXT NOT NULL, `FormulaThumbnail` TEXT, `FormulaConfig` TEXT, `PaidState` INTEGER NOT NULL, `ProductsInfo` TEXT, `IconRatio` TEXT, `CreateTime` INTEGER NOT NULL, `HotState` INTEGER NOT NULL, `HotSort` INTEGER NOT NULL, `RecommendState` INTEGER NOT NULL, `RecommendSort` INTEGER NOT NULL, `NewState` INTEGER NOT NULL, `EndedAt` INTEGER NOT NULL, `Sort` TEXT, `OnlineSort` INTEGER NOT NULL, `RecentUseTime` INTEGER NOT NULL, `mainLayerCount` INTEGER NOT NULL, `effectPay` INTEGER NOT NULL, `CategoryId` TEXT, `DownloadState` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, PRIMARY KEY(`FormulaId`))", "fields": [{"fieldPath": "formulaId", "columnName": "FormulaId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "formulaThumbnail", "columnName": "FormulaThumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "formulaConfig", "columnName": "FormulaConfig", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paidState", "columnName": "PaidState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "productsInfo", "columnName": "ProductsInfo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iconRatio", "columnName": "IconRatio", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createTime", "columnName": "CreateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hotState", "columnName": "HotState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hotSort", "columnName": "HotSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendState", "columnName": "RecommendState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendSort", "columnName": "RecommendSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newState", "columnName": "NewState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endAtTime", "columnName": "EndedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sorts", "columnName": "Sort", "affinity": "TEXT", "notNull": false}, {"fieldPath": "onlineSort", "columnName": "OnlineSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recentUseTime", "columnName": "RecentUseTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mainLayerCount", "columnName": "mainLayerCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "effectPay", "columnName": "effectPay", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "downloadState", "columnName": "DownloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["FormulaId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "ONLINE_DIALOG_ENTITY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`Rid` TEXT NOT NULL, `SubStatus` INTEGER NOT NULL, `UserStatus` INTEGER NOT NULL, `<PERSON><PERSON><PERSON>evel` INTEGER NOT NULL, `Weight` INTEGER NOT NULL, `Bout` INTEGER NOT NULL, `LastShowDate` TEXT, `Number` INTEGER NOT NULL, `TriggerType` INTEGER NOT NULL, `PopupType` INTEGER NOT NULL, `PopupConfig` TEXT, `FeatureName` TEXT NOT NULL DEFAULT '', `FeedbackGuidance` INTEGER NOT NULL DEFAULT 0, `FreeFeatureTime` TEXT, `FeatureDialogNumber` INTEGER NOT NULL DEFAULT 1, `Trigger` TEXT, `AbCode` INTEGER NOT NULL DEFAULT 0, `createdAt` INTEGER NOT NULL, `updateAt` INTEGER NOT NULL, PRIMARY KEY(`Rid`))", "fields": [{"fieldPath": "rid", "columnName": "Rid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "subStatus", "columnName": "SubStatus", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userStatus", "columnName": "UserStatus", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deviceLevel", "columnName": "DeviceLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "weight", "columnName": "Weight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bout", "columnName": "Bout", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastShowDate", "columnName": "LastShowDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "number", "columnName": "Number", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "triggerType", "columnName": "TriggerType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "popupType", "columnName": "PopupType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "popupConfig", "columnName": "PopupConfig", "affinity": "TEXT", "notNull": false}, {"fieldPath": "featureName", "columnName": "FeatureName", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "feedbackGuidance", "columnName": "FeedbackGuidance", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "freeFeatureTime", "columnName": "FreeFeatureTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "featureDialogNumber", "columnName": "FeatureDialogNumber", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "trigger", "columnName": "<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "abCode", "columnName": "AbCode", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateAt", "columnName": "updateAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["Rid"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "FORMULA_CATEGORY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`CategoryId` TEXT NOT NULL, `CategoryName` TEXT, `CategorySort` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, PRIMARY KEY(`CategoryId`))", "fields": [{"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "categoryName", "columnName": "CategoryName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categorySort", "columnName": "CategorySort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["CategoryId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "LOOK_CATEGORY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`CategoryId` TEXT NOT NULL, `CategoryName` TEXT, `CategorySort` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, PRIMARY KEY(`CategoryId`))", "fields": [{"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "categoryName", "columnName": "CategoryName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categorySort", "columnName": "CategorySort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["CategoryId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "NEW_DOODLE_CATEGORY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`CategoryId` TEXT NOT NULL, `CategoryName` TEXT, `CategorySort` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, PRIMARY KEY(`CategoryId`))", "fields": [{"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "categoryName", "columnName": "CategoryName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categorySort", "columnName": "CategorySort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["CategoryId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "TEXT_TEMPLATE_CATEGORY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`CategoryId` TEXT NOT NULL, `CategoryName` TEXT, `CategorySort` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, PRIMARY KEY(`CategoryId`))", "fields": [{"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "categoryName", "columnName": "CategoryName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categorySort", "columnName": "CategorySort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["CategoryId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "BG_TEXTURE_CAT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`CategoryId` TEXT NOT NULL, `CategoryName` TEXT, `CategorySort` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, PRIMARY KEY(`CategoryId`))", "fields": [{"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "categoryName", "columnName": "CategoryName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categorySort", "columnName": "CategorySort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["CategoryId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "VSticker", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `newTag` INTEGER NOT NULL, `NewTime` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `historyTime` INTEGER NOT NULL, `parentPaidType` INTEGER NOT NULL, `CategoryIds` TEXT, `paid_type` INTEGER NOT NULL, `createAt` INTEGER NOT NULL, `recommendState` INTEGER NOT NULL, `recommendTime` INTEGER NOT NULL, `bgColorModel` INTEGER NOT NULL, `overlayMode` INTEGER NOT NULL, `file` TEXT, `icon` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "newTag", "columnName": "newTag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "NewTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "historyTime", "columnName": "historyTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "parentPaidType", "columnName": "parentPaidType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryIds", "columnName": "CategoryIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createAt", "columnName": "createAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendState", "columnName": "recommendState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendTime", "columnName": "recommendTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bgColorModel", "columnName": "bgColorModel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "overlayMode", "columnName": "overlayMode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "VStickerCategory", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `icon` TEXT, `name` TEXT, `paidType` INTEGER NOT NULL, `copyrightOwner` TEXT, `internalState` INTEGER NOT NULL, `onlineSort` INTEGER NOT NULL, `ParentId` TEXT, `CatLevel` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paidType", "columnName": "paidType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "copyrightOwner", "columnName": "copyrightOwner", "affinity": "TEXT", "notNull": false}, {"fieldPath": "internalState", "columnName": "internalState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineSort", "columnName": "onlineSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "parentId", "columnName": "ParentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "catLevel", "columnName": "CatLevel", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "Work", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `workType` INTEGER NOT NULL DEFAULT 0, `workDuration` INTEGER NOT NULL DEFAULT 0, `workId` TEXT NOT NULL, `height` INTEGER NOT NULL, `width` INTEGER NOT NULL, `updateTime` INTEGER NOT NULL, `needPay` INTEGER NOT NULL, `workFilePath` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "workType", "columnName": "workType", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "workDuration", "columnName": "workDuration", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "workId", "columnName": "workId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "height", "columnName": "height", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "width", "columnName": "width", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needPay", "columnName": "needPay", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "workFilePath", "columnName": "workFilePath", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "MOSAIC_MATERIAL", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`m_id` TEXT NOT NULL, `icon` TEXT, `name` TEXT, `file` TEXT, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `ended_at` INTEGER NOT NULL, `download_type` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `sort` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `internalState` INTEGER NOT NULL, PRIMARY KEY(`m_id`))", "fields": [{"fieldPath": "id", "columnName": "m_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "download_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "internalState", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["m_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "NEW_FILTER_MATERIAL", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`m_id` TEXT NOT NULL, `icon` TEXT, `Local_Icon` TEXT, `name` TEXT, `file` TEXT, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `ended_at` INTEGER NOT NULL, `download_type` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `internalState` INTEGER NOT NULL, `CollectAt` INTEGER NOT NULL, `CategoryIds` TEXT, `Sort` INTEGER NOT NULL, `ListDisplay` INTEGER NOT NULL, `ShowState` INTEGER NOT NULL, `RecommendState` INTEGER NOT NULL, `RecommendSort` INTEGER NOT NULL, `Hot` INTEGER NOT NULL, `scenes` TEXT, `CollectedState` INTEGER NOT NULL, `FilterDefaultAlpha` INTEGER NOT NULL, `NeedNewMode` INTEGER NOT NULL, `OLdId` TEXT, `new_picture_editor` INTEGER NOT NULL DEFAULT -1, `new_video_editor` INTEGER NOT NULL DEFAULT -1, `AlphaInCamera` INTEGER NOT NULL, PRIMARY KEY(`m_id`))", "fields": [{"fieldPath": "id", "columnName": "m_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "onlineIcon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "localIcon", "columnName": "Local_Icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadType", "columnName": "download_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "internalState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "collectAt", "columnName": "CollectAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryIds", "columnName": "CategoryIds", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "Sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "listDisplay", "columnName": "ListDisplay", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needShow", "columnName": "ShowState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendState", "columnName": "RecommendState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendSort", "columnName": "RecommendSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hot", "columnName": "Hot", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "scenes", "columnName": "scenes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "collectedState", "columnName": "CollectedState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "filterDefaultAlpha", "columnName": "FilterDefaultAlpha", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needNewMode", "columnName": "NeedNewMode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "oldId", "columnName": "OLdId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pictureEditor", "columnName": "new_picture_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "-1"}, {"fieldPath": "videoEditor", "columnName": "new_video_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "-1"}, {"fieldPath": "alphaInCamera", "columnName": "AlphaInCamera", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["m_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "NEW_FILTER_CATEGORY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`CategoryId` TEXT NOT NULL, `CategoryName` TEXT, `ended_at` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, `CatLevel` INTEGER NOT NULL, `CategorySort` INTEGER NOT NULL, `ParentId` TEXT, `Hot` INTEGER NOT NULL, `HotSort` INTEGER NOT NULL, `PaidSort` INTEGER NOT NULL, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `RecommendState` INTEGER NOT NULL, `RecommendSort` INTEGER NOT NULL, `GroupPaidState` INTEGER NOT NULL, `ui_color` TEXT, `GroupDesc` TEXT, `sku` TEXT, `icon` TEXT, `DownloadType` INTEGER NOT NULL, `new_picture_editor` INTEGER NOT NULL DEFAULT -1, `new_video_editor` INTEGER NOT NULL DEFAULT 1, <PERSON><PERSON>AR<PERSON> KEY(`CategoryId`))", "fields": [{"fieldPath": "categoryId", "columnName": "CategoryId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "categoryName", "columnName": "CategoryName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "catLevel", "columnName": "CatLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categorySort", "columnName": "CategorySort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "parentId", "columnName": "ParentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "hot", "columnName": "Hot", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hotSort", "columnName": "HotSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidSort", "columnName": "PaidSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendState", "columnName": "RecommendState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommendSort", "columnName": "RecommendSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupPaidState", "columnName": "GroupPaidState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupColor", "columnName": "ui_color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupDesc", "columnName": "GroupDesc", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sku", "columnName": "sku", "affinity": "TEXT", "notNull": false}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "downloadType", "columnName": "DownloadType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pictureEditor", "columnName": "new_picture_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "-1"}, {"fieldPath": "videoEditor", "columnName": "new_video_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}], "primaryKey": {"columnNames": ["CategoryId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "music_material", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `duration` INTEGER NOT NULL, `file` TEXT, `ended_at` INTEGER NOT NULL, `singer` TEXT NOT NULL, `icon` TEXT NOT NULL, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL, `listBest` INTEGER NOT NULL, `listBestTime` INTEGER NOT NULL, `listFeaturedSort` INTEGER NOT NULL, `hot` INTEGER NOT NULL, `hotSort` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL DEFAULT 0, `collectState` INTEGER NOT NULL DEFAULT 0, `collectTime` INTEGER NOT NULL, `picture_editor` INTEGER NOT NULL DEFAULT 1, `video_editor` INTEGER NOT NULL DEFAULT 1)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "singer", "columnName": "singer", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "listBest", "columnName": "listBest", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "listBestTime", "columnName": "listBestTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "listFeaturedSort", "columnName": "listFeaturedSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hot", "columnName": "hot", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hotSort", "columnName": "hotSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectState", "columnName": "collectState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectTime", "columnName": "collectTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pictureEditor", "columnName": "picture_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "videoEditor", "columnName": "video_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "music_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `ended_at` INTEGER NOT NULL, `icon` TEXT NOT NULL, `childs` TEXT, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "childs", "columnName": "childs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "MaterialAnim", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `materialType` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `onlineSort` INTEGER NOT NULL, `parentId` TEXT, `minDuration` INTEGER NOT NULL, `maxDuration` INTEGER NOT NULL, `defaultDuration` INTEGER NOT NULL, `animationType` INTEGER NOT NULL, `file` TEXT, `icon` TEXT, `name` TEXT, `newTag` INTEGER NOT NULL, `NewTime` INTEGER NOT NULL, `createAt` INTEGER NOT NULL, `paidType` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "materialType", "columnName": "materialType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineSort", "columnName": "onlineSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "parentId", "columnName": "parentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "minDuration", "columnName": "minDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxDuration", "columnName": "maxDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "defaultDuration", "columnName": "defaultDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "animationType", "columnName": "animationType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newTag", "columnName": "newTag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "NewTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createAt", "columnName": "createAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paidType", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "TextMaterialAnim", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `materialType` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL, `onlineSort` INTEGER NOT NULL, `parentId` TEXT, `minDuration` INTEGER NOT NULL, `maxDuration` INTEGER NOT NULL, `defaultDuration` INTEGER NOT NULL, `animationType` INTEGER NOT NULL, `name` TEXT, `file` TEXT, `icon` TEXT, `newTag` INTEGER NOT NULL, `NewTime` INTEGER NOT NULL, `createAt` INTEGER NOT NULL, `paidType` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "materialType", "columnName": "materialType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineSort", "columnName": "onlineSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "parentId", "columnName": "parentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "minDuration", "columnName": "minDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxDuration", "columnName": "maxDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "defaultDuration", "columnName": "defaultDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "animationType", "columnName": "animationType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newTag", "columnName": "newTag", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "NewTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createAt", "columnName": "createAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paidType", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "sound_effect_material", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `duration` INTEGER NOT NULL, `file` TEXT, `ended_at` INTEGER NOT NULL, `singer` TEXT NOT NULL, `icon` TEXT NOT NULL, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL, `hot` INTEGER NOT NULL, `hotSort` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL DEFAULT 0, `collectState` INTEGER NOT NULL DEFAULT 0, `collectTime` INTEGER NOT NULL, `picture_editor` INTEGER NOT NULL DEFAULT 1, `video_editor` INTEGER NOT NULL DEFAULT 1)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "singer", "columnName": "singer", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hot", "columnName": "hot", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hotSort", "columnName": "hotSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectState", "columnName": "collectState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectTime", "columnName": "collectTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pictureEditor", "columnName": "picture_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "videoEditor", "columnName": "video_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "sound_effect_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `ended_at` INTEGER NOT NULL, `icon` TEXT NOT NULL, `childs` TEXT, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "childs", "columnName": "childs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "special_effect_material", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `duration` INTEGER NOT NULL, `file` TEXT, `ended_at` INTEGER NOT NULL, `singer` TEXT NOT NULL, `icon` TEXT NOT NULL, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL, `paid_type` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL DEFAULT 0, `collectState` INTEGER NOT NULL DEFAULT 0, `collectTime` INTEGER NOT NULL, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `createAt` INTEGER NOT NULL, `picture_editor` INTEGER NOT NULL DEFAULT 1, `video_editor` INTEGER NOT NULL DEFAULT 1)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "singer", "columnName": "singer", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectState", "columnName": "collectState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectTime", "columnName": "collectTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createAt", "columnName": "createAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pictureEditor", "columnName": "picture_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "videoEditor", "columnName": "video_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "special_effect_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `ended_at` INTEGER NOT NULL, `icon` TEXT NOT NULL, `childs` TEXT, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "childs", "columnName": "childs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "transition_material", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `duration` INTEGER NOT NULL, `file` TEXT, `ended_at` INTEGER NOT NULL, `singer` TEXT NOT NULL, `icon` TEXT NOT NULL, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL, `paid_type` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL DEFAULT 0, `collectState` INTEGER NOT NULL DEFAULT 0, `collectTime` INTEGER NOT NULL, `picture_editor` INTEGER NOT NULL DEFAULT 1, `video_editor` INTEGER NOT NULL DEFAULT 1, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `createAt` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "singer", "columnName": "singer", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectState", "columnName": "collectState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectTime", "columnName": "collectTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pictureEditor", "columnName": "picture_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "videoEditor", "columnName": "video_editor", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createAt", "columnName": "createAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "transition_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `ended_at` INTEGER NOT NULL, `icon` TEXT NOT NULL, `childs` TEXT, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "childs", "columnName": "childs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "KeyWordInfo", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` TEXT, `label` TEXT, `name` TEXT NOT NULL, `time` INTEGER NOT NULL, `orderNo` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "label", "columnName": "label", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "orderNo", "columnName": "orderNo", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "style_material", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `oldId` TEXT, `duration` INTEGER NOT NULL, `file` TEXT, `ended_at` INTEGER NOT NULL, `singer` TEXT NOT NULL, `icon` TEXT NOT NULL, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL, `listBest` INTEGER NOT NULL, `listBestTime` INTEGER NOT NULL, `listFeaturedSort` INTEGER NOT NULL, `hot` INTEGER NOT NULL, `hotSort` INTEGER NOT NULL, `paid_type` INTEGER NOT NULL, `isPortrait` INTEGER NOT NULL, `isMask` INTEGER NOT NULL, `isTouch` INTEGER NOT NULL, `isGl3` INTEGER NOT NULL, `downloadState` INTEGER NOT NULL DEFAULT 0, `collectState` INTEGER NOT NULL DEFAULT 0, `collectTime` INTEGER NOT NULL, `isInternal` INTEGER NOT NULL, `presetValue` TEXT, `effect` TEXT, `is_new` INTEGER NOT NULL, `is_new_time` INTEGER NOT NULL, `createAt` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "oldId", "columnName": "oldId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "file", "columnName": "file", "affinity": "TEXT", "notNull": false}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "singer", "columnName": "singer", "affinity": "TEXT", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "listBest", "columnName": "listBest", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "listBestTime", "columnName": "listBestTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "listFeaturedSort", "columnName": "listFeaturedSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hot", "columnName": "hot", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hotSort", "columnName": "hotSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "paid_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "portrait", "columnName": "isPortrait", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mask", "columnName": "isMask", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "touch", "columnName": "is<PERSON><PERSON>ch", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gl3", "columnName": "isGl3", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadState", "columnName": "downloadState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectState", "columnName": "collectState", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "collectTime", "columnName": "collectTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isInternal", "columnName": "isInternal", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "presetValue", "columnName": "presetValue", "affinity": "TEXT", "notNull": false}, {"fieldPath": "effectValue", "columnName": "effect", "affinity": "TEXT", "notNull": false}, {"fieldPath": "newState", "columnName": "is_new", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newTime", "columnName": "is_new_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createAt", "columnName": "createAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "style_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `mId` TEXT NOT NULL, `ended_at` INTEGER NOT NULL, `icon` TEXT NOT NULL, `childs` TEXT, `sort` INTEGER NOT NULL, `name` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineId", "columnName": "mId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endedAt", "columnName": "ended_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "icon", "columnName": "icon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "childs", "columnName": "childs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sort", "columnName": "sort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "image_portrait_entity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `media_path` TEXT NOT NULL, `media_update_time` INTEGER NOT NULL, `face_count` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mediaPath", "columnName": "media_path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "mediaUpdateTime", "columnName": "media_update_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "faceCount", "columnName": "face_count", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "COUPON_TABLE", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT, `countdown` TEXT, `titleColor` TEXT, `title` TEXT, `title2` TEXT, `title2Color` TEXT, `bgPic` TEXT, `copywritingDescription` TEXT, `copywritingDescriptionColor` TEXT, `contents` TEXT, `contentColor` TEXT, `year` TEXT, `month` TEXT, `subColor` TEXT, `subBackgroundColor` TEXT, `homepageBannerBottom` TEXT, `m_id` TEXT NOT NULL, `mainTitlePic` TEXT, `userState` INTEGER NOT NULL, `abCode` INTEGER NOT NULL, `experiment` INTEGER NOT NULL, `homeCorner` TEXT, `couponContent` TEXT, `subBanner` TEXT, `cacheState` INTEGER NOT NULL, `claimTime` INTEGER NOT NULL, PRIMARY KEY(`m_id`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "countdown", "columnName": "countdown", "affinity": "TEXT", "notNull": false}, {"fieldPath": "titleColor", "columnName": "titleColor", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title2", "columnName": "title2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title2Color", "columnName": "title2Color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bgPic", "columnName": "bgPic", "affinity": "TEXT", "notNull": false}, {"fieldPath": "copywritingDescription", "columnName": "copywritingDescription", "affinity": "TEXT", "notNull": false}, {"fieldPath": "copywritingDescriptionColor", "columnName": "copywritingDescriptionColor", "affinity": "TEXT", "notNull": false}, {"fieldPath": "contents", "columnName": "contents", "affinity": "TEXT", "notNull": false}, {"fieldPath": "contentColor", "columnName": "contentColor", "affinity": "TEXT", "notNull": false}, {"fieldPath": "year", "columnName": "year", "affinity": "TEXT", "notNull": false}, {"fieldPath": "month", "columnName": "month", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subColor", "columnName": "subColor", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subBackgroundColor", "columnName": "subBackgroundColor", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homepageBannerBottom", "columnName": "homepageBannerBottom", "affinity": "TEXT", "notNull": false}, {"fieldPath": "id", "columnName": "m_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "mainTitlePic", "columnName": "mainTitlePic", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userState", "columnName": "userState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "abCode", "columnName": "abCode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "experiment", "columnName": "experiment", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "homeCorner", "columnName": "homeCorner", "affinity": "TEXT", "notNull": false}, {"fieldPath": "couponContent", "columnName": "couponContent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subBanner", "columnName": "subBanner", "affinity": "TEXT", "notNull": false}, {"fieldPath": "cacheState", "columnName": "cacheState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "claimTime", "columnName": "claimTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["m_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "Duffle_StickerGroup", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`GroupId` TEXT NOT NULL, `GroupName` TEXT, `GroupPaidState` INTEGER NOT NULL, `GroupDisplayImage` TEXT, `GroupSceneImage` TEXT, `GroupThumbnail` TEXT, `GroupCoverImage` TEXT, `StickerCreator` TEXT, `StickerEnableTint` INTEGER NOT NULL, `isAvailable` INTEGER NOT NULL, `LockLocalState` INTEGER NOT NULL, `CategoryLevel` INTEGER NOT NULL, `NeedShow` INTEGER NOT NULL, `CategoryIdSort` INTEGER NOT NULL, `SubMaterials` TEXT, `SubCateIds` TEXT, PRIMARY KEY(`GroupId`))", "fields": [{"fieldPath": "categoryId", "columnName": "GroupId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "categoryName", "columnName": "GroupName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupPaidState", "columnName": "GroupPaidState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupDisplayImage", "columnName": "GroupDisplayImage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupSceneImage", "columnName": "GroupSceneImage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupThumbnail", "columnName": "GroupThumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupCoverImage", "columnName": "GroupCoverImage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "stickerCreator", "columnName": "StickerCreator", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enableTint", "columnName": "StickerEnableTint", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "available", "columnName": "isAvailable", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "LockLocalState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cateLevel", "columnName": "CategoryLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "needShow", "columnName": "NeedShow", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categorySort", "columnName": "CategoryIdSort", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "subMaterials", "columnName": "SubMaterials", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subCateIds", "columnName": "SubCateIds", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["GroupId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "<PERSON><PERSON>_<PERSON>", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`StickerId` TEXT NOT NULL, `StickerRecommendState` INTEGER NOT NULL, `stickerThumbnail` TEXT, `StickerFile` TEXT, `StickerBG` TEXT, `BgColorModel` INTEGER NOT NULL, `Paid_Type` INTEGER NOT NULL, `Color_Witch` INTEGER NOT NULL, `overlayMode` INTEGER NOT NULL, `newState` INTEGER NOT NULL, `NewExpiredTime` INTEGER NOT NULL, `CreateAt` INTEGER NOT NULL, `CategoryID` TEXT NOT NULL, `DownloadState` INTEGER NOT NULL, `InternalState` INTEGER NOT NULL, `MigrationState` INTEGER NOT NULL, `custom` INTEGER NOT NULL, `HistoryTime` INTEGER NOT NULL, `DownloadFromRecommend` INTEGER NOT NULL, `ContainInHistory` INTEGER NOT NULL, PRIMARY KEY(`StickerId`))", "fields": [{"fieldPath": "materialId", "columnName": "StickerId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "stickerRecommendState", "columnName": "StickerRecommendState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stickerThumbnail", "columnName": "stickerThumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "stickerFile", "columnName": "StickerFile", "affinity": "TEXT", "notNull": false}, {"fieldPath": "stickerBgColor", "columnName": "StickerBG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "stickerBgColorMode", "columnName": "BgColorModel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidType", "columnName": "Paid_Type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enableTint", "columnName": "Color_Witch", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "overlayMode", "columnName": "overlayMode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newState", "columnName": "newState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "newExpiredTime", "columnName": "NewExpiredTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createAt", "columnName": "CreateAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryId", "columnName": "CategoryID", "affinity": "TEXT", "notNull": true}, {"fieldPath": "downloadState", "columnName": "DownloadState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "internalState", "columnName": "InternalState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "migrationState", "columnName": "MigrationState", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "custom", "columnName": "custom", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "historyTime", "columnName": "HistoryTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "downloadFrom", "columnName": "DownloadFromRecommend", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "containInHistory", "columnName": "ContainInHistory", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["StickerId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '12c04f26aab1b60b6d46e469ebf4abc6')"]}}