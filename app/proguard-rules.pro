# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Users/<USER>/Library/Android/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}
-dontshrink
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
#保留签名 解决泛型问题
-keepattributes Signature
#短方法内联
-allowaccessmodification


-ignorewarnings
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.mt.mtxx.image.NDKUtil{*;}
-keep public class com.commsource.beautyplus.R$*{
    public static final int *;
}

-keep class com.meitu.openad.** {*;}
-keep class com.meitu.openad.** {*;}

-keepclassmembers class * {
   public <init>(org.json.JSONObject);
}

-keep class com.google.gson.** { *; }
-keep class * implements java.io.Serializable {
    *;
}

-keepclasseswithmembernames class * {
    native <methods>;
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
-keep class com.commsource.billing.bean.** {*;}

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
-keepclassmembers class fqcn.of.javascript.interface.for.webview {
   public *;
}


#facebook
-keep class com.facebook.** {*;}

#andoroid-support-v4
-keep class android.support.** {*;}

# meitu core
-keep class com.meitu.core.** {*;}
-keep class com.meitu.mtlab.MTAiInterface.** {*;}
# meitu effecttools
-keep class com.meitu.beautypluseffecttools.** {*;}

# greendao
-keep class de.greenrobot.dao.** {*;}
-keepclassmembers class * extends de.greenrobot.dao.AbstractDao {
 public static java.lang.String TABLENAME;
}
-keep class **$Properties

# eventbus
-keepclassmembers class ** {
    public void onEvent*(**);
}

# Only required if you use AsyncExecutor
-keepclassmembers class * extends de.greenrobot.event.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

#-dontwarn okhttp3.**
-keep class okhttp3.** {*;}
#-dontwarn okio.*
-keep class okio.** {*;}

-keep class com.meitu.countrylocation.LocationBean {*;}

-keepclasseswithmembers class * extends com.meitu.countrylocation.Localizer {
   public <init>(android.content.Context,com.meitu.countrylocation.LocationParameter);
}

#美妆模块，底层混淆配置
-keep class com.meitu.makeup.core.**{*;}
# For communication with AdColony's WebView
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}
# Keep filenames and line numbers for stack traces
-keepattributes SourceFile,LineNumberTable
# Keep JavascriptInterface for WebView bridge
-keepattributes JavascriptInterface
# Sometimes keepattributes is not enough to keep annotations
-keep class android.webkit.JavascriptInterface {
   *;
}
# Keep all classes in Unity Ads package
-keep class com.unity3d.ads.** {
   *;
}
-keep class com.vungle.** {
   *;
}
-keep class com.jirbo.adcolony.** {
   *;
}

# WebView
-keep public class android.webkit.WebViewClient
-keepclassmembers class com.meitu.webview.core.CommonWebChromeClient* {
    public void openFileChooser(android.webkit.ValueCallback,java.lang.String,java.lang.String);
    public void openFileChooser(android.webkit.ValueCallback,java.lang.String);
    public void openFileChooser(android.webkit.ValueCallback);
}
-keep public class android.net.http.SslError
-keepclassmembers class MTCommandImageBase64SaveScript {
   public *;
}

-keep class com.meitu.realtimefilter.core.**{*;}

#统一推送sdk
-keep class com.meitu.pushkit.sdk.** {*;}
-keep class com.meitu.secret.** {*;}

#海外商业化SDK
-keep class com.meitu.hwbusinesskit.** {*;}


#Google Play 内购
-keep class com.android.vending.billing.**

# MobPower
#-keepattributes Signature
#-keepattributes *Annotation*
#-keep class com.gofun.appwallad.ui.** {*; }
#-keep class com.gofun.uicommon.ui.** {*; }
#-keep class com.gofun.video.ui.** {*; }
#-keep class com.gofun.splash.activity.** {*; }
#-keep class com.gofun.api.FhService{*; }
#-keep class com.gofun.api.FhAdCacheProvider {*; }
#-keep class com.gofun.componentad.interstitial.api.** {*; }
#-keepclassmembers class * implements android.os.Parcelable {
#public static final ** CREATOR;
#}

# admob mediation广告
-keep class com.google.ads.mediation.admob.AdMobAdapter {
    *;
}
-keep class com.google.ads.mediation.AdUrlAdapter {
    *;
}

-keep class com.meitu.makeup.**


# SNS 分享库
-keep public class com.meitu.libmtsns.framwork.i.PlatformConfig {*;}
-keep public class * extends com.meitu.libmtsns.framwork.i.PlatformConfig {*;}
# No Proguard Platform Constructor
-keepclasseswithmembers public class * extends com.meitu.libmtsns.framwork.i.Platform {
    public <init>(android.app.Activity);
}



# Required to preserve the Flurry SDK
-keep class com.flurry.** { *; }
-dontwarn com.flurry.**
-keepattributes *Annotation*,EnclosingMethod,Signature
-keepclasseswithmembers class * {
   public <init>(android.content.Context, android.util.AttributeSet, int);
 }

 # Google Play Services library
  -keep class * extends java.util.ListResourceBundle {
   protected Object[][] getContents();
}

 -keep public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
  public static final *** NULL;
 }

 -keepnames @com.google.android.gms.common.annotation.KeepName class *
 -keepclassmembernames class * {
    @com.google.android.gms.common.annotation.KeepName *;
  }

 -keepnames class * implements android.os.Parcelable {
  public static final ** CREATOR;
 }
 # FLurry SDK  end

 -keepnames class * extends android.view.View

 -keep class * extends android.app.Fragment {
  public void setUserVisibleHint(boolean);
  public void onHiddenChanged(boolean);
  public void onResume();
  public void onPause();
 }
 -keep class androidx.fragment.app.Fragment {
  public void setUserVisibleHint(boolean);
  public void onHiddenChanged(boolean);
  public void onResume();
  public void onPause();
 }

# Glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep class com.bumptech.glide.GeneratedAppGlideModuleImpl
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

# Chartboost
-keep class com.chartboost.** { *; }

-keep class org.xmlpull.v1.** { *;}
-dontwarn org.xmlpull.v1.**

-keep class org.apache.** { *; }


#bugly
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}


-keep public class com.android.installreferrer.** { *; }

#自动化测试需求
#-keep public class com.meitu.webview.**{*;}


#altamob
-dontwarn java.lang.invoke.*
-keep class com.altamob.** { *; }
-keep class com.mobi.** { *; }
-keepclassmembers class * {
@android.webkit.JavascriptInterface <methods>;
}

-keep public class com.bumptech.glide.integration.webp.WebpImage { *; }
-keep public class com.bumptech.glide.integration.webp.WebpFrame { *; }
-keep public class com.bumptech.glide.integration.webp.WebpBitmapFactory { *; }

# Giphy
-keep class com.giphy.sdk.core.** { *; }

#baidu
-keep class com.duapps.ad.**{*;}
-dontwarn com.duapps.ad.**
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
@com.google.android.gms.common.annotation.KeepName *;
}
-keep class com.google.android.gms.common.GooglePlayServicesUtil {
public <methods>;
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient{
public <methods>;
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info {
public <methods>;
}

#ucfunnel
-keep class com.ucfunnel.**{*;}
-dontwarn com.ucfunnel.**


## 性能测试SDK
-dontwarn com.meitu.library.harvest.**
-keep class com.meitu.library.harvest.**{*;}

## AR内核
-keep class com.meitu.mtlab.arkernelinterface.** { *; }

## 3D库
-keep class com.meitu.libmt3dface.**{*;}

## mtmvcore库
-keep class com.meitu.mtmvcore.application.MTMVCoreApplication {*;}
-keep class com.meitu.mtmvcore.application.MTMVPlayer {*;}
-keepclasseswithmembernames, includedescriptorclasses class * {
    native <methods>;
}

-keep class com.meitu.library.application.** { *; }


# model 类防混淆，放置json映射异常
-keep class com.meitu.ipstore.core.BaseModel
-keep class * implements com.meitu.ipstore.core.BaseModel{*;}
-keepclassmembers class * extends org.greenrobot.greendao.AbstractDao {
public static java.lang.String TABLENAME;
}
-keep class **$Properties { *; }
# If you DO use SQLCipher:
-keep class org.greenrobot.greendao.database.SqlCipherEncryptedHelper { *; }
# If you do NOT use SQLCipher:
-dontwarn net.sqlcipher.database.**
# If you do NOT use RxJava:
-dontwarn rx.**

# 各分平台实现
-keep public class * extends com.meitu.ipstore.gplay.IPPlatform

# firebase崩溃收集SDK
-keep public class * extends java.lang.Exception
-keep class com.crashlytics.** { *; }
-dontwarn com.crashlytics.**

## 代码覆盖率的库
-keep class org.jacoco.agent.rt.**{ *; }
-keep class com.meitu.autotest.kotrace.** { *; }

## 这里有用反射去获取设备信息，故保留EventContentProvider
-keep class com.meitu.library.analytics.sdk.db.EventContentProvider {*;}

## 以下自动化需求
## androidx fragment 不混淆
-keep class androidx.fragment.app.** {*;}
-keep class androidx.lifecycle.** {*;}
## 这里用AOP修改代码，这个类不加入混淆
-keep public class com.huawei.hms.ads.identifier.AdvertisingIdClient { *; }

## 这个包下有反射调用，不加入混淆
-keep class com.commsource.studio.opengl.b.** {*;}

## PAG
-keep class org.libpag.** {*;}
-keep class androidx.exifinterface.** {*;}

# need add for Fragment page route
# -keep public class * extends android.app.Fragment
# -keep public class * extends androidx.fragment.app.Fragment
# -keep public class * extends android.support.v4.app.Fragment

-keep class androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}
-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}
-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}
-keepclasseswithmembers class * {
    @com.therouter.router.Autowired <fields>;
}
-keepclasseswithmembers,includedescriptorclasses class * {
    native <methods>;
}

-keep class com.adjust.sdk.** { *; }
-keep class com.google.android.gms.common.ConnectionResult {
   int SUCCESS;
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient {
   com.google.android.gms.ads.identifier.AdvertisingIdClient$Info getAdvertisingIdInfo(android.content.Context);
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info {
   java.lang.String getId();
   boolean isLimitAdTrackingEnabled();
}
-keep public class com.android.installreferrer.** { *; }

-keep class com.commsource.ad.adtaskcenter.AdTask
-keep class com.commsource.ad.adtaskcenter.rewardcenter.VipReward

-keep class com.bumptech.glide.integration.webp.decoder.** { *; }

-keep class android.content.pm.IPackageStatsObserver {*;}
-keep class android.content.pm.PackageStats {*;}

-keep class com.meitu.library.appcia.crash.upload.MtUploadService { *; }

# for Pangle SDK
-keep class com.bytedance.sdk.** { *; }