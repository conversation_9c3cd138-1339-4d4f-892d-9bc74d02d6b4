<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist>
	<array>
		<dict>
			<key>Name</key>
			<string></string>
			<key>AI</key>
			<array>
				<string>ai/configuration.json</string>
			</array>
			<key>CustomParamDict</key>
			<dict>
				<key>EyelidRealtimeModelType</key>
				<integer>-1</integer>
			</dict>
			<key>IsNeedResetSound</key>
			<integer>0</integer>
			<key>EnableReplaceSpecialFacelift</key>
			<integer>0</integer>
			<key>FacePart</key>
			<array>
				<dict>
					<key>Type</key>
					<string>ARTextStickerPart</string>
					<key>Animation</key>
					<string>default.plist</string>
					<key>Alpha</key>
					<string>1.00</string>
					<key>BlendMode</key>
					<string>BlendNormal</string>
					<key>StickerType</key>
					<integer>2</integer>
					<key>ScreenStandardImageSize</key>
					<string>1024,1024</string>
					<key>ScreenPositionOffset</key>
					<string>-0.503704,0.339583</string>
					<key>ScreenRotateAngle</key>
					<string>1.570796</string>
					<key>ScreenScaleFactor</key>
					<string>1,1</string>
					<key>ScreenPivot</key>
					<integer>1</integer>
					<key>ScreenStretchMode</key>
					<integer>4</integer>
					<key>ScreenFollow</key>
					<integer>1</integer>
					<key>ScreenVerticalMirror</key>
					<integer>0</integer>
					<key>ScreenHorizontalMirror</key>
					<integer>0</integer>
					<key>ScreenHorizontalFit</key>
					<integer>0</integer>
					<key>ID</key>
					<string>105553162197888</string>
					<key>Name</key>
					<string></string>
					<key>CustomName</key>
					<string>TextAddSize</string>
					<key>DrawLayerBorder</key>
					<integer>0</integer>
					<key>EnableTextBoxInteraction</key>
					<integer>0</integer>
					<key>DesignedDraggable</key>
					<integer>0</integer>
					<key>DesignedForceSelectable</key>
					<integer>0</integer>
					<key>MVARTextConfigPath</key>
					<string>3_mvar-text.plist</string>
					<key>ImageSizeFilter</key>
					<string>1,1,1</string>
					<key>CustomParamDict</key>
					<dict>
						<key>MaxLoadCount</key>
						<string>1</string>
						<key>MaxLoadErrMsg</key>
						<string>目前仅支持新建一个文字效果！</string>
						<key>MaxLoadErrMsgEN</key>
						<string>Currently only supports creating one Head Size effect!</string>
						<key>MaxLoadErrMsgTW</key>
						<string>目前僅支援新建一個文字效果!</string>
						<key>MaxLoadErrMsgZH</key>
						<string>目前仅支持新建一个文字效果！</string>
					</dict>
				</dict>
			</array>
		</dict>
	</array>
</plist>
