package com.meitu.http

import android.util.LruCache
import java.lang.Exception

/**
 * Desc : 服务提供者 其实在日后管理中就是DBModel ApiModel FileModel中的ApiModel的角色 看量级扩展
 * Author : csxiong - 2019/6/24
 */
object ServiceProvider {
    /**
     * 最近最少使用缓存
     */
    private val serviceCache = LruCache<String, Any>(10)

    /**
     * 默认拉取service的方法
     *
     * @param classes
     * @param <T>
     * @return
    </T> */
    operator fun <T> get(classes: Class<T>): T {
        val o = serviceCache[classes.name]
        if (o != null) {
            return o as T
        }
        val t = create(classes)
        serviceCache.put(classes.name, t)
        return t!!
    }

    /**
     * 创建service方法
     */
    fun <T> create(classes: Class<T>): T? {
        var t: T? = null
        try {
            t = classes.newInstance()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return t
    }
}