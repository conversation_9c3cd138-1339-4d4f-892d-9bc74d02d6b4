package com.meitu.http

import okhttp3.MultipartBody
import okhttp3.Request

/**
 * author: admin
 * Date: 2022/6/13
 * Des:
 */
class MultiProgressRequest(requestMapping: String) :AbsRequest(requestMapping) {

    override val methodType: String = "post"

    /**
     * 进度监听
     */
    var progressListener: ProgressResponseListener? = null

    override fun onCreateRequest(requestBuilder: Request.Builder, fullRequestMapping: String, parameters: HashMap<String, Any?>): Request? {
        requestBuilder.url(fullRequestMapping)
        // 多表单生成
        val builder = MultipartBody.Builder().setType(MultipartBody.FORM)
        // 表单部分参数添加
        for (key in parameters.keys) {
            builder.addFormDataPart(key, parameters[key].toString())
        }
        val requestBody = MyMultipartBody(builder.build(), progressListener)
        return requestBuilder.url(fullRequestMapping).post(requestBody).build()
    }

    fun execute(responseListener: ProgressResponseListener?) {
        this.progressListener = responseListener
        super.execute(responseListener)
    }
}