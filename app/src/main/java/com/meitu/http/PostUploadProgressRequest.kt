package com.meitu.http

import com.meitu.http.body.ProgressRequestBody
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.Request
import okhttp3.RequestBody
import java.io.File

/**
 * @Desc : 支持文件上传和文件下载
 * <AUTHOR> Bear - 2020/4/2
 */
class PostUploadProgressRequest(requestMapping: String?) : AbsRequest(requestMapping!!) {
    /**
     * 媒体类型
     */
    private var mediaType: String = "multipart/form-data"

    /**
     * 进度监听
     */
    var responseListener: UploadProgressResponseListener<*>? = null

    /**
     * 文件map
     */
    val fileMap = HashMap<String, File>()

    /**
     * 设置类型
     */
    fun addMediaType(mediaType: String): PostUploadProgressRequest {
        this.mediaType = mediaType
        return this
    }

    /**
     * 添加目标文件
     */
    fun addFile(name: String, file: File): PostUploadProgressRequest {
        fileMap[name] = file
        return this
    }

    override val methodType: String = "post"

    override fun onCreateRequest(
        requestBuilder: Request.Builder,
        fullRequestMapping: String,
        parameters: HashMap<String, Any?>
    ): Request? {
        // 多表单生成
        val builder = MultipartBody.Builder().setType(MultipartBody.FORM)
        // 表单部分参数添加
        for (key in parameters.keys) {
            builder.addFormDataPart(key, parameters[key].toString())
        }
        //多文件表单
        fileMap.forEach {
            // 目标源requestBody建立
            builder.addFormDataPart(
                it.key,
                it.value.name,
                ProgressRequestBody(
                    RequestBody.create(
                        mediaType.toMediaTypeOrNull(), it.value),
                    responseListener
                )
            )
        }
        return requestBuilder.url(fullRequestMapping).post(builder.build()).build()
    }

    /**
     * 执行上传任务
     */
    fun <T> execute(responseListener: UploadProgressResponseListener<T>?) {
        this.responseListener = responseListener
        super.execute(responseListener)
    }
}