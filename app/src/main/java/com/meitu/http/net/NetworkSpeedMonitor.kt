package com.meitu.http.net

import android.net.TrafficStats
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.R
import com.commsource.util.UIHelper
import com.commsource.util.V
import com.meitu.common.utils.ToastUtils

/**
 * @Description : 网速监控
 * <AUTHOR> bear
 * @Date : 2022/4/14
 */
object NetworkSpeedMonitor {

    private const val DOWNLOAD_SPEED_CALCULATE = 1
    private const val UPLOAD_SPEED_CALCULATE = 2

    /**
     * 120kBytes/s的速度
     *
     * charles上的速度都是 kbps 所以charles设置都是bits
     */
    private const val LOW_SPEED = 120

    /**
     * 计算监控间隔
     * 5s
     */
    private const val calculateMonitorInterval = 5000L

    /**
     * 记录上一次统计时间
     */
    private var preDownloadCalculateTime = System.currentTimeMillis()
    private var preUploadCalculateTime = System.currentTimeMillis()

    /**
     * 记录上一次的总字节数
     */
    private var preDownloadTotalBytes = TrafficStats.getTotalRxBytes()
    private var preUploadTotalBytes = TrafficStats.getTotalTxBytes()

    private var visiblePreDownloadTotalBytes = TrafficStats.getTotalRxBytes()
    private var visiblePreUploadTotalBytes = TrafficStats.getTotalTxBytes()

    /**
     * 需要低网速上传提示
     */
    private var needLowUploadSpeedTips = false

    /**
     * 需要低网速下载提示
     */
    private var needLowDownloadSpeedTips = false

    /**
     * 下载任务count
     */
    @Volatile
    var downloadTaskCount = 0

    /**
     * 上传任务count
     */
    @Volatile
    var uploadTaskCount = 0

    var weakNetEvent: NoStickLiveData<Boolean> = NoStickLiveData()

    /**
     * 处理线程
     */
    private val handlerThread = HandlerThread("FlowMonitor")
    private lateinit var handler: Handler

    init {
        handlerThread.start()
        handler = object : Handler(handlerThread.looper) {
            override fun handleMessage(msg: Message) {
                super.handleMessage(msg)
                when (msg.what) {
                    DOWNLOAD_SPEED_CALCULATE -> {
                        calculateDownloadSpeed()
                        handler.sendEmptyMessageDelayed(DOWNLOAD_SPEED_CALCULATE, 1000)
                    }
                    UPLOAD_SPEED_CALCULATE -> {
                        calculateUploadSpeed()
                        handler.sendEmptyMessageDelayed(UPLOAD_SPEED_CALCULATE, 1000)
                    }
                }
            }
        }
    }

    /**
     * 需要下载低速提示
     */
    fun needDownloadTips() {
        if (!needLowDownloadSpeedTips) {
            preDownloadCalculateTime = System.currentTimeMillis()
            preDownloadTotalBytes = TrafficStats.getTotalRxBytes()
        }
        needLowDownloadSpeedTips = true
    }

    /**
     * 需要上传低速提示
     */
    fun needUploadTips() {
        if (!needLowUploadSpeedTips) {
            preUploadCalculateTime = System.currentTimeMillis()
            preUploadTotalBytes = TrafficStats.getTotalTxBytes()
        }
//        needLowUploadSpeedTips = true
    }

    /**
     * 清除提示标志位
     */
    fun clearAllTips() {
        needLowUploadSpeedTips = false
        needLowDownloadSpeedTips = false
    }

    /**
     * 添加上传监控
     */
    fun addUploadCount() {
        if (uploadTaskCount == 0) {
            preUploadCalculateTime = System.currentTimeMillis()
            preUploadTotalBytes = TrafficStats.getTotalTxBytes()
            visiblePreUploadTotalBytes = TrafficStats.getTotalTxBytes()
            handler.sendEmptyMessageDelayed(UPLOAD_SPEED_CALCULATE, 1000)
        }
        uploadTaskCount++
    }

    /**
     * 移除上传监控
     */
    fun removeUploadCount() {
        if (uploadTaskCount == 0) {
            return
        }
        uploadTaskCount--
        if (uploadTaskCount == 0) {
            currentUploadSpeed = 0
            handler.removeMessages(UPLOAD_SPEED_CALCULATE)
        }
    }

    /**
     * 添加监控
     */
    fun addDownloadCount() {
        //只有网络请求从0 -> 1的时候 需要重置最新统计点
        "添加任务 downloadTaskCount $downloadTaskCount".V()
        if (downloadTaskCount == 0) {
            preDownloadCalculateTime = System.currentTimeMillis()
            preDownloadTotalBytes = TrafficStats.getTotalRxBytes()
            visiblePreDownloadTotalBytes = TrafficStats.getTotalRxBytes()
            handler.sendEmptyMessageDelayed(DOWNLOAD_SPEED_CALCULATE, 1000)
        }
        downloadTaskCount++
    }

    /**
     * 移除监控
     */
    fun removeDownloadCount() {
        "添加任务 移除  downloadTaskCount $downloadTaskCount".V()
        if (downloadTaskCount == 0) {
            return
        }
        downloadTaskCount--
        if (downloadTaskCount == 0) {
            currentDownloadSpeed = 0
            handler.removeMessages(DOWNLOAD_SPEED_CALCULATE)
        }
    }

    var currentDownloadSpeed = 0
    var currentUploadSpeed = 0

    /**
     * 更新监控
     */
    private fun calculateDownloadSpeed() {
        val totalDownloadBytes = TrafficStats.getTotalRxBytes()
        val currentTime = System.currentTimeMillis()
        val timeInterval = currentTime - preDownloadCalculateTime
        if (timeInterval >= calculateMonitorInterval) {
            val allDownloadBytes = totalDownloadBytes - preDownloadTotalBytes
            val downloadSpeed = (allDownloadBytes) / timeInterval.toFloat()
            //网速都是KBit
//            "下行速:$downloadSpeed kB/s,总字节数:${allDownloadBytes} - ${timeInterval}ms".print(
//                "csx"
//            )
            if ((needLowDownloadSpeedTips && downloadSpeed < LOW_SPEED)) {
                needLowDownloadSpeedTips = false
                UIHelper.runOnUiThread {
                    ToastUtils.showLongToast(R.string.t_network_weak)
                    weakNetEvent.value = true
                }
            }
            preDownloadTotalBytes = totalDownloadBytes
            preDownloadCalculateTime = currentTime
        }
        //单独计算一次秒级别网速
        val secondsBytes = totalDownloadBytes - visiblePreDownloadTotalBytes
        currentDownloadSpeed = (secondsBytes / 1000f).toInt()
        visiblePreDownloadTotalBytes = totalDownloadBytes
    }

    private fun calculateUploadSpeed() {
        val totalUploadBytes = TrafficStats.getTotalTxBytes()
        val currentTime = System.currentTimeMillis()
        val timeInterval = currentTime - preUploadCalculateTime
        if (timeInterval >= calculateMonitorInterval) {
            val allUploadBytes = totalUploadBytes - preUploadTotalBytes
            val uploadSpeed = (allUploadBytes) / timeInterval.toFloat()
//            "上行速:$uploadSpeed kB/s,总字节数:${allUploadBytes} - ${timeInterval}ms".print(
//                "csx"
//            )
            if ((needLowUploadSpeedTips && uploadSpeed < LOW_SPEED)) {
                needLowUploadSpeedTips = false
                UIHelper.runOnUiThread {
                    ToastUtils.showLongToast(R.string.t_network_weak)
                    weakNetEvent.value = true
                }
            }
            preUploadTotalBytes = totalUploadBytes
            preUploadCalculateTime = currentTime
        }
        //单独计算一次秒级别网速
        val secondsBytes = totalUploadBytes - visiblePreUploadTotalBytes
        currentUploadSpeed = (secondsBytes / 1000f).toInt()
        visiblePreUploadTotalBytes = totalUploadBytes
    }

}