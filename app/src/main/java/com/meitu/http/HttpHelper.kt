package com.meitu.http

/**
 * @Description : 网络请求集成后查看网络json下发正确与否
 * <AUTHOR> bear
 * @Date : 2022/2/21
 */
object HttpHelper {

    /**
     * @param method 方法类型
     * @param fullPath 请求Mapping
     */
    data class HttpInfo(
        val method: String,
        val mapping: String,
        val fullPath: String,
        val time: Long,
        val jsonData: String,
        val isSuccess: Boolean = true,
        val requestHeaders:String
    )

    /**
     * 缓存
     */
    private val cache by lazy { ArrayList<HttpInfo>() }

    private val lock = Any()

    /**
     * 保存
     */
    fun push(info: HttpInfo) {
        synchronized(lock) {
            if (cache.size >= 50) {
                cache.removeAt(0)
            }
            if (cache.isEmpty()) {
                cache.add(info)
            } else {
                cache.add(0, info)
            }
        }
    }

    /**
     * 获取所有请求
     */
    fun getAll(): ArrayList<HttpInfo> {
        synchronized(lock) {
            return ArrayList(cache)
        }
    }


}