package com.meitu.template.bean

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

/**
 * 风格化预设值
 * 可以认为是服务端端下发的风格化滑杆信息
 */
@Keep
data class PresetValue(
    /**
     * 滑杆key
     */
    @SerializedName("key")
    var key: String = "",

    /**
     * 滑杆名称
     */
    @SerializedName("name")
    var name: String = "",

    /**
     * 滑杆程度值
     */
    @SerializedName("degree")
    var degree: Int = 0,

    @SerializedName("min")
    var min: Int = 0,

    @SerializedName("max")
    var max: Int = 100,

    /**
     * 类型
     * 0滑杆/1开关;默认0
     */
    @SerializedName("type")
    var type: Int = 0,

    /**
     * 默认值
     */
    @SerializedName("default_value")
    var defaultValue: Int = 0,

    /**
     * 开关互动状态 0否；1是；默认是（滑杆、开关均有）
     */
    @SerializedName("status")
    var status: Int = 1,

    /**
     * 使用的程度值
     * 一个和素材关联的记忆点 记忆当前这个滑杆的用户滑杆值
     */
    @Transient
    var useDegree: Int = -1
) {
    fun initUseDegree() {
        if (useDegree == -1) {
            useDegree = degree
        }
    }
}