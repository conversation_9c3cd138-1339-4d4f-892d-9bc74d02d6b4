package com.meitu.template.feedback.util;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import com.commsource.util.common.SpTableName;
import com.commsource.config.ApplicationConfig;
import com.commsource.util.AppTools;
import com.commsource.util.ThreadExecutor;
import com.commsource.util.common.SPConfig;
import com.commsource.util.thread.AbstractNamedRunnable;
import com.google.gson.Gson;
import com.meitu.common.AppContext;
import com.meitu.http.XHttp;
import com.meitu.hwbusinesskit.core.utils.SingleInstanceUtil;
import com.meitu.library.util.Debug.Debug;

import org.json.JSONObject;

import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Created by zby on 2017/5/31.
 * 预发布工具类
 *
 * <AUTHOR>
 */

public class PreReleaseUtils extends SPConfig {

    public static final int VALID_VALIDATION = 1;

    private static final String PRE_RELEASE_CONFIG = "PreReleaseConfig ";
    private static final String KEY_VERIFY_STATUS = "KEY_VERIFY_STATUS";
    private static final String KEY_VERIFY_EXPIRE_AT = "KEY_VERIFY_EXPIRE_AT";
    private static final String KEY_FORMAL_EVIRONMENT_SWITCH = "KEY_FORMAL_EVIRONMENT_SWITCH";
    private static final String KEY_IAP_SWITCH = "KEY_IAP_SWITCH";

    private static PreReleaseUtils mSpConfig;

    public interface VerifyListerner {
        /**
         * doAfterVerfity
         */
        void doAfterVerfity();
    }

    /**
     * @param context
     * @param name
     */
    public PreReleaseUtils(Context context, String name) {
        super(context, name);
    }

    private synchronized static SPConfig getSPConfig(Context context) {
        if (mSpConfig == null) {
            mSpConfig = new PreReleaseUtils(context, SpTableName.PreReleaseConfig);
        }
        return mSpConfig;
    }

    public static boolean getVerifyStatus() {
        return getSPConfig(AppContext.getContext()).getBoolean(KEY_VERIFY_STATUS, false);
    }

    public static void setVerifyStatus(boolean status) {
        getSPConfig(AppContext.getContext()).putValue(KEY_VERIFY_STATUS, status);
    }

    public static long getVerifyExpireAt() {
        return getSPConfig(AppContext.getContext()).getLong(KEY_VERIFY_EXPIRE_AT, 0L);
    }

    /**
     * 验证密码有效期
     *
     * @return
     */
    public static boolean verifyExpireAt() {
        long currentTime = System.currentTimeMillis() / 1000;
        long expireTime = getVerifyExpireAt();
        return currentTime <= expireTime;
    }

    public static void setVerifyExpireAt(long expire_at) {
        getSPConfig(AppContext.getContext()).putValue(KEY_VERIFY_EXPIRE_AT, expire_at);
    }

    public static boolean getFormalEnvironmentSwitch() {
        return getSPConfig(AppContext.getContext()).getBoolean(KEY_FORMAL_EVIRONMENT_SWITCH, false);
    }

    public static void setFormalEnvironmentSwitch(boolean status) {
        getSPConfig(AppContext.getContext()).putValue(KEY_FORMAL_EVIRONMENT_SWITCH, status);
    }

    public static boolean getIapSwitch() {
        return getSPConfig(AppContext.getContext()).getBoolean(KEY_IAP_SWITCH, false);
    }

    public static void setIapSwitch(boolean status) {
        getSPConfig(AppContext.getContext()).putValue(KEY_IAP_SWITCH, status);
    }

    /**
     * 判断是否预发布环境
     *
     * @return
     */
    public static boolean getPreEnvironment() {
        return !getFormalEnvironmentSwitch() && getVerifyStatus() && verifyExpireAt();
    }

    public static void setOpenCreate(boolean isOpen) {
        getSPConfig(AppContext.getContext()).putValue("OpenCreate", isOpen);
    }

    public static boolean isOpenCreate() {
        return getSPConfig(AppContext.getContext()).getBoolean("OpenCreate", false);
    }

    /**
     * 是否是创意模式
     *
     * @return
     */
    public static boolean isCreateMode() {
        return getPreEnvironment() || (AppTools.isDebug() && isOpenCreate());
    }

    /**
     * 判断是否预发布环境
     *
     * @return
     */
    public static boolean isNoNeedIap() {
        return (getPreEnvironment() && getIapSwitch()) || (AppTools.isDebug() && getIapSwitch());
    }

    /**
     * 获取密码有效状态
     *
     * @return
     */
    public static boolean getPwActiveState() {
        return getVerifyStatus() && verifyExpireAt();
    }

    /**
     * 验证预发布密码
     *
     * @param context
     * @param password
     * @param verifyListerner
     */
    public static void verifyPreReleasePW(Context context, String password, VerifyListerner verifyListerner) {
        ThreadExecutor.executeSlowTask(new AbstractNamedRunnable("VerifyPreReleasePWTask") {
            @Override
            public void execute() {
                try {
                    if (!TextUtils.isEmpty(password)) {

                        FormBody.Builder builder = new FormBody.Builder();
                        builder.add("phrase_word", password);

                        FormBody formBody = builder.build();
                        OkHttpClient okHttpClient = new OkHttpClient();
                        String url = XHttp.DEBUG_HOST + "/extra/pre_verify.json?soft_id=104&app_ver=1";
                        if (!ApplicationConfig.isTestMaterialEnvironment()) {
                            url = AppTools.getDomainName(context) + "/extra/pre_verify.json?soft_id=104&app_ver=1";
                        }
                        Debug.d("Domain: " + url);
                        Request request = new Request.Builder().url(url).post(formBody).build();
                        Response response = okHttpClient.newCall(request).execute();
                        if (response != null && response.isSuccessful() && response.body() != null) {
                            String result = response.body().string();
                            if (response.code() == 200 && result.contains("success")) {
                                JSONObject jsonObject = new JSONObject(result);
                                String data = jsonObject.has("data") ? jsonObject.getString("data") : "";
                                Gson gson = SingleInstanceUtil.getGson();
                                PreVerifyResult preVerifyResult = gson.fromJson(data, PreVerifyResult.class);
                                setVerifyStatus(preVerifyResult.status == VALID_VALIDATION);
                                setVerifyExpireAt(preVerifyResult.expire_at);
                            }
                        }
                    }

                } catch (Exception e) {
                    Log.d("zby log", e.getMessage());
                } finally {
                    if (verifyListerner != null) {
                        verifyListerner.doAfterVerfity();
                    }
                }
            }
        });
    }

}
