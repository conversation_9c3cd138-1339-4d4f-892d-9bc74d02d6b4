package com.meitu.room.database;

import com.meitu.library.util.Debug.Debug;

import androidx.room.migration.Migration;
import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.sqlite.db.SupportSQLiteDatabase;

/**
 * Created by meitu on 2018/5/16.
 * <AUTHOR>
 * 所有的版本到22版本的处理
 */

public class MigrationAllTo22 extends Migration {
    String CREATE_TABLE = "CREATE TABLE";
    String ALTER_TABLE = "ALTER TABLE";

    /**
     * Creates a new migration between {@code startVersion} and {@code endVersion}.
     *
     * @param startVersion The start version of the database.
     * @param endVersion   The end version of the database after this migration is applied.
     */
    public MigrationAllTo22(int startVersion, int endVersion) {
        super(startVersion, endVersion);
    }

    @Override
    public void migrate(@NonNull SupportSQLiteDatabase database) {
        // 升级时候的用户数据库处理
        updateUser(database);
        upgradeArMaterialGroupTable(database);
        upgradeArMaterialPaidInfoTable(database);
        upgradeArMaterialTable(database);
        upgradeChatTable(database);
        upgradeFiledTable(database);
        upgradeFilterTable(database);
        upgradeFilterGroupTable(database);
        upgradeLocalImageTable(database);
        upgradeCloudImageTable(database);

    }

    /***************用户表的升级处理***************/
    private void updateUser(SupportSQLiteDatabase db) {
        dropTableUser(db);
    }

    public void dropTableUser(SupportSQLiteDatabase db) {
        String sql = "DROP TABLE  IF EXISTS  'USER' ";
        db.execSQL(sql);
    }

    /***************意见反馈表的升级处理***************/

    /** ALTER database table. */
    public void upgradeChatTable(SupportSQLiteDatabase db) {
        Cursor c = null;
        int count = 0;
        String sql = "SELECT COUNT(*) as count FROM sqlite_master WHERE TYPE='table' AND NAME='CHAT'";
        count = 0;
        try {
            c = db.query(sql);
            if (null != c && c.moveToFirst()) {
                count = c.getInt(c.getColumnIndexOrThrow("count"));
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (c != null) {
                c.close();
                c = null;
            }
        }
        if (count == 0) {
            createChartTable(db);
            return;
        }
        try {
            String renameSql = "ALTER TABLE CHAT RENAME TO TMP_CHAT";
            db.execSQL(renameSql);
            createChartTable(db);
            copyTableData(db, "TMP_CHAT", "CHAT");
            db.execSQL("DROP TABLE TMP_CHAT");
        } catch (Exception e) {
            Debug.w(e);
        }
    }

    public void createChartTable(SupportSQLiteDatabase db) {
        String constraint = " IF NOT EXISTS ";
        db.execSQL(CREATE_TABLE + constraint + "'CHAT' (" + //
            "'ID' REAL PRIMARY KEY NOT NULL," + // 0: id
            "'UID' TEXT," + // 1: uid
            "'CONTENT' TEXT," + // 2: content
            "'ROLE' INTEGER," + // 3: role
            "'TIME' TEXT," + // 4: time
            "'CHAT_FAIL' INTEGER," + // 5: chatFail
            "'IMAGE' TEXT," + // 6: image
            "'HASIMG' INTEGER," + // 7: hasimg
            "'UPLOAD_STATE' INTEGER);"); // 8: uploadState
    }

    /** Drops the underlying database table. */
    public void dropChatTable(SupportSQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS 'CHAT'";
        db.execSQL(sql);
    }

    /************* chatFiled    *************/

    /** Creates the underlying database table. */
    public void createFiledTable(SupportSQLiteDatabase db) {
        String constraint = " IF NOT EXISTS ";
        db.execSQL(CREATE_TABLE + constraint + "'CHAT_FILED' (" + //
            "'_id' INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
            "'UID' TEXT," + // 1: uid
            "'TOKEN' TEXT," + // 2: token
            "'IMAGE_PATH' TEXT," + // 3: imagePath
            "'UPLOAD_STATE' INTEGER);"); // 4: uploadState
    }

    /** ALTER database table. */
    public void upgradeFiledTable(SupportSQLiteDatabase db) {
        Cursor c = null;
        int count = 0;
        String sql = "SELECT COUNT(*) as count FROM sqlite_master WHERE TYPE='table' AND NAME='CHAT_FILED'";
        count = 0;
        try {
            c = db.query(sql);
            if (null != c && c.moveToFirst()) {
                count = c.getInt(c.getColumnIndexOrThrow("count"));
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (c != null) {
                c.close();
                c = null;
            }
        }
        if (count == 0) {
            createFiledTable(db);
            return;
        }

        try {
            String renameSql = "ALTER TABLE CHAT_FILED RENAME TO TMP_CHAT_FILED";
            db.execSQL(renameSql);
            createFiledTable(db);
            copyTableData(db, "TMP_CHAT_FILED", "CHAT_FILED");
            db.execSQL("DROP TABLE TMP_CHAT_FILED");
        } catch (Exception e) {
            Debug.w(e);
        }

    }

    /************      ArMaterial ar    ***********/

    /** Creates the underlying database table. */
    public void createArMaterialTable(SupportSQLiteDatabase db) {
        String constraint = " IF NOT EXISTS ";
        db.execSQL(CREATE_TABLE + constraint + "'AR_MATERIAL' (" + //
            "'_id' INTEGER PRIMARY KEY ," + // 0: id
            "'NUMBER' INTEGER NOT NULL DEFAULT(0) ," + // 1: number
            "'SORT' INTEGER NOT NULL DEFAULT(-1) ," + // 2: sort
            "'VERSION_CONTROL' INTEGER NOT NULL DEFAULT(0) ," + // 3: version_control
            "'MIN_VERSION' TEXT," + // 4: min_version
            "'MAX_VERSION' TEXT," + // 5: max_version
            "'FILE_URL' TEXT," + // 6: file_url
            "'FILE_SIZE' TEXT," + // 7: file_size
            "'THUMBNAIL' TEXT," + // 8: thumbnail
            "'IS_HOT' INTEGER NOT NULL DEFAULT(0) ," + // 9: is_hot
            "'HOT_SORT' INTEGER NOT NULL DEFAULT(-1) ," + // 10: hot_sort
            "'HOT_END_TIME' INTEGER NOT NULL DEFAULT(0) ," + // 11: hot_end_time
            "'INTERACTIVE' INTEGER NOT NULL DEFAULT(0) ," + // 12: interactive
            "'AUTO_DOWNLOAD' INTEGER NOT NULL DEFAULT(0) ," + // 13: auto_download
            "'IS_NEW' INTEGER NOT NULL DEFAULT(0) ," + // 14: is_new
            "'NEW_SORT' INTEGER NOT NULL DEFAULT(-1) ," + // 15: new_sort
            "'RED_TIME' INTEGER NOT NULL DEFAULT(0) ," + // 16: red_time
            "'NEW_END_TIME' INTEGER NOT NULL DEFAULT(0) ," + // 17: new_end_time
            "'END_TIME' INTEGER NOT NULL DEFAULT(0) ," + // 18: end_time
            "'WEIGHT' INTEGER NOT NULL DEFAULT(0) ," + // 19: weight
            "'AFTER_WEIGHT' INTEGER NOT NULL DEFAULT(0) ," + // 20: after_weight
            "'TITLE' TEXT," + // 21: title
            "'BGM_FLAG' INTEGER NOT NULL DEFAULT(0) ," + // 22: bgm_flag
            "'DBG_ENABLE' INTEGER NOT NULL DEFAULT(0) ," + // 23: dbg_enable
            "'DBG_URL' TEXT," + // 24: dbg_url
            "'DBG_NUMBER' TEXT," + // 25: dbg_number
            "'IS_3D' INTEGER NOT NULL DEFAULT(0) ," + // 26: is_3d
            "'FEATURED_SORT' INTEGER NOT NULL DEFAULT(0) ," + // 27: featured_sort
            "'IS_FEATURED' INTEGER NOT NULL DEFAULT(0) ," + // 28: is_featured
            "'IS_COLLECTED' INTEGER NOT NULL DEFAULT(0) ," + // 29: is_collected
            "'COLLECTION_TIME' INTEGER NOT NULL DEFAULT(0) ," + // 30: collection_time
            "'IS_DOWNLOAD' INTEGER NOT NULL DEFAULT(0) ," + // 31: is_download
            "'IS_DOWNLOADING' INTEGER NOT NULL DEFAULT(0) ," + // 32: is_downloading
            "'DOWNLOAD_TIME' INTEGER NOT NULL DEFAULT(0) ," + // 33: download_time
            "'IS_HIDE_RED' INTEGER NOT NULL DEFAULT(0) ," + // 34: is_hide_red
            "'GROUP_NUMBER' INTEGER NOT NULL DEFAULT(0) ," + // 35: group_number
            "'IS_BG_DOWNLOAD' INTEGER NOT NULL DEFAULT(0) ," + // 36: is_bg_download
            "'IS_BG_DOWNLOADING' INTEGER NOT NULL DEFAULT(0) ," + // 37: is_bg_downloading
            "'IS_3D_DOWNLOAD' INTEGER NOT NULL DEFAULT(0) ," + // 38: is_3d_download
            "'IS_3D_DOWNLOADING' INTEGER NOT NULL DEFAULT(0) ," + // 39: is_3d_downloading
            "'END_USE_TIME' INTEGER NOT NULL DEFAULT(0) ," + // 40: end_use_time
            "'IS_DYE_HAIR' INTEGER NOT NULL DEFAULT(0) ," + // 41: is_dye_hair
            "'IS_DYE_HAIR_DOWNLOAD' INTEGER NOT NULL DEFAULT(0) ," + // 42: is_dye_hair_download
            "'IS_DYE_HAIR_DOWNLOADING' INTEGER NOT NULL DEFAULT(0), " + "'AR_ONLINE_TEXT'  TEXT  );"); // 43:
                                                                                                       // is_dye_hair_downloading
    }

    /** Drops the underlying database table. */
    public void dropArterialTable(SupportSQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS'AR_MATERIAL'";
        db.execSQL(sql);
    }

    /** ALTER database table. */
    public void upgradeArMaterialTable(SupportSQLiteDatabase db) {
        Cursor c = null;
        int count = 0;
        String sql = "SELECT COUNT(*) as count FROM sqlite_master WHERE TYPE='table' AND NAME='AR_MATERIAL'";
        count = 0;
        try {
            c = db.query(sql, null);
            if (null != c && c.moveToFirst()) {
                count = c.getInt(c.getColumnIndexOrThrow("count"));
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (c != null) {
                c.close();
                c = null;
            }
        }
        if (count == 0) {
            createArMaterialTable(db);
            return;
        }

        try {
            String reNameSql = "ALTER TABLE AR_MATERIAL  RENAME TO TEM_AR_MATERIAL";
            db.execSQL(reNameSql);
            createArMaterialTable(db);
            copyTableData(db, "TEM_AR_MATERIAL", "AR_MATERIAL");
            String dropTempTable = "DROP TABLE TEM_AR_MATERIAL";
            db.execSQL(dropTempTable);
        } catch (Exception e) {
            Debug.w(e);
        }

    }

    /*******   AR_MATERIAL_GROUP*******************/

    /** Creates the underlying database table. */
    public void createArMaterialGroupTable(SupportSQLiteDatabase db) {
        String constraint = " IF NOT EXISTS ";
        db.execSQL(CREATE_TABLE + constraint + "'AR_MATERIAL_GROUP' (" + //
            "'_id' INTEGER PRIMARY KEY ," + // 0: id
            "'NUMBER' INTEGER NOT NULL DEFAULT(0) ," + // 1: number
            "'VERSION_CONTROL' INTEGER NOT NULL DEFAULT(0) ," + // 2: version_control
            "'MIN_VERSION' TEXT," + // 3: min_version
            "'MAX_VERSION' TEXT," + // 4: max_version
            "'END_TIME' INTEGER NOT NULL DEFAULT(0) ," + // 5: end_time
            "'ICON' TEXT," + // 6: icon
            "'TITLE' TEXT," + // 7: title
            "'SORT' INTEGER NOT NULL DEFAULT(-1) ," + // 8: sort
            "'GROUP_RED' INTEGER NOT NULL DEFAULT(0) ," + // 9: group_red
            "'ONLINE_AT' INTEGER NOT NULL DEFAULT(0) ," + // 10: online_at
            "'SEQUENCE' TEXT," + // 11: sequence
            "'IS_RED' INTEGER NOT NULL DEFAULT(0) );"); // 12: is_red
    }

    /** Drops the underlying database table. */
    public void dropArMaterialGroupTable(SupportSQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS 'AR_MATERIAL_GROUP'";
        db.execSQL(sql);
    }

    /** ALTER database table. */
    public void upgradeArMaterialGroupTable(SupportSQLiteDatabase db) {
        Cursor c = null;
        int count = 0;
        String sql = "SELECT COUNT(*) as count FROM sqlite_master WHERE TYPE='table' AND NAME='AR_MATERIAL_GROUP'";
        count = 0;
        try {
            c = db.query(sql, null);
            if (null != c && c.moveToFirst()) {
                count = c.getInt(c.getColumnIndexOrThrow("count"));
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (c != null) {
                c.close();
                c = null;
            }
        }
        if (count == 0) {
            createArMaterialGroupTable(db);
            return;
        }

        try {
            String reNameSql = "ALTER TABLE AR_MATERIAL_GROUP  RENAME TO TEM_AR_MATERIAL_GROUP";
            db.execSQL(reNameSql);
            createArMaterialGroupTable(db);
            copyTableData(db, "TEM_AR_MATERIAL_GROUP", "AR_MATERIAL_GROUP");
            String dropTempTable = "DROP TABLE TEM_AR_MATERIAL_GROUP";
            db.execSQL(dropTempTable);
        } catch (Exception e) {
            Debug.w(e);
        }

    }

    /********AR_MATERIAL_PAID_INFO*********/

    /** Creates the underlying database table. */
    public void createArMaterialPaidInfoTable(SupportSQLiteDatabase db) {
        String constraint = " IF NOT EXISTS ";
        db.execSQL(CREATE_TABLE + constraint + "'AR_MATERIAL_PAID_INFO' (" + //
            "'_id' INTEGER PRIMARY KEY ," + // 0: id
            "'NUMBER' INTEGER NOT NULL DEFAULT(0) ," + // 1: number
            "'CATEGORY_NUMBER' INTEGER NOT NULL DEFAULT(0) ," + // 2: category_number
            "'GOODS_ID' TEXT," + // 3: goods_id
            "'ITEMS' TEXT," + // 4: items
            "'IS_PAID' INTEGER NOT NULL DEFAULT(0));"); // 5: is_paid
    }

    /** Drops the underlying database table. */
    public static void dropArMaterialPaidInfoTable(SupportSQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS 'AR_MATERIAL_PAID_INFO'";
        db.execSQL(sql);
    }

    /** ALTER database table. */
    public void upgradeArMaterialPaidInfoTable(SupportSQLiteDatabase db) {
        Cursor c = null;
        int count = 0;
        String sql = "SELECT COUNT(*) as count FROM sqlite_master WHERE TYPE='table' AND NAME='AR_MATERIAL_PAID_INFO'";
        count = 0;
        try {
            c = db.query(sql, null);
            if (null != c && c.moveToFirst()) {
                count = c.getInt(c.getColumnIndexOrThrow("count"));
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (c != null) {
                c.close();
                c = null;
            }
        }
        if (count == 0) {
            createArMaterialPaidInfoTable(db);
            return;
        }

        try {
            String reNameSql = "ALTER TABLE AR_MATERIAL_PAID_INFO  RENAME TO TEM_AR_MATERIAL_PAID_INFO";
            db.execSQL(reNameSql);
            createArMaterialPaidInfoTable(db);
            copyTableData(db, "TEM_AR_MATERIAL_PAID_INFO", "AR_MATERIAL_PAID_INFO");
            String dropTempTable = "DROP TABLE TEM_AR_MATERIAL_PAID_INFO";
            db.execSQL(dropTempTable);
        } catch (Exception e) {
            Debug.w(e);
        }

    }

    /********FILTER_GROUP***********/
    public void createFilterGroupTable(SupportSQLiteDatabase db) {
        String constraint = " IF NOT EXISTS";
        db.execSQL(CREATE_TABLE + constraint + "'FILTER_GROUP' (" + //
            "'_id' INTEGER PRIMARY KEY ," + // 0: id
            "'NUMBER' INTEGER NOT NULL ," + // 1: number
            "'VERSION_CONTROL' INTEGER NOT NULL DEFAULT(0) ," + // 2: version_control
            "'MIN_VERSION' TEXT," + // 3: min_version
            "'MAX_VERSION' TEXT," + // 4: max_version
            "'FILE' TEXT," + // 5: file
            "'ICON' TEXT," + // 6: icon
            "'UNION_ICON' TEXT," + // 7: union_icon
            "'IS_PAID' INTEGER NOT NULL DEFAULT(0)," + // 8: is_paid
            "'PAID_INFO' TEXT," + // 9: paid_info
            "'RECOMMEND' INTEGER NOT NULL DEFAULT(0)," + // 10: recommend
            "'SAMPLE_PICTURE' TEXT," + // 11: sample_picture
            "'TITLE' TEXT," + // 12: title
            "'DESCRIPTION' TEXT," + // 13: description
            "'COUNT' INTEGER NOT NULL DEFAULT(0)," + // 14: count
            "'IS_DOWNLOAD' INTEGER NOT NULL DEFAULT(0)," + // 15: is_download
            "'IS_DOWNLOADING' INTEGER NOT NULL DEFAULT(0)," + // 16: is_downloading
            "'DOWNLOAD_TIME' INTEGER NOT NULL DEFAULT(0)," + // 17: download_time
            "'IS_USE' INTEGER NOT NULL DEFAULT(0)," + // 18: is_use
            "'OLD_DATA' INTEGER NOT NULL DEFAULT(0)," + // 19: old_data
            "'AUTO_DOWNLOAD' INTEGER NOT NULL DEFAULT(0)," + // 20: auto_download
            "'IS_INTERNAL' INTEGER NOT NULL DEFAULT(0)," + // 21: is_internal
            "'EXPAND' INTEGER NOT NULL DEFAULT(0)," + // 22: expand
            "'MONEY' TEXT," + // 23: money
            "'SORT' INTEGER);"); // 24: sort
    }

    /** ALTER database table. */
    public void upgradeFilterGroupTable(SupportSQLiteDatabase db) {
        Cursor c = null;
        int count = 0;
        String sql = "SELECT COUNT(*) as count FROM sqlite_master WHERE TYPE='table' AND NAME='FILTER_GROUP'";
        count = 0;
        try {
            c = db.query(sql, null);
            if (null != c && c.moveToFirst()) {
                count = c.getInt(c.getColumnIndexOrThrow("count"));
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (c != null) {
                c.close();
                c = null;
            }
        }
        if (count == 0) {
            createFilterGroupTable(db);
            return;
        }
        try {
            String reNameSql = "ALTER TABLE FILTER_GROUP  RENAME TO TEM_FILTER_GROUP ";
            db.execSQL(reNameSql);
            createFilterGroupTable(db);
            copyTableData(db, "TEM_FILTER_GROUP", "FILTER_GROUP");
            String dropTempTable = "DROP TABLE IF EXISTS 'TEM_FILTER_GROUP'";
            db.execSQL(dropTempTable);
        } catch (Exception e) {
            Debug.w(e);
        }
    }

    /********** Filter     ************/

    public void createFilterTable(SupportSQLiteDatabase db) {
        String constraint = " IF NOT EXISTS ";
        db.execSQL(CREATE_TABLE + constraint + "'FILTER' (" + //
            "'_id' INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
            "'FILTER_ID' INTEGER," + // 1: filter_id
            "'BEAUTY_ALPHA' INTEGER NOT NULL DEFAULT(0) ," + // 2: BeautyAlpha
            "'FILTER_PATH' TEXT," + // 3: FilterPath
            "'ALPHA' INTEGER NOT NULL DEFAULT(0)," + // 4: Alpha
            "'THUMBNAIL' TEXT," + // 5: Thumbnail
            "'DARK_TYPE' INTEGER NOT NULL DEFAULT(0) ," + // 6: DarkType
            "'DARK_AFTER' INTEGER NOT NULL DEFAULT(0)," + // 7: DarkAfter
            "'FORCE_OPEN_DARK' INTEGER NOT NULL DEFAULT(0)," + // 8: ForceOpenDark
            "'FORCE_OPEN_BLUR' INTEGER NOT NULL DEFAULT(0)," + // 9: ForceOpenBlur
            "'STATICS_ID' TEXT," + // 10: StaticsID
            "'WEIGHT' INTEGER NOT NULL DEFAULT(0)," + // 11: Weight
            "'MAX_COUNT' INTEGER NOT NULL DEFAULT(0)," + // 12: MaxCount
            "'NEED_BODY_MASK' TEXT," + // 13: NeedBodyMask
            "'NEED_HAIR_MASK' TEXT," + // 14: NeedHairMask
            "'NEED_NEW_MODE' TEXT," + // 15: NeedNewMode
            "'EN' TEXT," + // 16: en
            "'ZH' TEXT," + // 17: zh
            "'TW' TEXT," + // 18: tw
            "'JP' TEXT," + // 19: jp
            "'KO' TEXT," + // 20: ko
            "'PT' TEXT," + // 21: pt
            "'ES' TEXT," + // 22: es
            "'VI' TEXT," + // 23: vi
            "'TH' TEXT," + // 24: th
            "'IN' TEXT," + // 25: in
            "'GROUP_NUMBER' INTEGER NOT NULL DEFAULT(0)," + // 26: group_number
            "'IS_COLLECTION' INTEGER NOT NULL DEFAULT(0)," + // 27: is_collection
            "'ALPHA_BEAUTIFY' INTEGER NOT NULL DEFAULT(0)," + // 28: AlphaBeautify
            "'GROUP_ID' INTEGER);"); // 29: group_id
    }

    private void upgradeFilterTable(SupportSQLiteDatabase db) {
        Cursor c = null;
        int count = 0;
        String sql = "SELECT COUNT(*) as count FROM sqlite_master WHERE TYPE='table' AND NAME='FILTER'";
        count = 0;
        try {
            c = db.query(sql, null);
            if (null != c && c.moveToFirst()) {
                count = c.getInt(c.getColumnIndexOrThrow("count"));
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (c != null) {
                c.close();
                c = null;
            }
        }
        if (count == 0) {
            createFilterTable(db);
            return;
        }
        try {
            String reNameSql = "ALTER TABLE FILTER  RENAME TO TEM_FILTER ";
            db.execSQL(reNameSql);
            createFilterTable(db);

            copyTableData(db, "TEM_FILTER", "FILTER");
            String dropTempTable = "DROP TABLE IF EXISTS 'TEM_FILTER'";
            db.execSQL(dropTempTable);
        } catch (Exception e) {
            Debug.w(e);
        }
    }

    /*******LocalimageDao*********/

    public void createLocalImageTable(SupportSQLiteDatabase db) {
        String constraint = " IF NOT EXISTS ";
        db.execSQL(CREATE_TABLE + constraint + "'LOCAL_IMAGE' (" + //
            "'IMG_ID' TEXT  PRIMARY KEY NOT NULL," + // 0: img_id
            "'IMG_PATH' TEXT," + // 1: img_path
            "'IMG_SIZE' INTEGER);"); // 2: img_size
    }

    /** ALTER database table. */
    public void upgradeLocalImageTable(SupportSQLiteDatabase db) {
        Cursor c = null;
        int count = 0;
        String sql = "SELECT COUNT(*) as count FROM sqlite_master WHERE TYPE='table' AND NAME='LOCAL_IMAGE'";
        count = 0;
        try {
            c = db.query(sql, null);
            if (null != c && c.moveToFirst()) {
                count = c.getInt(c.getColumnIndexOrThrow("count"));
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (c != null) {
                c.close();
                c = null;
            }
        }
        if (count == 0) {
            createLocalImageTable(db);
            return;
        }

        try {
            String reNameSql = "ALTER TABLE LOCAL_IMAGE  RENAME TO TEM_LOCAL_IMAGE ";
            db.execSQL(reNameSql);
            createLocalImageTable(db);
            copyTableData(db, "TEM_LOCAL_IMAGE", "LOCAL_IMAGE");
        } catch (Exception e) {
            Debug.w(e);
        }

    }

    /********  CloudImage**********/

    public void createCloudImageTable(SupportSQLiteDatabase db) {
        String constraint = " IF NOT EXISTS ";
        db.execSQL(CREATE_TABLE + constraint + "'CLOUD_IMAGE' (" + //
            "'_id' INTEGER PRIMARY KEY ," + // 0: id
            "'USER_ID' TEXT," + // 1: user_id
            "'IMG_ID' TEXT," + // 2: img_id
            "'IMG_URL' TEXT," + // 3: img_url
            "'IMG_SIZE' INTEGER," + // 4: img_size
            "'IMG_SCORE' REAL NOT NULL DEFAULT(0) ," + // 5: img_score
            "'IMG_PATH' TEXT," + // 6: img_path
            "'IS_DOWNLOAD' INTEGER)"); // 7: is_download
    }

    /** Drops the underlying database table. */
    public void dropCloudImageTable(SupportSQLiteDatabase db) {
        String sql = "DROP TABLE IF EXISTS 'CLOUD_IMAGE'";
        db.execSQL(sql);
    }

    /** ALTER database table. */
    public void upgradeCloudImageTable(SupportSQLiteDatabase db) {
        Cursor c = null;
        int count = 0;
        String sql = "SELECT COUNT(*) as count FROM sqlite_master WHERE TYPE='table' AND NAME='CLOUD_IMAGE'";
        count = 0;
        try {
            c = db.query(sql, null);
            if (null != c && c.moveToFirst()) {
                count = c.getInt(c.getColumnIndexOrThrow("count"));
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (c != null) {
                c.close();
                c = null;
            }
        }
        if (count == 0) {
            createCloudImageTable(db);
            return;
        }

        try {
            String reNameSql = "ALTER TABLE CLOUD_IMAGE  RENAME TO TEM_CLOUD_IMAGE";
            db.execSQL(reNameSql);
            createCloudImageTable(db);
            copyTableData(db, "TEM_CLOUD_IMAGE", "CLOUD_IMAGE");
            String dropTempTable = "DROP TABLE TEM_CLOUD_IMAGE";
            db.execSQL(dropTempTable);
        } catch (Exception e) {
            Debug.w(e);
        }
    }

    /**
     *
     * @param supportSQLiteDatabase supportSQLiteDatabase
     * @param tableA 数据源表
     * @param tableB 新表
     */
    private void copyTableData(SupportSQLiteDatabase supportSQLiteDatabase, String tableA, String tableB) {
        // 从表A查询所有的字段
        String findSql = "PRAGMA table_info(" + tableA + ")";
        Cursor mNameCursor = null;
        try {

            mNameCursor = supportSQLiteDatabase.query(findSql);
            StringBuffer insertSqlBuffer = new StringBuffer();
            StringBuffer selectSqlBuffer = new StringBuffer();

            if (mNameCursor != null) {
                insertSqlBuffer.append("insert or ignore into " + tableB + "(");
                selectSqlBuffer.append("select ");

                while (mNameCursor.moveToNext()) {
                    String name = mNameCursor.getString(mNameCursor.getColumnIndexOrThrow("name"));

                    if ("in".equalsIgnoreCase(name)) {
                        if (mNameCursor.isLast()) {
                            insertSqlBuffer.append("[" + name + "]");
                            selectSqlBuffer.append("[" + name + "]");
                        } else {
                            insertSqlBuffer.append("[" + name + "],");
                            selectSqlBuffer.append("[" + name + "],");
                        }
                    } else {
                        if (mNameCursor.isLast()) {
                            insertSqlBuffer.append(name);
                            selectSqlBuffer.append(name);
                        } else {
                            insertSqlBuffer.append(name + ",");
                            selectSqlBuffer.append(name + ",");
                        }
                    }

                }
                insertSqlBuffer.append(")");
                selectSqlBuffer.append(" from " + tableA);

                String finalSql = insertSqlBuffer.toString() + " " + selectSqlBuffer.toString();
                supportSQLiteDatabase.execSQL(finalSql);

            }

        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (mNameCursor != null) {
                mNameCursor.close();
                mNameCursor = null;
            }
        }

    }

    /***************/

}
