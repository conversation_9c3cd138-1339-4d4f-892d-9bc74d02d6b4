package com.meitu.room.dao

import androidx.room.*
import com.meitu.template.bean.SoundEffectTag

@Dao
interface SoundEffectTagDao {

    @Query("select * from sound_effect_tag where mId = :id")
    fun loadByMid(id: String?): SoundEffectTag

    @Query("select mId from sound_effect_tag")
    fun loadKeys(): MutableList<String>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(entity: SoundEffectTag)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(list: Array<SoundEffectTag>)

    @Update
    fun updateAll(list: Array<SoundEffectTag>)

    @Update
    fun update(entity: SoundEffectTag)

    @Delete
    fun delete(entity: SoundEffectTag)

    @Delete
    fun deleteAll(list: Array<SoundEffectTag>)

    @Query("select * from sound_effect_tag")
    fun loadAll(): MutableList<SoundEffectTag>

    @Query("select * from sound_effect_tag")
    fun loadAllEnableEntity(): Mu<PERSON>List<SoundEffectTag>

}