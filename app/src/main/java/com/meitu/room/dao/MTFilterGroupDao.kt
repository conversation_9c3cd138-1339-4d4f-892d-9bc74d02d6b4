package com.meitu.room.dao

import androidx.room.*
import com.meitu.template.bean.FilterGroup

@Dao
interface MTFilterGroupDao : IDataResource<FilterGroup, Int> {
    @Query("select * from FILTER_GROUP_INFO where GroupId=:key")
    override fun loadEntity(key: Int?): FilterGroup

    @Query("select GroupId from FILTER_GROUP_INFO")
    override fun loadKeys(): List<Int>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    override fun insert(entity: FilterGroup)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    override fun insertAll(list: Array<FilterGroup>)

    @Delete
    override fun delete(entity: FilterGroup)

    @Delete
    override fun deleteAll(list: Array<FilterGroup>)

    @Update
    override fun updateAll(list: Array<FilterGroup>)

    @Update
    override fun update(entity: FilterGroup)

    @Query("select * from filter_group_info")
    fun loadAll(): List<FilterGroup>
}