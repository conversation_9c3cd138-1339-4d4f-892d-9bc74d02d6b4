package com.commsource.billing.cache

import com.commsource.billing.isFirstInstall
import com.commsource.util.common.SPConfig
import com.commsource.util.random
import com.meitu.common.AppContext


object ProCache : SPConfig(AppContext.context, "pro_cache") {
    //===========================================新用户订阅优化


    private const val NEWUSER_SUBSCRIBE_STATUS = "NEWUSER_SUBSCRIBE_STATUS"

    private const val NEWUSER_SUBSCRIBE_END_TIME = "NEWUSER_SUBSCRIBE_END_TIME"

    private const val NEWUSER_SUBSCRIBE_OFF_PERCENT = "NEWUSER_SUBSCRIBE_OFF_PERCENT"

    private const val NEWUSER_SUBSCRIBE_DISCOUNT_PERCENT = "NEWUSER_SUBSCRIBE_DISCOUNT_PERCENT"

    const val NEWUSER_SUBSCRIBE_FIRST_INSTALL = "NEWUSER_SUBSCRIBE_FIRST_INSTALL"

    fun getNewUserSubscribeStatus(): Int {
        val status = getInt(NEWUSER_SUBSCRIBE_STATUS, TEST_INIT)
        return if (status == TEST_IN) {
            if (System.currentTimeMillis() <= getNewUserSubscribeEndTime()) {
                TEST_DOWN
            } else {
                TEST_DOWN_END
            }
        } else {
            status
        }
    }

    fun setNewUserSubscribeStatus(status: Int) = putValue(NEWUSER_SUBSCRIBE_STATUS, status)

    fun setNewUserSubscribeEndTime(time: Long) = putValue(NEWUSER_SUBSCRIBE_END_TIME, time)

    fun getNewUserSubscribeEndTime() = getLong(NEWUSER_SUBSCRIBE_END_TIME, -1)

    fun initNewUserSubscribeOffPercent(percent: String?) {
        putValue(NEWUSER_SUBSCRIBE_OFF_PERCENT, percent)
    }
    fun getNewUserSubscribeOffPercent() = getString(NEWUSER_SUBSCRIBE_OFF_PERCENT, "")

    fun initNewUserSubscribeDiscountPercent(percent: String?) {
        putValue(NEWUSER_SUBSCRIBE_DISCOUNT_PERCENT, percent)
    }
    fun getNewUserSubscribeDiscountPercent() = getString(NEWUSER_SUBSCRIBE_DISCOUNT_PERCENT, "")

    fun isNewUserSubscribeFirstInstall() =
        getInt(NEWUSER_SUBSCRIBE_FIRST_INSTALL, -1) == -1 && isFirstInstall()

    fun setNewUserSubscribeFirstInstall() = putValue(NEWUSER_SUBSCRIBE_FIRST_INSTALL, 1)


    //===========================================周订阅开关
    private const val SHOW_WEEKLY_PLAN = "SHOW_WEEKLY_PLAN"

    //高级开关 设置
    fun setShowWeeklyPlan(value: Boolean) = putValue(SHOW_WEEKLY_PLAN, value)

    fun showWeeklyPlan() = getBoolean(SHOW_WEEKLY_PLAN, true)
}