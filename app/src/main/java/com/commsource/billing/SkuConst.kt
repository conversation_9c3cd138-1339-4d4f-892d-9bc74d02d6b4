package com.commsource.billing

import com.commsource.billing.cache.ProCache
import com.commsource.statistics.Meepo
import com.commsource.util.BPLocationUtils
import com.commsource.util.LOGV_Pro
import com.commsource.util.random
import com.meitu.common.AppContext

/**
 * 订阅sku
 * Created on 2021/10/31
 * <AUTHOR>
 */
object SkuConst {

    /**订阅-年-全价 */
    const val SUB_YEARLY_FULL = "com.commsource.beautyplus.subscribtion.1year.fullprice"

    /**订阅-月-全价 */
    const val SUB_MONTHLY_FULL = "com.commsource.beautyplus.subscribtion.1month.fullprice"

    /**订阅-年-针对已购单项的用户25%优惠 */
    const val SUB_YEARLY_25DISCOUNT = "com.commsource.beautyplus.subscribtion.1year.25discount"

    /**订阅-月-针对已购单项的用户25%优惠 */
    const val SUB_MONTHLY_25DISCOUNT = "com.commsource.beautyplus.subscribtion.1month.25discount"

    /**订阅-年-针对新用户25%优惠 */
    const val SUB_YEARLY_25DISCOUNT_NEW =
        "com.commsource.beautyplus.subscribtion.1year.25discount.new.users"

    /**订阅-月-针对新用户25%优惠 */
    const val SUB_MONTHLY_25DISCOUNT_NEW =
        "com.commsource.beautyplus.subscribtion.1month.25discount.new.users"

    /**
     * 订阅-年-节日优惠
     *
     *
     * 在后台订阅管理之前写的本地节日优惠逻辑，当时针对的是泰国的宋干节
     *
     */
    const val SUB_YEARLY_FEAST = "com.commsource.beautyplus.subscribtion.1year.festival"

    /**
     * 订阅-月-节日优惠
     *
     *
     * 在后台订阅管理之前写的本地节日优惠逻辑，当时针对的是泰国的宋干节
     *
     */
    const val SUB_MONTHLY_FEAST = "com.commsource.beautyplus.subscribtion.1month.festival"


    const val PLAY_STORE_SUBSCRIPTION_DEEPLINK_URL =
        "https://play.google.com/store/account/subscriptions?sku=%s&package=%s"
    const val PLAY_STORE_SUBSCRIPTION_URL = "https://play.google.com/store/account/subscriptions"


    const val US_NO_TRIAL_YEAR_SKU = "beautyplus.subs.month12.func00.lev00.v30"

    const val WEEKLY_SKU_TEST_B = "beautyplus.subs.week1.func00.lev00.v1"

    const val MONTH_SKU_TEST_B = "beautyplus.subs.month1.func00.lev00.v23"
    const val YEAR_SKU_TEST_B = "beautyplus.subs.month12.func00.lev00.v38"

    const val NEW_USER_TEST_A = "beautyplus.subs.month12.func00.lev00.v35"
    const val NEW_USER_TEST_B = "beautyplus.subs.month12.func00.lev00.v36"

    /**
     * 订阅挽留策略优惠SKU，https://meitu.feishu.cn/docx/X9ujdZD9Bo16n7xmCIUcZIZQndf
     */
    fun getRevenueSubOffYearSku(): String {
        if (BPLocationUtils.isUS(AppContext.context)) {
            return "beautyplus.subs.month12.func00.lev00.v31"
        } else {
            return when {
                is2023Tie1() -> "beautyplus.subs.month12.func00.lev00.v24"
                is2023Tie3() -> "beautyplus.subs.month12.func00.lev00.v26"
                else -> "beautyplus.subs.month12.func00.lev00.v25"
            }
        }
    }

    /**
     * 谷歌推荐商品，https://meitu.feishu.cn/docs/doccnqvxnGfbEbxTfdbwwAyckig#
     */
    fun getGooglePlayFeatureYearSku(): String {
        return when {
            is2023Tie1() -> "beautyplus.subs.month12.func00.lev00.campaign.gpfeature.ver0"
            is2023Tie3() -> "beautyplus.subs.month12.func00.lev00.campaign.gpfeature.ver2"
            else -> "beautyplus.subs.month12.func00.lev00.campaign.gpfeature.ver1"
        }
    }

    /**
     * 新用户
     */
    fun getNewUserYearSku(): String {
        return when {
            is2023Tie1() -> "beautyplus.subs.month12.func00.lev00.v18"
            is2023Tie3() -> "beautyplus.subs.month12.func00.lev00.v20"
            else -> "beautyplus.subs.month12.func00.lev00.v19"
        }
    }

    /**
     * 新用户
     */
    fun getNewUserMonthSku(): String {
        return when {
            is2023Tie1() -> "beautyplus.subs.month1.func00.lev00.v14"
            is2023Tie3() -> "beautyplus.subs.month1.func00.lev00.v16"
            else -> "beautyplus.subs.month1.func00.lev00.v15"
        }
    }


    /**
     * 再订阅用户
     */
    fun getReSubUserYearSku(): String {
        return when {
            is2023Tie1() -> "beautyplus.subs.month12.func00.lev00.v21"
            is2023Tie3() -> "beautyplus.subs.month12.func00.lev00.v23"
            else -> "beautyplus.subs.month12.func00.lev00.v22"
        }
    }

    /**
     * 再订阅用户
     */
    fun getReSubUserMonthSku(): String {
        return when {
            is2023Tie1() -> "beautyplus.subs.month1.func00.lev00.v14"
            is2023Tie3() -> "beautyplus.subs.month1.func00.lev00.v16"
            else -> "beautyplus.subs.month1.func00.lev00.v15"
        }
    }

    /**
     * 付费用户-有内购的用户
     */
    fun getDiscountYearSku(): String {
        return when {
            is2023Tie1() -> "beautyplus.subs.month12.func00.lev00.v15"
            is2023Tie3() -> "beautyplus.subs.month12.func00.lev00.v17"
            else -> "beautyplus.subs.month12.func00.lev00.v16"
        }
    }

    /**
     * 付费用户-有内购的用户
     */
    fun getDiscountMonthSku(): String {
        return when {
            is2023Tie1() -> "beautyplus.subs.month1.func00.lev00.v14"
            is2023Tie3() -> "beautyplus.subs.month1.func00.lev00.v16"
            else -> "beautyplus.subs.month1.func00.lev00.v15"
        }
    }

    /**
     * 普通用户
     */
    fun getCommonYearSku(): String {
        return when {
            is2023Tie1() -> "beautyplus.subs.month12.func00.lev00.v15"
            is2023Tie3() -> "beautyplus.subs.month12.func00.lev00.v17"
            else -> "beautyplus.subs.month12.func00.lev00.v16"
        }
    }

    /**
     * 普通用户
     */
    fun getCommonMonthSku(): String {
        return when {
            is2023Tie1() -> "beautyplus.subs.month1.func00.lev00.v14"
            is2023Tie3() -> "beautyplus.subs.month1.func00.lev00.v16"
            else -> "beautyplus.subs.month1.func00.lev00.v15"
        }
    }

    /**
     * 美、英、澳、加、瑞典、瑞士、阿联酋、卡塔尔、沙特
     */
    private fun is2023Tie1(): Boolean {
        val locationBean = BPLocationUtils.getLocationBean(AppContext.context)
        return listOf(
            "US", "GB", "AU", "CA", "SE", "CH",
            "AE", "QA", "SA"
        ).contains(locationBean.country_code)
    }

    /**
     * 日、韩、泰、越南、马来西亚、其他
     */
    private fun is2023Tie2(): Boolean {
        val locationBean = BPLocationUtils.getLocationBean(AppContext.context)
        return listOf("JP", "KR", "TH", "VN", "MY").contains(locationBean.country_code)
    }

    /**
     * 巴西、土耳其、印度尼西亚、菲律宾、中国
     */
    private fun is2023Tie3(): Boolean {
        val locationBean = BPLocationUtils.getLocationBean(AppContext.context)
        return listOf("BR", "TR", "ID", "PH", "CN").contains(locationBean.country_code)
    }


}