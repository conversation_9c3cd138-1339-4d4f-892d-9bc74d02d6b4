package com.commsource.billing.retain

import android.content.DialogInterface
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.os.CountDownTimer
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.commsource.BaseDialog
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.DialogCommonUserRetainBinding
import com.commsource.billing.activity.ProActivity
import com.commsource.billing.activity.SubscribeViewModel
import com.commsource.billing.cache.ProCache
import com.commsource.billing.pro.SubsConfigManager
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.Meepo
import com.commsource.statistics.SpmParamConstant
import com.commsource.util.ResourcesUtils
import com.commsource.util.XDrawableFactory
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.setMarginCompat
import com.commsource.util.text
import com.commsource.util.visible
import com.meitu.library.hwanalytics.spm.SPMShare
import com.meitu.library.util.device.DeviceUtils

/**
 * 无优惠配置
 * author: admin
 * Date: 2023/8/20
 * Des:
 */
class CommonUserRetainDialog:BaseDialog<DialogCommonUserRetainBinding>() {

    private val viewModel by lazy {
        ViewModelProvider(context as FragmentActivity)[SubscribeViewModel::class.java]
    }

    private var countDownTimer: CountDownTimer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = false
    }

    override fun getLayoutId(): Int {
        return R.layout.dialog_common_user_retain
    }

    override fun onDismiss(dialog: DialogInterface?) {
        super.onDismiss(dialog)
        countDownTimer?.cancel()
        countDownTimer = null
    }

    override fun getDialogTag(): String {
        return "CommonUserRetainDialog"
    }

    override fun bindView() {
        val margin = if (DeviceUtils.getScreenWidth() < 375.dp()) 30.dp() else 40.dp()
        mBinding.ccRoot.setMarginCompat(start = margin, end = margin)
        mBinding.tvSubscribe.setBackground(XDrawableFactory.getProButtonDrawable(25.dpf()))
        mBinding.ccFrame.cornerDelegate.apply {
            gradient =intArrayOf(
                0x1AF09AFF.toInt(),
                0x1AA479FF.toInt(),
                0x1A47D8FF.toInt(),
                0x1AAFF2C0.toInt()
            )
            updateCorner(12.dpf)
            strokeWidth = 1.dpf
        }
        mBinding.ccFrame.background = GradientDrawable(
            GradientDrawable.Orientation.TL_BR,
            intArrayOf(
                0x0DFF99D6.toInt(),
                0x05AFF2C0.toInt()
            )
        ).apply {
            cornerRadius = 12.dpf
        }

        initOnClickListener()

        if (viewModel.subUserState.isRetain) {
            countDown(viewModel.subUserState.subUserType.discountDeadLine - System.currentTimeMillis())
            mBinding.llCountdown.visible()
        } else {
            mBinding.llCountdown.gone()
        }
        viewModel.subsConfigInfoEvent.value?.let { config->
            val countDownLeftTime = SubsConfigManager.getCountDownLeftTime(config)
            if (countDownLeftTime > 0 && !config.experiment) {
                mBinding.llCountdown.visible()
                countDown(countDownLeftTime)
            } else {
                mBinding.llCountdown.gone()
            }
        }

        mBinding.tvTitle.text = getString(R.string.t_not_miss_out)
        mBinding.tvSubscribe.text = R.string.t_continue.text()
        viewModel.loadPriceEvent.value?.let { subPriceInfo ->
            val price = if (subPriceInfo.isYearPeriodicity) {
                subPriceInfo.yearIntroductoryPrice
            } else {
                subPriceInfo.yearlyPrice
            }
            mBinding.tvPrice.text = String.format(R.string.t_subs_only_price_yearly.text(), price)
            mBinding.tvOriPrice.text = subPriceInfo.yearlyFullPrice + ResourcesUtils.getString(R.string.per_year)
            mBinding.tvOriPrice.paint.isStrikeThruText = true
            mBinding.tvNextYearPrice.text = String.format(
                ResourcesUtils.getString(R.string.t_if_miss_offer),
                subPriceInfo.yearlyFullPrice
            )
            if (subPriceInfo.yearlyFreeTrialPeriod > 0) {
                mBinding.tvSubscribe.text =
                    R.string.t_start_7_free.text().replace("7", subPriceInfo.yearlyFreeTrialPeriod.toString())
            }
        }
        MTAnalyticsAgent.logEvent("subpage_retain_pop_appr","pop_type", "普通用户优惠弹窗")
    }

    private fun countDown(leftTimeMillis: Long) {
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer(leftTimeMillis, 1000) {
            override fun onFinish() {
                (requireActivity() as? ProActivity)?.let {
                    if (!it.isFinishing) {
                        it.onBack()
                    }
                }
            }

            override fun onTick(millisUntilFinished: Long) {
                if (isVisible) {
                    val totalSeconds = millisUntilFinished / 1000
                    val second = (totalSeconds % 60).toInt()
                    val minute = ((totalSeconds / 60) % 60).toInt()
                    val hour = (totalSeconds / 3600).toInt()

                    mBinding.tvHour.text = if (hour < 10) {
                        "0$hour"
                    } else {
                        hour.toString()
                    }
                    mBinding.tvMinute.text = if (minute < 10) {
                        "0$minute"
                    } else {
                        minute.toString()
                    }
                    mBinding.tvSecond.text = if (second < 10) {
                        "0$second"
                    } else {
                        second.toString()
                    }
                }
            }
        }
        countDownTimer?.start()
    }

    private fun initOnClickListener() {
        mBinding.ifvClose.setOnClickListener {
            dismissAllowingStateLoss()
            MTAnalyticsAgent.logEvent("subpage_retain_pop_clk", HashMap<String, String>(8).apply {
                put("type", "close")
                put("pop_type", "普通用户优惠弹窗")
            })
            (requireActivity() as? ProActivity)?.onBack()
        }

        mBinding.tvSubscribe.setOnClickListener {
            MTAnalyticsAgent.logEvent("subpage_retain_pop_clk", HashMap<String, String>(8).apply {
                put("type", "accept")
                put("pop_type", "普通用户优惠弹窗")
            })
            SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "订阅页挽留弹窗")
            viewModel.setRetainSubs(true)
            viewModel.subscribeYearlyImpl(requireActivity(), false)
        }

        viewModel.subSuccessEvent.observe(viewLifecycleOwner) {
            if (it) {
                dismissAllowingStateLoss()
            }
        }
    }
}