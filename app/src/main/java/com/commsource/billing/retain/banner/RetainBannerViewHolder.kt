package com.commsource.billing.retain.banner

import android.content.Context
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemRetainBannerBinding
import com.commsource.util.GlideProxy
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

/**
 * author: admin
 * Date: 2023/8/22
 * Des:
 */
class RetainBannerViewHolder(context:Context, parent:ViewGroup):
    BaseViewHolder<RetainBanner>(context, parent, R.layout.item_retain_banner) {

    private val viewBinding = ItemRetainBannerBinding.bind(itemView)

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<RetainBanner>?,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        item?.entity?.let {
            GlideProxy.with(mContext)
                .load(it.icon)
                .into(viewBinding.ivBanner)
            viewBinding.tvName.text = it.name
        }
    }
}