package com.commsource.beautyplus

import android.graphics.Bitmap
import android.media.ThumbnailUtils
import android.os.Build
import android.provider.MediaStore.Video.Thumbnails
import android.util.Size
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.Key
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.data.DataFetcher
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.load.model.ModelLoaderFactory
import com.bumptech.glide.load.model.MultiModelLoaderFactory
import com.commsource.album.MediaInfoCover
import com.commsource.util.BitmapUtils
import com.commsource.util.measureDuration
import com.commsource.util.print
import com.meitu.common.AppContext
import com.meitu.media.tools.editor.MVEditorTool
import com.pixocial.library.albumkit.media.MediaType
import java.io.ByteArrayInputStream
import java.io.File
import java.io.InputStream
import java.security.MessageDigest

/**
 * MediaInfoCover专属加载Factory
 */
class MediaInfoFactory : ModelLoaderFactory<MediaInfoCover, InputStream> {

    override fun build(multiFactory: MultiModelLoaderFactory): ModelLoader<MediaInfoCover, InputStream> {
        return MediaInfoLoader()
    }

    override fun teardown() {
    }
}


/**
 * MediaInfoLoader
 * 针对[MediaInfoCover] 专属的加载工具
 */
class MediaInfoLoader : ModelLoader<MediaInfoCover, InputStream> {

    override fun buildLoadData(
        model: MediaInfoCover,
        width: Int,
        height: Int,
        options: Options
    ): ModelLoader.LoadData<InputStream>? {
        return ModelLoader.LoadData(
            MediaInfoSignature(
                model.mediaInfo.mediaPath ?: model.mediaInfo.mediaUriString ?: ""
            ), MediaInfoFetcher(model)
        )
    }

    override fun handles(model: MediaInfoCover): Boolean {
        return true
    }

}

class MediaInfoSignature(path: String) : Key {

    private val file by lazy { File(path) }

    private val sb by lazy { StringBuilder() }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        val orientation = BitmapUtils.getImageFileOrientation(file.absolutePath)
        sb.append(file.lastModified()).append(file.absolutePath).append("_")
            .append(orientation.toString())
        val bs = sb.toString().toByteArray()
        messageDigest.update(bs, 0, bs.size)
    }
}

class MediaInfoFetcher(val cover: MediaInfoCover) : DataFetcher<InputStream> {

    override fun loadData(priority: Priority, callback: DataFetcher.DataCallback<in InputStream>) {
        var bitmap: Bitmap? = null
        var exception: Exception? = null
        var byteArray: ByteArray? = null
        measureDuration("mediaInfo加载耗时", tag = "csx") {
            try {
                if (cover.mediaInfo.mediaType == MediaType.VIDEO) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        bitmap = ThumbnailUtils.createVideoThumbnail(
                            File(cover.mediaInfo.mediaPath ?: ""),
                            Size(cover.size, cover.size), null
                        )
                    } else {
                        bitmap = ThumbnailUtils.createVideoThumbnail(
                            cover.mediaInfo.mediaPath ?: "",
                            Thumbnails.MICRO_KIND
                        )
                    }
                } else {
                    BitmapUtils.loadBitmapFromFile(
                        cover.mediaInfo.mediaPath ?: "", cover.size, cover.size
                    )?.let {
                        val orientation =
                            BitmapUtils.getImageFileOrientation(cover.mediaInfo.mediaPath ?: "")
                        bitmap = if (orientation != 1) {
                            BitmapUtils.getBitmapByOrientation(it, orientation, true)
                        } else {
                            it
                        }
                    }
                }
            } catch (e: Exception) {
                exception = e
                try {
                    if (cover.mediaInfo.isVideo() && bitmap == null) {
                        bitmap = MVEditorTool.getFirstFrame(
                            AppContext.application, cover.mediaInfo.mediaPath
                        )
                    }
                } catch (e: Exception) {
                    exception = e
                }
            }
            "size:${cover.size} >>> bitmap:${bitmap?.width} - ${bitmap?.height}".print("csx")
            byteArray = BitmapUtils.BitmapToByte(bitmap, 100)
        }
        if (byteArray != null) {
            callback.onDataReady(ByteArrayInputStream(byteArray))
        } else {
            callback.onLoadFailed(
                IllegalArgumentException("originMessage:${exception?.message}>>>>mediaInfo,bitmap create error")
            )
        }
    }

    override fun cleanup() {
    }

    override fun cancel() {
    }

    override fun getDataClass(): Class<InputStream> {
        return InputStream::class.java
    }

    override fun getDataSource(): DataSource {
        return DataSource.LOCAL
    }

}