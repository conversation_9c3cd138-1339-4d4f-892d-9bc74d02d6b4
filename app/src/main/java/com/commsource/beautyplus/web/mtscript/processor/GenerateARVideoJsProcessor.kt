package com.commsource.beautyplus.web.mtscript.processor

import android.app.Activity
import android.net.Uri
import androidx.annotation.Keep
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.commsource.beautyplus.util.PathUtil
import com.commsource.beautyplus.web.mtscript.JsProcessor
import com.commsource.material.DownloadManager
import com.commsource.material.download.request.MaterialRequest
import com.commsource.material.download.request.OnDownloadListener
import com.commsource.material.download.task.CommonDownloadTask
import com.commsource.util.BitmapUtils
import com.commsource.util.PermissionUitl
import com.commsource.util.ipermission.EnsureAllPermissionCallBack
import com.commsource.util.ipermission.IPermission
import com.commsource.videostudio.VideoProcessPipeline
import com.google.gson.annotations.SerializedName
import com.meitu.common.AppContext
import com.meitu.library.util.io.FileUtils
import com.meitu.webview.core.CommonWebView
import com.meitu.webview.utils.UnProguard
import com.pixocial.library.albumkit.media.MediaInfo
import com.pixocial.library.albumkit.media.MediaType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 生成十二星座协议
 *
 * 内容就是根据视频生成AR包
 */
class GenerateARVideoJsProcessor :
    JsProcessor<GenerateARVideoJsProcessor.InputParameters>(
        jsProtocol = "mtcommand://generateARVideo",
        InputParameters::class.java
    ) {

    data class InputParameters(
        @SerializedName("source")
        var source: String = "",

        @SerializedName("assetsFile")
        var assetsFile: List<AssetsFile> = emptyList(),

        @SerializedName("fps")
        var fps: Int = 30,

        @SerializedName("time")
        var time: Long = 3000

    ) : UnProguard

    /**
     * 资源包
     */
    @Keep
    data class AssetsFile(val type: String, val file: String)

    override fun processLocalLogic(
        activity: Activity,
        webView: CommonWebView,
        protocolUri: Uri,
        inputParameters: InputParameters?
    ) {
        inputParameters ?: return
        //1.视频渲染链路
        //2.输入主轨道视频/图片
        //3.素材包
        //4.保存视频
        activity.takeIf { it is FragmentActivity }?.let {
            it as FragmentActivity
            IPermission(it)
                .request(*PermissionUitl.getPartialMediaPermissionsWhenAndroid14())
                .execute(object : EnsureAllPermissionCallBack() {
                    override fun onAllPermissionEnable(isEnable: Boolean) {
                        var hasReadMediaVisualUserSelected = PermissionUitl.hasReadMediaVisualUserSelected()
                        if (!isEnable && !hasReadMediaVisualUserSelected) {
                            post(HashMap<String, Any>(4).apply {
                                this["status"] = false
                                this["source"] = Any()
                                this["message"] = "用户不提供存储权限~"
                            })
                            return
                        }
                        it.lifecycleScope.launch(Dispatchers.IO) {
                            val parentPath = PathUtil.getCacheDir(AppContext.context) + "/mtjsCache"
                            val localSourcePath = "$parentPath/${File(inputParameters.source).name}"
                            val result = suspendCoroutine<Boolean> {
                                var hasResume = false

                                val executor = MaterialRequest.Executor()
                                //主图
                                if (!FileUtils.isFileExist(localSourcePath)) {
                                    executor.addTask(
                                        CommonDownloadTask(
                                            inputParameters.source,
                                            localSourcePath,
                                            needUnzip = false,
                                            unzipPath = null
                                        ), DownloadManager.asyncQueue
                                    )
                                }
                                //资源包
                                inputParameters.assetsFile.forEach {
                                    val name = File(it.file).nameWithoutExtension
                                    val localRootPath = "$parentPath/${name}"
                                    val downloadZipPath = "$parentPath/${name}.zip"
                                    if (!FileUtils.isFileExist(localRootPath)) {
                                        executor.addTask(
                                            CommonDownloadTask(
                                                it.file,
                                                downloadZipPath,
                                                needUnzip = true,
                                                unzipPath = localRootPath
                                            ), DownloadManager.asyncQueue
                                        )
                                    }
                                }
                                if (!executor.tasks.isEmpty()) {
                                    executor.execute(object : OnDownloadListener {
                                        override fun onStart() {
                                        }

                                        override fun onProgressChange(progress: Int) {
                                        }

                                        override fun onError(e: Throwable) {
                                            if (!hasResume) {
                                                hasResume = true
                                                it.resume(false)
                                            }
                                        }

                                        override fun onSuccess() {
                                            if (!hasResume) {
                                                hasResume = true
                                                it.resume(true)
                                            }
                                        }
                                    })
                                } else {
                                    it.resume(true)
                                }
                            }
                            if (!result) {
                                post(HashMap<String, Any>(4).apply {
                                    this["status"] = false
                                    this["source"] = Any()
                                    this["message"] = "素材下载失败"
                                })
                                return@launch
                            }
                            //到这里所有资源包要下载的均已下载好
                            val size = BitmapUtils.getBitmapSize(localSourcePath)
                            val videoProcessResult = VideoProcessPipeline(it)
                                .process(
                                    editorInfo = VideoProcessPipeline.buildVideoEditorInfo(
                                        arrayListOf(
                                            MediaInfo().apply {
                                                mediaType = MediaType.IMAGE
                                                mediaPath = localSourcePath
                                                size?.let {
                                                    width = it[0]
                                                    height = it[1]
                                                }
                                            }),
                                        inputParameters.fps
                                    ),
                                    effects = ArrayList<VideoProcessPipeline.AREffect>().apply {
                                        inputParameters.assetsFile.forEach {
                                            add(
                                                VideoProcessPipeline.AREffect(
                                                    configPath = "$parentPath/${
                                                        File(
                                                            it.file
                                                        ).nameWithoutExtension
                                                    }/ar/configuration.plist"
                                                )
                                            )
                                        }
                                    },
                                    time = inputParameters.time,
                                )
                            //如果保存地址为空 说明生效效果不成功
                            if (videoProcessResult.savePath == null) {
                                post(HashMap<String, Any>(4).apply {
                                    this["status"] = false
                                    this["source"] = Any()
                                    this["message"] = videoProcessResult.errorMessage
                                })
                                return@launch
                            }
                            post(HashMap<String, Any>(4).apply {
                                this["status"] = true
                                this["source"] = HashMap<String, Any>(4).apply {
                                    this["video"] = videoProcessResult.savePath
                                }
                                this["message"] = videoProcessResult.errorMessage
                            })
                        }
                    }
                })
        }
    }

}