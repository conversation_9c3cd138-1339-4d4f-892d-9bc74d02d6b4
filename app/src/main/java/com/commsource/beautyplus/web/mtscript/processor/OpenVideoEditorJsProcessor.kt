package com.commsource.beautyplus.web.mtscript.processor

import android.app.Activity
import android.net.Uri
import android.webkit.URLUtil
import androidx.annotation.WorkerThread
import com.commsource.beautyplus.util.PathUtil
import com.commsource.beautyplus.web.mtscript.JsProcessor
import com.commsource.beautyplus.web.mtscript.processor.OpenVideoEditorJsProcessor.InputParameters
import com.commsource.util.GoFunctionUtils
import com.commsource.util.ThreadExecutor
import com.commsource.util.UIHelper
import com.commsource.videostudio.VideoStudioEnterSource
import com.google.gson.annotations.SerializedName
import com.meitu.common.AppContext
import com.meitu.http.ProgressResponseListener
import com.meitu.http.XHttp
import com.meitu.library.util.io.FileUtils
import com.meitu.media.tools.editor.MVEditorTool
import com.meitu.webview.core.CommonWebView
import com.meitu.webview.utils.UnProguard
import com.pixocial.library.albumkit.media.MediaInfo
import com.pixocial.library.albumkit.media.MediaType
import java.io.File

/**
 * 打开视频编辑
 */
class OpenVideoEditorJsProcessor :
    JsProcessor<InputParameters>(
        jsProtocol = "mtcommand://openVideoEditor",
        inputParametersClazz = InputParameters::class.java
    ) {

    public class InputParameters : UnProguard {

        @SerializedName("source")
        val source: String = ""

        @SerializedName("sourceType")
        val sourceType: String = ""

        @SerializedName("minDuration")
        val minDuration: Long = 0

        @SerializedName("maxDuration")
        val maxDuration: Long = 0
    }

    override fun processLocalLogic(
        activity: Activity,
        webView: CommonWebView,
        protocolUri: Uri,
        inputParameters: InputParameters?
    ) {
        inputParameters ?: return
        //保存结果
        @WorkerThread
        fun goVideoEdit(localPath: String) {
            //读取文件进入编辑
            val propertyInfo = MVEditorTool.extractVideoPropertyInfo(
                AppContext.context,
                localPath
            )
            //构建信息
            val info = if (propertyInfo != null) {
                MediaInfo().apply {
                    mediaUri = Uri.fromFile(File(localPath))
                    this.mediaPath = localPath
                    this.width = propertyInfo.width
                    this.height = propertyInfo.height
                    this.orientation = propertyInfo.videoRotation
                    this.mediaType = MediaType.VIDEO
                    this.duration = (propertyInfo.duration * 1000).toLong()
                }
            } else {
                MediaInfo()
                    .apply {
                        mediaUri = Uri.fromFile(File(localPath))
                        this.orientation = 0
                        this.mediaPath = localPath
                        this.mediaType = MediaType.VIDEO
                    }
            }
            UIHelper.runOnUiThread(activity) {
                GoFunctionUtils.goVideoInfusion(
                    activity,
                    VideoStudioEnterSource.VideoSource.OTHER,
                    info
                )
            }

        }
        //先保存视频到本地 再进入编辑
        when (inputParameters.sourceType) {
            "file" -> {
                ThreadExecutor.executeSlowTask("loadFile:mtcommand://openVideoEditor") {
                    goVideoEdit(inputParameters.source)
                }
            }
            "url" -> {
                val savePath = PathUtil.getVideoDraftPath() + "/" + URLUtil.guessFileName(
                    inputParameters.source,
                    null,
                    null
                )
                //如果存在这个
                if (FileUtils.isFileExist(savePath)) {
                    ThreadExecutor.executeSlowTask("loadFile") {
                        goVideoEdit(savePath)
                    }
                } else {
                    XHttp.download(requestMapping = inputParameters.source, savePath = savePath)
                        .execute(object : ProgressResponseListener {
                            override fun onNext(t: String?) {
                                goVideoEdit(savePath)
                            }

                            override fun onProgress(currentWrite: Long, contentLength: Long) {
                                //下载进度
                            }
                        })
                }

            }
        }
    }

}