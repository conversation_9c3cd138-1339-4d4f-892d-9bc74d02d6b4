package com.commsource.beautyplus.web.mtscript

import android.app.Activity
import android.net.Uri
import com.commsource.config.SubscribeConfig
import com.meitu.webview.core.CommonWebView
import com.meitu.webview.mtscript.MTScript

class MTSubStateScript(activity: Activity, webView: CommonWebView, protocolUri: Uri) : MTScript(activity, webView, protocolUri) {
    override fun isNeedProcessInterval(): Boolean {
        return false
    }
    override fun execute(): Boolean {
        val result: Int = SubscribeConfig.getSubsState()
        val message = "javascript:MTJs.postMessage({$PARAM_HANDLER:$handlerCode,data:{status:$result}});"
        doJsPostMessage(message)
        return true
    }
}