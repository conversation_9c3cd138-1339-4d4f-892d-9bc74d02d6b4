package com.commsource.beautyplus.web.mtscript

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import androidx.fragment.app.FragmentActivity
import com.commsource.billing.activity.ProActivity
import com.commsource.config.SelfieConfig
import com.commsource.statistics.SpmParamConstant
import com.commsource.util.V
import com.commsource.util.VerifyMothod
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.delegate.process.GoSubsActScriptProcess
import com.commsource.util.delegate.process.SelfieSettingProcess
import com.commsource.util.delegate.process.SubscribeProcess
import com.google.gson.annotations.SerializedName
import com.meitu.common.AppContext
import com.meitu.library.hwanalytics.spm.SPMShare
import com.meitu.webview.core.CommonWebView
import com.meitu.webview.mtscript.MTScript
import com.meitu.webview.utils.UnProguard
import java.lang.ref.WeakReference

class MTGoSubsActScript(activity: Activity, webView: CommonWebView, protocolUri: Uri) :
    MTScript(activity, webView, protocolUri) {

    companion object {
        const val BEAUTY_GO_SUBS = "mtcommand://openSubscribe"
    }

    private var wkActivity: WeakReference<Activity>? = null

    var onClose: (() -> Boolean)? = null

    class Model : UnProguard {
        @SerializedName("source")
        var source: String? = null

        @SerializedName("type")
        var type = 0
    }

    init {
        wkActivity = WeakReference(activity)
    }

    override fun isNeedProcessInterval(): Boolean {
        return false
    }

    override fun execute(): Boolean {
        requestParams(object : MTScriptParamsCallback<Model>(Model::class.java) {
            public override fun onReceiveValue(model: Model) {
                wkActivity?.get()?.let {
                    SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "H5页面")
                    val source = model.source
                    if (!TextUtils.isEmpty(source)) {
                        SPMShare.put(SpmParamConstant.KEY_SOURCE_FEATURE_CONTENT, source!!)
                    }

                    if (activity is FragmentActivity) {
                        IProcessHandler(activity as FragmentActivity)
                            .execute(object : SubscribeProcess("") {
                                override fun onUseStateResult(
                                    isSubcribe: Boolean,
                                    canFreeUseOnce: Boolean
                                ) {
                                    "订阅结果回调".V()
                                    doJsPostMessage(defaultCmdJsPost)
                                }
                            })
                        VerifyMothod.doInAnimationEx(activity)
                    }

                }
            }
        })
        return true
    }

}