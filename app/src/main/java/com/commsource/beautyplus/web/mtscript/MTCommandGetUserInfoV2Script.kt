package com.commsource.beautyplus.web.mtscript

import android.app.Activity
import android.net.Uri
import android.text.TextUtils
import com.commsource.ad.AdSlotIds.ad_tooniverse
import com.commsource.advertisiting.newad.FunctionRewardAdHelper
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.util.JsonUtil
import com.commsource.beautyplus.web.mtscript.entity.getDefaultTooniverseMetaEJson
import com.commsource.config.ApplicationConfig
import com.commsource.util.LOGV
import com.commsource.util.LOGV_Pro
import com.google.gson.annotations.SerializedName
import com.meitu.webview.core.CommonWebView
import com.meitu.webview.mtscript.MTScript
import com.meitu.webview.utils.UnProguard
import java.util.UUID

class MTCommandGetUserInfoV2Script(activity: Activity?, webView: CommonWebView?, protocolUri: Uri?) :
    MTScript(activity, webView, protocolUri) {


    override fun execute(): Boolean {

        val result =
            "javascript:MTJs.postMessage({ \"${MTScript.PARAM_HANDLER}\":${handlerCode}, \"meta\" : ${getDefaultTooniverseMetaEJson()}," +
                    " \"response\":{\"gid\" : ${ApplicationConfig.getOverseaGID()}}});"
        "GetUserInfoV2 result $result".LOGV()
        doJsPostMessage(result)
        return true
    }

    override fun isNeedProcessInterval(): Boolean {
        return false
    }

}