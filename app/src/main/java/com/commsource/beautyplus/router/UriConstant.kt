package com.commsource.beautyplus.router

object UriConstant {
    /***************************************** Scheme  ***************************************/
    const val BEAUTYPLUS_SCHEME = "beautyplus"

    /***************************************** Host  ***************************************/
    /** 内置浏览器 */
    const val HOST_WEB_VIEW = "p_webview"

    /**
     * 外部浏览器链接
     */
    const val HOST_EXTERNAL_LINK = "p_external_link"

    /** 相机 */
    const val HOST_CAMERA = "p_camera"

    /** miniApp */
    const val HOST_MINI_APP = "p_miniapp"

    // 批量编辑额
    const val HOST_BATCH_EDIT = "p_batch_edit"

    /** 我的 */
    const val HOST_MINE = "p_mine"

    /** IPAR */
    const val HOST_IP_STORE = "p_ip_store"

    /** 帮拍 */
    const val HOST_HAND_OVER = "p_hand_over"

    /** 订阅页 */
    const val HOST_SUBSCRIBE = "p_subscription"

    /** EE */
    const val HOST_EASY_EDITOR = "p_easy_editor"

    /** 关于页 */
    const val HOST_ABOUT = "p_about"

    /** 额度页 */
    const val HOST_CREDIT = "p_credit"

    /**
     * 视频截图页
     */
    const val HOST_VIDEO_SCREENSHOT = "p_video_screenshot"


    /// 塑封
    const val HOST_MOCKUP = "p_mockup"

    /** AIEditor */
    const val HOST_AI_EDITOR = "p_ai_editor"

    const val HOST_AI_ENHANCE = "p_ai_enhance"


    /** 修图 */
    const val HOST_P_EDIT = "p_edit"

    /**
     * 贴纸包
     */
    const val HOST_STI_DETAIL = "p_sticker_detail"

    /**
     * AI 修复推送
     */
    const val HOST_PUSH = "push"

    /** 意见反馈 */
    const val HOST_FEEDBACK = "p_feedback"

    /** 设置页 */
    const val HOST_SETTINGS = "p_settings"

    /** 拼图 */
    const val HOST_COLLAGE = "p_collage"

    /** 证件照 */
    const val HOST_ID_PHOTO = "p_id_photo"

    /** 首页 */
    const val HOST_HOMEPAGE = "p_homepage"

    /** 相册 */
    const val HOST_ALBUM = "p_album"

    /** 视频 */
    const val HOST_VIDEO = "p_video"

    /** 相册 */
    const val HOST_TOPIC = "p_topic"

    /** 全局搜索 */
    const val HOST_SEARCH = "p_search"


    /***************************************** Path  ***************************************/

    const val PATH_MY_RESOURCE = "p_my_resource"

    /** AI 修复*/
    const val PATH_AI_ENHANCE = "f_ai_enhance"

    /** camera模式：自拍*/
    const val PATH_M_SELFIE = "m_selfie"

    /** camera模式：电影*/
    const val PATH_M_MOVIE = "m_movie"

    /** camera模式：视频*/
    const val PATH_M_VIDEO = "m_video"

    const val PATH_IPHONE_M_SELFIE = "m_selfie_iphone"

    const val PATH_IPHONE_M_VIDEO = "m_video_iphone"

    const val PARAM_F_BURST = "f_burst"

    const val PARAM_F_GESTURE = "f_gesture"

    const val PARAM_F_FREEZFRAME = "freezframe"

    /** camera模式：视频*/

    const val PATH_STAMP_M_SELFIE = "m_selfie_timestamp"

    const val PATH_STAMP_M_VIDEO = "m_video_timestamp"

    /** glow camera模式：视频*/
    const val PATH_GLOW_M_SELFIE = "m_selfie_glow"

    const val PATH_GLOW_M_VIDEO = "m_video_glow"

    /** 复古 camera模式：视频*/
    const val PATH_RETRO_M_SELFIE = "m_selfie_film"

    const val PATH_RETRO_M_VIDEO = "m_video_film"

    const val PARAM_POLAROID = "polaroid"

    const val PARAM_FILTER = "filter"

    const val PARAM_SPECIAL = "effect"

    const val PARAM_ISNEEDREFRESHLIMIT = "isNeedRefreshLimit"

    /** 自动光斑虚化/柔焦 */
    const val PATH_F_BOKEH_AUTO = "f_bokeh_auto"

    /** 消除笔选择区域 */
    const val PATH_F_REMOVER_SELECT = "f_remover_select"

    /** 涂鸦笔选择区域 */
    const val PATH_F_DOODLE_PEN_SELECT = "f_doodle_pen_select"

    /** 一键美颜/自动美颜 */
    const val PATH_F_BEAUTIFY_AUTO = "f_beautify_auto"

    /** 自动瘦脸 */
    const val PATH_F_SLIM_AUTO = "f_slim_auto"

    /** 手动瘦脸 */
    const val PATH_F_SLIM_MANUAL = "f_slim_manual"

    /** 增加滤镜 */
    const val PATH_F_FILTER = "f_filter"

    /** 新增文字 */
    const val PATH_F_TEXT = "f_text"


    const val PATH_F_PICSRTIP = "f_picstirp"
    const val PATH_F_COLLAGE = "f_collage"


    /**
     * 背景。
     */
    const val PATH_F_BACKGROUND = "f_background"

    /**
     * 调整。
     */
    const val PATH_F_ADJUST = "f_adjust"

    /** 新增匀肤 */
    const val PATH_F_CONCEALEAR = "f_concealer"

    /** 新增去油光自动 */
    const val PATH_F_ONLINE_AUTO = "f_oiliness_auto"

    /** 新增牙齿矫正自然 */
    const val PATH_F_TEETH_CORRECTION = "f_teeth_correction"

    /** 裁剪 */
    const val PATH_F_CROP_CUSTOM = "f_crop_custom"

    /**
     * 裁剪
     */
    const val f_crop = "f_crop"

    /**
     * ai扩展
     */
    const val f_crop_expansion = "f_crop_expansion"

    /**
     * 透视
     */
    const val f_crop_perspective = "f_crop_perspective"

    /**
     * 尺寸
     */
    const val f_crop_size = "f_crop_size"

    /** ai增强 */
    const val PATH_F_ENHANCE_AUTO = "f_enhance_auto"

    /** 风格化 */
    const val PATH_F_STYLE = "f_style"

    /** 图层样式 */
    const val PATH_F_LAYER_STYLE = "f_layer_style"

    /** 亮度 */
    const val PATH_F_BRIGHTNESS = "f_brightness"

    /** 智能补光 */
    const val PATH_F_FILL_LIGHT = "f_fill_light"

    /** 对比度 */
    const val PATH_F_CONTRAST = "f_contrast"

    /** 饱和度 */
    const val PATH_F_SATURATION = "f_saturation"

    /** 色温 */
    const val PATH_F_TEMPERATURE = "f_temperature"

    /** 高光 */
    const val PATH_F_HIGHLIGHT = "f_highlight"

    /** 阴影 */
    const val PATH_F_SHADOW = "f_shadow"

    /** 锐化 */
    const val PATH_F_SHARPEN = "f_sharpen"

    /** 褪色 */
    const val PATH_F_FADE = "f_fade"

    // 曝光
    const val PATH_F_Exposure = "f_exposure"

    // 鲜明度
    const val PATH_F_Brilliance = "f_brilliance"

    // 自然饱和度
    const val PATH_F_Vibrance = "f_vibrance"

    // 色调
    const val PATH_F_Tint = "f_tint"

    // 去灰
    const val PATH_F_Dehaze = "f_dehaze"

    // 白色色阶
    const val PATH_F_Whites = "f_whites"

    // 黑色色阶
    const val PATH_F_Blacks = "f_blacks"

    /** 暗角 */
    const val PATH_F_VIGNETTE = "f_vignette"

    /** 颗粒 */
    const val PATH_F_GRAIN = "f_grain"

    /** ai美颜 */
    const val PATH_F_HD_RETOUCH = "f_hd_retouch"

    /** 美妆 */
    const val PATH_F_MAKEUP = "f_makeup"

    /** 美妆笔 */
    const val PATH_F_PAINT = "f_paint"

    /** 风格 */
    const val PATH_F_LOOKS = "f_looks"

    /** 美发 */
    const val PATH_F_HAIR = "f_hair"

    /**
     * 美发子功能
     */
    const val PATH_F_HAIR_COLORING = "f_hair_coloring"
    const val PATH_F_HAIR_YO = "f_hair_yo"
    const val PATH_F_HAIR_HAIRTHICKNESS = "f_hair_hairthickness"
    const val PATH_F_HAIR_HAIRLINE = "f_hair_hairline"


    /** 磨皮 */
    const val PATH_F_SMOOTH_AUTO = "f_smooth_auto"

    /** 肤色 */
    const val PATH_F_SKIN_TONE_AUTO = "f_skin_tone_auto"

    /** 面部重塑 */
    const val PATH_F_FACIAL_RESHAPE = "f_facial_reshape"

    /** 面部丰盈 */
    const val PATH_F_FACE_PLUMP = "f_plump"

    /** 缩头 */
    const val PATH_F_HEAD_SIZE = "f_head_size"

    /** 祛双下巴 */
    const val PATH_F_DOUBLE_CHIN = "f_doublechin"

    /** 面部打光 */
    const val PATH_F_RELIGHT = "f_relight_manual"

    /** 新面部打光 自动 面部 背景 氛围的协议开头都是f_relight */
    const val PATH_F_RELIGHT_START = "f_relight"
    const val PATH_F_RELIGHT_AUTO = "f_relight_auto"
    const val PATH_F_RELIGHT_FACE = "f_relight_face"
    const val PATH_F_RELIGHT_BACKGROUND = "f_relight_background"
    const val PATH_F_RELIGHT_AMBIENT = "f_relight_ambient"

    /** 去皱 */
    const val PATH_F_FIRM_SELECT = "f_firm_select"

    /** 祛皱自动 */
    const val PATH_F_FIRM_AUTO = "f_firm_auto"

    /** 眼睛放大 */
    const val PATH_F_EYES_RESIZE_AUTO = "f_eyes_resize_auto"

    /** 祛痘 */
    const val PATH_F_ACNE_AUTO = "f_acne_auto"

    /** 五官立体 */
    const val PATH_F_FACIAL_CONTOURING = "f_facial_contouring"

    /** 缩小鼻翼 */
    const val PATH_F_NARROW_NOSE = "f_narrow_nose"

    /** 亮眼 */
    const val PATH_F_EYE_WHITEN_AUTO = "f_eye_whiten_auto"

    /** 淡化黑眼圈 */
    const val PATH_F_DARK_CIRCLES_AUTO = "f_dark_circles_auto"

    /** 牙齿美白 */
    const val PATH_F_TEETH_WHITEN_AUTO = "f_teeth_whiten_auto"

    /** 塑型 */
    const val PATH_F_RESHAPE_AUTO = "f_reshape_auto"
    const val PATH_F_RESHAPE_RESHAPE = "f_reshape_reshape"

    /** 塑形手动*/
    const val PATH_F_RESHAPE_MANUAL = "f_reshape_manual"
    const val PATH_F_RESHAPE_MANUAL_HEIGHT = "f_reshape_height"
    const val PATH_F_RESHAPE_MANUAL_WIDTH = "f_reshape_width"
    const val PATH_F_RESHAPE_MANUAL_WAIST = "f_reshape_manual_waist"
    const val PATH_F_RESHAPE_MANUAL_RESIZE = "f_reshape_manual_resize"
    const val PATH_F_RESHAPE_MANUAL_RESHAPE = "f_reshape_manual_reshape"

    /**
     * 视频塑形
     */
    const val PATH_F_BODY_TUNE = "f_body_tune"


    /** 表情**/
    const val PATH_F_EXPRESSION = "f_expression"

    /** 塑形：瘦身*/
    const val PATH_F_RESHAPE_TRUNK = "f_reshape_trunk"

    /** 塑形：瘦腿*/
    const val PATH_F_RESHAPE_LEG = "f_reshape_leg"

    /** 塑形：长腿*/
    const val PATH_F_RESHAPE_LENGTH = "f_reshape_length"

    /** 塑形：瘦手臂*/
    const val PATH_F_RESHAPE_ARM = "f_reshape_arm"

    /** 塑形：美胯*/
    const val PATH_F_RESHAPE_HIP = "f_reshape_hip"

    /** 塑形：小头*/
    const val PATH_F_RESHAPE_HEAD = "f_reshape_head"

    /** 塑形：直角肩*/
    const val PATH_F_STRAIGHT_SHOULDERS = "f_reshape_straight_shoulders"

    /** 塑形：天鹅颈*/
    const val PATH_F_RESHAPE_ELEGANT = "f_reshape_elegant_neck"

    /** 塑形：增高*/
    const val PATH_F_RESHAPE_HEIGHT = "f_reshape_height"

    /** 塑形：胸部*/
    const val PATH_F_RESHAPE_BREAST = "f_reshape_breast"

    /** 塑形：肩膀*/
    const val PATH_F_RESHAPE_SHOULDER = "f_reshape_shoulder"

    /** 塑形：腰部*/
    const val PATH_F_RESHAPE_WAIST = "f_reshape_waist"

    /** 贴纸 */
    const val PATH_F_STICKERS = "f_stickers"

    /**
     * ai增肌
     */
    const val PATH_F_MUSCLE = "f_muscle"
    const val f_muscle_body = "f_muscle_body"
    const val f_muscle_abs = "f_muscle_abs" //腹肌
    const val f_muscle_vline = "f_muscle_vline" //马甲线
    const val f_muscle_pecs = "f_muscle_pecs" //胸肌
    const val f_muscle_chest = "f_muscle_chest" //丰胸
    const val f_muscle_arms = "f_muscle_arms" //手臂
    const val f_muscle_collarbone = "f_muscle_collarbone" //锁骨


    /** 马赛克 */
    const val PATH_F_MOSAIC_SELECT = "f_mosaic_select"

    /** 照片修复 */
    const val PATH_F_PHOTO_REPAIR = "f_photo_repair"

    /** 夜景相机 */
    const val PATH_F_NIGHT_PHOTO = "f_night_photo"

    /** 人像移除 */
    const val PATH_F_PERSON_REMOVE = "f_person_remove"

    /** 背景移除 */
    const val PATH_F_REMOVE_BACKGROUND = "f_remove_background"

    /** ColorPlus */
    const val PATH_F_COLOR_PLUS = "f_color_plus"

    /** 旅行AR */
    const val PATH_F_TRAVEL = "f_travel"

    /** 视频塑形-瘦身*/
    const val PATH_F_BODY_TRUNK = "f_body_tune_trunk"

    /** 视频塑形-瘦腰*/
    const val PATH_F_BODY_WAIST = "f_body_tune_waist"

    /** 视频塑形-瘦腿*/
    const val PATH_F_BODY_LEG = "f_body_tune_leg"

    /** 视频塑形-长腿*/
    const val PATH_F_BODY_LENGTH = "f_body_tune_length"

    /** 视频塑形-丰胸*/
    const val PATH_F_BODY_BREAST = "f_body_tune_breast"

    /** 视频塑形-肩宽*/
    const val PATH_F_BODY_SHOULDER = "f_body_tune_shoulder"

    /** 视频塑形-瘦手臂*/
    const val PATH_F_BODY_ARM = "f_body_tune_arm"

    /** 视频塑形-美胯*/
    const val PATH_F_BODY_HIP = "f_body_tune_hip"

    /** 视频塑形-小头*/
    const val PATH_F_BODY_HEAD = "f_body_tune_head"

    /** 视频塑形-直角肩*/
    const val PATH_F_BODY_STRAIGHT_SHOULDERS = "f_body_tune_straight_shoulders"

    /** 视频塑形-天鹅颈*/
    const val PATH_F_BODY_ELEGANT_NECK = "f_body_tune_elegant_neck"


    /** ************************* 面部重塑 start ************************* */

    /** 面部重塑-整体 */
    const val PATH_F_OVERALL = "f_overall"

    /** 面部重塑-整体-小头 */
    const val PATH_F_OVERALL_HEAD_SIZE = "f_overall_head_size"

    /** 面部重塑-整体(3D塑颜)-上下 */
    const val PATH_F_OVERALL_HEIGHT = "f_overall_height"

    /** 面部重塑-整体(3D塑颜)-左右 */
    const val PATH_F_OVERALL_ROTATE = "f_overall_rotate"


    /** 面部重塑-比例 */
    const val PATH_F_RATIO = "f_ratio"

    /** 面部重塑-比例-颅顶 */
    const val PATH_F_RATIO_TOP = "f_ratio_top"

    /** 面部重塑-比例-额头 */
    const val PATH_F_RATIO_FOREHEAD = "f_ratio_forehead"

    /** 面部重塑-比例-中庭 */
    const val PATH_F_RATIO_MIDDLE = "f_ratio_middle"

    /** 面部重塑-脸型-下庭 */
    const val PATH_F_RATIO_LOWER_FACE = "f_ratio_lower_face"

    /** 面部重塑-比例-人中 */
    const val PATH_F_RATIO_PHILTRUM = "f_ratio_philtrum"

    /** 面部重塑-比例-短脸 */
    const val PATH_F_RATIO_SHORT = "f_ratio_short"


    /** 面部重塑-脸型 */
    const val PATH_F_FACE = "f_face"

    /** 面部重塑-脸型-瘦脸 */
    const val PATH_F_FACE_SLIM = "f_face_slim"

    /** 面部重塑-脸型-脸宽 */
    const val PATH_F_FACE_WIDTH = "f_face_width"

    /** 面部重塑-脸型-下颌骨 */
    const val PATH_F_FACE_MANDIBLE = "f_face_mandible"

    /** 面部重塑-脸型-下巴长短 */
    const val PATH_F_FACE_CHIN_LENGTH = "f_face_chin_length"

    /** 面部重塑-脸型-发际线 */
    const val PATH_F_FACE_HAIRLINE = "f_face_hairline"

    /** 面部重塑-脸型-去双下巴 */
    const val PATH_F_FAE_DOUBLE_CHIN = "f_face_double_chin"

    /** 面部重塑-脸型-面部流畅 */
    const val PATH_F_FACE_SMOOTH = "f_face_smooth"

    /** 面部重塑-脸型-面部折叠 */
    const val PATH_F_FACE_SCULPTING = "f_face_sculpting"

    /** 面部重塑-脸型-颧骨 */
    const val PATH_F_FACE_CHEEKBONE = "f_face_cheekbones"

    /** 面部重塑-脸型-下颌线 */
    const val PATH_F_FACE_JAWLINE = "f_face_jawline"


    /** 面部重塑-眉毛 */
    const val PATH_F_EYEBROWS = "f_eyebrows"

    /** 面部重塑-眉毛-高度 */
    const val PATH_F_EYEBROWS_HEIGHT = "f_eyebrows_height"

    /** 面部重塑-眉毛-倾斜 */
    const val PATH_F_EYEBROWS_ANGLE = "f_eyebrows_angle"

    /** 面部重塑-眉毛-厚度 */
    const val PATH_F_EYEBROWS_THICKNESS = "f_eyebrows_thickness"

    /** 面部重塑-眉毛-间距 */
    const val PATH_F_EYEBROWS_END = "f_eyebrows_end"

    /** 面部重塑-眉毛-眉峰 */
    const val PATH_F_EYEBROWS_BROWS_RIDGE = "f_eyebrows_brow_ridge"

    /** 面部重塑-眉毛长短 */
    const val PATH_F_EYEBROWS_LENGTH = "f_eyebrows_length"


    /** 面部重塑-眼睛 */
    const val PATH_F_EYES = "f_eyes"

    /** 面部重塑-眼睛-大眼 */
    const val PATH_F_EYES_SIZE = "f_eyes_size"

    /** 面部重塑-眼睛-眼高 */
    const val PATH_F_EYES_HEIGHT = "f_eyes_height"

    /** 面部重塑-眼睛-眼距 */
    const val PATH_F_EYES_DISTANCE = "f_eyes_distance"

    /** 面部重塑-眼睛-眼睛角度 */
    const val PATH_F_EYES_ANGLE = "f_eyes_angle"

    /** 面部重塑-眼睛-宽度 */
    const val PATH_F_EYES_LENGTH = "f_eyes_length"

    /** 面部重塑-眼睛-下至 */
    const val PATH_F_EYES_EYELID = "f_eyes_eyelid"

    /** 面部重塑-眼睛-瞳孔大小 */
    const val PATH_F_EYES_PUPIL = "f_eyes_pupil"

    /** 面部重塑-眼睛-卧蚕 */
    const val PATH_F_EYES_EYE_SMILES = "f_eyes_eye_smiles"

    /** 面部重塑-眼睛-内眼角 */
    const val PATH_F_EYES_INNER = "f_eyes_inner"

    /** 面部重塑-眼睛-外眼角 */
    const val PATH_F_EYES_OUTER = "f_eyes_outer"


    /** 面部重塑-鼻子 */
    const val PATH_F_NOSE = "f_nose"

    /** 面部重塑-鼻子-大小 */
    const val PATH_F_NOSE_SIZE = "f_nose_size"

    /** 面部重塑-鼻子-高度 */
    const val PATH_F_NOSE_LIFT = "f_nose_lift"

    /** 面部重塑-鼻子-鼻翼 */
    const val PATH_F_NOSE_WING = "f_nose_wing"

    /** 面部重塑-鼻子-鼻梁 */
    const val PATH_F_NOSE_BRIDGE = "f_nose_bridge"

    /** 面部重塑-鼻子-鼻尖 */
    const val PATH_F_NOSE_TIP = "f_nose_tip"

    /** 面部重塑-鼻子-山根 */
    const val PATH_F_NOSE_ROOT = "f_nose_root"


    /** 面部重塑-嘴巴 */
    const val PATH_F_LIPS = "f_lips"

    /** 面部重塑-嘴巴-大小 */
    const val PATH_F_LIPS_SIZE = "f_lips_size"

    /** 面部重塑-嘴巴-微笑 */
    const val PATH_F_LIPS_SMILE = "f_lips_smile"

    /** 面部重塑-嘴巴-高度 */
    const val PATH_F_LIPS_HEIGHT = "f_lips_vertical"

    /** 面部重塑-嘴巴-丰唇 */
    const val PATH_F_LIPS_AUGMENTATION = "f_lips_augmentation"

    /** 面部重塑-嘴巴-宽度 */
    const val PATH_F_LIPS_WIDTH = "f_lips_width"

    /** 面部重塑-嘴巴-M唇 */
    const val PATH_F_LIPS_M_SHAPE = "f_lips_m_shape"


    /** 面部重塑-智能脸型 */
    const val PATH_F_FACE_AUTO = "f_face_auto"

    /** 面部重塑-智能脸型-无 */
    const val PATH_F_FACE_AUTO_NONE = "f_face_auto_none"

    /** 面部重塑-智能脸型-原生脸 */
    const val PATH_F_FACE_BASIC = "f_face_basic"

    /** 面部重塑-智能脸型-清瘦脸 */
    const val PATH_F_FACE_PETITE = "f_face_petite"

    /** 面部重塑-智能脸型-幼短脸 */
    const val PATH_F_FACE_DELICATE = "f_face_delicate"

    /** 面部重塑-智能脸型-圆润脸 */
    const val PATH_F_FACE_ROUND = "f_face_round"

    /** 面部重塑-智能脸型-棱角脸 */
    const val PATH_F_FACE_DIAMOND = "f_face_diamond"


    /** 面部重塑-智能鼻型 */
    const val PATH_F_NOSE_AUTO = "f_nose_auto"

    /** 面部重塑-智能鼻型-无 */
    const val PATH_F_NOSE_AUTO_NONE = "f_nose_auto_none"

    /** 面部重塑-智能鼻型-直鼻 */
    const val PATH_F_NOSE_STRAIGHT = "f_nose_straight"

    /** 面部重塑-智能鼻型-小翘鼻 */
    const val PATH_F_NOSE_TURN_UP = "f_nose_turn_up"

    /** 面部重塑-智能鼻型-驼峰鼻 */
    const val PATH_F_NOSE_HUMP = "f_nose_hump"

    /** ************************* 面部重塑 end ************************* */


    const val PATH_F_REMOVER = "f_remover"

    /**
     * AI消除
     */
    const val f_remover_ai = "f_remover_ai"

    const val f_remover_auto = "f_remover_auto"

    const val f_remover_auto_passersby = "f_remover_auto_passersby"

    const val f_remover_auto_text = "f_remover_auto_text"

    const val f_remover_auto_watermark = "f_remover_auto_watermark"

    const val f_remover_auto_creases = "f_remover_auto_creases"

    /**
     * 抠图
     */
    const val PATH_F_CUTOUT = "f_cutout"

    /**
     * AR特效
     */
    const val PATH_F_AR_EFFECTS = "f_ar_effects"

    /**
     * 修图Tab:编辑。
     */
    const val PATH_T_EDIT = "t_edit"

    /**
     * 修图Tab:美颜。
     */
    const val PATH_T_RETOUCH = "t_retouch"

    /**
     * 修图Tab:配方。
     */
    const val PATH_T_FORMULA = "t_template"

    /**
     * 修图Tab:创意。
     */
    const val PATH_T_CREATIVE = "t_creative"

    /**
     * 修图Tab:滤镜。
     */
    const val PATH_T_FILTER = "t_filter"

    /***************************************** 协议参数 ****************************************/

    /** 模特图类型key */
    const val KEY_PARAM_PHOTO_TYPE = "photo_type"

    /** 模特图URL key */
    const val KEY_PARAM_PHOTO_URL = "photo_url"

    /** 内容key */
    const val KEY_PARAM_CONTENT = "content"

    /** URL key */
    const val KEY_PARAM_URL = "url"

    /** 字体Key */
    const val KEY_PARAM_FONT = "font"

    /** 强度key */
    const val KEY_PARAM_STRENGTH = "strength"

    /** 笔触大小key */
    const val KEY_PARAM_SIZE = "size"

    /** 统计ID */
    const val KEY_PARAM_CAMPAIGN_ID = "campaign_id"

    /** 颜色 */
    const val KEY_PARAM_COLOR = "color"

    /** 美妆右侧栏样式ID */
    const val KEY_PARAM_STYLE = "style"

    /** 分类 */
    const val KEY_PARAM_CATEGORY = "category"

    /**
     * 有值  搜索贴纸跳转的情况
     * */
    const val FROM_SEARCH = "from_search"

    /** 类型 */
    const val KEY_PARAM_TYPE = "type"


    /** 场景 */
    const val KEY_SCENE = "scene"

    /** 画质修复 - 场景 - 进入 */
    const val KEY_ENTER = "enter"

    /** 相册入口 */
    const val KEY_ALBUM_INSTANT = "album_instant"

    /** 判断订阅页样式 */
    const val KEY_P_SUB_STYLE = "p_sub_style"

    /***************************************** 协议参数类型说明 ****************************************/

    /** photo_type：本地默认模特 */
    const val PHOTO_TYPE_LOCAL = "photo_local"

    /** photo_type：网络图 */
    const val PHOTO_TYPE_URL = "photo_url"

    /** photo_type：用户图片 */
    const val PHOTO_TYPE_USER = "photo_user"

    /** camera效果类型：ar*/
    const val VALUE_AR = "ar"

    /** camera效果类型：ar搜索*/
    const val VALUE_AR_SEARCH = "arSearch"

    /** camera效果类型：蒙太奇*/
    const val VALUE_ANIME = "f_anime"

    /** camera效果类型：giphy*/
    const val VALUE_GIPHY = "giphy"

    /** camera效果类型：滤镜*/
    const val VALUE_FILTER = "filter"

    /** camera效果类型：美颜*/
    const val VALUE_FACIAL = "facial"

    /** camera效果类型：美妆*/
    const val VALUE_MAKEUP = "makeup"

    /** camera效果类型：高级美颜*/
    const val VALUE_ADVANCE = "advance"

    /** camera效果类型：Look*/
    const val VALUE_LOOK = "look"

    /** 订阅页类型：正常*/
    const val VALUE_NORMAL = "normal"

    /** 订阅页类型：对比图 */
    const val VALUE_COMPARE = "compare"


    /**
     *   视频部分
     */

    /** 贴纸 */
    const val PATH_F_SPECIAL_EFFECT = "f_special_effects"

    /** 音乐 */
    const val PATH_F_MUSIC = "f_music"

    /** 音乐 */
    const val PATH_F_SOUND_EFFECT = "f_sound_effect"

}