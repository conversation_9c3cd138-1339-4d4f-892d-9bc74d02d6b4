package com.commsource.beautyplus.setting.language

import android.app.Application
import android.content.res.Resources
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.commsource.beautyplus.R
import com.commsource.util.ThreadExecutor
import java.util.*

/**
 * @Desc : 多语言Viewmodel
 * <AUTHOR> meitu - 5/6/21
 */
class LanguageViewModel(application: Application) : AndroidViewModel(application) {

    val languages = arrayListOf(
        LanguageMaterial(R.string.t_language_en, Locale("en")),//e
        LanguageMaterial(R.string.t_language_es, Locale("es")),//e
        LanguageMaterial(R.string.t_language_in, Locale("in")),//i
        LanguageMaterial(R.string.t_language_ja, Locale("ja")),//j
        LanguageMaterial(R.string.t_language_ko, Locale("ko")),//k
        LanguageMaterial(R.string.t_language_pt, Locale("pt")),//p
        LanguageMaterial(R.string.t_language_ru, Locale("ru")),//r
        LanguageMaterial(R.string.t_language_th, Locale("th")),//t
        LanguageMaterial(R.string.t_language_tr, Locale("tr")),//t
        LanguageMaterial(R.string.t_language_vi, Locale("vi")),//v
        LanguageMaterial(R.string.t_language_ar, Locale("ar")),//a
        LanguageMaterial(R.string.v77100_B_1, Locale("fr")),//a
        LanguageMaterial(R.string.v77100_B_2, Locale("de")),//a
        LanguageMaterial(R.string.v77100_B_3, Locale("it")),//a
        LanguageMaterial(R.string.t_language_zh, Locale.SIMPLIFIED_CHINESE),
        LanguageMaterial(R.string.t_language_tw, Locale.TRADITIONAL_CHINESE)
    )

    val languageMap = HashMap<String, Resources>()

    val dataEvent = MutableLiveData<List<LanguageMaterial>>()

    val language = LanguageConfig.getLanguage()

    init {
        ThreadExecutor.executeFastTask("buildLanguageResources") {
            for (mateiral in languages) {
                languageMap[mateiral.locale.toLanguageTag()] = LanguageConfig.getResourceByLocale(mateiral.locale)
            }
            dataEvent.postValue(languages)
        }
    }

    fun getStringByLocale(resId: Int, locale: Locale): String {
        return languageMap[locale.toLanguageTag()]?.getString(resId) ?: ""
    }

    fun isSame(language: String): Boolean {
        val language1 = LanguageConfig.getLanguage()
        return Objects.equals(language1, language)
    }

}