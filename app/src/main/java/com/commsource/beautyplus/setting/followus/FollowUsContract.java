package com.commsource.beautyplus.setting.followus;

import android.content.Intent;

import com.commsource.BasePresenter;
import com.commsource.BaseView;

/*********************************************
 * 关注我们逻辑与视图联系类
 *
 * Author: lbh 2016/12/14
 * ********************************************
 * Version: 1.0.0
 * Author: lbh
 * Changes: 创建该类
 * <AUTHOR>
 * ********************************************
 */
public class FollowUsContract {

    /**
     * 关注我们模块通知视图接口
     */
    public interface View extends BaseView {
        /**
         * 网络异常
         */
        void onNetworkError();

        /**
         * 平台未安装
         * @param webUrl
         */
        void onPlatformNotInstalled(String webUrl);

        /**
         * 显示确认跳转对话框
         * @param message 文案
         */
        void onShowOpenDialog(String message);

        /**
         * 直接打开平台
         * @param intent 打开平台intent
         * @param webUrl 跳转平台web地址
         */
        void onStartPlatform(Intent intent, String webUrl);
    }

    /**
     * 关注我们逻辑相关操作接口
     */
    interface Presenter extends BasePresenter {
        /**
         * 通过fb关注
         */
        void followFaceBook();

        /**
         * 通过twitter关注
         */
        void followTwitter();

        /**
         * 通过instagram关注
         */
        void followInstagram();

        /**
         * 关注tiktok
         */
        void followTikTok();

        /**
         * 通过line关注统计事件
         */
        void logFollowLineEvent();

        /**
         * 处理确认打开弹窗按钮事件
         */
        void handleFollowOpenClick();
    }
}