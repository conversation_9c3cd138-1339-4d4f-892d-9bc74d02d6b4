package com.commsource.beautyplus;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.widget.PopupWindow;

import com.adjust.sdk.Adjust;
import com.adjust.sdk.AdjustDeeplink;
import com.commsource.beautyplus.start.StartUpAdvertActivity;
import com.commsource.beautyplus.web.WebConstant;
import com.commsource.config.ApplicationConfig;
import com.commsource.homev3.HomeRouter;
import com.commsource.push.NotificationBarPush;
import com.commsource.push.NotificationBroadcastReceiver;
import com.commsource.statistics.AdjustComponent;
import com.commsource.statistics.MTAnalyticsAgent;
import com.commsource.statistics.MTFirebaseAnalyticsAgent;
import com.commsource.statistics.constant.MTAnalyticsConstant;
import com.commsource.statistics.constant.MTFirebaseConstant;
import com.commsource.studio.PopupWindowExtKt;
import com.commsource.util.AppTools;
import com.commsource.util.LanguageUtil;
import com.meitu.common.AppContext;
import com.meitu.library.util.Debug.Debug;

import java.util.HashMap;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/**
 * 接收ACTION_VIEW为beautyplus://的Activity，如机外推送点击通知栏。
 * Created by lhy on 2015/11/18.
 *
 * <AUTHOR>
 */
public class PushSchemeActivity extends BaseActivity {

    private boolean firstResume = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);

        setIntent(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d("yyp", ">>>onResume>>>>");
        if (firstResume) {
            processPush(getIntent());
            firstResume = false;
        }
    }

    /**
     * 接收处理推送
     *
     * @param intent
     */
    private void processPush(Intent intent) {
        if (intent == null) {
            return;
        }
        // Adjust 再归因设置
        Uri uri = intent.getData();
        if (uri != null && uri.toString().contains(WebConstant.KEY_ADJUST_DEEPLINK)
                && AdjustComponent.INSTANCE.isAdjustSDKInit()) {
            Adjust.processDeeplink(new AdjustDeeplink(uri), AppContext.context);
        }

        if (uri != null && uri.toString().contains(WebConstant.KEY_ADJUST_DEEPLINK)) {
            String source = uri.getQueryParameter("adj_t");
            if (source == null) {
                source = "";
            }
            MTAnalyticsAgent.logEvent("link_app_start", "onelink_source", source);
        }

        NotificationBarPush notificationBarPush = null;
        try {
            notificationBarPush =
                    (NotificationBarPush) intent.getSerializableExtra(NotificationBroadcastReceiver.PUSH_NOTIFICATION);
        } catch (Throwable throwable) {
            Debug.w(throwable);
        }
        if (notificationBarPush != null) {
            if (notificationBarPush.getPushType() == NotificationBarPush.GCM_PUSH) {
                // 统计推送点击
                Debug.v("MLog", "onClick:" + notificationBarPush.getTaskId() + "," + notificationBarPush.getTitle());
                statisticsPushEnter(this, notificationBarPush.getTaskId());
            }
            // 如果是意见反馈，直接回到意见反馈页
            if (ApplicationConfig.hasFeedbackEdit(this) && notificationBarPush.getUri() != null
                    && notificationBarPush.getUri().startsWith(WebConstant.URL_PROTOCOL_FEEDBACK)) {
                finish();
            }
        }

        // 判断假如有图片和视频编辑先弹窗提示
        int editMode = ApplicationConfig.hasImageEdit(this);
        if (editMode != 0 || ApplicationConfig.hasFeedbackEdit(this)) {

            getWindow().getDecorView().post(new Runnable() {
                @Override
                public void run() {
                    PopupWindowExtKt.showExitEditConfirm(getWindow().getDecorView(),
                            false,
                            null,
                            new Function1<PopupWindow, Unit>() {
                                @Override
                                public Unit invoke(PopupWindow popupWindow) {
                                    finish();
                                    return null;
                                }
                            },
                            new Function1<PopupWindow, Unit>() {
                                @Override
                                public Unit invoke(PopupWindow popupWindow) {
                                    popupWindow.dismiss();
                                    if (ApplicationConfig.hasImageEdit(PushSchemeActivity.this) != 0) {
                                        ApplicationConfig.setHasImageEdit(PushSchemeActivity.this, 0);
                                    }

                                    if (ApplicationConfig.hasFeedbackEdit(PushSchemeActivity.this)) {
                                        ApplicationConfig.setHasFeedbackEdit(PushSchemeActivity.this, false);
                                    }
                                    jumpToHome(intent);
                                    return null;
                                }
                            },
                            new Function0<Unit>() {
                                @Override
                                public Unit invoke() {
                                    if (!AppTools.isFinishing(PushSchemeActivity.this)) {
                                        finish();
                                    }
                                    return null;
                                }
                            }

                    );
                }
            });
        } else {
            jumpToHome(intent);
        }
    }

    /**
     * 始终从首页处理push
     */
    private void jumpToHome(Intent it) {
        Intent intent = new Intent(this, StartUpAdvertActivity.class);
        // 注意本行的FLAG设置
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        NotificationBarPush notificationBarPush = null;
        try {
            notificationBarPush =
                    (NotificationBarPush) it.getSerializableExtra(NotificationBroadcastReceiver.PUSH_NOTIFICATION);
        } catch (Throwable throwable) {
            Debug.w(throwable);
        }
        if (notificationBarPush == null) {
            if (!TextUtils.isEmpty(it.getDataString())
                    && it.getDataString().startsWith(WebConstant.URL_PROTOCOL_BEAUTYPLUS)) {
                notificationBarPush = new NotificationBarPush();
                notificationBarPush.setUri(it.getDataString());
                Uri uri = it.getData();
                if (uri != null) {
                    try {
                        notificationBarPush.setUrl(uri.getQueryParameter("url"));
                    } catch (Exception e) {
                        Debug.w(e);
                    }
                }
            }
        }
        logFBAdvertiseId(it.getDataString());
        intent.putExtra(HomeRouter.HOME_EXTRA_SCHEME, notificationBarPush);
        startActivity(intent);
        finish();
    }

    // 统计FB的广告ID
    private void logFBAdvertiseId(String dataString) {
        if (TextUtils.isEmpty(dataString)) {
            return;
        }
        try {
            Uri uri = Uri.parse(dataString);
            String advertiseId = uri.getQueryParameter("ad_id");
            if (!TextUtils.isEmpty(advertiseId)) {
                MTAnalyticsAgent.logEvent(MTAnalyticsConstant.DEEPLINK_AD_ID, "ad_id", advertiseId);
            }
        } catch (Throwable throwable) {
            Debug.d(throwable);
        }
    }

    /**
     * 统计机外push进入
     *
     * @param context
     * @param id
     */
    private static void statisticsPushEnter(final Context context, String id) {
        HashMap<String, String> hashMap = new HashMap<>(4);
        hashMap.put("event_key", "进入量");
        hashMap.put("event_value", id);
        hashMap.put("language", LanguageUtil.getLanguage(context));
        MTFirebaseAnalyticsAgent.logEvent(context, MTFirebaseConstant.EVENT_OUTPUSHENTER, hashMap);
    }

    @Override
    protected void addSpm() {
        // 屏蔽SPM,不删除此处代码
    }

    @Override
    protected void popSpm() {
        // 屏蔽SPM,不删除此处代码
    }
}
