package com.commsource.home.subscribe_right

import android.content.Context
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.databinding.DataBindingUtil
import com.bumptech.glide.Glide
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemSubscribeRightsContentBinding
import com.commsource.util.dp
import com.commsource.util.resize
import com.commsource.util.setMarginStart
import com.commsource.util.setSize
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

class ContentViewHolder(context: Context, viewGroup: ViewGroup) :
    BaseViewHolder<SubRightsMaterialsFragment.MaterialsBean>(
        context, viewGroup, R.layout.item_subscribe_rights_content
    ) {

    internal val binding: ItemSubscribeRightsContentBinding by lazy {
        DataBindingUtil.bind<ItemSubscribeRightsContentBinding>(
            itemView
        ) as ItemSubscribeRightsContentBinding
    }

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<SubRightsMaterialsFragment.MaterialsBean>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)

        val material = item.entity.materials

        if (position == 0) {
            binding.cvItem.setMarginStart(24.dp)
        }

        binding.ivMaterial.viewTreeObserver.addOnGlobalLayoutListener(object :
            OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                // 宽高1：1
                var ratio = 1f
                if (material.width != 0 && material.height != 0) {
                    ratio = material.height / material.width.toFloat()
                }
                if (ratio == 1f) {
                    binding.ivMaterial.setSize(width = binding.ivMaterial.height)
                } else {
                    binding.ivMaterial.setSize(width = (binding.ivMaterial.height / ratio).toInt())
                }

                binding.ivMaterial.viewTreeObserver.removeOnGlobalLayoutListener(this)
            }
        })

        val url =
            material.url.resize(heightSize = item.entity.rvHeight * 2, widthHeightRatio = 1f)
        Glide.with(mContext)
            .load(url)
            .override(binding.ivMaterial.layoutParams.width, binding.ivMaterial.height)
            .dontTransform()
            .placeholder(R.drawable.material_loading_placeholder)
            .error(R.drawable.material_loading_placeholder)
            .into(binding.ivMaterial)
    }
}