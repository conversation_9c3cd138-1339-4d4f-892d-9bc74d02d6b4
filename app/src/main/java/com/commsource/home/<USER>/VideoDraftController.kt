package com.commsource.home.homepagedialog

import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.R
import com.commsource.studio.StudioConfig
import com.commsource.util.AppTools
import com.commsource.util.ResourcesUtils
import com.commsource.videostudio.VideoStudioActivity
import com.commsource.videostudio.VideoStudioEnterSource
import com.commsource.videostudio.draft.VideoDraftManger
import com.commsource.widget.dialog.delegate.*

class VideoDraftController(val context: BaseActivity) : DialogController() {

    private var dialog: XDialog? = null

    override fun isCanShow(): Boolean {
        if (StudioConfig.isNeedDeleteVideoDraft) {
            VideoDraftManger.clearDrat()
            StudioConfig.isNeedDeleteVideoDraft = false
            return false
        }
        return VideoDraftManger.isExistVideoDraft()
    }

    override fun showTheDialog() {
        if (!AppTools.isFinishing(context)) {
            if (dialog == null) {
                dialog = buildDraftDialog()
            }
            dialog?.takeIf { !it.isAdded && !it.isVisible && !it.isStateSaved }?.show(context)
        }
    }

    override fun isShowing(): Boolean {
        return dialog?.isVisible == true || dialog?.dialog?.isShowing == true
    }

    override fun getControllerType(): Int {
        return ControllerType.VIDEO_DRAFT_DIALOG_TYPE
    }

    private fun buildDraftDialog(): XDialog {
        return XDialog {
            VideoPictureTips {
                Title(ResourcesUtils.getString(R.string.feed_back_prompt_save_video))
                PositiveButton(ResourcesUtils.getString(R.string.t_continue)) {
                    it.dismissAllowingStateLoss()
                    VideoStudioActivity.start(
                        <EMAIL>,
                        VideoStudioEnterSource.VideoSource.DRAFT
                    )
                }
                NegativeButton(ResourcesUtils.getString(R.string.cancel)) {
                    it.dismissAllowingStateLoss()
                    // 删除草稿文件。
                    VideoDraftManger.clearDrat(true)
                    // 继续展示其他弹窗
                    getChain().pickNextToShow(getControllerType())
                }
                closeEnable = false
                cancelOutside = false
                cancelAble = false
                popupCenter()
            }
        }
    }
}