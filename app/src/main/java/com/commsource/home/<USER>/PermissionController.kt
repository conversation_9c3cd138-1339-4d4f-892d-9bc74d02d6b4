package com.commsource.home.homepagedialog

import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.R
import com.commsource.beautyplus.permission.PermissionAlertDialog
import com.commsource.beautyplus.permission.PermissionBean
import com.commsource.config.ApplicationConfig
import com.commsource.util.AppTools
import com.commsource.util.PermissionUitl
import com.commsource.util.ResourcesUtils
import com.meitu.common.AppContext
import java.util.ArrayList

class PermissionController(val context: BaseActivity) : DialogController() {
    private val permissionAlertDialog = buildDialog()
    override fun isCanShow(): Boolean {
        return ApplicationConfig.isNeedShowPermissionTip(AppContext.context)
                && PermissionUitl.lackPermission()
    }

    override fun getControllerType(): Int {
        return ControllerType.PERMISSION_TYPE
    }

    override fun showTheDialog() {
        if (!AppTools.isFinishing(context)) {
            permissionAlertDialog.show()
        }
    }

    private fun buildDialog(): PermissionAlertDialog {
        val permissionBeanList: MutableList<PermissionBean> = ArrayList()
        permissionBeanList.add(PermissionBean(R.drawable.common_permission_sdcard_icon,
                ResourcesUtils.getString(R.string.permission_item1_title), ResourcesUtils.getString(R.string.permission_item1_msg)))
        permissionBeanList.add(PermissionBean(R.drawable.common_permission_read_icon,
                ResourcesUtils.getString(R.string.permission_item2_title), ResourcesUtils.getString(R.string.permission_item2_msg)))
        permissionBeanList.add(PermissionBean(R.drawable.common_permission_camera_icon,
                ResourcesUtils.getString(R.string.permission_item3_title), ResourcesUtils.getString(R.string.permission_item3_msg)))
        permissionBeanList.add(PermissionBean(R.drawable.common_permission_audio_icon,
                ResourcesUtils.getString(R.string.permission_item4_title), ResourcesUtils.getString(R.string.permission_item4_msg)))
        val permissionBuilder = PermissionAlertDialog.Builder(context)
        permissionBuilder.setTitle(R.string.tips)
        permissionBuilder.setSubTitle(R.string.permission_alert_subtitle)
        permissionBuilder.setCancelableOnTouch(false)
        permissionBuilder.setPermissionList(permissionBeanList)
        permissionBuilder.setCancelable(true)
        permissionBuilder.setOnDismissListener { _ ->
            ApplicationConfig.setPermissionTipInterrupted(AppContext.context, false)
            ApplicationConfig.setNeedShowPermissionTip(AppContext.context, false)
            // 继续展示下一个弹窗
            getChain().pickNextToShow(getControllerType())
        }
        permissionBuilder.setPositiveButton(R.string.permission_btn_text) { dialog, which -> dialog.dismiss() }
        return permissionBuilder.create()
    }

    override fun isShowing(): Boolean {
        return permissionAlertDialog.isShowing
    }
}