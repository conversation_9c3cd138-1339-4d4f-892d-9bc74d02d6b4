package com.commsource.home.tools

import android.content.Context
import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import android.renderscript.RSRuntimeException
import android.util.AttributeSet
import android.view.View
import androidx.annotation.IntRange
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.graphics.toRect
import com.commsource.beautyplus.R
import com.commsource.camera.util.animationTransition
import com.commsource.util.*
import com.meitu.core.processor.BlurProcessor
import com.meitu.library.util.Debug.Debug


/**
 * @Description : 高斯模糊View
 * <AUTHOR> bear
 * @Date : 2022/5/16
 *
 * 提供高斯过渡使用 提供bitmap区域抠图和控制alpha填充色使用
 * 为了Android端实现的高斯效果，优点是是适配程度高，可能有机型问题，但是适配是最广的了，过渡前期有时间差，也是没办法的 看高斯处理的效率
 *
 * 还有gl方案去反射Canvas中方法的方案，太黑科技了。github -> hokoblur
 *
 */
class BlurView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    init {
        scaleType = ScaleType.CENTER_CROP
    }

    /**
     * 目标图片 缓存一份
     */
    var blurBitmap: Bitmap? = null

    /**
     * 画布
     */
    var canvas: Canvas? = null

    // sampling: Int = 10,//采样率 可以控制图片大小
    var sampling: Int = 10

    /**
     * 开始
     */
    fun start(
        rootView: View = this.rootView,//提供bitmapRootView
        cutoutView: View? = null,//抠出区域 这里也可以是一个Rect的区域
        cutoutFillRect: RectF? = null,
        cutoutFillBitmap: Bitmap? = null,
        cutoutFillColor: Int = Color.WHITE,//抠图填充色
        alphaCheck: Boolean = true,//针对透明度处理，主要是gl视图以及视频texture视图的填充
        alphaFillColor: Int = 0xffcccccc.toInt(),//透明填充色
        maskColor: Int = R.color.white80.resColor(),//表面填充色
        @IntRange(from = 0, to = 25) radius: Int = 15,//高斯范围 (这个radius是基于RenderScript的调整范围设定的)
        isRestBlurViewBg: Boolean = false
    ) {
        //读图
        alpha = 0f
        val log = TimeLog.createAndStart()
        if (blurBitmap == null || isRestBlurViewBg) {
            "onConfigurationChanged  width = ${rootView.width}  height = ${rootView.height}".print(debugLevel = Debug.DebugLevel.ERROR)
            var rootViewW = rootView.width
            var rootViewH = rootView.height
            if (rootViewW <= 0 || rootViewH <= 0) {
                rootViewW = 1000
                rootViewH = 1000
            }
            blurBitmap =
                Bitmap.createBitmap(
                    rootViewW / sampling,
                    rootViewH / sampling,
                    Bitmap.Config.ARGB_8888
                )
            canvas = Canvas(blurBitmap!!)
        }
        canvas?.let {
            it.save()
            it.setMatrix(Matrix().apply {
                postScale(1 / sampling.toFloat(), 1 / sampling.toFloat())
                postTranslate(-rootView.scrollX.toFloat(), -rootView.scrollY.toFloat())
            })
            rootView.draw(it)
            it.restore()
        }
        val cutoutRect: Rect? =
            if (cutoutFillRect != null) {
                val matrix = Matrix()
                val scale = 1f / sampling
                matrix.postScale(scale, scale)
                matrix.mapRect(cutoutFillRect)
                cutoutFillRect.toRect()
            } else if (cutoutView != null) {
                val leftTop = IntArray(2)
                cutoutView.getLocationInWindow(leftTop)
                Rect(
                    leftTop[0],
                    leftTop[1],
                    leftTop[0] + cutoutView.measuredWidth,
                    leftTop[1] + cutoutView.measuredHeight
                )
            } else {
                null
            }
        "生成底图耗时:${log.update()}".print("blur")
        //生成高斯图片
        //高斯加载
        ThreadExecutor.executeFastTask("loadBlur") {
            blurBitmap?.let {
                val time = TimeLog.createAndStart()
                val width = it.width
                val height = it.height
                "onConfigurationChanged  bitmap width = $width  height = $height".print(debugLevel = Debug.DebugLevel.ERROR)
                var bitmap: Bitmap =
                    Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
                val canvas = Canvas(bitmap)
                if (alphaCheck) {
                    canvas.drawColor(alphaFillColor)
                }
                canvas.drawBitmap(it, 0f, 0f, Paint().apply {
                    flags = Paint.FILTER_BITMAP_FLAG
                })
                cutoutRect?.let {
                    if (cutoutFillBitmap != null) {
                        "onConfigurationChanged  cutoutFillBitmap  bitmap width = ${cutoutFillBitmap.width}  height = ${cutoutFillBitmap.height}".print(debugLevel = Debug.DebugLevel.ERROR)
                        canvas.drawBitmap(
                            cutoutFillBitmap,
                            Rect(0, 0, cutoutFillBitmap.width, cutoutFillBitmap.height),
                            it,
                            null
                        )
                    } else {
                        canvas.drawRect(it, Paint().apply {
                            color = cutoutFillColor
                        })
                    }
                }
                canvas.drawColor(maskColor)
                try {
                    BlurProcessor.stackBlur_bitmap(bitmap, 90)
                } catch (e: RSRuntimeException) {
                    e.printStackTrace()
                }
                UIHelper.runOnUiThread {
                    if (AppTools.isFinishing(context) || !isAttachedToWindow) {
                        return@runOnUiThread
                    }
                    setImageDrawable(BitmapDrawable(bitmap))
                    "onConfigurationChanged  setImageDrawable bitmap width = ${bitmap.width}  height = ${bitmap.height}".print(debugLevel = Debug.DebugLevel.ERROR)
                    "渲染${bitmap.width}x${bitmap.height}高斯渲染耗时:${time.update()} ".print("blur")
                    animationTransition(duration = 250) {
                        alpha = 1f
                    }
                }
            }

        }
//        GlideApp.with(context)
//            .load(blurBitmap)
//            .skipMemoryCache(true)//内存不缓存此图
//            .priority(Priority.IMMEDIATE)
//            .transform(
//                //高斯变换
//                BlurTransformation(
//                    context,
//                    sampling = 1,//内部不要再压缩了 已经是小图了
//                    radius = radius,//高斯模糊程度
//                    cutoutRect,//裁剪区域
//                    cutoutFillColor,//裁剪区域颜色
//                    alphaCheck,//透明检测
//                    alphaFillColor//透明填充颜色
//                )
//            )
//            .listener(object : RequestListener<Drawable> {
//                override fun onLoadFailed(
//                    e: GlideException?,
//                    model: Any?,
//                    target: com.bumptech.glide.request.target.Target<Drawable>?,
//                    isFirstResource: Boolean
//                ): Boolean {
//                    //暂时不管 内部无特殊api 几乎无此问题
//                    return false
//                }
//
//                override fun onResourceReady(
//                    resource: Drawable?,
//                    model: Any?,
//                    target: com.bumptech.glide.request.target.Target<Drawable>?,
//                    dataSource: DataSource?,
//                    isFirstResource: Boolean
//                ): Boolean {
//                    //图片加载好了 开启背景透明过渡 利用alpha做假动态高斯
//                    animationTransition(duration = 250) {
//                        alpha = 1f
//                    }
//                    return false
//                }
//            })
//            .into(this)
    }

    /**
     * 清理
     */
    fun clear() {
        animationTransition {
            alpha = 0f
        }
    }

}