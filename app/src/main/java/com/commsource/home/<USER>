package com.commsource.home

import androidx.lifecycle.LifecycleOwner
import com.commsource.beautyplus.BuildConfig
import com.commsource.beautyplus.util.JsonUtil
import com.commsource.config.CacheConfig
import com.commsource.config.GlobalSearchCache
import com.commsource.home.entity.SearchFunJsonEntity
import com.commsource.homev2.MiniAppDataRepo
import com.commsource.search_common.SearchApi
import com.commsource.search_common.entity.CAMERA
import com.commsource.search_common.entity.FunEntity
import com.commsource.search_common.entity.MiniAppSearchEntity
import com.commsource.search_common.entity.OTHER
import com.commsource.search_common.entity.PHOTO_EDIT
import com.commsource.search_common.entity.SearchTagEntity
import com.commsource.search_common.entity.VIDEO
import com.commsource.search_common.repo.KeyWordRepo
import com.commsource.util.LOGV
import com.commsource.util.LanguageUtil
import com.commsource.util.NetworkMonitor
import com.commsource.util.V
import com.commsource.util.coroutine.CloseableCoroutineScope
import com.commsource.util.getEnStr
import com.commsource.util.getLanguage
import com.commsource.util.getStr
import com.meitu.common.AppContext
import com.meitu.http.kotex.api
import com.meitu.http.kotex.response
import com.meitu.room.database.DBHelper
import com.pixocial.androidx.core.extension.isSameDay
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.CopyOnWriteArrayList

object SearchDataTasks {

    val scope = CloseableCoroutineScope(SupervisorJob() + Dispatchers.IO)

    /**
     * 初次获取推荐词等数据  重试的次数
     */
    @Volatile
    var hasRetry = false

    //获取搜索热词 没有失败的返回
    fun initHotWord(
        lifecycle: LifecycleOwner,
        type: String,
        onSuc: () -> Unit
    ) {
        NetworkMonitor.registerNetworkStateListener(lifecycle) {
            if (it) {
                scope.launch {
                    withContext(Dispatchers.IO) {
                        KeyWordRepo.requestData(type, onSucCallBack = onSuc, null)
                    }
                }
            }
        }
    }

    //获取搜索热词,有失败的回调
    fun initHotWord(
        lifecycle: LifecycleOwner,
        type: String,
        onSuccessCallback: () -> Unit,
        onErrorCallback: () -> Unit
    ) {
        NetworkMonitor.registerNetworkStateListener(lifecycle) {
            if (it) {
                scope.launch {
                    withContext(Dispatchers.IO) {
                        KeyWordRepo.requestData(type, onSuccessCallback, onErrorCallback)
                    }
                }
            }
        }
    }

    suspend fun initSearchTags(way: String, types: Array<String>) {
        //判断一下语言，防止切换语言之后没有重新请求接口，而本地数据库保存的是之前的语言的数据，如果没有请求到目前语言的数据，保底一个英文
        if (CacheConfig.getSearchTagTime(way).isSameDay(System.currentTimeMillis())
            && (CacheConfig.getSearchTagLanguage("${way}_language") == AppContext.context.getLanguage()
                    || CacheConfig.getSearchTagLanguage("${way}_language") == "language_en")//如果是保底的英文，也不请求
        ) {
            "同一天，无需刷新  $way".LOGV()
        } else {
            "不是同一天，刷新推荐词  $way   language = ${CacheConfig.getSearchTagLanguage(way)}}".LOGV()
            withContext(Dispatchers.IO) {
                SearchApi::class.java.api()
                    .searchTagsRequest(types)
                    .responseOnBackground()
                    .response {
                        onNext = {
                            val tags = mutableListOf<SearchTagEntity>()
                            it.data?.forEach { entity ->
                                if (entity.language == AppContext.context.getLanguage()) {
                                    if (entity.tags.isNotEmpty()) {
                                        CacheConfig.setSearchTagLanguage(
                                            "${way}_language",
                                            AppContext.context.getLanguage()
                                        )
                                    }
                                    entity.tags.forEach { value ->
                                        tags.add(SearchTagEntity().apply {
                                            tag = value
                                            tagWay = way
                                        })
                                    }
                                }
                            }

                            //如果没有配置，就用英文兜底
                            if (tags.isNullOrEmpty()) {
                                it.data?.forEach { searchTagNetEntity ->
                                    if (searchTagNetEntity.language == "en" && searchTagNetEntity.tags.isNotEmpty()) {//如果是英文
                                        CacheConfig.setSearchTagLanguage(
                                            "${way}_language",
                                            "language_en"
                                        )
                                        searchTagNetEntity.tags.forEach { value ->
                                            tags.add(SearchTagEntity().apply {
                                                tag = value
                                                tagWay = way
                                            })
                                        }
                                    }
                                }
                            }

                            DBHelper.dataBase.searchTagDao.run {
                                deleteAll(way)
                                insertAll(tags.toTypedArray())
                                CacheConfig.setSearchTagTime(way)
                                "数据个数 ${DBHelper.dataBase.searchTagDao.entityCount()}".LOGV()
                            }
                        }
                        onError = {
                            it.printStackTrace()
                            scope.launch {
                                val hasData = DBHelper.dataBase.searchTagDao.entityCount() > 0
                                "数据个数 ${DBHelper.dataBase.searchTagDao.entityCount()}  是否已经重试 ${hasRetry}".LOGV()
                                if (!hasData && !hasRetry) {
                                    delay(1000 * 10)
                                    hasRetry = true
                                    initSearchTags(way, types)
                                }

                            }
                        }
                    }
            }
        }
    }


    private val allMiniAppTagList = CopyOnWriteArrayList<MiniAppSearchEntity>()

    private val searchMiniAppsMutex = Mutex()

    /**
     * 解析得到mini app 名字和tag搜索的集合
     */
//    @Synchronized 和 suspend不能同时使用，会没有效果，新版本上面会报错
    suspend fun getSearchMiniApps(): MutableList<MiniAppSearchEntity> {
        searchMiniAppsMutex.withLock {
            return withContext(Dispatchers.IO) {
                if (allMiniAppTagList.isNotEmpty()) {
                    allMiniAppTagList
                } else {

                    val nameList = mutableListOf<MiniAppSearchEntity>()
                    val tagList = mutableListOf<MiniAppSearchEntity>()

                    var miniapps = MiniAppDataRepo.loadCacheData()
                    if (miniapps.isNullOrEmpty()) {
                        "miniapp tag使用后台返回数据".LOGV()
                        miniapps = MiniAppDataRepo.loadOnlineMiniAppData()
                    } else {
                        "miniapp tag使用缓存数据".LOGV()
                    }

                    miniapps?.forEach { entity ->
                        entity.navbarTitle?.run {
                            nameList.add(MiniAppSearchEntity(this, null, entity))
                            entity.tags?.forEach {
                                tagList.add(MiniAppSearchEntity(this, it, entity))
                            }
                        }

                    }
                    "=======================================================miniapp name".LOGV()
                    nameList.forEach {
                        allMiniAppTagList.add(it)
                        it.name.LOGV()
                    }
                    "========================================================miniapp tag".LOGV()
                    tagList.forEach {
                        allMiniAppTagList.add(it)
                        "${it.tag} -- ${it.name}".LOGV()
                    }

                    allMiniAppTagList
                }
            }
        }
    }


    private val allTagList = mutableListOf<FunEntity>()

    private val searchFunMutex = Mutex()

    suspend fun getSearchFun(): MutableList<FunEntity> {
        searchFunMutex.withLock {
            return withContext(Dispatchers.IO) {
                if (allTagList.isNotEmpty()) {
                    allTagList
                } else {
                    val editNameList = mutableListOf<FunEntity>()
                    val editTagList = mutableListOf<FunEntity>()

                    val photoNameList = mutableListOf<FunEntity>()
                    val photoTagList = mutableListOf<FunEntity>()

                    val videoNameList = mutableListOf<FunEntity>()
                    val videoTagList = mutableListOf<FunEntity>()

                    val otherNameList = mutableListOf<FunEntity>()
                    val otherTagList = mutableListOf<FunEntity>()

                    var json = GlobalSearchCache.getGlobalSearchJson()
                    if (json != "") {
                        "使用后台返回数据".V()
                        string2Entity(
                            json,
                            editNameList,
                            editTagList,
                            photoNameList,
                            photoTagList,
                            videoNameList,
                            videoTagList,
                            otherNameList,
                            otherTagList,
                        )
                    } else {
                        "使用缓存数据".V()
                        val file = AppContext.context.assets.open("search_fun.json")
                        val jsonTmp = StringBuilder()
                        file.reader().readLines().forEach {
                            jsonTmp.appendLine(it)
                        }
                        json2Entity(
                            jsonTmp.toString(),
                            editNameList,
                            editTagList,
                            photoNameList,
                            photoTagList,
                            videoNameList,
                            videoTagList,
                            otherNameList,
                            otherTagList,
                        )
                    }
                    editNameList.forEach {
                        allTagList.add(it)
                    }
                    editTagList.forEach {
                        allTagList.add(it)
                    }
                    photoNameList.forEach {
                        allTagList.add(it)
                    }
                    photoTagList.forEach {
                        allTagList.add(it)
                    }
                    videoNameList.forEach {
                        allTagList.add(it)
                        it.toString().LOGV()
                    }
                    videoTagList.forEach {
                        allTagList.add(it)
                        it.toString().LOGV()
                    }
                    otherNameList.forEach {
                        allTagList.add(it)
                    }
                    otherTagList.forEach {
                        allTagList.add(it)
                    }
                    allTagList
                }
            }
        }
    }

    private fun string2Entity(
        json: String,
        editNameList: MutableList<FunEntity>,
        editTagList: MutableList<FunEntity>,
        photoNameList: MutableList<FunEntity>,
        photoTagList: MutableList<FunEntity>,
        videoNameList: MutableList<FunEntity>,
        videoTagList: MutableList<FunEntity>,
        otherNameList: MutableList<FunEntity>,
        otherTagList: MutableList<FunEntity>
    ) {
        val jsonArray = JSONArray(JSONArray(json).get(0) as String)
        for (i in 0 until jsonArray.length()) {
            jsonArray.get(i).run {
                if (this is JSONObject) {
                    val version = getString("version")
                    if (BuildConfig.VERSION_CODE < getVersionCode(version)) {
                        "now ${BuildConfig.VERSION_CODE}  json version ${getVersionCode(version)}".LOGV()
                    } else {
                        val id = getInt("id")
                        val deeplink = getString("deeplink")
                        val icon = getString("icon")
                        val tags = getJSONObject("tags")

                        val validTags = getValidTags(tags)
                        val name = getString("name") ?: ""
                        val nameKey = getString("nameKey") ?: ""

                        val nameStr = addNameToTag(
                            nameKey,
                            name,
                            id,
                            deeplink,
                            icon,
                            editNameList,
                            photoNameList,
                            videoNameList,
                            otherNameList
                        )
                        validTags.forEach { tag ->
                            for (j in 0 until tag.length()) {
                                if (id in 1000..1999) {
                                    if (id == 1002 && LanguageUtil.getLanguage(AppContext.context) == LanguageUtil.LANGUAGE_ZH_HANS) {
                                        "大陆地区不显示分身".LOGV()
                                    } else {
                                        editTagList.add(
                                            FunEntity(
                                                deeplink,
                                                nameStr,
                                                tag.get(j).toString(),
                                                PHOTO_EDIT,
                                                id,
                                                icon,
                                            )
                                        )
                                    }

                                } else if (id in 2000..2999) {
                                    photoTagList.add(
                                        FunEntity(
                                            deeplink,
                                            nameStr,
                                            tag.get(j).toString(),
                                            CAMERA,
                                            id,
                                            icon,
                                        )
                                    )

                                } else if (id in 3000..3999) {
                                    videoTagList.add(
                                        FunEntity(
                                            deeplink,
                                            nameStr,
                                            tag.get(j).toString(),
                                            VIDEO,
                                            id,
                                            icon,
                                        )
                                    )

                                } else {
                                    otherTagList.add(
                                        FunEntity(
                                            deeplink,
                                            nameStr,
                                            tag.get(j).toString(),
                                            OTHER,
                                            id,
                                            icon,
                                        )
                                    )
                                }
                            }
                        }
                    }

                }

            }
        }
    }

    private fun addNameToTag(
        nameKey: String, name: String, id: Int, deeplink: String, icon: String?,
        editNameList: MutableList<FunEntity>,
        photoNameList: MutableList<FunEntity>,
        videoNameList: MutableList<FunEntity>,
        otherNameList: MutableList<FunEntity>,
    ):String {
        val nameStr =  try {
            nameKey.getStr()
        }catch (ex:Exception){
            ex.printStackTrace()
            name
        }
        val nameEnStr =  try {
            nameKey.getEnStr()
        }catch (ex:Exception){
            ex.printStackTrace()
            name
        }
        if (id in 1000..1999) {
            if (id == 1002 && LanguageUtil.getLanguage(AppContext.context) == LanguageUtil.LANGUAGE_ZH_HANS) {
                "大陆地区不显示分身".LOGV()
            } else {
                editNameList.add(
                    FunEntity(
                        deeplink,
                        nameStr,
                        nameStr,
                        PHOTO_EDIT,
                        id,
                        icon,
                    )
                )
                editNameList.add(
                    FunEntity(
                        deeplink,
                        nameStr,
                        nameEnStr,
                        PHOTO_EDIT,
                        id,
                        icon,
                    )
                )
            }

        } else if (id in 2000..2999) {
            photoNameList.add(
                FunEntity(
                    deeplink,
                    nameStr,
                    nameStr,
                    CAMERA,
                    id,
                    icon,
                )
            )
            photoNameList.add(
                FunEntity(
                    deeplink,
                    nameStr,
                    nameEnStr,
                    CAMERA,
                    id,
                    icon,
                )
            )
        } else if (id in 3000..3999) {
            videoNameList.add(
                FunEntity(
                    deeplink,
                    nameStr,
                    nameStr,
                    VIDEO,
                    id,
                    icon,
                )
            )
            videoNameList.add(
                FunEntity(
                    deeplink,
                    nameStr,
                    nameEnStr,
                    VIDEO,
                    id,
                    icon,
                )
            )
        } else {
            otherNameList.add(
                FunEntity(
                    deeplink,
                    nameStr,
                    nameStr,
                    OTHER,
                    id,
                    icon,
                )
            )
            otherNameList.add(
                FunEntity(
                    deeplink,
                    nameStr,
                    nameEnStr,
                    OTHER,
                    id,
                    icon,
                )
            )
        }
        return  nameStr
    }


    private fun json2Entity(
        json: String,
        editNameList: MutableList<FunEntity>,
        editTagList: MutableList<FunEntity>,
        photoNameList: MutableList<FunEntity>,
        photoTagList: MutableList<FunEntity>,
        videoNameList: MutableList<FunEntity>,
        videoTagList: MutableList<FunEntity>,
        otherNameList: MutableList<FunEntity>,
        otherTagList: MutableList<FunEntity>
    ) {

        JsonUtil.jsonToList(json, SearchFunJsonEntity::class.java)
            .forEach { searchFunJsonEntity ->
                val nameStr = addNameToTag(
                    searchFunJsonEntity.nameKey,
                    searchFunJsonEntity.name,
                    searchFunJsonEntity.id,
                    searchFunJsonEntity.deeplink,
                    searchFunJsonEntity.icon,
                    editNameList,
                    photoNameList,
                    videoNameList,
                    otherNameList
                )
                searchFunJsonEntity.getValidTags().forEachIndexed { index, tag ->

                    if (BuildConfig.VERSION_CODE < getVersionCode(searchFunJsonEntity.version)) {
                        "now ${BuildConfig.VERSION_CODE}  json version ${
                            getVersionCode(
                                searchFunJsonEntity.version
                            )
                        }".LOGV()
                        return@forEachIndexed
                    }

                    if (searchFunJsonEntity.id in 1000..1999) {
                        if (searchFunJsonEntity.id == 1002 && LanguageUtil.getLanguage(AppContext.context) == LanguageUtil.LANGUAGE_ZH_HANS) {
                            "大陆地区不显示分身".LOGV()
                        } else {
                            if (index == 0) {
                                editNameList.add(
                                    FunEntity(
                                        searchFunJsonEntity.deeplink,
                                        nameStr,
                                        tag,
                                        PHOTO_EDIT,
                                        searchFunJsonEntity.id,
                                        searchFunJsonEntity.icon,
                                    )
                                )
                            } else {
                                editTagList.add(
                                    FunEntity(
                                        searchFunJsonEntity.deeplink,
                                        nameStr,
                                        tag,
                                        PHOTO_EDIT,
                                        searchFunJsonEntity.id,
                                        searchFunJsonEntity.icon,
                                    )
                                )
                            }
                        }

                    } else if (searchFunJsonEntity.id in 2000..2999) {
                        photoTagList.add(
                            FunEntity(
                                searchFunJsonEntity.deeplink,
                                nameStr,
                                tag,
                                CAMERA,
                                searchFunJsonEntity.id,
                                searchFunJsonEntity.icon,
                            )
                        )
                    } else if (searchFunJsonEntity.id in 3000..3999) {
                        videoTagList.add(
                            FunEntity(
                                searchFunJsonEntity.deeplink,
                                nameStr,
                                tag,
                                VIDEO,
                                searchFunJsonEntity.id,
                                searchFunJsonEntity.icon,
                            )
                        )
                    } else {
                        otherTagList.add(
                            FunEntity(
                                searchFunJsonEntity.deeplink,
                                nameStr,
                                tag,
                                OTHER,
                                searchFunJsonEntity.id,
                                searchFunJsonEntity.icon,
                            )
                        )
                    }
                }
            }
    }

    private fun getValidTags(tagObject: JSONObject): List<JSONArray> {
        val lang = LanguageUtil.getLanguage(AppContext.context)
        val oriTagList = mutableListOf(getTag(tagObject, lang))
        // 不是英语的话。把英语的结果也追加进去。
        if (lang != LanguageUtil.LANGUAGE_EN) {
            oriTagList.add(getTag(tagObject, LanguageUtil.LANGUAGE_EN))
        }
        return oriTagList
    }


    private fun getTag(tagObject: JSONObject, lang: String) =
        when (lang) {

            LanguageUtil.LANGUAGE_VI -> {
                tagObject.getJSONArray("vi")
            }

            LanguageUtil.LANGUAGE_ZH_HANS -> {
                tagObject.getJSONArray("zh-Hans")
            }

            LanguageUtil.LANGUAGE_ZH_HANT -> {
                tagObject.getJSONArray("zh-Hant")
            }


            LanguageUtil.LANGUAGE_EN -> {
                tagObject.getJSONArray("en")
            }

            LanguageUtil.LANGUAGE_JP -> {
                tagObject.getJSONArray("ja")
            }

            LanguageUtil.LANGUAGE_KOR -> {
                tagObject.getJSONArray("ko")
            }

            LanguageUtil.LANGUAGE_TH -> {
                tagObject.getJSONArray("th")
            }

            LanguageUtil.LANGUAGE_ID -> {
                tagObject.getJSONArray("id")
            }


            LanguageUtil.LANGUAGE_ES -> {
                tagObject.getJSONArray("es")
            }


            LanguageUtil.LANGUAGE_PT -> {
                tagObject.getJSONArray("pt")
            }

            LanguageUtil.LANGUAGE_TR -> {
                tagObject.getJSONArray("tr")
            }

            LanguageUtil.LANGUAGE_RU -> {
                tagObject.getJSONArray("ru")
            }

            LanguageUtil.LANGUAGE_AR -> {
                tagObject.getJSONArray("ar")
            }

            LanguageUtil.LANGUAGE_FR -> {
                tagObject.getJSONArray("fr")
            }

            LanguageUtil.LANGUAGE_DE -> {
                tagObject.getJSONArray("de")
            }

            LanguageUtil.LANGUAGE_IT -> {
                tagObject.getJSONArray("it")
            }

            else -> {
                tagObject.getJSONArray("en")
            }
        }

    private fun getVersionCode(version: String) = try {
        version.replace(".", "").toInt()
    } catch (ex: Exception) {
        ex.printStackTrace()
        Int.MAX_VALUE
    }

    fun onActivityDestroy() {
        scope.close()
    }
}