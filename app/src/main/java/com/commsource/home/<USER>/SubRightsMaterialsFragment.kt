package com.commsource.home.subscribe_right

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.databinding.FragmentSubscribeRightsBinding
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.beautyplus.router.Router
import com.commsource.home.entity.PopupConfig
import com.commsource.home.subscribe_right.SubscribeRightsDialog.Companion.TypeId_FuncName_Map
import com.commsource.homev2.HomeContentViewModel
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.LOGV_Pro
import com.commsource.util.dp
import com.commsource.util.set
import com.commsource.util.setRTL
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter

/**
 * 显示某个给定素材类型[materialType]下的素材集合。
 */
class SubRightsMaterialsFragment : BaseFragment() {
    companion object {
        // 素材类型
        private const val MATERIAL_TYPE_ID = "material_type_id"
        fun newInstance(materialTypeId: String) = SubRightsMaterialsFragment().apply {
            val bundle = Bundle()
            bundle.putString(MATERIAL_TYPE_ID, materialTypeId)
            arguments = bundle
        }

        // 素材列表正在滚动
        val isRvMaterialsScrollingEvent = NoStickLiveData<Boolean>()

        val closeDialogEvent = NoStickLiveData<Boolean>()
    }

    private val homeContentViewModel by lazy { ViewModelProvider(requireActivity())[HomeContentViewModel::class.java] }

    private val contentAdapter by lazy { BaseRecyclerViewAdapter(requireContext()) }

    private val binding by lazy {
        FragmentSubscribeRightsBinding.inflate(layoutInflater, null, false)
    }

    private var materialTypeId: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        materialTypeId = arguments?.getString(MATERIAL_TYPE_ID)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ) = binding.root

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.rvContent.adapter = contentAdapter
        binding.rvContent.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        binding.rvContent.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                isRvMaterialsScrollingEvent.value = newState == RecyclerView.SCROLL_STATE_DRAGGING
            }
        })
        binding.rvContent.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
            ) {
                val position = parent.getChildAdapterPosition(view)
                if (position == RecyclerView.NO_POSITION) return

                val itemCount = parent.adapter?.itemCount ?: 0
                // 设置 marginEnd
                outRect.setRTL(0, 0, if (position == itemCount - 1) 24.dp else 10.dp, 0)
            }
        })

        contentAdapter.setOnEntityClickListener(
            MaterialsBean::class.java
        ) { position, entity ->
            val deeplink = entity.materials.deeplink
            val id = entity.materials.id
            if (deeplink != null && id != null) {
                val link = "$deeplink$id"
                "deeplink $link".LOGV_Pro()
                Router.startRouter(requireActivity(), link)
            }
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.premium_pop_func_try,
                "func",
                TypeId_FuncName_Map[materialTypeId]
            )

            closeDialogEvent.set(true)
            true
        }

        homeContentViewModel.materialConfig.observe(viewLifecycleOwner) { materialConfig ->
            materialConfig.find { it.typeId == materialTypeId }.let {
                contentAdapter.clearItems()

                binding.rvContent.post {
                    val data = mutableListOf<MaterialsBean>()
                    it?.materials?.forEach { material ->
                        material?.let {
                            data.add(MaterialsBean(materialTypeId, binding.rvContent.height, it))
                        }
                    }
                    contentAdapter.updateItemEntities(
                        AdapterDataBuilder.create()
                            .addEntities(data, ContentViewHolder::class.java)
                            .build()
                    )
                }
            }
        }
    }

    data class MaterialsBean(
        val materialTypeId: String?,
        val rvHeight: Int,
        val materials: PopupConfig.MaterialConfig.Materials
    )
}