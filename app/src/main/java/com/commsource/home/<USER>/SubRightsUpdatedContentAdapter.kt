package com.commsource.home.subscribe_right

import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.commsource.home.entity.PopupConfig

class SubRightsUpdatedContentAdapter(
    activity: FragmentActivity,
    private val materialConfigs: List<PopupConfig.MaterialConfig>
) :
    FragmentStateAdapter(activity) {

    override fun getItemCount() = materialConfigs.size

    override fun createFragment(position: Int) =
        // typeId 不应该为空或者null
        SubRightsMaterialsFragment.newInstance(materialConfigs[position].typeId ?: "")
}