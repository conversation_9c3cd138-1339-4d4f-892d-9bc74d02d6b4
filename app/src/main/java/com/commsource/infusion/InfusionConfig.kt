package com.commsource.infusion

import android.text.TextUtils
import com.commsource.beautyplus.R
import com.commsource.beautyplus.setting.language.LanguageConfig
import com.commsource.util.BPLocationUtils
import com.commsource.util.LanguageUtil
import com.meitu.common.AppContext
import java.util.Locale

/**
 * 导流配置
 *
 * 导流位置banner资源按需获取
 */
object InfusionConfig {

    private fun getCurrentLanguageTag(): String {
        var languageTag = LanguageConfig.getLanguage()
        if (TextUtils.isEmpty(languageTag)) {
            languageTag = Locale.getDefault().toLanguageTag() ?: ""
        }
        return languageTag
    }

    /**
     * 获取视频拍后确认页面物料
     */
    fun getVideoConfirmBanner(): Int {
        val languageTag = getCurrentLanguageTag()
        return when {
            Locale.SIMPLIFIED_CHINESE.toLanguageTag()
                .equals(languageTag) -> R.drawable.ic_infusion_video_confirm_cn

            Locale("ja").toLanguageTag()
                .equals(languageTag) -> R.drawable.ic_infusion_video_confirm_jp

            Locale("th").toLanguageTag()
                .equals(languageTag) -> R.drawable.ic_infusion_video_confirm_th

            Locale("ko").toLanguageTag()
                .equals(languageTag) -> R.drawable.ic_infusion_video_confirm_kr

            Locale("in").toLanguageTag().equals(languageTag)
                    || Locale("vi").toLanguageTag()
                .equals(languageTag) -> R.drawable.ic_infusion_video_confirm_asia

            LanguageUtil.isChinese(AppContext.context) -> R.drawable.ic_infusion_video_confirm_cn
            LanguageUtil.isJapanese(AppContext.context) -> R.drawable.ic_infusion_video_confirm_jp
            LanguageUtil.isTh(AppContext.context) -> R.drawable.ic_infusion_video_confirm_th
            LanguageUtil.isKorean(AppContext.context) -> R.drawable.ic_infusion_video_confirm_kr
            BPLocationUtils.isAsiaCountry() -> R.drawable.ic_infusion_video_confirm_asia
            else -> R.drawable.ic_infusion_video_confirm_en
        }
    }

}