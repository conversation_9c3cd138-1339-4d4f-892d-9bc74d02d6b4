package com.commsource.camera.mvp.helper;

import android.content.Context;

import com.commsource.config.SelfieConfig;
import com.commsource.util.BPLocationUtils;
import com.commsource.util.LanguageUtil;

/*********************************************
 * 静音拍照帮助类，日韩语静音模式下只能使用截屏拍照，不调用takePicture接口。后续如果有其他截屏拍照方案可被替代。
 * ********************************************
 * Version: 1.0.0
 * Date: 2017/4/7
 * Author: lhy
 * Changes: 创建该类
 * <AUTHOR>
 * ********************************************
 */

public class MuteCaptureHelper {

    /**
     * 是否是日韩静音模式
     * @param context context
     * @return isKoreaOrJapanMute
     */
    public static boolean isKoreaOrJapanMute(Context context) {
        return shouldCloseTakePictureSound(context) && !SelfieConfig.isTakePictureSound(context);
    }

    // A、通过系统语言判断，如果用户的系统语言是日语、韩语
    // B、用户在日韩地区
    public static boolean shouldCloseTakePictureSound(Context context) {
        return LanguageUtil.isJapanese(context) || LanguageUtil.isKorean(context) || BPLocationUtils.isJapan(context)
            || BPLocationUtils.isKorea(context);
    }
}
