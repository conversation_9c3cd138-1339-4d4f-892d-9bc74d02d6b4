package com.commsource.camera.makeup;

import android.util.SparseArray;

import com.commsource.beautyplus.util.PathUtil;
import com.commsource.camera.param.MakeupParam;
import com.commsource.camera.param.MakeupType;
import com.commsource.camera.xcamera.cover.bottomFunction.effect.makeup.MakeupUtils;
import com.commsource.repository.child.makeup.MakeupMaterial;

import java.util.HashMap;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/16.
 */

public class MakeupHelper {
    /**
     * 目录
     */
    private static final String MAKEUP_DIR = "camera_makeup";
    private static final String CONFIG_NAME = "configuration.plist";
    private static final String SIGNLE_MAKEUP_CONFIG_NAME = "makeup_suit_config.json";
    /**
     * 应用模块枚举。
     */
    public static final int ALL_MODEL = 0;
    public static final int SELFIE_MODEL = 1;
    public static final int EASY_EDITOR_MODEL = 3;

    public static final int[] newMakeupTypeList = {
            MakeupType.TYPE_LIP_STICK,
            MakeupType.TYPE_BLUSH,
            MakeupType.TYPE_TRIMMING,
            MakeupType.TYPE_EYE_BROW,
            MakeupType.TYPE_EYE_LASH,
            MakeupType.TYPE_BEAUTY_EYELINER,
            MakeupType.TYPE_EYE_SHADOW,
            MakeupType.TYPE_BEAUTY_DECORATION,
            MakeupType.TYPE_BEAUTY_FRECKLE,
            MakeupType.TYPE_BEAUTY_PUPIL,
            MakeupType.TYPE_HAIR,
            MakeupType.TYPE_EYE_SMILES,
    };

    /**
     * 获取美妆的Params
     *
     * @param type
     * @param material
     * @return
     */
    public static MakeupParam transToMakeupParam(int type, int faceIndex, MakeupMaterial material, MakeupMaterial colorMaterial) {
        int alpha = 0;
        //因为tab位置和颜色素材
        if (colorMaterial != null && MakeupUtils.Companion.isMainColorTab(material.getCurrentMakeupType())) {
            if (faceIndex == -1) {
                alpha = colorMaterial.getCurrentAlpha();
            } else {
                alpha = colorMaterial.getFaceAlpha(faceIndex);
            }
        } else {
            if (faceIndex == -1) {
                alpha = material.getCurrentAlpha();
            } else {
                alpha = material.getFaceAlpha(faceIndex);
            }
        }
        if (alpha < 0) {
            alpha = 0;
        } else if (alpha > 100) {
            alpha = 100;
        }
        if (material.isSuitConfig()) {
            // 套装中的单独配置。
            MakeupParam param = new MakeupParam();
            param.setId(material.getId());
            param.setMakeupType(type);
            param.setAlpha(alpha / 100f);
            param.setSuitSingleConfig(true);
            param.setCloseSuitOtherEffect(false);
            return param;
        }
        if (material != null) {
            // 在线非眼妆素材。
            MakeupParam param = new MakeupParam();
            param.setMakeupType(type);
            param.setAlpha(alpha / 100f);
            if (material.isInsideMaterial()) {
                param.setPlistPath("makeup_material/" + material.getOnlineId() + "/ar/" + CONFIG_NAME);
            } else {
                param.setPlistPath(PathUtil.getMakeupMaterialUnzipPath(material.getOnlineId()) + "/ar/" + CONFIG_NAME);
            }
            return param;
        }
        return null;
    }

    /**
     * 单人脸使用
     *
     * @param makeupEntities       美妆的素材文件
     * @param colorMakeupMaterials 美妆的颜色素材
     * @return 人脸参数
     * 本来大家一起用多人脸就好 但是是先有单人脸 多人脸是单人脸拷贝出来处理的 之后再合并
     */
    public static HashMap<Integer, MakeupParam> transToMakeupParams(SparseArray<MakeupMaterial> makeupEntities, SparseArray<MakeupMaterial> colorMakeupMaterials) {
        HashMap<Integer, MakeupParam> paramHashMap = new HashMap<>(16);
        if (makeupEntities == null) {
            return paramHashMap;
        }
        for (int i = 0; i < makeupEntities.size(); i++) {
            MakeupMaterial entity = makeupEntities.valueAt(i);
            MakeupMaterial _colorMaterial = null;
            if (colorMakeupMaterials != null) {
                _colorMaterial = colorMakeupMaterials.get(entity.getCurrentMakeupType());
            }
            MakeupParam makeupParam = transToMakeupParam(entity.getCurrentMakeupType(), -1, entity, _colorMaterial);
            if (makeupParam != null) {
                paramHashMap.put(makeupParam.getMakeupType(), makeupParam);
            }
        }
        return paramHashMap;
    }

    /**
     * 多人脸使用
     *
     * @param faceIndex      人脸位置
     * @param makeupEntities 对应人脸位置的妆容配置
     * @return
     */
    public static HashMap<Integer, MakeupParam> transToMultiFaceMakeupParams(int faceIndex,
                                                                             SparseArray<MakeupMaterial> makeupEntities, SparseArray<MakeupMaterial> colorMakeupMaterials) {
        HashMap<Integer, MakeupParam> paramHashMap = new HashMap<>(16);
        if (makeupEntities == null) {
            return paramHashMap;
        }
        for (int i = 0; i < makeupEntities.size(); i++) {
            MakeupMaterial entity = makeupEntities.valueAt(i);
            MakeupMaterial _colorMaterial = null;
            if (colorMakeupMaterials != null) {
                _colorMaterial = colorMakeupMaterials.get(entity.getCurrentMakeupType());
            }
            MakeupParam makeupParam = transToMakeupParam(entity.getCurrentMakeupType(), faceIndex, entity, _colorMaterial);
            if (makeupParam != null) {
                paramHashMap.put(makeupParam.getMakeupType(), makeupParam);
            }
        }
        return paramHashMap;
    }

}
