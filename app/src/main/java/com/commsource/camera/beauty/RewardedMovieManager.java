package com.commsource.camera.beauty;

import android.content.Context;
import android.text.TextUtils;

import com.commsource.config.ApplicationConfig;
import com.meitu.common.AppContext;
import com.meitu.hwbusinesskit.core.utils.TestLog;


import java.util.ArrayList;
import java.util.List;

/**
 * 奖励光斑控制类
 * <AUTHOR> 2018/1/25.
 */

public class RewardedMovieManager {
    /**
     * 有激励视频的付费光斑模型
     */
    private static List<Integer> mRVMovieBlur;

    /**
     *  已奖励光斑模型号列表
     */
    private static List<Integer> mRewardedMovieBlurList;

    private static volatile RewardedMovieManager mInstance;

    public static RewardedMovieManager getInstance() {
        return getInstance(false);
    }

    /**
     * 获取单例
     * @param ignoreCache 初始化时是否忽略从缓存读取数据
     * @return 单例
     */
    public static RewardedMovieManager getInstance(boolean ignoreCache) {
        if (mInstance == null) {
            synchronized (RewardedMovieManager.class) {
                if (mInstance == null) {
                    mInstance = new RewardedMovieManager();
                    init(ignoreCache);
                }
            }
        }
        return mInstance;
    }

    /**
     * 初始化
     * @param ignoreCache 是否忽略从缓存读取数据
     */
    public static void init(boolean ignoreCache) {
        if (!ignoreCache && mRVMovieBlur == null) {
            mRVMovieBlur = new ArrayList<>();

            String movieBlurList = ApplicationConfig.getRewardedVideoMovieBlurList(AppContext.getContext());
            if (!TextUtils.isEmpty(movieBlurList)) {
                for (String filterThemeNumber : movieBlurList.split(",")) {
                    mRVMovieBlur.add(Integer.valueOf(filterThemeNumber));
                }
            }
            TestLog.log("从缓存中读取含激励视频的电影光斑模型：" + movieBlurList);
        }

        mRewardedMovieBlurList = new ArrayList<>();
    }

    /**
     * 更新含有激励视频的付费电影光斑模型
     * @param modeList 付费主题滤镜编号。格式为："6001,6002,6003"
     */
    public synchronized void updateRVMovieBlur(Context context, String modeList) {
        TestLog.log("从Firebase中读取含激励视频的滤镜号：" + modeList);
        if (mRVMovieBlur == null) {
            mRVMovieBlur = new ArrayList<>();
        }
        mRVMovieBlur.clear();

        if (!TextUtils.isEmpty(modeList)) {
            for (String mode : modeList.split(",")) {
                mRVMovieBlur.add(Integer.valueOf(mode));
            }
        }

        ApplicationConfig.setRewardedVideoMovieBlurList(context, modeList);
    }

    /**
     * 是否包含激励视频
     * @param mode 滤镜ID
     * @return true，表示包含激励视频
     */
    public boolean hasRewardedVideo(int mode) {
        return !(mRVMovieBlur == null || mRVMovieBlur.isEmpty()) && mRVMovieBlur.contains(mode);
    }

    /**
     * 缓存已奖励电影光斑模型
     * @param filterThemeNumber 滤镜主题编号
     */
    public synchronized void cacheRewardedFilter(int filterThemeNumber) {
        if (!mRewardedMovieBlurList.contains(filterThemeNumber)) {
            mRewardedMovieBlurList.add(filterThemeNumber);
        }
    }

    /**
     * 判断电影光斑模型是否已获取奖励
     * @param filterThemeNumber 滤镜电影光斑模型
     * @return true，已获取奖励
     */
    public synchronized boolean hasRewarded(int filterThemeNumber) {
        return mRewardedMovieBlurList.contains(filterThemeNumber);
    }

    /**
     * 删除缓存的已奖励电影光斑模型
     * @param mode 电影光斑模型
     */
    public synchronized void deleteRewardedMovieBlur(int mode) {
        if (mRewardedMovieBlurList.contains(mode)) {
            mRewardedMovieBlurList.remove(mRewardedMovieBlurList.indexOf(mode));
        }
    }

    /**
     * 删除所有缓存的已奖励电影光斑模型
     */
    public synchronized void deleteAllRewardedFilter() {
        if (mRewardedMovieBlurList != null) {
            mRewardedMovieBlurList.clear();
        }

    }

}
