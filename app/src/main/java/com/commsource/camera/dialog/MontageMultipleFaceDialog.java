package com.commsource.camera.dialog;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.commsource.beautymain.data.RemoldFaceTool;
import com.commsource.beautyplus.R;
import com.commsource.beautyplus.databinding.DialogMontageMultipleFaceBinding;
import com.commsource.config.ImageConfig;
import com.commsource.makeup.widget.MakeupMultipleFaceSelectView;
import com.commsource.util.GlideProxy;
import com.meitu.core.types.FaceData;
import com.meitu.library.util.device.DeviceUtils;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;

import androidx.databinding.DataBindingUtil;

/**
 * Created by Yj on 2019/4/17
 */
public class MontageMultipleFaceDialog extends Dialog {

    private DialogMontageMultipleFaceBinding mViewBinding;

    private String imagePath;
    private FaceData mFaceData;

    private RemoldFaceTool mFaceTool;

    private OnDialogClickListener mOnDialogClickListener;

    private int mFaceIndex;

    private int mImgWidth;
    private int mImgHeight;

    private RequestOptions mRequestOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.RESOURCE)
            .skipMemoryCache(false)
            .placeholder(R.drawable.setting_loading_icon_placeholder)
            .error(R.drawable.setting_loading_icon_placeholder)
            .fallback(R.drawable.setting_loading_icon_placeholder)
            .fitCenter()
            .override(ImageConfig.getShowImgMaxSize());

    public MontageMultipleFaceDialog(Context context) {
        super(context, R.style.fullScreenDialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        LayoutInflater inflater = LayoutInflater.from(getContext());
        mViewBinding = DataBindingUtil.inflate(inflater, R.layout.dialog_montage_multiple_face, null, false);
        setContentView(mViewBinding.getRoot());
        initView();
    }

    private void initView() {
        if (mViewBinding == null || TextUtils.isEmpty(imagePath)) {
            return;
        }

        GlideProxy.with(getContext())
                .load(imagePath)
                .placeHolder(R.drawable.setting_loading_icon_placeholder)
                .into(mViewBinding.ivImage);

        mFaceTool = new RemoldFaceTool(mFaceData, mImgWidth, mImgHeight);

        mViewBinding.mmfsvMultipleFaceView.setNeedShowBlingAnimation(true);
        mViewBinding.mmfsvMultipleFaceView.setIsSelectSingleFace(true);
        mViewBinding.mmfsvMultipleFaceView.setNormalRectColor(0xffffffff);
        mViewBinding.mmfsvMultipleFaceView.setSelectedRectColor(0xfffb5986);
        mViewBinding.mmfsvMultipleFaceView.setShowOkOnlySelected(true);
        mViewBinding.mmfsvMultipleFaceView.post(() -> {
            mViewBinding.mmfsvMultipleFaceView.setFaceDataSource(
                    mFaceTool.getMakeupFaceDatas(mViewBinding.mmfsvMultipleFaceView.getWidth(),
                            mViewBinding.mmfsvMultipleFaceView.getHeight()));

            mViewBinding.mmfsvMultipleFaceView.invalidate();
        });
        // TODO: 2019/4/17 设置图片宽高
        mViewBinding.mmfsvMultipleFaceView
                .setOnMultipleFaceSelectListener(new MakeupMultipleFaceSelectView.OnMultipleFaceSelectListener() {
                    @Override
                    public void onMultipleFaceSelected(int[] faceIndexArr) {

                    }

                    @Override
                    public void onSingleFaceSelected(int faceIndex, boolean beforeAnim) {
                        mFaceIndex = faceIndex;
                    }

                });

        mViewBinding.ibtnFaceSelectCancel.setOnClickListener(v -> {
            dismiss();
            if (mOnDialogClickListener != null) {
                mOnDialogClickListener.onClickCancel();
            }
        });
        mViewBinding.tvOk.setOnClickListener(v -> {
            dismiss();
            if (mOnDialogClickListener != null) {
                mOnDialogClickListener.onClickOk(mFaceIndex);
            }
        });

    }

    public void setDataSource(String imagePath, FaceData faceData) {
        this.imagePath = imagePath;
        this.mFaceData = faceData;
    }

    public void setImageSize(int width, int height) {
        mImgWidth = width;
        mImgHeight = height;
    }

    @Override
    public void show() {
        mFaceIndex = 0;
        initView();
        super.show();
    }

    public void setOnDialogClickListener(OnDialogClickListener listener) {
        this.mOnDialogClickListener = listener;
    }

    public interface OnDialogClickListener {

        void onClickOk(int index);

        void onClickCancel();
    }
}
