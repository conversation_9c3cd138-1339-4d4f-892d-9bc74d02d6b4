package com.commsource.camera.aiengine;

import com.meitu.library.media.renderarch.arch.annotation.RenderThread;
import com.meitu.mtlab.MTAiInterface.MTSegmentModule.MTSegment;

import androidx.annotation.Nullable;


/**
 * User: zxb3
 * Date: 2021-07-19
 * Description: AI引擎版本的相机检测组件接收协议
 */
public interface MTAiSkinMaskReceiver extends MTAiNodesReceiver {

    /**
     * 是否需要 皮肤 抠图
     *
     * @return true 需要皮肤抠图检测
     */
    boolean isSkinMaskRequired();

    /**
     * 皮肤抠图检测结果
     *
     * @param skinMaskResult 皮肤抠图检测结果
     */
    @RenderThread
    void onSkinMaskDetected(@Nullable MTSegment skinMaskResult);

}
