package com.commsource.camera.montage;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;

import com.commsource.beautyplus.fragment.BaseFragment;

public abstract class MontagePageFragment extends BaseFragment {
    private String pageCategoryId;
    private @MontageConfig.MontageGroupType int groupType;
    private static  final String GROUP_TYPE="GROUP_TYPE";
    private static  final String CATEGORY_ID="CATEGORY_ID";
    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
       if(isVisibleToUser){
           onVisibleToUser();
       }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if(savedInstanceState!=null){
            pageCategoryId= savedInstanceState.getString(CATEGORY_ID);
            groupType= savedInstanceState.getInt(GROUP_TYPE);
        }
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(GROUP_TYPE,groupType);
        outState.putString(CATEGORY_ID,pageCategoryId);
    }

    public abstract void onVisibleToUser();

    public  void refreshItem(String pageCategoryId,int itemPosition,boolean needCallBack){
        this.pageCategoryId = pageCategoryId;
    }

    public String getPageCategoryId() {
        return pageCategoryId;
    }

    public void setPageCategoryId(String pageCategoryId) {
        this.pageCategoryId = pageCategoryId;
    }

    public int getGroupType() {
        return groupType;
    }

    public void setGroupType(int groupType) {
        this.groupType = groupType;
    }
}
