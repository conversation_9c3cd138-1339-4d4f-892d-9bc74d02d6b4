package com.commsource.camera.montage;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

@Entity(tableName = "MONTAGE_GROUP_ENTITY")
public class MontageGroupEntity {

    @PrimaryKey
    @ColumnInfo(name = "id")
    @NonNull
    private String id;

    @ColumnInfo(name = "category_id")
    private String categoryId;

    @ColumnInfo(name = "group_name")
    private String groupName;

    @ColumnInfo(name = "icon_url")
    private String iconUrl;

    @ColumnInfo(name = "group_type")
    private int groupType;

    @ColumnInfo(name = "group_sort")
    private int groupSort;

    @ColumnInfo(name = "group_is_available")
    private int groupIsAvailable;

    // 1 女性 2 男性 3 通用
    @ColumnInfo(name = "group_gender")
    private int groupGender;

    @ColumnInfo(name = "group_is_new_girl")
    private int groupIsNewForGirl;

    @ColumnInfo(name = "group_is_new_man")
    private int groupIsNewForMan;


    @Ignore
    private boolean isLocalEntity;

    @Ignore
    private boolean isSelected;

    public MontageGroupEntity(String id, String categoryId, String groupName, String iconUrl, int groupType,
        int groupSort, int groupIsAvailable, int groupGender) {
        this.id = id;
        this.categoryId = categoryId;
        this.groupName = groupName;
        this.iconUrl = iconUrl;
        this.groupType = groupType;
        this.groupSort = groupSort;
        this.groupIsAvailable = groupIsAvailable;
        this.groupGender = groupGender;
    }

    @Ignore
    public MontageGroupEntity() {
    }

    public int getGroupIsNewForGirl() {
        return groupIsNewForGirl;
    }

    public void setGroupIsNewForGirl(int groupIsNewForGirl) {
        this.groupIsNewForGirl = groupIsNewForGirl;
    }

    public int getGroupIsNewForMan() {
        return groupIsNewForMan;
    }

    public void setGroupIsNewForMan(int groupIsNewForMan) {
        this.groupIsNewForMan = groupIsNewForMan;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public int getGroupType() {
        return groupType;
    }

    public void setGroupType(int groupType) {
        this.groupType = groupType;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public int getGroupSort() {
        return groupSort;
    }

    public void setGroupSort(int groupSort) {
        this.groupSort = groupSort;
    }

    public int getGroupIsAvailable() {
        return groupIsAvailable;
    }

    public void setGroupIsAvailable(int groupIsAvailable) {
        this.groupIsAvailable = groupIsAvailable;
    }

    public boolean isLocalEntity() {
        return isLocalEntity;
    }

    public void setLocalEntity(boolean localEntity) {
        isLocalEntity = localEntity;
    }

    public int getGroupGender() {
        return groupGender;
    }

    public void setGroupGender(int groupGender) {
        this.groupGender = groupGender;
    }
}
