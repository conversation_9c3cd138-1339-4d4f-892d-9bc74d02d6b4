package com.commsource.camera.montage;


import static com.meitu.library.media.renderarch.arch.ConstantValues.TexMatR0_4x4;
import static com.meitu.library.media.renderarch.arch.ConstantValues.TexMatR180_4x4;
import static com.meitu.library.media.renderarch.arch.ConstantValues.TexMatR270_4x4;
import static com.meitu.library.media.renderarch.arch.ConstantValues.TexMatR90_4x4;
import static com.meitu.library.media.renderarch.arch.input.AbsInput.INVERSE_LANDSCAPE;
import static com.meitu.library.media.renderarch.arch.input.AbsInput.INVERSE_PORTRAIT;
import static com.meitu.library.media.renderarch.arch.input.AbsInput.LANDSCAPE;
import static com.meitu.library.media.renderarch.arch.input.AbsInput.PORTRAIT;

import com.commsource.beautyplus.R;
import com.commsource.camera.movingaverage.TextViewOutlineProvider;
import com.meitu.library.media.camera.MTCameraContainer;
import com.meitu.library.media.camera.nodes.HubNodesUiStatusObserver;
import com.meitu.library.media.camera.nodes.NodesServer;
import com.meitu.library.media.renderarch.arch.ConstantValues;
import com.meitu.library.media.renderarch.arch.RenderTexturePrograms;
import com.meitu.library.media.renderarch.arch.TextureProgram;
import com.meitu.library.media.renderarch.arch.data.frame.RenderFrameData;
import com.meitu.library.media.renderarch.arch.output.AbsTextureOutputReceiver;
import com.meitu.library.media.renderarch.arch.output.impl.BaseEasyTextureOutputReceiver;
import com.meitu.library.media.renderarch.gles.EglCore;
import com.meitu.library.media.renderarch.gles.WindowSurface;
import com.meitu.library.media.renderarch.util.PhoneAdaptUtils;
import com.meitu.library.util.Debug.Debug;

import android.graphics.SurfaceTexture;
import android.opengl.GLES20;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.view.TextureView;
import android.widget.FrameLayout;

public class SmallSizePreviewHelper extends BaseEasyTextureOutputReceiver
        implements HubNodesUiStatusObserver, TextureView.SurfaceTextureListener {
    private int[] mContainer;
    private WindowSurface mWindowSurface;
    private TextureView mSurfaceView;
    private EglCore mEglCore;
    private boolean mWindowSurfaceCreated;
    private PicInPicRenderer picInPicRenderer;
    private Handler mHandler;
    private volatile boolean isGlResourceInit = false;
    private boolean isHolderAvailable = false;

    private TextureProgram program;

    public SmallSizePreviewHelper(FrameLayout previewSurface, PicInPicRenderer picInPicRenderer) {
        this.mSurfaceView = previewSurface.findViewById(R.id.preview_surface);
        this.picInPicRenderer = picInPicRenderer;
        mSurfaceView.setSurfaceTextureListener(this);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            mSurfaceView.setOutlineProvider(new TextViewOutlineProvider(6));
            mSurfaceView.setClipToOutline(true);
        }
    }

    public SmallSizePreviewHelper() {
    }

    public void setSurfaceView(TextureView surfaceView) {
        this.mSurfaceView = surfaceView;
        mSurfaceView.setSurfaceTextureListener(this);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            mSurfaceView.setOutlineProvider(new TextViewOutlineProvider(6));
            mSurfaceView.setClipToOutline(true);
        }
    }

    public void setPicInPicRenderer(PicInPicRenderer picInPicRenderer) {
        this.picInPicRenderer = picInPicRenderer;
    }

    @Override
    public void onPrepareGLContext(EglCore eglCore) {
        this.mEglCore = eglCore;
        mWindowSurfaceCreated = false;
        if (null == mHandler) {
            mHandler = new Handler();
        }
        isGlResourceInit = true;
        tryPrepareWindowSurface();
        program = new TextureProgram(TextureProgram.TEX_2D);
    }




    @Override
    public void onReleaseGLContext() {
        mWindowSurfaceCreated = false;
        isGlResourceInit = false;
        if (picInPicRenderer != null) {
            picInPicRenderer.release();
        }
        if (null != mWindowSurface) {
            mWindowSurface.release();
            mWindowSurface = null;
        }
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        if (program != null) {
            program.release();
        }
    }

    @Override
    public boolean hasIndependentSurface() {
        return true;
    }

    @Override
    public boolean isCurrWorkThread() {
        return true;
    }


    @Override
    public boolean onOutPutTexture(int i, int i1, int i2, int deviceOrientation) {
        if (!isGlResourceInit || !mWindowSurfaceCreated || !isHolderAvailable || picInPicRenderer == null) {
            return false;
        }
        int textureId = picInPicRenderer.getTextureId();
        if (textureId == 0 || mWindowSurface == null) {
            return false;
        }
        if (mContainer == null) {
            mContainer = new int[1];
        }
        mContainer[0] = textureId;
        GLES20.glViewport(0, 0, mWindowSurface.getWidth(), mWindowSurface.getHeight());
        // float[] drawToScreenUVMatrix = textureInfo.getDrawToScreenUVMatrix();
        program.draw(ConstantValues.RECTANGLE_BUF, ConstantValues.RECTANGLE_2D_TEX_BUF, mContainer,
                GLES20.GL_TEXTURE_2D, 0, ConstantValues.IDENTITY_MAT_2x2, getFboToScreenTexMatrix(deviceOrientation));
        mWindowSurface.swapBuffers();
        return true;
    }


    private boolean isFrontCamera(RenderFrameData textureInfo) {
        return textureInfo != null && textureInfo.cameraInfo != null && !textureInfo.cameraInfo.isBackCamera;
    }

    private float[] getFboToScreenTexMatrix(int orientation) {
        float matrix[] = null;
        switch (orientation) {
            case LANDSCAPE:
                matrix = TexMatR90_4x4;
                break;
            case PORTRAIT:
                matrix = TexMatR0_4x4;
                break;
            case INVERSE_LANDSCAPE:
                matrix = TexMatR270_4x4;
                break;
            case INVERSE_PORTRAIT:
                matrix = TexMatR180_4x4;
                break;
        }

        return matrix;
    }

    /**
     * 尝试创建 WindowSurface
     * EglCore 和 Surface 分别从不同线程回调，需要两者就位才能创建
     */
    private void tryPrepareWindowSurface() {
        if (null != mSurfaceView && null != mEglCore && !mWindowSurfaceCreated && isGlResourceInit) {
            if (mWindowSurface != null) {
                mWindowSurface.release();
                mWindowSurface = null;
            }
            if (isHolderAvailable) {
                mWindowSurface = new WindowSurface(mEglCore, mSurfaceView.getSurfaceTexture(), false);
                if (mWindowSurface.makeCurrent()) {
                    mWindowSurfaceCreated = true;
                    Debug.d("PicInPicWindowSurfaceCreated");
                }
            }
        }
    }

    @Override
    public boolean glMakeCurrent() {
        return isGlResourceInit && isHolderAvailable && mWindowSurfaceCreated && mWindowSurface != null
                && mWindowSurface.makeCurrent();
    }

    @Override
    public String getName() {
        return null;
    }

    @Override
    public void onCreate(MTCameraContainer container, Bundle savedInstanceState) {

    }

    @Override
    public void onViewCreated(MTCameraContainer container, Bundle saveInstanceState) {

    }

    @Override
    public void onStart(MTCameraContainer container) {

    }

    @Override
    public void onResume(MTCameraContainer container) {

    }

    @Override
    public void onPause(MTCameraContainer container) {

    }

    @Override
    public void onStop(MTCameraContainer container) {

    }

    @Override
    public void onDestroy(MTCameraContainer container) {

    }

    @Override
    public void onSaveInstanceState(MTCameraContainer container, Bundle outState) {

    }

    @Override
    public NodesServer getNodesServer() {
        return null;
    }

    @Override
    public void bindServer(NodesServer server) {

    }

    @Override
    public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
        isHolderAvailable = true;
        if (null != mHandler) {
            mHandler.post(this::tryPrepareWindowSurface);
        }
        if (listener != null) {
            listener.onSurfaceAvailable();
        }
    }

    @Override
    public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {

    }

    @Override
    public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
        isHolderAvailable = false;
        mWindowSurfaceCreated = false;
        if (listener != null) {
            listener.onSurfaceDestroy();
        }
        return true;
    }

    @Override
    public void onSurfaceTextureUpdated(SurfaceTexture surface) {

    }

    private OnSurfaceStateChangeListener listener;

    public void setListener(OnSurfaceStateChangeListener listener) {
        this.listener = listener;
    }

    public interface OnSurfaceStateChangeListener {
        void onSurfaceAvailable();

        void onSurfaceDestroy();
    }
}
