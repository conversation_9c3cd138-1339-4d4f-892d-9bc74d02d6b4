package com.commsource.camera.datas;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class BeautyDefaultConfigVaule implements Serializable {

    @SerializedName("Version")
    public int version;

    @SerializedName("Group")
    public Group group;

    @SerializedName("Smooth")
    public Smooth smooth;

    @SerializedName("SkinColorLight")
    public SkinColorLight skinColorLight;

    @SerializedName("Beauty")
    public Beauty beauty;

    @SerializedName("Face")
    public Face face;

    @SerializedName("MakeupDefault")
    public MakeupDefault makeupDefault;

    public final class Group implements Serializable {
        @SerializedName("Country")
        public String country;

        @SerializedName("Gender")
        public String gender;

        @SerializedName("Age")
        public String age;

    }

    public final class Smooth implements Serializable {
        @SerializedName("Name")
        public String name;

        @SerializedName("Alpha")
        private float alpha;

        public float getAlpha() {
            if (alpha < 0) {
                alpha = 0;
            } else if (alpha > 1.0) {
                alpha = 1.0f;
            }
            return alpha;
        }

        public void setAlpha(float alpha) {
            this.alpha = alpha;
        }
    }

    public final class SkinColorLight implements Serializable {
        @SerializedName("Name")
        public String name;

        @SerializedName("Alpha")
        private float alpha;

        public float getAlpha() {
            if (alpha < -1.0) {
                alpha = -1.0f;
            } else if (alpha > 1.0) {
                alpha = 1.0f;
            }
            return alpha;
        }

        public void setAlpha(float alpha) {
            this.alpha = alpha;
        }
    }

    public final class Beauty implements Serializable {
        @SerializedName("RemoveBlackEye")
        private float removeBlackEye;

        @SerializedName("LightenEye")
        private float lightenEye;

        @SerializedName("WhitenTeeth")
        private float whitenTeeth;

        @SerializedName("RemoveBeverageAndAcne")
        public boolean removeBeverageAcneSwitch;

        public float getRemoveBlackEye() {
            if (removeBlackEye < 0) {
                removeBlackEye = 0;
            } else if (removeBlackEye > 1.0) {
                removeBlackEye = 1.0f;
            }
            return removeBlackEye;
        }

        public void setRemoveBlackEye(float removeBlackEye) {
            this.removeBlackEye = removeBlackEye;
        }

        public float getLightenEye() {
            if (lightenEye < 0) {
                lightenEye = 0;
            } else if (lightenEye > 1.0) {
                lightenEye = 1.0f;
            }
            return lightenEye;
        }

        public void setLightenEye(float lightenEye) {
            this.lightenEye = lightenEye;
        }

        public float getWhitenTeeth() {
            if (whitenTeeth < 0) {
                whitenTeeth = 0;
            } else if (whitenTeeth > 1.0) {
                whitenTeeth = 1.0f;
            }
            return whitenTeeth;
        }

        public void setWhitenTeeth(float whitenTeeth) {
            this.whitenTeeth = whitenTeeth;
        }
    }

    public final class Face implements Serializable {
        @SerializedName("Name")
        private String name;

        @SerializedName("ThinFaceValue")
        private float thinFaceValue;

        @SerializedName("BigEyeValue")
        private float bigEyeValue;

        @SerializedName("MouthValue")
        private float mouthValue;

        @SerializedName("NoseWidthValue")
        private float noseWidthValue;

        @SerializedName("NoseHeightValue")
        private float noseHeightValue;

        @SerializedName("JawValue")
        private float jawValue;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public float getThinFaceValue() {
            if (thinFaceValue < 0) {
                thinFaceValue = 0;
            } else if (thinFaceValue > 1.0) {
                thinFaceValue = 1.0f;
            }
            return thinFaceValue;
        }

        public void setThinFaceValue(float thinFaceValue) {
            this.thinFaceValue = thinFaceValue;
        }

        public float getBigEyeValue() {
            if (bigEyeValue < 0) {
                bigEyeValue = 0;
            } else if (bigEyeValue > 1.0) {
                bigEyeValue = 1.0f;
            }
            return bigEyeValue;
        }

        public void setBigEyeValue(float bigEyeValue) {
            this.bigEyeValue = bigEyeValue;
        }

        public float getMouthValue() {
            if (mouthValue < 0) {
                mouthValue = 0;
            } else if (mouthValue > 1.0) {
                mouthValue = 1.0f;
            }
            return mouthValue;
        }

        public void setMouthValue(float mouthValue) {
            this.mouthValue = mouthValue;
        }

        public float getNoseWidthValue() {
            if (noseWidthValue < 0) {
                noseWidthValue = 0;
            } else if (noseWidthValue > 1.0) {
                noseWidthValue = 1.0f;
            }
            return noseWidthValue;
        }

        public void setNoseWidthValue(float noseWidthValue) {
            this.noseWidthValue = noseWidthValue;
        }

        public float getNoseHeightValue() {
            if (noseHeightValue < -0.5) {
                noseHeightValue = -0.5f;
            } else if (noseHeightValue > 0.5) {
                noseHeightValue = 0.5f;
            }
            return noseHeightValue;
        }

        public void setNoseHeightValue(float noseHeightValue) {
            this.noseHeightValue = noseHeightValue;
        }

        public float getJawValue() {
            if (jawValue < -0.5) {
                jawValue = -0.5f;
            } else if (jawValue > 0.5) {
                jawValue = 0.5f;
            }
            return jawValue;
        }

        public void setJawValue(float jawValue) {
            this.jawValue = jawValue;
        }
    }

    public final class MakeupDefault implements Serializable {

        @SerializedName("Switch")
        public boolean makeupSwitch;
    }

}
