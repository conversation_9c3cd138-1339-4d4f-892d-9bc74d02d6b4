package com.commsource.camera.xcamera.glow_model.bean

import androidx.annotation.StringRes
import com.commsource.beautyplus.R
import com.commsource.camera.xcamera.glow_model.helper.GlowDataKeeper
import com.commsource.util.dp
import com.pixocial.business.duffle.repo.DuffleType
import com.pixocial.business.duffle.repo.glow.GlowRepo

enum class LightModeFunction(@StringRes val textId: Int, val panelHeight: Int, @DuffleType val typeId: Int, val analyticsKey: String = "") {
    DreamLight(
        textId = R.string.v7130_B_1,
        panelHeight = 248.dp,
        typeId = DuffleType.GlowDreamLightMaterial,
        analyticsKey = ""
    ),
    PureLight(
        textId = R.string.v7130_B_2,
        panelHeight = 248.dp,
        typeId = DuffleType.GlowPureLightMaterial,
        analyticsKey = ""
    ),
    DynamicLight(
        textId = R.string.v7130_B_3,
        panelHeight = 248.dp,
        typeId = DuffleType.GlowDynamicLightMaterial,
        analyticsKey = ""
    ),
    EyeSparkle(
        textId = R.string.v7130_B_4,
        panelHeight = 275.dp,
        typeId = DuffleType.GlowEyeSparkleMaterial,
        analyticsKey = ""
    )


}

fun Int.toLightModeFunction(): LightModeFunction? {
    return LightModeFunction.values().find { it.typeId == this }
}

fun LightModeFunction.getDefaultMaterialId(): String? {
    return when (this) {
        LightModeFunction.DreamLight -> GlowRepo.DEFAULT_ID
        else -> {
            null
        }
    }
}

fun String.protocolToLightFunction(): LightModeFunction? {
    return when (this) {
        "gradientglow" -> {
            LightModeFunction.DreamLight
        }

        "glow" -> {
            LightModeFunction.PureLight
        }

        "eyespark" -> {
            LightModeFunction.EyeSparkle
        }

        "dynamicglow" -> {
            LightModeFunction.DynamicLight
        }

        else -> {
            null
        }
    }
}