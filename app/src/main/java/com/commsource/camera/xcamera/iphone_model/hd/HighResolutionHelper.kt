package com.commsource.camera.xcamera.iphone_model.hd

import android.os.Build
import androidx.lifecycle.MutableLiveData
import com.commsource.beautyplus.R
import com.commsource.beautyplus.setting.abtest.ABTestDataEnum
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.xcamera.iphone_model.RecordType
import com.commsource.config.SelfieConfig
import com.commsource.statistics.Meepo
import com.commsource.util.DeviceLevelAdapter
import com.commsource.util.DeviceLevelStrategy
import com.commsource.util.GsonUtils
import com.commsource.util.UIHelper
import com.google.gson.reflect.TypeToken
import com.meitu.library.devicelevellib.LevelStrategy
import com.meitu.library.media.camera.common.AspectRatioGroup
import com.meitu.library.media.camera.common.Facing
import com.meitu.library.media.camera.common.PreviewSize
import com.meitu.library.media.camera.component.videorecorder.hardware.MediaCodecUtil
import com.meitu.library.util.device.DeviceUtils
import java.util.TreeMap
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs

object HighResolutionHelper {

    private var disableResolutionCache = ConcurrentHashMap<String, List<Int>?>()
    private val targetPreviewSizes: TreeMap<RecordType, PreviewSize?> = TreeMap()
    private val availableResolutions = ConcurrentHashMap<RecordType, PreviewSize?>()

    // 当前摄像头所有比例是否支持大图尺寸
    val cameraHDSupportEvent = MutableLiveData<Boolean>()

    // 当前摄像头特定画幅是否支持大图尺寸
    val ratioHDSupportEvent = MutableLiveData<Boolean>()

    // 当前的视频录制模式
    val recordTypeEvent = MutableLiveData<RecordType>()

    // 当前能支持的视频录制模式
    val availableRecordTypes = MutableLiveData<List<RecordType>?>()

    var captureMode = CameraMode.CAPTURE_MODE

    // 视频录制模式是否选择最高分辨率
    var selectMostResolution: Boolean = false

    private val sizeComparator =
        Comparator<PreviewSize> { o1, o2 -> if (o1.height >= o2.height) 1 else -1 }

    init {
        SelfieConfig.getDisabledResolution()?.let { value ->
            GsonUtils.getInstance().fromJson(value, DisabledVideoResolution::class.java)
                ?.let { model ->
                    model.front?.let { disableResolutionCache[Facing.FRONT] = it }
                    model.back?.let { disableResolutionCache[Facing.BACK] = it }
                }
        }
    }

    fun updateHDSupport(curPreviewSize: PreviewSize, maxPreviewSize: PreviewSize) {
        UIHelper.runOnUiThread {
            ratioHDSupportEvent.value =
                maxPreviewSize.width > curPreviewSize.width || maxPreviewSize.height > curPreviewSize.height
        }
    }

    fun getVideoPreviewSize(facing: String): PreviewSize? {
        if (availableResolutions.isEmpty()) {
            return null
        }

        val oldType: RecordType? = recordTypeEvent.value
        var target = SelfieConfig.getIphoneRecordType(facing)
        // 协议跳转且选择最高分辨率
        if (selectMostResolution) {
            availableRecordTypes.value?.last()?.let {
                target = it
                SelfieConfig.setIphoneRecordType(facing, it.type)
            }
            selectMostResolution = false
        }
        val availableRecordTypes = mutableListOf<RecordType>()
        availableResolutions.forEach {
            if (it.value != null) {
                availableRecordTypes.add(it.key)
            }
        }
        availableRecordTypes.sortBy { it.type }
        if (!availableRecordTypes.contains(target)) {
            target = availableRecordTypes[availableRecordTypes.size - 1]
        }
        if (oldType != target) {
            recordTypeEvent.postValue(target)
        }
        return availableResolutions[target]
    }

    fun setAvailableResolutionList(
        supportPreviewSizes: List<PreviewSize>?, targetRatio: Float, facing: String
    ) {
        if (supportPreviewSizes.isNullOrEmpty()) {
            return
        }

        targetPreviewSizes.clear()
        availableResolutions.clear()

        val disableResolutions = disableResolutionCache[facing]
        val sortedPreviewSizes = supportPreviewSizes.sortedWith(sizeComparator)
        val filterPreviewSizes = mutableListOf<PreviewSize>()
        sortedPreviewSizes.forEach { previewSize ->
            val ratio = previewSize.width.toFloat() / previewSize.height
            if (abs(ratio - targetRatio) <= 0.05f) {
                if (disableResolutions == null || !disableResolutions.contains(previewSize.height)) {
                    if (previewSize.height == RecordType.Video_4K.size) {
                        val isMediaCodecSupport =
                            isMediaCodecSupport(previewSize, RecordType.Video_4K.size)
                        if (!isMediaCodecSupport) {
                            filterPreviewSizes.add(previewSize)
                        }
                        if (isMediaCodecSupport && isDeviceSupport3kOr4k()) {
                            compareResolutionKeyAndSave(RecordType.Video_4K, previewSize)
                        }
                    } else if (previewSize.height == RecordType.Video_3K.size) {
                        val isMediaCodecSupport =
                            isMediaCodecSupport(previewSize, RecordType.Video_3K.size)
                        if (!isMediaCodecSupport) {
                            filterPreviewSizes.add(previewSize)
                        }
                        if (isMediaCodecSupport && isDeviceSupport3kOr4k()) {
                            compareResolutionKeyAndSave(RecordType.Video_3K, previewSize)
                        }
                    } else if (previewSize.height == RecordType.Video_2K.size) {
                        val isMediaCodecSupport =
                            isMediaCodecSupport(previewSize, RecordType.Video_2K.size)
                        if (!isMediaCodecSupport) {
                            filterPreviewSizes.add(previewSize)
                        }
                        if (isMediaCodecSupport && isDeviceSupport2k()) {
                            compareResolutionKeyAndSave(RecordType.Video_2K, previewSize)
                        }
                    } else if (previewSize.height == RecordType.Video_1080P.size) {
                        if (isDeviceSupport1080p()) {
                            compareResolutionKeyAndSave(RecordType.Video_1080P, previewSize)
                        }
                    } else if (previewSize.height == RecordType.Video_720P.size) {
                        compareResolutionKeyAndSave(RecordType.Video_720P, previewSize)
                    }
                }
            }
        }

        val targetRecordTypes = mutableListOf<RecordType>()
        targetPreviewSizes.forEach {
            if (it.value != null) {
                targetRecordTypes.add(it.key)
            }
        }
        if (targetRecordTypes.size > 3) {
            val size = targetRecordTypes.size
            val result = mutableListOf(targetRecordTypes[0], targetRecordTypes[1])
            // 支持 4K 情况下，需要判断 2K 是否存在，存在也要支持
            if (targetRecordTypes[size - 1] == RecordType.Video_4K) {
                val index2K = targetRecordTypes.indexOf(RecordType.Video_2K)
                if (index2K in 2 until size - 1) {
                    result.add(targetRecordTypes[index2K])
                }
                result.add(RecordType.Video_4K)
            } else {
                result.add(targetRecordTypes[size - 1])
            }
            targetRecordTypes.clear()
            targetRecordTypes.addAll(result)
        }
        targetRecordTypes.forEach {
            availableResolutions[it] = targetPreviewSizes[it]
        }

        UIHelper.runOnUiThread {
            availableRecordTypes.value = targetRecordTypes
        }
    }

    private fun compareResolutionKeyAndSave(recordType: RecordType, previewSize: PreviewSize) {
        if (targetPreviewSizes[recordType] != null) {
            val cache = targetPreviewSizes[recordType]
            if (cache!!.height >= previewSize.height) {
                return
            }
        }
        targetPreviewSizes[recordType] = previewSize
    }

    private fun isMediaCodecSupport(previewSize: PreviewSize, height: Int): Boolean {
        MediaCodecUtil.getMediaCodecSupportInfo()?.let {
            if (it.mMaxSupportWidth == 0 || it.mMaxSupportHeight == 0) {
                return false
            }
            if (previewSize.width > it.mMaxSupportWidth || previewSize.height > it.mMaxSupportHeight) {
                return false
            }
            return true
        }
        return false
    }

    fun isDeviceSupport3kOr4k(): Boolean {
        if (isDeviceSupport2k()) {
            DeviceLevelStrategy.getDeviceModel()?.let {
                if (it.cpuGrade >= 23 && it.cpuGrade >= 17) {
                    return true
                }
            }
        }
        return false
    }

    fun isDeviceSupport2k(): Boolean {
        return DeviceLevelStrategy.getDeviceLevelForCamera() == DeviceLevelStrategy.VHIGH
    }

    fun isDeviceSupport1080p(): Boolean {
        return DeviceLevelStrategy.getDeviceLevelForCamera() == DeviceLevelStrategy.VHIGH ||
                DeviceLevelAdapter.getDeviceLevel() >= LevelStrategy.HIGH
    }

    fun getDefinedCameraPreviewSize(targetRatio: Float): PreviewSize {
        val deviceLevel = getAppleDeviceLevel()
        if (abs((targetRatio - 4f / 3f).toDouble()) < abs((targetRatio - 16f / 9f).toDouble())) {
            // 4：3。
            if (deviceLevel == LevelStrategy.LOW) {
                return PreviewSize(480, 360)
            }
            if (deviceLevel == LevelStrategy.MID) {
                return if (bTestHDEnable()) {
                    PreviewSize(1024, 768)
                } else {
                    PreviewSize(800, 600)
                }
            }
            if (deviceLevel == LevelStrategy.HIGH) {
                return if (bTestHDEnable()) {
                    PreviewSize(1280, 960)
                } else {
                    PreviewSize(1024, 768)
                }
            }
            if (deviceLevel == DeviceLevelStrategy.VHIGH) {
                return if (bTestHDEnable()) {
                    PreviewSize(1440, 1080)
                } else {
                    PreviewSize(1280, 960)
                }
            }
            return PreviewSize(640, 480)
        } else {
            if (deviceLevel == LevelStrategy.LOW) {
                return PreviewSize(640, 360)
            }
            if (deviceLevel == LevelStrategy.MID) {
                return if (bTestHDEnable()) {
                    PreviewSize(1280, 720)
                } else {
                    PreviewSize(960, 540)
                }
            }
            if (deviceLevel == LevelStrategy.HIGH) {
                return if (bTestHDEnable()) {
                    PreviewSize(1440, 810)
                } else {
                    PreviewSize(1280, 720)
                }
            }
            if (deviceLevel == DeviceLevelStrategy.VHIGH) {
                return if (bTestHDEnable()) {
                    PreviewSize(1920, 1080)
                } else {
                    PreviewSize(1440, 810)
                }
            }
            return PreviewSize(960, 540)
        }
    }

    /**
     * 实验组 B 开启高清，预览尺寸不一样
     */
    private fun bTestHDEnable(): Boolean {
        return Meepo.isInABTest(ABTestDataEnum.APPLE_HD_TEST_B.code) && SelfieConfig.isIphoneHdEnable()
    }

    fun getVideoCameraPreviewSize(facing: String, targetRatio: Float): PreviewSize {
        val recordType = SelfieConfig.getIphoneRecordType(facing)
        val deviceLevel = getAppleDeviceLevel()
        if (abs((targetRatio - 4f / 3f).toDouble()) < abs((targetRatio - 16f / 9f).toDouble())) {
            return when (deviceLevel) {
                LevelStrategy.LOW -> PreviewSize(480, 360)
                LevelStrategy.MID -> PreviewSize(800, 600)

                LevelStrategy.HIGH -> {
                    when (recordType) {
                        RecordType.Video_1080P -> {
                            if (screenWidthAbove1080()) {
                                PreviewSize(1280, 960)
                            } else {
                                PreviewSize(1024, 768)
                            }
                        }

                        RecordType.Video_720P -> PreviewSize(960, 720)
                        else -> PreviewSize(1024, 768)
                    }
                }

                DeviceLevelStrategy.VHIGH -> {
                    return when (recordType) {
                        RecordType.Video_2K, RecordType.Video_3K, RecordType.Video_4K -> {
                            if (screenWidthAbove1080()) {
                                PreviewSize(1440, 1080)
                            } else {
                                PreviewSize(1280, 960)
                            }
                        }

                        RecordType.Video_1080P -> {
                            if (screenWidthAbove1080()) {
                                PreviewSize(1280, 960)
                            } else {
                                PreviewSize(1024, 768)
                            }
                        }

                        RecordType.Video_720P -> PreviewSize(960, 720)
                        else -> PreviewSize(1280, 960)
                    }
                }

                else -> return PreviewSize(640, 480)
            }
        } else {
            return when (deviceLevel) {
                LevelStrategy.LOW -> PreviewSize(640, 360)
                LevelStrategy.MID -> PreviewSize(960, 540)

                LevelStrategy.HIGH -> {
                    when (recordType) {
                        RecordType.Video_1080P -> {
                            if (screenWidthAbove1080()) {
                                PreviewSize(1440, 810)
                            } else {
                                PreviewSize(1280, 720)
                            }
                        }

                        RecordType.Video_720P -> PreviewSize(1280, 720)
                        else -> PreviewSize(1280, 720)
                    }
                }

                DeviceLevelStrategy.VHIGH -> {
                    when (recordType) {
                        RecordType.Video_2K, RecordType.Video_3K, RecordType.Video_4K -> {
                            if (screenWidthAbove1080()) {
                                PreviewSize(1920, 1080)
                            } else {
                                PreviewSize(1707, 960)
                            }
                        }

                        RecordType.Video_1080P -> {
                            if (screenWidthAbove1080()) {
                                PreviewSize(1707, 960)
                            } else {
                                PreviewSize(1365, 768)
                            }
                        }

                        RecordType.Video_720P -> PreviewSize(1280, 720)
                        else -> PreviewSize(1440, 810)
                    }
                }

                else -> return PreviewSize(960, 540)
            }
        }
    }

    /**
     * 机型等级
     */
    fun getAppleDeviceLevel(): Int {
        val deviceLevel = DeviceLevelStrategy.getDeviceLevelForCamera()
        return if (deviceLevel == DeviceLevelStrategy.VHIGH) {
            DeviceLevelStrategy.VHIGH
        } else {
            DeviceLevelAdapter.getDeviceLevel()
        }
    }

    private fun screenWidthAbove1080(): Boolean {
        val deviceWidth = DeviceUtils.getScreenWidth()
        return deviceWidth >= 1080
    }

    /**
     * 获取高清图标、对照组不需要显示
     */
    fun getHDIcon(): Int? {
        return if (isAppleHDTest()) {
            R.drawable.camera_icon_applemode_entrance_hd
        } else {
            null
        }
    }

    /**
     * 是否处于高清测试组
     */
    fun isAppleHDTest(): Boolean {
        return Meepo.isInABTest(ABTestDataEnum.APPLE_HD_TEST_A.code)
                || Meepo.isInABTest(ABTestDataEnum.APPLE_HD_TEST_B.code)
    }

    fun setAppleModeHD(enable: Boolean) {
        SelfieConfig.setIphoneHdEnable(enable)
    }

    /**
     * 根据相机提供的预览尺寸，判断全部比例都是否支持高清
     */
    fun updateCameraHDSupport(supportPreviewSizes: List<PreviewSize>?, facing: String) {
        if (SelfieConfig.hasCameraSupportHD(facing)) {
            val allSupportHD = SelfieConfig.isCameraSupportHD(facing)
            UIHelper.runOnUiThread { cameraHDSupportEvent.value = allSupportHD }
            return
        }

        var result = false
        val ratios = arrayOf(
            AspectRatioGroup.RATIO_1_1, AspectRatioGroup.RATIO_16_9,
            AspectRatioGroup.RATIO_3_4, AspectRatioGroup.FULL_SCREEN
        )
        for (ratio in ratios) {
            val smallPreviewSize = CameraSizeUtil.getPreviewSize(
                supportPreviewSizes,
                ratio.value()
            )
            val largePreviewSize = CameraSizeUtil.getPreviewSize(
                supportPreviewSizes,
                ratio.value(),
                5000,
                5000
            )
            if (largePreviewSize.width > smallPreviewSize.width || largePreviewSize.height > smallPreviewSize.height) {
                result = true
                break
            }
        }
        SelfieConfig.setCameraSupportHD(facing, result)
        UIHelper.runOnUiThread { cameraHDSupportEvent.value = result }
    }

    /**
     * 设置机型对应禁用的分辨率
     */
    fun disabledVideoResolution(value: String?) {
        if (value.isNullOrEmpty()) {
            clearDisableCache()
            return
        }

        val type = object : TypeToken<Map<String, DisabledVideoResolution>>() {}.type
        kotlin.runCatching {
            val models =
                GsonUtils.getInstance().fromJson<Map<String, DisabledVideoResolution>>(value, type)
            models[Build.MODEL]?.also { model ->
                SelfieConfig.setDisabledResolution(GsonUtils.getInstance().toJson(model))
                model.front?.let { disableResolutionCache[Facing.FRONT] = it }
                model.back?.let { disableResolutionCache[Facing.BACK] = it }
            } ?: run {
                clearDisableCache()
            }
        }
    }

    private fun clearDisableCache() {
        SelfieConfig.setDisabledResolution(null)
        disableResolutionCache.clear()
    }
}