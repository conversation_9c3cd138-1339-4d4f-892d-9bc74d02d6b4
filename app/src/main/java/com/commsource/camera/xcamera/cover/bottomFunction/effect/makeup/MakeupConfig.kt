package com.commsource.camera.xcamera.cover.bottomFunction.effect.makeup

import android.util.SparseArray
import com.commsource.camera.param.MakeupType
import com.commsource.camera.xcamera.bean.SelectedMakeupColorMaterialData
import com.commsource.camera.xcamera.bean.SelectedMakeupMaterialData
import com.commsource.util.common.SpTableName
import com.commsource.repository.child.makeup.MakeupMaterial
import com.commsource.util.common.SPConfig
import com.google.errorprone.annotations.Keep
import com.meitu.common.AppContext

/**
 * @Desc : 美妆Config
 * <AUTHOR> Bear - 2020/7/15
 */
object MakeupConfig {

    private val spConfig by lazy { SPConfig(AppContext.context, SpTableName.MAKEUP_CONFIG) }

    val makeupTypes = intArrayOf(
        MakeupType.TYPE_LIP_STICK,
        MakeupType.TYPE_EYE_BROW,
        MakeupType.TYPE_EYE_SHADOW,
        MakeupType.TYPE_BLUSH,
        MakeupType.TYPE_HAIR,
        MakeupType.TYPE_TRIMMING,
        MakeupType.TYPE_EYE_LASH,
        MakeupType.TYPE_EYE_SMILES,
        MakeupType.TYPE_BEAUTY_PUPIL,
        MakeupType.TYPE_BEAUTY_FRECKLE,
        MakeupType.TYPE_BEAUTY_DECORATION,
        MakeupType.TYPE_BEAUTY_EYELINER,
    )

    /**
     * 保存预发布key
     */
    fun setPreReleaseKey(key: String) {
        spConfig.putValue("makeup_pre_release", key)
    }

    /**
     * 获取预发布key
     */
    fun getPreReleaseKey(): String {
        return spConfig.getString("makeup_pre_release", "")
    }

    // 缓存中无效的美妆ID
    const val INVALID_MAKEUP_ID = "-1"
    const val INVALID_MAKEUP_ALPHA = -500

    private const val KEY_TEST_SAVE_MAKEUP_ID = "KEY_NEW_SAVE_MAKEUP_ID"
    private const val KEY_TEST_SAVE_MAKEUP_COLOR_ID = "KEY_NEW_SAVE_MAKEUP_COLOR_ID"
    private const val KEY_TEST_SAVE_MAKEUP_COLOR_ALPHA = "KEY_NEW_SAVE_MAKEUP_COLOR_ALPHA"

    private const val KEY_MAKEUP_COLOR = "KEY_MAKEUP_COLOR"

    //这是旧的美妆记忆前缀的key
    private const val KEY_TEST_CAMERA_BEAUTY_ALPHA = "KEY_NEW_CAMERA_BEAUTY_ALPHA_"

    /**
     * 获取look无选中的美妆数据集合
     *
     * @return
     */
    fun getSelectedMakeupDatas(): List<SelectedMakeupMaterialData>? {
        val materialData: MutableList<SelectedMakeupMaterialData> = ArrayList()
        for (type in makeupTypes) {
            val id = getSaveMakeupId(type)
            if (id != INVALID_MAKEUP_ID) {
                materialData.add(SelectedMakeupMaterialData(type, id, getSaveMakeupTypeAlpha(type)))
            }
        }
        return materialData
    }

    /**
     * 获取选中的颜色数据
     */
    fun getSelectMakeupColorDatas(): SparseArray<SelectedMakeupColorMaterialData>? {
        val intArray = SparseArray<SelectedMakeupColorMaterialData>()
        for (type in makeupTypes) {
            val id = getSaveMakeupColorId(type)
            if (id != INVALID_MAKEUP_ID) {
                intArray.put(
                    type,
                    SelectedMakeupColorMaterialData(type, id, getSaveMakeupColorAlpha(type))
                )
            }
        }
        return intArray
    }

    fun isShowMakeupVerticalGuide(): Boolean {
        return spConfig.getBoolean("isShowMakeupVerticalGuide", true)
    }

    fun setShowMakeupVerticalGuide(isShowMakeupVerticalGuide: Boolean) {
        spConfig.putValue("isShowMakeupVerticalGuide", isShowMakeupVerticalGuide)
    }

    /**
     * 获取Look无美妆alpha值
     * alpha 值为-500表示没有选中
     *
     * @param type
     * @return
     */
    fun getSaveMakeupTypeAlpha(@MakeupType type: Int): Int {
        return spConfig.getInt(KEY_TEST_CAMERA_BEAUTY_ALPHA + type, getNewMakeupDefaultAlpha(type))
    }

    /**
     * 保存Look无美妆的Alpha值
     *
     * @param alpha
     */
    fun saveMakeupTypeAlpha(@MakeupType type: Int, alpha: Int) {
        spConfig.putValue(KEY_TEST_CAMERA_BEAUTY_ALPHA + type, alpha)
    }


    private fun getSaveMakeupId(@MakeupType type: Int): String {
        return spConfig.getString(KEY_TEST_SAVE_MAKEUP_ID + type, getNewMakeupDefaultId(type))
    }

    private fun saveMakeupId(@MakeupType type: Int, id: String?) {
        spConfig.putValue(KEY_TEST_SAVE_MAKEUP_ID + type, id)
    }

    fun saveMakeupColorId(@MakeupType type: Int, id: String?) {
        spConfig.putValue(KEY_TEST_SAVE_MAKEUP_COLOR_ID + type, id)
    }

    private fun getSaveMakeupColorId(@MakeupType type: Int): String {
        return spConfig.getString(
            KEY_TEST_SAVE_MAKEUP_COLOR_ID + type,
            getNewMakeupColorDefaultId(type)
        )
    }

    fun saveMakeupColorAlpha(@MakeupType type: Int, alpha: Int) {
        spConfig.putValue(KEY_TEST_SAVE_MAKEUP_COLOR_ALPHA + type, alpha)
    }

    fun getSaveMakeupColorAlpha(@MakeupType type: Int): Int {
        return spConfig.getInt(
            KEY_TEST_SAVE_MAKEUP_COLOR_ALPHA + type,
            getNewMakeupColorAlpha(type)
        )
    }

    /**
     * 保存美妆颜色素材的当前ID和Alpha
     */
    fun saveMakeupColorMaterials(materials: SparseArray<MakeupMaterial>?) {
        materials?.let {
            for (type in makeupTypes) {
                val entity = materials[type]
                if (entity == null) {
                    saveMakeupColorId(type, "")
                    saveMakeupColorAlpha(type, 0)
                } else {
                    saveMakeupColorId(type, entity.getMakeupID())
                    saveMakeupColorAlpha(type, entity.getCurrentAlpha())
                }
            }
        }
    }


    fun saveMakeupColorMaterial(type: Int, alpha: Int?, material: MakeupMaterial) {
        saveMakeupColorId(type, material.getMakeupID())
        saveMakeupColorAlpha(type, alpha ?: 0)
    }

    /**
     * 记忆Look无美妆效果集合
     *
     * @param materials
     */
    fun saveMakeupMaterials(materials: SparseArray<MakeupMaterial>?) {
        materials?.let {
            for (type in makeupTypes) {
                val entity = materials[type]
                if (entity == null) {
                    saveMakeupTypeAlpha(type, INVALID_MAKEUP_ALPHA)
                    saveMakeupId(type, INVALID_MAKEUP_ID)
                } else {
                    saveMakeupTypeAlpha(type, entity.getCurrentAlpha())
                    saveMakeupId(type, entity.getMakeupID())
                }
            }
        }
    }

    /**
     * 获取默认选中的美型alpha值
     *
     * @param type
     * @return
     */
    fun getNewMakeupDefaultId(@MakeupType type: Int): String {
        return when (type) {
            MakeupType.TYPE_LIP_STICK -> "BP_LIP_00000002"
            MakeupType.TYPE_TRIMMING -> "BP_CON_00000002"
            MakeupType.TYPE_BLUSH, MakeupType.TYPE_EYE_LASH, MakeupType.TYPE_EYE_SHADOW, MakeupType.TYPE_EYE_BROW, MakeupType.TYPE_HAIR -> INVALID_MAKEUP_ID
            else -> INVALID_MAKEUP_ID
        }
    }

    /**
     * 获取新的美妆默认值
     *
     * @param type
     * @return
     */
    private fun getNewMakeupDefaultAlpha(@MakeupType type: Int): Int {
        return when (type) {
            MakeupType.TYPE_LIP_STICK -> 20
            MakeupType.TYPE_BLUSH -> 20
            MakeupType.TYPE_TRIMMING -> 60
            MakeupType.TYPE_EYE_LASH -> 70
            MakeupType.TYPE_EYE_BROW, MakeupType.TYPE_EYE_SHADOW, MakeupType.TYPE_HAIR, MakeupType.TYPE_EYE_SMILES -> INVALID_MAKEUP_ALPHA
            else -> INVALID_MAKEUP_ALPHA
        }
    }

    /**
     * 获取默认选中美妆风格id
     *
     * @param type
     * @return
     */
    fun getNewMakeupColorDefaultId(@MakeupType type: Int): String {
        return when (type) {
            MakeupType.TYPE_LIP_STICK -> "BP_LIC_00000002"
            MakeupType.TYPE_BLUSH -> "BP_BLC_00000001"
            MakeupType.TYPE_EYE_BROW -> "BP_EBC_00000003"
            MakeupType.TYPE_EYE_LASH -> "BP_ELC_00000002"
            MakeupType.TYPE_TRIMMING, MakeupType.TYPE_EYE_SHADOW, MakeupType.TYPE_HAIR -> "-1"
            else -> "-1"
        }
    }

    /**
     * 获取新美妆素材中颜色素材的Alpha值
     * [MakeupUtils]中的isMainColorTab来控制滑杆值
     */
    fun getNewMakeupColorAlpha(@MakeupType type: Int): Int {
        return when (type) {
            MakeupType.TYPE_LIP_STICK -> 40
            MakeupType.TYPE_BLUSH -> 55
            else -> 0
        }
    }


    fun saveMakeupColors(json: String) {
        spConfig.putValue(KEY_MAKEUP_COLOR, json)
    }


    fun getMakeupColors(): String {
        return spConfig.getString(KEY_MAKEUP_COLOR, "")
    }

    @Keep
    class MakeupColorEntity(
        val color: Color,
        val makeupType: Int,
        val isColorConfig: Boolean,
    )
}