package com.commsource.camera.xcamera.iphone_model.render

import com.pixocial.camerasuite.camera.core.renders.effectbean.AbsEffectSection
import kotlin.math.max
import kotlin.math.min

class CameraWhiteBalanceSection : AbsEffectSection() {
    private var configPath: String = "MTAiModel/ColortoningModel"

    private var temp: Int = 0
    private var tint: Int = 0

    fun setConfig(path: String) {
        this.configPath = path
    }

    fun setTemp(value: Float) {
        temp = paramGetInt(value)
    }

    fun setTint(value: Float) {
        tint = paramGetInt(value)
    }

    fun fetchConfigPath() = configPath
    fun fetchTemp() = temp
    fun fetchTint() = tint

    @Synchronized
    override fun onSyncCurrent2Target(target: AbsEffectSection) {
        (target as? CameraWhiteBalanceSection)?.let {
            it.configPath = configPath
            it.temp = temp
            it.tint = tint
        }
    }

    override fun copy(): AbsEffectSection {
        val target = CameraWhiteBalanceSection()
        target.updateDetectType(target.needDetectType())
        onSyncCurrent2Target(target)
        return target
    }

    override fun isConfigChanged(old: AbsEffectSection?): Boolean {
        return this.configPath != (old as? CameraWhiteBalanceSection)?.configPath
    }

    private fun paramGetInt(value: Float): Int {
        return min(max((value * 100.0f).toInt(), -100), 100)
    }
}