package com.commsource.camera.xcamera.cover.topfunction

import android.animation.ValueAnimator
import android.view.View
import androidx.annotation.CallSuper
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverIphoneRadioBinding
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.commsource.camera.xcamera.cover.AbsLazyCover
import com.commsource.camera.xcamera.cover.CameraConfigViewModel
import com.commsource.camera.xcamera.iphone_model.IphoneModelCache
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.resColor
import com.commsource.util.setMarginTop
import com.commsource.util.visible
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.framework.cover.AbstractViewBindingCover

open class BaseFragmentRadioCover : AbstractViewBindingCover<CoverIphoneRadioBinding>() {


    data class Config(
        val selectedColor: Int = R.color.color_fbc73c,
        val unSelectedColor: Int = R.color.white,
        val background: Int = R.color.black80
    )

    private val config: Config by lazy {
        configured()
    }

    override fun getLayoutId(): Int {
        return R.layout.cover_iphone_radio
    }

 

    override fun onBindViewModel() {
    }

    @CallSuper
    override fun onViewDidLoad() {
        viewBinding.llSetting.delegate?.backgroundColor = config.background.resColor()

        viewBinding.run {

            radio11.setOnClickListener {
                switchRatio(CameraRatioType.PICTURE_RATIO_1_1, true)
            }
            radio34.setOnClickListener {
                switchRatio(CameraRatioType.PICTURE_RATIO_4_3, true)

            }

            if (!DeviceUtils.support916()) {
                radio916.gone()
            } else {
                radio916.setOnClickListener {
                    switchRatio(CameraRatioType.PICTURE_RATIO_9_16, true)
                }
            }

            radioFull.setOnClickListener {
                switchRatio(CameraRatioType.PICTURE_RATIO_FULL, true)

            }

            rlContain.setOnClickListener {
                closeRadio()
            }
        }
        viewBinding.llSetting.setMarginTop(CameraConfigViewModel.TOP_BAR_HEIGHT - 6.dp() + CameraConfigViewModel.getCameraSafePaddingTop() + 16.dp)
    }

    open fun switchRatio(ratio: Int, userClick: Boolean = false) {

    }

    open fun closeRadio() {

    }

  

    open fun configured(): Config {
        return Config()
    }


    fun selectRadio(ratio: Int) {
        val selectedColor = config.selectedColor.resColor()
        val unSelectedColor = config.unSelectedColor.resColor()
        when (ratio) {
            CameraRatioType.PICTURE_RATIO_1_1 -> {
                viewBinding.run {
                    radio11.setTextColor(selectedColor)
                    radio34.setTextColor(unSelectedColor)
                    radio916.setTextColor(unSelectedColor)
                    radioFull.setTextColor(unSelectedColor)
                }
            }

            CameraRatioType.PICTURE_RATIO_4_3 -> {
                viewBinding.run {
                    radio11.setTextColor(unSelectedColor)
                    radio34.setTextColor(selectedColor)
                    radio916.setTextColor(unSelectedColor)
                    radioFull.setTextColor(unSelectedColor)
                }
            }

            CameraRatioType.PICTURE_RATIO_9_16 -> {
                viewBinding.run {
                    radio11.setTextColor(unSelectedColor)
                    radio34.setTextColor(unSelectedColor)
                    radio916.setTextColor(selectedColor)
                    radioFull.setTextColor(unSelectedColor)
                }
            }

            CameraRatioType.PICTURE_RATIO_FULL -> {
                viewBinding.run {
                    radio11.setTextColor(unSelectedColor)
                    radio34.setTextColor(unSelectedColor)
                    radio916.setTextColor(unSelectedColor)
                    radioFull.setTextColor(selectedColor)
                }
            }

            else -> {
                viewBinding.run {
                    radio11.setTextColor(selectedColor)
                    radio34.setTextColor(unSelectedColor)
                    radio916.setTextColor(unSelectedColor)
                    radioFull.setTextColor(unSelectedColor)
                }
            }
        }
    }

    /**
     * 显示对应的Setting
     */
    protected fun showRadio(visible: Boolean) {
        if (visible) {

            viewBinding.rlContain.showTopCover(true)
        } else {
            viewBinding.rlContain.showTopCover(false)
        }
    }
}

