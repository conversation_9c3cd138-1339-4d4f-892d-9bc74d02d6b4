package com.commsource.camera.xcamera.util

import android.hardware.Camera
import android.util.Log
import android.util.SparseArray
import com.commsource.ad.ADCache
import com.commsource.beautyplus.R
import com.commsource.beautyplus.constants.FilterConstants
import com.commsource.camera.fastcapture.SelfiePhotoData
import com.commsource.camera.makeup.MakeupHelper
import com.commsource.camera.montage.bean.MontageSuitConfig
import com.commsource.camera.mvp.annotation.CameraCountDownType
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.commsource.camera.mvp.annotation.TakePhotoMode
import com.commsource.camera.xcamera.CameraAnalytics
import com.commsource.camera.xcamera.bean.SwitchCameraMode
import com.commsource.camera.xcamera.burst.BurstMode
import com.commsource.camera.xcamera.cover.bottomFunction.effect.advance.CameraBeautyEType
import com.commsource.camera.xcamera.cover.bottomFunction.effect.advance.ShapeBeautyAlphaCache
import com.commsource.camera.xcamera.cover.bottomFunction.effect.advance.ShapeBeautyFuncDataProvider
import com.commsource.camera.xcamera.cover.bottomFunction.effect.makeup.MakeupUtils
import com.commsource.homev3.HomeRouter
import com.commsource.repository.child.makeup.MakeupMaterial
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.SpmAnalytics
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.DecorateConstant
import com.commsource.util.ResourcesUtils
import com.commsource.util.notnull
import com.meitu.library.hwanalytics.spm.SPMManager
import com.pixocial.androidx.core.utils.isSameDay

class EffectUseLogger private constructor() {

    private val paramsMapForSelfie = HashMap<String, String>(32)

    companion object {
        val instance = EffectUseLoggerHolder.mEffectUseLogger

        /**
         * 获取美妆的数据埋点
         */
        fun getMakeupMap(
            makeupMaterials: SparseArray<MakeupMaterial>?,
            colorMaterials: SparseArray<MakeupMaterial>?,
            faceIndex: Int = -1
        ): HashMap<String, String> {
            var hashMap = HashMap<String, String>()

            for (makeupType in MakeupHelper.newMakeupTypeList) {
                makeupMaterials?.get(makeupType)?.let {
                    //素材ID
                    val makeupStatisticKey = MakeupUtils.getMakeupStatisticKey(makeupType)
                    //滑杆值
                    val makeupSlideStatisticKey = MakeupUtils.getMakeupSlideStatisticKey(makeupType)

                    var color = colorMaterials?.get(makeupType)
                    hashMap[makeupStatisticKey] = it.getCameraStatisticId()
                    //如果人脸Index不为-1 那么需要获取多人脸的Alpha
                    if (faceIndex == -1) {
                        if (MakeupUtils.isMainColorTab(makeupType) && color != null) {
                            hashMap[makeupSlideStatisticKey] = color.getCurrentAlpha().toString()
                        } else {
                            hashMap[makeupSlideStatisticKey] = it.getCurrentAlpha().toString()
                        }
                    } else {
                        if (MakeupUtils.isMainColorTab(makeupType) && color != null) {
                            hashMap[makeupSlideStatisticKey] = color.getFaceAlpha(faceIndex).toString()
                        } else {
                            hashMap[makeupSlideStatisticKey] = it.getFaceAlpha(faceIndex).toString()
                        }
                    }
                    //如果颜色素材不为空 那么就要编辑颜色素材的信息
                    if (color != null) {
                        //美妆名称
                        val makeupStatisticValue = MakeupUtils.getMakeupStatisticValue(makeupType)
                        if (it.isPreset()) {
                            hashMap[makeupStatisticValue + "颜色"] = "Preset"
                        } else {
                            hashMap[makeupStatisticValue + "颜色"] = color.getMakeupID()
                        }
                    }
                }
            }
            return hashMap
        }

        fun getMakeupMap(selfiePhotoData: SelfiePhotoData?): HashMap<String, String> {
            return getMakeupMap(selfiePhotoData?.makeupWrappers, selfiePhotoData?.makeupStyleMaterials)
        }
    }

    private object EffectUseLoggerHolder {
        val mEffectUseLogger = EffectUseLogger()
    }

    fun logSelfieSaveEvent(
        mode: Int,
        selfiePhotoData: SelfiePhotoData? = null,
        imageRecognition: Map<String, String>,
        spmMap: Map<String, String>? = SPMManager.instance.getCurrentSpmInfo()
    ) {

        if (paramsMapForSelfie.isNotEmpty()) {
            // 保存方式
            when (mode) {
                0 -> paramsMapForSelfie[ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_mode)] =
                    ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_tick)

                1 -> paramsMapForSelfie[ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_mode)] =
                    ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_volume)

                2 -> paramsMapForSelfie[ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_mode)] =
                    ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_share)

                3 -> paramsMapForSelfie[ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_mode)] =
                    ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_fast_capture)

                4 -> paramsMapForSelfie[ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_mode)] = "美学评分"
                5 -> paramsMapForSelfie[ResourcesUtils.getString(R.string.meitu_statistics_selfiesave_save_mode)] = "去人像美颜"
                else -> {
                }
            }
            // 保存时重新写入水印。
            if (selfiePhotoData != null) {
                when {
                    selfiePhotoData.waterEntity == null -> "0"
                    selfiePhotoData.waterEntity!!.id == 0 -> paramsMapForSelfie["水印ID"] = "1000"
                    else -> paramsMapForSelfie["水印ID"] = selfiePhotoData.waterEntity!!.id.toString()
                }

                selfiePhotoData.webEntity?.getParameter(HomeRouter.HOME_FUNC_ID)?.let {
                    paramsMapForSelfie["homepage_func_id"] = it
                }

                if (selfiePhotoData.faceCount > 0 && selfiePhotoData.aiBeautyProgress >= 0) {
                    paramsMapForSelfie["AI美颜滑竿值"] = selfiePhotoData.aiBeautyProgress.toString()
                }
                if (selfiePhotoData.isCaptureMode) {
                    val currentTime = System.currentTimeMillis()
                    if (!ADCache.getLastEnterTime(MTAnalyticsConstant.EVENT_SELFIESAVE).isSameDay(currentTime)) {
                        // 重置进入次数
                        ADCache.resetEnterCount(MTAnalyticsConstant.EVENT_SELFIESAVE)
                    }
                    ADCache.setLastEnterTime(MTAnalyticsConstant.EVENT_SELFIESAVE, currentTime)
                    ADCache.addEnterCount(MTAnalyticsConstant.EVENT_SELFIESAVE)
                    paramsMapForSelfie["times"] =
                        ADCache.getEnterCount(MTAnalyticsConstant.EVENT_SELFIESAVE).toString()
                }
                paramsMapForSelfie["特效ID"] = selfiePhotoData.filterId
                paramsMapForSelfie["特效滑竿值"] = (selfiePhotoData.filter?.userAlpha ?: 0).toString()
            }
            if (imageRecognition.isNotEmpty()) {
                paramsMapForSelfie.putAll(imageRecognition)
            }
            spmMap?.let {
                paramsMapForSelfie.putAll(spmMap)
            }
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.EVENT_SELFIESAVE, paramsMapForSelfie)
        }
        Log.i("lyddd","resetApprWhenCapture")
        CameraAnalytics.resetApprWhenCapture()

    }

    fun logSelfieTakeEvent(selfiePhotoData: SelfiePhotoData) {
        val map = generateSelfieEffectParams(selfiePhotoData)
        if (map.isNotEmpty()) {
            paramsMapForSelfie.clear()
            paramsMapForSelfie.putAll(map)
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.EVENT_SELFIETAKEPIC, map)
        }
        val filterId = selfiePhotoData.filterId
        if ((filterId == null || filterId == FilterConstants.ORIGINAL_ID || !filterId.startsWith("BP_FIL_")) && selfiePhotoData.arMaterialId.isNullOrEmpty()) {
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIETAKEPIC_NOARNOFILTER)
        }
        if (filterId == null || filterId == FilterConstants.ORIGINAL_ID) {
            //原图滤镜
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.SELFIETAKEPIC_ONLYFILTER,
                HashMap<String, String>().apply {
                    put("特效ID", FilterConstants.ORIGINAL_ID)
                    put("滤镜分类", "BP_cat_FIL_COL")
                })
        } else if (filterId == FilterConstants.PRESET_FILTER_ID) {
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.SELFIETAKEPIC_ONLYFILTER,
                HashMap<String, String>().apply {
                    put("特效ID", "Preset")
                    put("滤镜分类", "BP_cat_FIL_COL")
                })
        } else if (filterId.startsWith("BP_FIL_")) {
            val catId = selfiePhotoData.filterWrapper?.categoryId ?: ""
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.SELFIETAKEPIC_ONLYFILTER,
                HashMap<String, String>().apply {
                    put("特效ID", filterId)
                    put("滤镜分类", SpmAnalytics.transCategoryId(catId, filterId))
                })
        }
    }


    fun getShapeBeautyUseInfo(): HashMap<String, String> {
        val hashMap = HashMap<String, String>()
        val provider = ShapeBeautyFuncDataProvider.getDataProvider()
        (provider.fetchBeautyFunctionList() + provider.fetchShapeFunctionList().flatMap { it.subList }).forEach {
            when (it.itemType) {
                CameraBeautyEType.Type_Face -> {
                    provider.fetchFuncWithType(ShapeBeautyAlphaCache.getSelectFaceType())?.let {
                        hashMap.put("${it.staticName}", "${if (it.isEnable) it.useAlpha else 0}")
                    }
                }

                CameraBeautyEType.Type_Skin -> {
                    provider.fetchFuncWithType(ShapeBeautyAlphaCache.getSelectSkinType())?.let {
                        hashMap.put("${it.staticName}", "${if (it.isEnable) it.useAlpha else 0}")
                    }
                }

                CameraBeautyEType.Type_BigNose -> {
                    provider.fetchFuncWithType(ShapeBeautyAlphaCache.getSelectNoseType())?.let {
                        hashMap.put("${it.staticName}", "${if (it.isEnable) it.useAlpha else 0}")
                    }
                }

                else -> {
                    hashMap["${it.staticName}"] = "${if (it.isEnable) it.useAlpha else 0}"
                }
            }

        }
        return hashMap
    }



    private fun generateSelfieEffectParams(selfiePhotoData: SelfiePhotoData): HashMap<String, String> {
        val hashMap = HashMap<String, String>(16)
        hashMap.putAll(getShapeBeautyUseInfo())
        // 上报所有的功能的埋点
        if (selfiePhotoData.arMaterialId.isNullOrEmpty()) {
            if (selfiePhotoData.faceCount > 0) {
                hashMap.putAll(getMakeupMap(selfiePhotoData))
            }
            val lookMaterial = selfiePhotoData.lookMaterial
            if (lookMaterial != null) {
                hashMap["Look素材ID"] = lookMaterial.getStatisticId()
                lookMaterial.belongCatId?.let {
                    hashMap["lok_material_tag"] = it
                }
                if (selfiePhotoData.faceCount > 0 && !lookMaterial.isDefaultLook) {
                    hashMap["Look素材性别"] = when (lookMaterial.sex) {
                        0 -> "无"
                        1 -> "男"
                        2 -> "女"
                        else -> "无"
                    }
                    hashMap["Look美妆滑竿值"] = lookMaterial.effectBean?.lookMakeupAlpha.toString()
                }
            }
        }


        if (selfiePhotoData.faceCount > 0) {
            hashMap["男:女"] = "男${selfiePhotoData.maleCount}女${selfiePhotoData.femaleCount}"
        }

        selfiePhotoData.filterWrapper?.let {
            hashMap["特效ID"] = it.filter.id
            hashMap["滤镜分类"] = it.applyFromSpecialCategory
                ?: if (it.filter.id == FilterConstants.ORIGINAL_ID || it.filter.id == FilterConstants.PRESET_FILTER_ID) {
                    SpmAnalytics.transCategoryId(
                        DecorateConstant.NEW_CATEGORY_COLLECT,
                        FilterConstants.ORIGINAL_ID
                    )
                } else {
                    SpmAnalytics.transCategoryId(it.categoryId, FilterConstants.ORIGINAL_ID)
                }
        }
        when {
            selfiePhotoData.isArGiphy -> hashMap["AR素材ID"] = MTAnalyticsConstant.ARGIPHY
            selfiePhotoData.arMaterialId.isNullOrEmpty() -> hashMap["AR素材ID"] = "AR" + 0
            selfiePhotoData.arMaterial?.isMontageAr() == true -> hashMap["AR素材ID"] = "Montage"

            else -> {
                selfiePhotoData.arMaterialId?.let {
                    hashMap["AR素材ID"] = it
                }

            }
        }
        hashMap["是否调整过曝光滑竿"] = if (selfiePhotoData.exportValue == 0) "否" else "是"
        hashMap["人脸识别"] = selfiePhotoData.faceCount.toString()
        hashMap["摄像头"] = if (selfiePhotoData.isFront) "前置" else "后置"
        hashMap["拍照模式"] = if (selfiePhotoData.isFastCapture) "快速自拍" else "正常拍照"
        when (selfiePhotoData.mTakePictureRatio) {
            CameraRatioType.PICTURE_RATIO_1_1 -> hashMap["比例"] = "1:1"
            CameraRatioType.PICTURE_RATIO_FULL -> hashMap["比例"] = "full"
            CameraRatioType.PICTURE_RATIO_4_3 -> hashMap["比例"] = "4:3"
            CameraRatioType.PICTURE_RATIO_9_16 -> hashMap["比例"] = "9:16"
        }
        when (selfiePhotoData.delayMode) {
            CameraCountDownType.TAKEPHOTO_NORMAL -> hashMap["延时方式"] = "不延时"
            CameraCountDownType.TAKEPHOTO_TIMING_3 -> hashMap["延时方式"] = "延时3s"
            CameraCountDownType.TAKEPHOTO_TIMING_6 -> hashMap["延时方式"] = "延时6s"
        }
        when (selfiePhotoData.takePictureBtn) {
            TakePhotoMode.TAKE_PHOTO_MODE_BTN -> hashMap["拍摄按钮方式"] = "点击拍照按钮"
            TakePhotoMode.TAKE_PHOTO_MODE_VOLUME -> hashMap["拍摄按钮方式"] = "音量键拍摄"
            TakePhotoMode.TAKE_PHOTO_MODE_SCREEN -> hashMap["拍摄按钮方式"] = "触屏拍照"
            TakePhotoMode.TAKE_PHOTO_MODE_GESUTRE -> hashMap["拍摄按钮方式"] = "手势拍照"
        }

        hashMap["闪光灯"] = if (selfiePhotoData.isFront) {
            if (selfiePhotoData.isFillLight) {
                "前置开启"
            } else {
                "前置关闭"
            }
        } else {
            if (selfiePhotoData.flashMode != Camera.Parameters.FLASH_MODE_OFF) {
                "后置手电筒"
            } else {
                "后置关闭"
            }
        }
        if (selfiePhotoData.burstMode != BurstMode.BRUST_CLOSE) {
            var burstMode = when(selfiePhotoData.burstMode) {
                BurstMode.BRUST_4 -> "4张"
                BurstMode.BRUST_9 -> "9张"
                BurstMode.BRUST_LONG_PRESS -> "长按拍照"
                else -> ""
            }
            hashMap["连续拍照"] = burstMode
            if (selfiePhotoData.burstIndex > 0) {
                hashMap["连续拍照所在张数"] = selfiePhotoData.burstIndex.toString()
            }
        }

        if (selfiePhotoData.gestureCapture) {
            hashMap["手势拍照"] = "开"
        }

        if (selfiePhotoData.isGrid) {
            hashMap["网格"] = "开"
        } else {
            hashMap["网格"] = "关"
        }
        return hashMap
    }

}