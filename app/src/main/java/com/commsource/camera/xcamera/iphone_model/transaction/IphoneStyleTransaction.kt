package com.commsource.camera.xcamera.iphone_model.transaction

import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneCameraNewViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneWBViewModel
import com.pixocial.framework.cover.AbstractTransaction

class IphoneStyleTransaction : AbstractTransaction() {
    private val iphoneCameraViewModel by lazy { fragmentViewModel(IphoneCameraNewViewModel::class) }
    private val iphoneWBViewModel by lazy { fragmentViewModel(IphoneWBViewModel::class) }

    override fun onAttachTransaction() {
        iphoneWBViewModel.wbProgress.observe(coverContainer.attachActivity) {
            val wbProgress = iphoneWBViewModel.wbProgress.value
            wbProgress?.let {
                iphoneCameraViewModel.updateWbProgress(it)
            }
        }
    }

    override fun onDetachTransaction() {

    }

    override fun onWindowFocusChanged(hasWindowFocus: Boolean) {

    }
}