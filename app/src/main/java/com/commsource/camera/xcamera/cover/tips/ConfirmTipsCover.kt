package com.commsource.camera.xcamera.cover.tips

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.graphics.Rect
import android.text.TextUtils
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.lifecycle.Observer
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverConfirmTipsBinding
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.camera.xcamera.cover.AbsLazyCover
import com.commsource.util.dpf
import com.commsource.util.visible
import com.meitu.library.util.device.DeviceUtils

/**
 * @Desc : 相机提示Cover
 * <AUTHOR> Bear - 2020/4/15
 */
class ConfirmTipsCover : AbsLazyCover<CoverConfirmTipsBinding>() {

    private val tipsViewModel: TipsViewModel by lazy { getViewModel(TipsViewModel::class.java) }

    override fun getLayoutId(): Int {
        return R.layout.cover_confirm_tips
    }

    override fun onLazyCreate() {
        // 普通提示。
        tipsViewModel.confirmFloatingTipsEvent.observe(coverGroup.mActivity, Observer { tips ->
            if (!TextUtils.isEmpty(tips)) {
                showFloatingTips(tips, null, tipsViewModel.confirmFloatingTipsEvent.isLeftToRight)
            }
        })
    }

    override fun initView() {
    }

    override fun initViewModel() {
    }

    override fun onCoverSizeChange(fullRect: Rect, cameraViewPort: Rect) {
        super.onCoverSizeChange(fullRect, cameraViewPort)
        changeFilterTips(cameraViewPort)
    }

    override fun onCameraVisibleSizeChange(cameraViewPort: Rect, fraction: Float) {
        super.onCameraVisibleSizeChange(cameraViewPort, fraction)
        changeFilterTips(cameraViewPort)
    }

    /**
     * 改变FilterTips
     */
    private fun changeFilterTips(cameraViewPort: Rect) {
        var minHeight = DeviceUtils.dip2px(62f)
        if (cameraViewPort.top <= minHeight) {
            mViewBinding?.rlFloating?.translationY = minHeight.toFloat()
        } else {
            mViewBinding?.rlFloating?.translationY = cameraViewPort.top.toFloat()
        }
    }

    /**
     * 显示悬浮Tips
     */
    private fun showFloatingTips(
        tips: String?,
        subTips: String?,
        leftToRight: Boolean = true,
        colorStr: String? = null
    ) {
        createCover()
        tips?.let {

            mViewBinding!!.rlFloating.animate().setStartDelay(0).setListener(null).cancel()
            mViewBinding!!.rlFloating.visible()
            mViewBinding!!.rlFloating.alpha = 0f
            mViewBinding!!.tvFloating.text = tips
            mViewBinding!!.rlFloating.translationX =
                if (leftToRight) (-DeviceUtils.dip2px(30f)).toFloat() else (DeviceUtils.dip2px(30f)).toFloat()
            mViewBinding!!.rlFloating.animate()
                .alpha(1f)
                .setStartDelay(0)
                .translationX(0f)
                .setInterpolator(DecelerateInterpolator())
                .setDuration(1000)
                .setListener(object : AnimatorListenerAdapter() {

                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        mViewBinding!!.rlFloating.animate().setListener(null)
                            .alpha(0f)
                            .translationX(
                                if (leftToRight) 30.dpf() else (-30).dpf()
                            )
                            .setInterpolator(AccelerateInterpolator())
                            .setDuration(1000)
                            .start()
                    }
                })
                .start()
        }
    }

    var translationYValuer = XAnimatorCalculateValuer(0f, DeviceUtils.dip2fpx(50f))

    override fun onViewRotationChange(rotation: Float, fraction: Float) {
        super.onViewRotationChange(rotation, fraction)
        //文案不参与旋转
        // mViewBinding?.rlFloating?.rotation = rotation
        // mViewBinding?.tvTips?.rotation = rotation
        // mViewBinding?.tvTips?.translationY = translationYValuer.caculateValue(if (coverGroup.isDeviceOrientationVertical()) 1 - fraction else fraction)
    }
}