package com.commsource.camera.xcamera.iphone_model.cover

import android.animation.ValueAnimator
import android.graphics.Paint
import android.view.View
import androidx.core.animation.addListener
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.commsource.album.BpAlbumJumpRouter
import com.commsource.album.SelectPhotoInterstitialCover
import com.commsource.album.XAlbum
import com.commsource.album.XAlbumConfig
import com.commsource.album.def.AlbumSource
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverIphoneFunctionBinding
import com.commsource.camera.common.CameraBundleExtras
import com.commsource.camera.common.CameraContainerViewModel
import com.commsource.camera.common.CameraTab
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.util.XAnimator
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.camera.xcamera.CameraCache
import com.commsource.camera.xcamera.bean.SwitchCameraMode
import com.commsource.camera.xcamera.bean.getAnalyticsName
import com.commsource.camera.xcamera.common.ModeSelectorHelper
import com.commsource.camera.xcamera.cover.bottomFunction.BottomFunction
import com.commsource.camera.xcamera.cover.bottomFunction.BottomInInterpolator
import com.commsource.camera.xcamera.cover.bottomFunction.GuideViewModel
import com.commsource.camera.xcamera.iphone_model.IphoneFunction
import com.commsource.camera.xcamera.iphone_model.IphoneModelCache
import com.commsource.camera.xcamera.iphone_model.hd.HighResolutionHelper
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneCameraNewViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneCaptureViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneFunctionViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneSettingViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneWBViewModel
import com.commsource.camera.xcamera.retro.RetroCameraConfig
import com.commsource.camera.xcamera.widget.CustomModeSelector
import com.commsource.config.ApplicationConfig
import com.commsource.statistics.BPAnalyticsSource
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.statistics.trace.TraceManager
import com.commsource.statistics.trace.TraceModule
import com.commsource.studio.ImageStudioActivity
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.AppTools
import com.commsource.util.DeviceLevelStrategy
import com.commsource.util.Function
import com.commsource.util.LOGV_Camera
import com.commsource.util.ResourcesUtils
import com.commsource.util.ViewUtils
import com.commsource.util.XFunctionFragmentHelper
import com.commsource.util.common.ProcessUtil
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.hapticVirtualKey
import com.commsource.util.invisible
import com.commsource.util.resColor
import com.commsource.util.setMarginBottom
import com.commsource.util.sp2dp
import com.commsource.util.string
import com.commsource.util.visible
import com.commsource.videostudio.VideoStudioActivity
import com.commsource.videostudio.VideoStudioEnterSource
import com.meitu.common.AppContext
import com.meitu.library.media.camera.common.Facing
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.pixocial.framework.cover.AbstractViewBindingCover
import com.pixocial.library.albumkit.media.MediaType
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlin.collections.set

/**
 * @Desc : 相机底部功能栏Cover
 */
class IphoneFunctionCover : AbstractViewBindingCover<CoverIphoneFunctionBinding>() {

    private val containerViewModel by lazy { activityViewModel(CameraContainerViewModel::class) }
    private val iphoneFunctionViewModel by lazy { fragmentViewModel(IphoneFunctionViewModel::class) }
    private val iphoneCaptureViewModel by lazy { fragmentViewModel(IphoneCaptureViewModel::class) }
    private val iphoneCameraNewViewModel by lazy { fragmentViewModel(IphoneCameraNewViewModel::class) }
    private val iphoneWBViewModel by lazy { fragmentViewModel(IphoneWBViewModel::class) }
    private val iphoneSettingViewModel by lazy { fragmentViewModel(IphoneSettingViewModel::class) }

    private val bottomFunctionHelper by lazy {
        XFunctionFragmentHelper(
            coverContainer.attachFragment.childFragmentManager,
            R.id.fl_full
        )
    }

    //是否切换到iphone模式  这时候不播放选择分辨率消失动画
    private var isFirstSwitchIphoneModel = true

    private var isFromCreate = false

    override fun getLayoutId(): Int {
        return R.layout.cover_iphone_function
    }

    /**
     * 是否是白色风格
     */
    var isWhiteStyle = false

    private var lastHasWindowFocus = false

    private var guideAnimator: ValueAnimator? = null
    private var animator: XAnimator? = null

    private val captureModeList = listOf(
        CustomModeSelector.ModeItem(
            ResourcesUtils.getString(R.string.camera_video_mode),
            tag = CameraMode.VIDEO_MODE
        ),
        CustomModeSelector.ModeItem(
            ResourcesUtils.getString(R.string.shoot),
            tag = CameraMode.CAPTURE_MODE
        ),
    )

    /**
     * 初始化部分数据
     */
    override fun onViewDidLoad() {
        viewBinding.root.tag = true

        val modeList = ModeSelectorHelper.createCameraTabList()

        isFromCreate = true
        initFunction()
        initClick()
        initObserve()
        fixFuncSize()

        val currentMode =
            if (IphoneModelCache.getIphoneModelCacheEntity().captureMode == CameraMode.VIDEO_MODE) captureModeList[0] else captureModeList[1]
        //设置默认模式
        viewBinding.cst.init(
            captureModeList,
            currentMode,
            object : CustomModeSelector.OnModeChangeListener {
                override fun onTouchEvent(): Boolean {
                    return false
                }

                override fun onMainModePressed(modeItem: CustomModeSelector.ModeItem) {

                }

                override fun onMainModeChanged(
                    lastMode: CustomModeSelector.ModeItem?,
                    newMode: CustomModeSelector.ModeItem,
                    fromUser: Boolean
                ) {
                    if (fromUser) {
                        MTAnalyticsAgent.logEvent(
                            MTAnalyticsConstant.shoot_video_switch,
                            HashMap<String, String>(4).apply {
                                put(
                                    MTAnalyticsConstant.camera_mode,
                                    SwitchCameraMode.IPhone.getAnalyticsName()
                                )
                                (newMode.tag as? Int)?.let { mode ->
                                    when (mode) {
                                        CameraMode.CAPTURE_MODE -> this[MTAnalyticsConstant.next_mode_a] =
                                            "shoot"

                                        CameraMode.VIDEO_MODE -> this[MTAnalyticsConstant.next_mode_a] =
                                            "video"
                                    }
                                }
                            })
                    }
                    // 隐藏从设置面板
                    viewBinding.cst.hapticVirtualKey()
                    iphoneSettingViewModel.showSetting(false)
                    iphoneSettingViewModel.showRadio(false)
                    iphoneSettingViewModel.showHDVideoSelector(false)
                    when (newMode.tag) {
                        CameraMode.CAPTURE_MODE,
                        CameraMode.VIDEO_MODE -> iphoneCaptureViewModel.updateMode(newMode.tag as Int)
                    }
                    if (newMode.tag == CameraMode.VIDEO_MODE) {
                        containerViewModel.requestAudioPermission()
                    }
                    iphoneCameraNewViewModel.logCamAppr()
                }


            })
        iphoneCaptureViewModel.updateMode(currentMode.tag as Int)
        viewBinding.ifvBeauty.setNeedPressState(true)
        registerLifecycleObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                onResume()
            }
        }
        viewBinding.cms.init(
            modeList,
            modeList.find { it.tag == SwitchCameraMode.IPhone }!!,
            object : CustomModeSelector.OnModeChangeListener {
                override fun onTouchEvent(): Boolean {
                    return false
                }

                override fun onMainModePressed(modeItem: CustomModeSelector.ModeItem) {

                }

                override fun onMainModeChanged(
                    lastMode: CustomModeSelector.ModeItem?,
                    newMode: CustomModeSelector.ModeItem,
                    fromUser: Boolean
                ) {
                    if (newMode.tag == SwitchCameraMode.Glow) {
                        updateSource()

                        iphoneCameraNewViewModel.switchCameraMode()
                        containerViewModel.cameraTabEvent.postValue(CameraTab.GLOW)
                        CameraCache.setCameraModel(CameraCache.getDefaultCamera())

                        if (fromUser) {
                            logSelectMode(newMode, lastMode)
                        }
                    } else if (newMode.tag == SwitchCameraMode.Retro) {
                        updateSource()
                        RetroCameraConfig.setCaptureMode(CameraMode.CAPTURE_MODE)

                        iphoneCameraNewViewModel.switchCameraMode()
                        containerViewModel.cameraTabEvent.postValue(CameraTab.RETRO)
                        CameraCache.setCameraModel(CameraCache.getDefaultCamera())

                        if (fromUser) {
                            logSelectMode(newMode, lastMode)
                        }
                    } else {
                        ModeSelectorHelper.onCameraModeChanged(
                            fromUser,
                            lastMode,
                            newMode,
                            iphoneCaptureViewModel.getMode(),
                            coverContainer.attachFragment.requireActivity(),
                            iphoneCameraNewViewModel.currentZoom
                        )
                        IphoneModelCache.updateIphoneModelEntity()
                    }
                }
            })
    }

    private fun updateSource() {
        coverContainer.attachActivity.intent.putExtra(
            CameraBundleExtras.CAMERA_SOURCE,
            BPAnalyticsSource.Source.CameraPage.sourceString
        )
    }

    private fun logSelectMode(
        newMode: CustomModeSelector.ModeItem,
        lastMode: CustomModeSelector.ModeItem?
    ) {
        MTAnalyticsAgent.logEvent(
            MTAnalyticsConstant.selfie_camera_clk,
            HashMap<String, String>(4).apply {
                when (iphoneCaptureViewModel.getMode()) {
                    CameraMode.MOVIE_MODE -> this["mode_a"] = "movie"
                    CameraMode.CAPTURE_MODE -> this["mode_a"] = "shoot"
                    CameraMode.VIDEO_MODE -> this["mode_a"] = "video"
                }
                (newMode.tag as? SwitchCameraMode)?.getAnalyticsName()?.let {
                    put(MTAnalyticsConstant.camera_mode, it)
                }
                (lastMode?.tag as? SwitchCameraMode)?.getAnalyticsName()?.let {
                    put(MTAnalyticsConstant.pre_camera_mode, it)
                }
            })
    }

    private fun initFunction() {
        val deviceLevel = DeviceLevelStrategy.getDeviceLevelForIphoneCamera()
        when (deviceLevel) {
            DeviceLevelStrategy.LOW -> {
                "wxy 低端机".LOGV_Camera()
                viewBinding.llStyle.visible()
                viewBinding.llLight.gone()
                viewBinding.llWB.gone()
                viewBinding.llNight.visible()
            }

            DeviceLevelStrategy.MID -> {
                "wxy 中端机".LOGV_Camera()
                viewBinding.llStyle.visible()
                viewBinding.llLight.gone()
                viewBinding.llWB.visible()
                viewBinding.llNight.visible()
            }

            DeviceLevelStrategy.HIGH -> {
                "wxy 高端机".LOGV_Camera()
                viewBinding.llStyle.visible()
                viewBinding.llLight.visible()
                viewBinding.llWB.visible()
                viewBinding.llNight.visible()
            }

            DeviceLevelStrategy.VHIGH -> {
                "wxy 超高端机".LOGV_Camera()
                viewBinding.llStyle.visible()
                viewBinding.llLight.visible()
                viewBinding.llWB.visible()
                viewBinding.llNight.visible()
            }
        }
    }

    private fun fixFuncSize() {
        var topTextSize = 12f
        val topPaint = Paint()
        topPaint.textSize = topTextSize.sp2dp()


        var bottomTextSize = 8f
        val bottomPaint = Paint()
        bottomPaint.textSize = bottomTextSize.sp2dp()

        var bottomFontMetrics = bottomPaint.fontMetrics
        var actualBottomTextHeight =
            bottomFontMetrics.descent - bottomFontMetrics.ascent

        var topFontMetrics = topPaint.fontMetrics
        var actualTopTextHeight =
            topFontMetrics.descent - topFontMetrics.ascent


        "topTextSize $topTextSize  actualTopTextHeight $actualTopTextHeight  actualBottomTextHeight $actualBottomTextHeight  ${26.dpf}".LOGV_Camera()
        while (actualTopTextHeight + actualBottomTextHeight > 24.dpf) {
            topTextSize -= 0.5f
            topPaint.textSize = (topTextSize).sp2dp()
            topFontMetrics = topPaint.fontMetrics

            bottomTextSize -= 0.5f
            bottomPaint.textSize = (bottomTextSize).sp2dp()
            bottomFontMetrics = bottomPaint.fontMetrics
            actualBottomTextHeight =
                bottomFontMetrics.descent - bottomFontMetrics.ascent

            actualTopTextHeight = topFontMetrics.descent - topFontMetrics.ascent
        }

        "topTextSize $topTextSize  bottomTextSize $bottomTextSize".LOGV_Camera()
        viewBinding.style.textSize = topTextSize
        viewBinding.light.textSize = topTextSize
        viewBinding.wb.textSize = topTextSize
        viewBinding.night.textSize = topTextSize

        viewBinding.tvStyle.textSize = bottomTextSize
        viewBinding.tvLight.textSize = bottomTextSize
        viewBinding.tvWB.textSize = bottomTextSize
        viewBinding.tvNight.textSize = bottomTextSize
    }

    private fun initObserve() {

        // 动态调整取景框的高度
        iphoneSettingViewModel.screenRatioChangeEvent.observe(lifecycleOwner) {
            iphoneFunctionViewModel.bottomSizeParamEvent.value?.let {
                viewBinding.clFloat.setMarginBottom(
                    iphoneSettingViewModel.getBottomPanelHeight()
                        .coerceAtLeast(it.mBottomBarHeight) - it.mBottomBarHeight
                )
            }
        }

        //相机预览比例切换
        iphoneSettingViewModel.screenRatioChangeEvent.observe(lifecycleOwner) { ratio ->
            when (ratio) {
                CameraRatioType.PICTURE_RATIO_1_1 -> {
                    viewBinding.ifvBeauty.text = R.string.selfie_main_icon_beauty_line.string()
                    changeBottomStyle(ratio, false)
                }

                CameraRatioType.PICTURE_RATIO_4_3 -> {
                    viewBinding.ifvBeauty.text = R.string.selfie_main_icon_beauty_line.string()
                    changeBottomStyle(ratio, false)
                }

                CameraRatioType.PICTURE_RATIO_9_16 -> {
                    viewBinding.ifvBeauty.text =
                        R.string.selfie_main_icon_beauty_shape_border.string()
                    changeBottomStyle(ratio, true)
                }

                CameraRatioType.PICTURE_RATIO_FULL -> {
                    viewBinding.ifvBeauty.text =
                        R.string.selfie_main_icon_beauty_shape_border.string()
                    changeBottomStyle(ratio, true)
                }
            }
        }

        //底部size切换
        iphoneFunctionViewModel.bottomSizeParamEvent.observe(lifecycleOwner) {
            it?.let {
                //高度变化
                ViewUtils.setHeight(viewBinding.flBottomBar, it.mBottomBarHeight)
                //如果是超小尺寸
                if (IphoneFunctionViewModel.BottomSizeParam.isMinBottomBar) {
                    viewBinding.cst.translationY = 12.dpf()
                }
                iphoneSettingViewModel.bottomSizeParam = it
                viewBinding.clFloat.setMarginBottom(
                    iphoneSettingViewModel.getBottomPanelHeight()
                        .coerceAtLeast(it.mBottomBarHeight) - it.mBottomBarHeight
                )
            }
        }

        iphoneCaptureViewModel.cameraCaptureModeEvent.observe(lifecycleOwner) { mode ->
            captureModeList.find { it.tag == mode }?.let {
                viewBinding.cst.switchModeWithoutAnim(it)
            }

            HighResolutionHelper.captureMode = mode
            if (HighResolutionHelper.isAppleHDTest()) {
                updateRatio()
            }
            if (iphoneCaptureViewModel.isVideoMode()) {
                iphoneCameraNewViewModel.forceApplyDefaultCameraParams(false)
            } else {
                iphoneCameraNewViewModel.forceApplyDefaultCameraParams(true)
            }
        }

        iphoneFunctionViewModel.bottomFunctionChangeEvent.observe(lifecycleOwner) { bottomFunction ->

            viewBinding.root.id = R.id.fl_full
            when (bottomFunction) {
                IphoneFunction.BEAUTY, IphoneFunction.STYLE -> {
                    viewBinding.clFunc.gone()
                    viewBinding.clModel.gone()
                }

                else -> {
                    viewBinding.clFunc.visible()
                    viewBinding.clModel.visible()
                }
            }

            //打光，白平衡，补光 是需要显示底部的function列表的, 这里是为了协议跳转来，确认状态
            if (!isFuncShow && bottomFunction != null && bottomFunction != IphoneFunction.BEAUTY && bottomFunction != IphoneFunction.STYLE) {
                showFunc()
            }
            changeBottomFuncColor(bottomFunction)
            showBottom(bottomFunction)
            // 通知一下
            iphoneFunctionViewModel.dispatchFunctionChangeEvent.value = bottomFunction

        }
        //视频切换
        iphoneCaptureViewModel.cameraVideoChangeEvent.observe(lifecycleOwner) {
            when (it) {
                IphoneCaptureViewModel.NORMAL -> {
                    //BugFix:如果没有底部栏功能 才显示switchBar
                    if (!iphoneFunctionViewModel.hasBottomFunction()) {
                        if (!iphoneCaptureViewModel.isInAppCaptureMode()) {
                            viewBinding.llAlbum.visible()
                            viewBinding.llBeauty.visible()
                            viewBinding.ivScreenShot.gone()
                            viewBinding.cst.visible()
                            viewBinding.flAtv.visible()
                            viewBinding.funcContainer.visible()
                        }
                    }
                    if (iphoneCameraNewViewModel.cameraFacingLiveData.value == Facing.BACK) {
                        viewBinding.tvZoom.visible()
                    }
                }

                IphoneCaptureViewModel.START_RECORDING -> {

                }

                IphoneCaptureViewModel.RECORDING -> {
                    viewBinding.llAlbum.gone()
                    viewBinding.llBeauty.gone()

                    if (iphoneCaptureViewModel.cameraCaptureModeEvent.value == CameraMode.VIDEO_MODE) {
                        viewBinding.ivScreenShot.visible()
                    } else {
                        if (iphoneCameraNewViewModel.cameraFacingLiveData.value == Facing.BACK && iphoneCaptureViewModel.isCaptureMode()) {
                            viewBinding.tvZoom.gone()
                        }
                    }
                    viewBinding.cst.gone()
                    viewBinding.flAtv.gone()
                    viewBinding.funcContainer.gone()
                }

                IphoneCaptureViewModel.STOP_RECORDING -> {
                }
            }
        }

        //图片保存事件 如果有图标被保存 那么直接使用作为预览图
        iphoneCaptureViewModel.albumAddImageEvent.observe(lifecycleOwner) {
            it?.let {
                viewBinding.mIvAlbumNew.previewNext(it)
            }
        }

        // 摄像头改变。
        iphoneCameraNewViewModel.cameraFacingLiveData.observe(lifecycleOwner) {
            if (it == Facing.FRONT) {
                viewBinding.tvZoom.invisible()
            } else {
                viewBinding.tvZoom.visible()
            }
            isFirstSwitchIphoneModel = false
        }

        iphoneFunctionViewModel.floatShowEvent.observe(lifecycleOwner) {
            if (it) {
                viewBinding.clFloat.visible()
            } else {
                viewBinding.clFloat.gone()
            }
        }

        iphoneCameraNewViewModel.pinchZoomLiveData.observe(lifecycleOwner) {
            viewBinding.tvZoom.text = formatFloat(it)
        }

        iphoneCameraNewViewModel.currentIphoneStylePreset.observe(lifecycleOwner) {
            it?.let {
                viewBinding.tvStyle.text = it.displayName
            }
        }

        iphoneCameraNewViewModel.currentIphoneReLight.observe(lifecycleOwner) {
            it?.let {
                viewBinding.tvLight.text = it.displayName
            }
        }

        iphoneCameraNewViewModel.currentIphoneNight.observe(lifecycleOwner) {
            it?.let {
                viewBinding.tvNight.text = it.toString()
            }
        }

        iphoneCameraNewViewModel.currentIphoneWB.observe(lifecycleOwner) {
            it?.let {
                iphoneWBViewModel.applyWBMode(it)
                viewBinding.tvWB.text = ResourcesUtils.getString(it.displayNameId)
            }
        }

        iphoneFunctionViewModel.funcAnimFlow.onEach {
            showFuncAnim()
        }.launchIn(iphoneFunctionViewModel.viewModelScope)

        iphoneSettingViewModel.layoutShiftLiveData.observe(lifecycleOwner) { layoutShift ->
            val bottomModeShiftDistance =
                layoutShift?.takeIf { it.shouldShift }?.bottomModeShiftDistance ?: return@observe

            // 如果该控件没有偏移过，才进行偏移，避免影响别的业务逻辑
            if (viewBinding.cst.translationY == 0f) {
                viewBinding.cst.translationY = bottomModeShiftDistance
            }
        }
    }

    /**
     * 视频/拍照模式切换
     * 需要使用当前模式下的 ratio 重新设置一下，否则 forceApplyDefaultCameraParams 会使用上一次的 ratio
     */
    private fun updateRatio() {
        val ratio = IphoneModelCache.getIphoneModelCacheEntity().ratio
        iphoneCameraNewViewModel.switchPictureRatio(ratio, false)
    }

    private fun initClick() {
        //美颜、美妆
        if (IphoneModelCache.shouldShowBeautyRedDot()) {
            viewBinding.vRedDot.visible()
        }
        viewBinding.llBeauty.setOnClickListener {
            if (IphoneModelCache.shouldShowBeautyRedDot()) {
                IphoneModelCache.consumeBeautyRedDot()
            }
            viewBinding.vRedDot.gone()
            iphoneFunctionViewModel.show(IphoneFunction.BEAUTY)
            iphoneFunctionViewModel.logFuncCategoryClick("美颜")
        }

        viewBinding.ivScreenShot.setOnClickListener {
            viewBinding.ivScreenShot.isEnabled = false
            iphoneCaptureViewModel.captureOneFrame {
                viewBinding.ivScreenShot.isEnabled = true
            }
        }
        // 风格
        viewBinding.llStyle.setOnClickListener {
            //刷新一下页面
            iphoneSettingViewModel.showGridLineEvent.postValue(IphoneModelCache.getIphoneModelCacheEntity().showGrid)
            iphoneFunctionViewModel.show(IphoneFunction.STYLE, true)
            iphoneFunctionViewModel.logFuncCategoryClick("风格")
        }
        // 打光
        viewBinding.llLight.setOnClickListener {
            iphoneFunctionViewModel.show(IphoneFunction.RELIGHT, true)
            iphoneFunctionViewModel.logFuncCategoryClick("打光")
        }
        // 白平衡
        viewBinding.llWB.setOnClickListener {
            iphoneFunctionViewModel.show(IphoneFunction.WHITE_BALANCE, true)
            iphoneFunctionViewModel.logFuncCategoryClick("白平衡")
        }
        // 补光
        viewBinding.llNight.setOnClickListener {
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.iphone_mode_light_filling_clk
            )
            iphoneFunctionViewModel.show(IphoneFunction.FILL_LIGHT, true)
        }

        //相册
        viewBinding.llAlbum.setOnClickListener {
            // 设置切换动画
            coverContainer.attachActivity.overridePendingTransition(
                R.anim.push_up_in,
                R.anim.push_up_out
            )
            when {
                iphoneCaptureViewModel.isCaptureMode() -> MTAnalyticsAgent.logEvent(
                    MTAnalyticsConstant.selfiepage_enter_album,
                    HashMap<String, String>(4).apply {
                        put(
                            MTAnalyticsConstant.camera_mode,
                            SwitchCameraMode.IPhone.getAnalyticsName()
                        )
                        put(MTAnalyticsConstant.mode_s, "shoot")
                    }
                )

                iphoneCaptureViewModel.isVideoMode() -> MTAnalyticsAgent.logEvent(
                    MTAnalyticsConstant.selfiepage_enter_album,
                    HashMap<String, String>(4).apply {
                        put(
                            MTAnalyticsConstant.camera_mode,
                            SwitchCameraMode.IPhone.getAnalyticsName()
                        )
                        put(MTAnalyticsConstant.mode_s, "video")
                    }
                )
            }

            if (AppTools.isDebug() && ApplicationConfig.getCameraEffectCheckSwitch(AppContext.context)) {
                // 如果是测试环境自拍效果核对，走这里的选图逻辑，回来后会替换相机的输入源为图片
                BpAlbumJumpRouter.selectImageForCameraEffectCheck(coverContainer.attachActivity) {
                    it.mediaPath?.let {
                        iphoneCameraNewViewModel.setTestInputImage(it)
                    }
                }
            } else {
                XAlbum.toAlbum(
                    coverContainer.attachActivity,
                    XAlbumConfig(videoEnable = true, gifEnable = true).apply {
                        directDisplayPreview = true
                        enablePreviewJumpEdit = true
                        enableSelectGif = true
                        keepAlbum = true
                        source = AlbumSource.BeautyCamera
                        selectPhotoInterstitialCover = SelectPhotoInterstitialCover()
                    }) {
                    it.getOrNull(0)?.let { mediaInfo ->
                        if (mediaInfo.mediaType == MediaType.VIDEO || mediaInfo.isGif()) {
                            //视频 or gif 前往视频编辑
                            TraceManager.pop(TraceModule.CAMERA)
                            VideoStudioActivity.start(
                                coverContainer.attachActivity, arrayListOf(mediaInfo),
                                VideoStudioActivity.infusionSource
                                    ?: VideoStudioEnterSource.VideoSource.VIDEO_CONFIRM_BTN
                            )
                        } else {
                            mediaInfo.mediaPath?.let {
                                TraceManager.pop(TraceModule.CAMERA)
                                ImageStudioActivity.startActivity(
                                    coverContainer.attachActivity, it,
                                    null,
                                    ImageStudioViewModel.FROM_CAMERA
                                )
                            }
                        }
                    }
                }
            }
        }

        viewBinding.clAtv.setOnClickListener {
            viewBinding.atv.switch()
            if (viewBinding.atv.isDown) {
                showFunc()
                MTAnalyticsAgent.logEvent(MTAnalyticsConstant.iphone_mode_func_show_clk)
            } else {
                showMode()
            }
        }

        viewBinding.tvZoom.setOnClickListener {
            if (!ProcessUtil.isProcessing(300)) {
                viewBinding.tvZoom.text = iphoneCameraNewViewModel.switchZoom2OriOrDouble(
                    iphoneCaptureViewModel.getMode()
                )
            }
        }
    }

    /**
     * 初始化数据
     */
    override fun onBindViewModel() {
        iphoneCameraNewViewModel.deviceOrientationFlow.onEach { rotation ->
            viewBinding.ifvBeauty.rotation = rotation
            viewBinding.mIvAlbumNew.rotation = rotation

            if ((rotation + 360) % 360 >= 90) {
                viewBinding.tvAlbum.invisible()
                viewBinding.tvBeauty.invisible()
            } else {
                viewBinding.tvAlbum.visible()
                viewBinding.tvBeauty.visible()
            }
        }.launchIn(lifecycleOwner.lifecycleScope)
    }

    /**
     * 是有有显示底部
     */
    var hasShowBottom = false

    val guideViewModel by lazy { fragmentViewModel(GuideViewModel::class) }

    /**
     * 显示具体某个Bottom
     *
     * @param function
     */
    private fun showBottom(function: IphoneFunction?) {
        if (function == null) {
            bottomFunctionHelper.showFunction(null)
            iphoneFunctionViewModel.floatShowEvent.value = true
        } else {
            bottomFunctionHelper.showFunction(Function(function.tag, function.fgClass))
            iphoneFunctionViewModel.floatShowEvent.value = false
        }
        val hasBottom = function != null
        if (hasShowBottom == hasBottom) {
            return
        }
        hasShowBottom = hasBottom
        when (function) {
            IphoneFunction.WHITE_BALANCE, IphoneFunction.FILL_LIGHT, IphoneFunction.RELIGHT -> {
                hasShowBottom = false
            }

            else -> {
            }
        }
        bottomAnimator.cancel()
        bottomAnimator.start()
    }

    val transitionYValuer = XAnimatorCalculateValuer(0f)
    val alphaValuer = XAnimatorCalculateValuer(1f)
    private val bottomAnimator: XAnimator = XAnimator.ofFloat(0f, 1f)
        .duration(BottomFunction.BOTTOM_DURATION)
        .interpolator(BottomInInterpolator())
        .setAnimationListener(object : XAnimator.SimpleAnimationListener() {

            override fun onAnimationUpdate(fraction: Float, value: Float) {
                viewBinding.rlCameraBar.translationY = transitionYValuer.calculateValue(fraction)
                viewBinding.rlCameraBar.alpha = alphaValuer.calculateValue(fraction)
            }

            override fun onAnimationStart(animation: XAnimator?) {
                if (hasShowBottom) {
                    //虽然已经使用动画 但是还是使用gone处理
                    alphaValuer.to(0f)
                    transitionYValuer.to(50f.dpf())
                    viewBinding.cst.gone()
                    viewBinding.rlCameraBar.gone()
                } else {
                    alphaValuer.to(1f)
                    transitionYValuer.to(0f)
                    viewBinding.rlCameraBar.visible()
                    if (!iphoneCaptureViewModel.isRecording()
                        && !iphoneCaptureViewModel.isInAppCaptureMode()
                        && !iphoneCaptureViewModel.isInMultiRecording()
                    ) {
                        viewBinding.cst.visible()
                    }
                }
            }
        })

    /**
     * 拦截返回键
     */
    override fun onDispatchBackPressed(): Boolean {
        if (iphoneFunctionViewModel.hasBottomFunction()) {
            iphoneFunctionViewModel.show(null)
            coverContainer.postDelayed({
                iphoneSettingViewModel.showGridLineEvent.postValue(IphoneModelCache.getIphoneModelCacheEntity().showGrid)
            }, 100)
            return true
        }
        return super.onDispatchBackPressed()
    }

    /**
     * 改变底部颜色风格
     */
    private fun changeBottomStyle(ratio: Int, isWhiteStyle: Boolean) {
        viewBinding.cst.setIsFullScreen(ratio == CameraRatioType.PICTURE_RATIO_FULL)
        if (ratio == CameraRatioType.PICTURE_RATIO_FULL || ratio == CameraRatioType.PICTURE_RATIO_9_16) {
            viewBinding.mIvAlbumNew.switchIconFullScreenStyle(true)
        } else {
            viewBinding.mIvAlbumNew.switchIconFullScreenStyle(false)
        }
        if (this.isWhiteStyle != isWhiteStyle) {
            this.isWhiteStyle = isWhiteStyle
        }
    }

    fun onResume() {
        viewBinding.mIvAlbumNew.init(isAnimator = isFromCreate)
        if (isFromCreate) {
            isFromCreate = false
        }
    }

    override fun onDispatchPermissionResult(
        results: List<Pair<String, Boolean>>?,
        isRequestResult: Boolean
    ) {
        if (isRequestResult) {
            viewBinding.mIvAlbumNew.init()
        }
    }

    private fun showFuncAnim() {
        guideAnimator?.cancel()
        guideAnimator = ValueAnimator.ofFloat(0.dpf, (-42).dpf).setDuration(500)
        guideAnimator?.let { anim ->
            anim.repeatMode = ValueAnimator.REVERSE
            anim.repeatCount = 1
            anim.addUpdateListener {
                (it.animatedValue as? Float)?.let {
                    viewBinding.clFunc.translationY = it
                    viewBinding.clModel.translationY = it
                }
            }
            anim.addListener(
                onStart = {
                    clickInterceptor(true)
                    viewBinding.atv.switch()
                    viewBinding.clFunc.visible()
                    viewBinding.clModel.visible()
                },
                onEnd = {
                    clickInterceptor(false)
                    viewBinding.clFunc.invisible()
                },
                onRepeat = {
                    it.pause()
                    viewBinding.root.postDelayed({
                        it.resume()
                        viewBinding.atv.switch()
                    }, 1000)
                }
            )
        }
        guideAnimator?.start()
    }

    private fun clickInterceptor(interceptor: Boolean) {
        iphoneFunctionViewModel.setInterceptor(interceptor)
        viewBinding.flBlocker.visibility = if (interceptor) View.VISIBLE else View.GONE
    }

    //======================切换功能区和模式相关动画

    private var isFuncShow = false
    private fun showFunc() {
        isFuncShow = true
        animator?.cancel()
        animator = XAnimator.ofFloat(0.dpf, -42.dpf)
            .duration(500)
            .setAnimationListener(
                object : XAnimator.SimpleAnimationListener() {
                    override fun onAnimationEnd(animation: XAnimator?) {
                        viewBinding.clModel.invisible()
                    }

                    override fun onAnimationUpdate(fraction: Float, value: Float) {
                        viewBinding.clFunc.translationY = value
                        viewBinding.clModel.translationY = value
                    }

                    override fun onAnimationStart(animation: XAnimator?) {
                        viewBinding.clFunc.visible()
                        viewBinding.clModel.visible()
                    }
                }
            )
        animator?.start()
    }

    private fun showMode() {
        isFuncShow = false
        animator?.cancel()
        animator = XAnimator.ofFloat(-42.dpf, 0.dpf)
            .duration(500)
            .setAnimationListener(
                object : XAnimator.SimpleAnimationListener() {

                    override fun onAnimationStart(animation: XAnimator?) {
                        super.onAnimationStart(animation)
                        viewBinding.clFunc.visible()
                        viewBinding.clModel.visible()
                    }

                    override fun onAnimationEnd(animation: XAnimator?) {
                        viewBinding.clFunc.invisible()
                    }

                    override fun onAnimationUpdate(fraction: Float, value: Float) {
                        viewBinding.clFunc.translationY = value
                        viewBinding.clModel.translationY = value
                    }
                }
            )
        animator?.start()
    }

    private fun changeBottomFuncColor(iphoneFunction: IphoneFunction?) {
        viewBinding.style.setTextColor(R.color.white.resColor())
        viewBinding.tvStyle.setTextColor(R.color.white40.resColor())
        viewBinding.light.setTextColor(R.color.white.resColor())
        viewBinding.tvLight.setTextColor(R.color.white40.resColor())
        viewBinding.wb.setTextColor(R.color.white.resColor())
        viewBinding.tvWB.setTextColor(R.color.white40.resColor())
        viewBinding.night.setTextColor(R.color.white.resColor())
        viewBinding.tvNight.setTextColor(R.color.white40.resColor())
        iphoneFunction?.let {
            when (iphoneFunction) {
                IphoneFunction.STYLE -> {
                    viewBinding.style.setTextColor(R.color.color_fbc73c.resColor())
                    viewBinding.tvStyle.setTextColor(R.color.color_fbc73c_66.resColor())
                }

                IphoneFunction.RELIGHT -> {
                    viewBinding.light.setTextColor(R.color.color_fbc73c.resColor())
                    viewBinding.tvLight.setTextColor(R.color.color_fbc73c_66.resColor())
                }

                IphoneFunction.WHITE_BALANCE -> {
                    viewBinding.wb.setTextColor(R.color.color_fbc73c.resColor())
                    viewBinding.tvWB.setTextColor(R.color.color_fbc73c_66.resColor())
                }

                IphoneFunction.FILL_LIGHT -> {
                    viewBinding.night.setTextColor(R.color.color_fbc73c.resColor())
                    viewBinding.tvNight.setTextColor(R.color.color_fbc73c_66.resColor())
                }

                else -> {
                }
            }
        }
    }

    private fun formatFloat(number: Float): String {
        return "${
            (if (number % 1 == 0f) {
                number.toInt().toString() // 如果小数部分为0，仅显示整数
            } else {
                String.format("%.1f", number) // 保留一位小数
            })
        }x"
    }

    override fun onWindowFocusChanged(hasWindowFocus: Boolean) {
        if (hasWindowFocus) {
            if (lastHasWindowFocus) {
                // 重新设置补光效果
                var progress =
                    IphoneModelCache.getIphoneModelCacheEntity().fillLightProgress
                progress += 1
                iphoneCameraNewViewModel.updateIphoneFillLight(progress, false)
                if (progress <= 0) {
                    progress = 1
                }
                if (progress >= 100) {
                    progress = 99
                }
                iphoneCameraNewViewModel.updateIphoneFillLight(progress, false)
                "重新设置补光 $progress ".LOGV_Camera()
            }
            lastHasWindowFocus = true
        }
    }
}