package com.commsource.camera.xcamera.glow_model.cover

import android.util.Log
import android.view.KeyEvent
import android.view.MotionEvent
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverGlowCaptureBinding
import com.commsource.billing.SubSource
import com.commsource.camera.common.CameraContainerViewModel
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.util.XAnimator
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.camera.xcamera.cover.bottomFunction.BottomFunction
import com.commsource.camera.xcamera.cover.bottomFunction.BottomInInterpolator
import com.commsource.camera.xcamera.glow_model.GlowCameraViewModel
import com.commsource.camera.xcamera.glow_model.viewmodel.GlowBottomFunctionViewModel
import com.commsource.camera.xcamera.glow_model.viewmodel.GlowCaptureStateViewModel
import com.commsource.camera.xcamera.glow_model.viewmodel.GlowSettingViewModel
import com.commsource.camera.xcamera.widget.GlowCaptureView
import com.commsource.mtmvcore.DeviceAdaptHelper
import com.commsource.util.LOGV_Camera
import com.commsource.util.ViewUtils
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.delegate.process.SubscribeProcess
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.invisible
import com.commsource.util.setSize
import com.commsource.util.visible
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.camerasuite.camera.core.service.notifier.VideoNotifier
import com.pixocial.framework.cover.AbstractViewBindingCover
import kotlinx.coroutines.launch

/**
 * @Desc : 相机底部按钮Cover
 *
 * 主要控制底部按钮动画切换的Cover 模式切换等
 */
class GlowCaptureCover : AbstractViewBindingCover<CoverGlowCaptureBinding>() {

    val glowCameraViewModel by lazy { fragmentViewModel(GlowCameraViewModel::class) }

    val glowBottomFunctionViewModel by lazy { fragmentViewModel(GlowBottomFunctionViewModel::class) }

    val glowSettingViewModel by lazy { fragmentViewModel(GlowSettingViewModel::class) }

    /**
     * 相机拍照ViewModel
     */
    val glowCaptureStateViewModel by lazy { fragmentViewModel(GlowCaptureStateViewModel::class) }

    private val containerViewModel by lazy { activityViewModel(CameraContainerViewModel::class) }


    override fun getLayoutId(): Int {
        return R.layout.cover_glow_capture
    }

    override fun onViewDidLoad() {
        registerLifecycleObserver { _, event ->
            if (event == Lifecycle.Event.ON_PAUSE) {
                viewBinding.cb.onPause()
                viewBinding.cb.isEnabled = true
            }
        }

        viewBinding.cb.setSupportVideo(DeviceAdaptHelper.supportHDRecorder())

        glowBottomFunctionViewModel.bottomSizeParamEvent.observe(lifecycleOwner) {
            ViewUtils.setHeight(viewBinding.flBottomBar, it.mBottomBarHeight)
        }

        // 动态调整取景框的高度
        glowSettingViewModel.screenRatioChangeEvent.observe(lifecycleOwner) {
            glowBottomFunctionViewModel.bottomSizeParamEvent.value?.let {
                viewBinding.flBottomBar.setSize(height = it.mBottomBarHeight)
            }
        }
        viewBinding.cb.onDisableChecked = {
            val canCapture = canCapture()
            if (!canCapture) {
                glowCameraViewModel.toProEvent.value = true
            }
            !canCapture
        }

        glowCameraViewModel.updateTraceInfoEvent.observe(lifecycleOwner) {
            containerViewModel.updateTraceInfoEvent.value = it
        }

        glowCameraViewModel.savePictureEvent.observe(lifecycleOwner) {
            if (it) {
                glowCameraViewModel.tryConsumeFreeLimit()
            }
        }
    }

    override fun onBindViewModel() {
        glowBottomFunctionViewModel.hideCaptureBtnEvent.observe(lifecycleOwner) {
            if (it) {
                viewBinding.cb.invisible()
            } else {
                viewBinding.cb.visible()
            }
        }


        //相机模式切换
        glowCaptureStateViewModel.cameraCaptureModeEvent.observe(
            lifecycleOwner,
            Observer { mode ->
                //拍照按钮变化单独一次 内部有切换动画
                viewBinding.cb.setCameraMode(mode)
                glowCaptureStateViewModel.updateSpm(mode == CameraMode.CAPTURE_MODE)
            })


        //重置相机按钮
        glowCaptureStateViewModel.cameraResetEvent.observe(lifecycleOwner, Observer {
            it?.let {
                viewBinding.cb.isEnabled = true
                viewBinding.cb.reset()
            }
        })

        //录制视频状态回调
        glowCameraViewModel.fetchVideoRecordingEvent()?.observe(lifecycleOwner) {
            if (it is VideoNotifier.VideoState.Recording || it is VideoNotifier.VideoState.StartRecord) {
                if (it is VideoNotifier.VideoState.Recording) {
                    glowCameraViewModel.currentVideoData?.duration = it.time
                    viewBinding.cb.updateRecordTime(it.time)
                }
            } else {
                if (it is VideoNotifier.VideoState.RecordComplete) {
                    try {
                        //保存视频
                        glowCameraViewModel.saveVideo(it.data) { savePath ->
                            glowCameraViewModel.tryConsumeFreeLimit()
                            glowCameraViewModel.previewEvent.value = savePath
                        }
                    } catch (e: Exception) {

                        e.printStackTrace()
                    }
                }
                viewBinding.cb.reset()
            }
        }


        //监听底部变化
        glowBottomFunctionViewModel.bottomFunctionChangeEvent.observe(
            lifecycleOwner,
            Observer { function ->
                //相机按钮在切换时 对应显示状态
                if (function != null) {
                    expandCameraButton(false)

                } else {
                    expandCameraButton(true)
                }

            })


        //相机按钮UI 回调监听
        viewBinding.cb.setCallback(object : GlowCaptureView.CameraButtonCallback {
            override fun onTakePicture(mode: Int) {
                "btj 发起拍照流程".LOGV_Camera()
                //发起拍照流程
                val action = {
                    startCapture()
                }
                goToSubscribe(action)
            }
        })

        //视频录制UI手势 回调
        viewBinding.cb.videoRecordListener = object : GlowCaptureView.VideoRecordListener {

            override fun onStartRecord() {
                if (!containerViewModel.requestAudioPermission()) {
                    "btj 发起录视频流程".LOGV_Camera()

                    val action = {
                        startRecordVideo()
                    }
                    goToSubscribe(action)
                }
            }

            override fun onStopRecord(isMax: Boolean, needStop: Boolean): Boolean {
                if (needStop) {
                    stopRecordVideo()
                }
                return true
            }
        }

        glowCameraViewModel.takePictureStateEvent.observe(lifecycleOwner) {
            if (it == GlowCameraViewModel.NORMAL) {
                viewBinding.cb.isEnabled = true
            }
        }
    }


    /**
     * 是否是底部Bottom上升的UI
     */
    var isExpandCameraButton = true

    /**
     * 水平位移
     */
    val transitionYValuer = XAnimatorCalculateValuer()
    val scaleValuer = XAnimatorCalculateValuer(1.0f)

    /**
     * 相机按钮动画
     */
    val cameraButtonAnimator = XAnimator.ofFloat(0f, 1f)
        .duration(BottomFunction.BOTTOM_DURATION)
        .interpolator(BottomInInterpolator())
        .setAnimationListener(object : XAnimator.XAnimationListener {

            override fun onAnimationEnd(animation: XAnimator?) {

            }

            override fun onAnimationCancel(animation: XAnimator?) {
            }

            override fun onAnimationStart(animation: XAnimator?) {

            }

            override fun onAnimationUpdate(fraction: Float, value: Float) {
                Log.i("lyddd", "cameraButtonAnimator fraction: $fraction value: $value  transY:${transitionYValuer.calculateValue(fraction)}")
                viewBinding.rlCameraBar.translationY =
                    transitionYValuer.calculateValue(fraction)
                viewBinding.cb.apply {
                    val scale = scaleValuer.calculateValue(fraction)
                    scaleX = scale
                    scaleY = scale
                }
            }
        })

    private val shrinkRunnable = Runnable {
        glowBottomFunctionViewModel.bottomSizeParamEvent.value?.let { param ->
            cameraButtonAnimator.cancel()
            val scale = if (DeviceUtils.isFullScreenDevice()) 0.7714f else 0.7142f
            transitionYValuer.to(50.dpf)
            scaleValuer.to(scale)
            cameraButtonAnimator.start()
        }
    }


    /**
     * 扩张拍照按钮
     */
    private fun expandCameraButton(isExpandCameraButton: Boolean) {
        if (this.isExpandCameraButton == isExpandCameraButton) {
            return
        }
        this.isExpandCameraButton = isExpandCameraButton
        if (this.isExpandCameraButton) {
            viewBinding.root.removeCallbacks(shrinkRunnable)
            glowBottomFunctionViewModel.bottomSizeParamEvent.value?.let { param ->
                cameraButtonAnimator.cancel()
                transitionYValuer.to(0f)
                viewBinding.cb.visible()
                scaleValuer.to(1f)
                cameraButtonAnimator.start()
            }
        } else {
            viewBinding.root.removeCallbacks(shrinkRunnable)
            viewBinding.root.post(shrinkRunnable)
            viewBinding.cb.gone()
        }
    }

    private fun startCapture() {
        glowCameraViewModel.viewModelScope.launch {
//            if (glowBottomFunctionViewModel.hasBottomFunction()) {
//                glowBottomFunctionViewModel.show(null)
//            }
            glowSettingViewModel.dismissAll()
            //发起拍照
            viewBinding.cb.isEnabled = false

            glowCameraViewModel.capture()

        }
    }

    /**
     * 开始录制视频
     */
    private fun startRecordVideo() {
        if (glowCameraViewModel.isVideoRecording()) {
            return
        }
        if (glowBottomFunctionViewModel.hasBottomFunction()) {
            glowBottomFunctionViewModel.show(null)
        }
        glowSettingViewModel.dismissAll()
        viewBinding.cb.onVideoRecording(true)
        glowCameraViewModel.startRecord()
    }

    private fun stopRecordVideo() {
        glowCameraViewModel.stopRecord()
    }

    private var isSubscribing = false
    private fun goToSubscribe(action: () -> Unit) {
        if (glowCameraViewModel.isNeedPay(filterLimited = true)) {
            if (isSubscribing) {
                return
            }
            isSubscribing = true
            glowCameraViewModel.addSpmSourceFeatureContent()
            IProcessHandler(coverContainer.attachActivity)
                .execute(object : SubscribeProcess(SubSource.FROM_DEFAULT) {
                    override fun onSubscribeResult(isSubcribe: Boolean) {
                        isSubscribing = false
                        viewBinding.cb.canStartRecording = true
                    }
                })
        } else {
            action.invoke()
        }

    }


    /**
     * 拦截物理按键
     */
    override fun onDispatchPhysicKeyEvent(event: KeyEvent): Boolean {
        when (event.keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP,
            KeyEvent.KEYCODE_VOLUME_DOWN -> {
                viewBinding.cb.onPhoneKeyEvent(event)
                return true
            }
        }
        return super.onDispatchPhysicKeyEvent(event)
    }

    /**
     * 拦截屏幕按键
     */
    override fun onDispatchScreenGestureEvent(event: MotionEvent): Boolean {
        return super.onDispatchScreenGestureEvent(event)
    }

    private fun canCapture(): Boolean {
        return true
    }


}