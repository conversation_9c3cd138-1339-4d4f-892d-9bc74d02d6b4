package com.commsource.camera.xcamera.cover.confirmbottomfunction

import android.content.Context
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemWaterMarkBinding
import com.commsource.util.GlideProxy
import com.commsource.util.gone
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

/**
 * @Desc : 水印的ViewHolder
 * <AUTHOR> Bear - 2021/1/8
 */
class WaterMarkViewHolder(context: Context?, parent: ViewGroup?) : BaseViewHolder<WaterEntity>(context, parent, R.layout.item_water_mark) {

    val mViewBinding by lazy { DataBindingUtil.bind<ItemWaterMarkBinding>(itemView) as ItemWaterMarkBinding }

    override fun onBindViewHolder(position: Int, item: BaseItem<WaterEntity>?, payloads: MutableList<Any>?) {
        super.onBindViewHolder(position, item, payloads)
        item?.let {
            if (payloads == null || payloads.isEmpty()) {
                mViewBinding.root.setTag(position)
                GlideProxy.with(mContext)
                        .load("file:///android_asset/water_mark/" + it.entity.thumb)
                        .into(mViewBinding.ivIcon)
            }

            // 处理选中图标
            if (it.isSelect) {
                mViewBinding.ivSelected.visible()
            } else {
                mViewBinding.ivSelected.gone()
            }
        }
    }
}