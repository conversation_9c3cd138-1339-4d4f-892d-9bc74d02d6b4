package com.commsource.camera.xcamera.glow_model.viewmodel

import android.app.Application
import android.view.KeyEvent
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.commsource.beautyplus.R
import com.commsource.camera.mvp.CameraConstant
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.commsource.camera.xcamera.cover.CameraConfigViewModel.Companion.calculateCameraPreviewHeight
import com.commsource.camera.xcamera.cover.CameraConfigViewModel.Companion.calculateCameraPreviewMarginTop
import com.commsource.camera.xcamera.glow_model.GlowFunction
import com.commsource.util.LOGV_Camera
import com.commsource.util.UIHelper
import com.meitu.library.util.app.ResourcesUtils
import com.meitu.library.util.device.DeviceUtils

class GlowBottomFunctionViewModel(application: Application) : AndroidViewModel(application) {

    /**
     * 屏幕比例切换
     */
    val screenRatioChangeEvent = MutableLiveData<Int>()

    /**
     * 底部状态事件
     */
    val bottomFunctionChangeEvent = MutableLiveData<GlowFunction?>()


    /**
     * 相机页面可见时回调
     */
    val cameraResumeEvent = MutableLiveData<Boolean>()

    /**
     * FIXME:物理按键监听{使用非粘性事件处理 一次性的 KeyEvent}
     */
    val physicKeyEvent = MutableLiveData<KeyEvent>()

    /**
     * 底部size适配变化
     */
    val bottomSizeParamEvent = MutableLiveData<BottomSizeParam>()

    /**
     * 预览区域功能的底部margin
     */
    val previewFunctionBottomHeightEvent = MutableLiveData<Int>()

    val hideCaptureBtnEvent = MutableLiveData<Boolean>()

    val floatShowEvent = MutableLiveData<Boolean>()


    /**
     * 上一次Function
     */
    var lastFunction: GlowFunction? = null


    /**
     * 底部适配的所有参数
     */
    private var mBottomSizeParam = BottomSizeParam()

    init {
        initTopSize()
        initBottomSize(DeviceUtils.getScreenHeight())
    }

    /**
     * 顶部栏Size
     * TODO 不应该在此处 可修改位置
     */
    private fun initTopSize() {
        mBottomSizeParam!!.mTopCoverHeight =
            calculateCameraPreviewMarginTop(CameraRatioType.PICTURE_RATIO_1_1)
    }

    /**
     * 初始化底部Size
     *
     *
     * 此方法计算所有需要使用的高度以及位移偏差问题
     *
     * @param screenHeight 因为部分虚拟键盘全面屏动态改变时 会发生高度变化
     */
    fun initBottomSize(screenHeight: Int) {
        CameraConstant.setsCameraScreenHeight(screenHeight)
        mBottomSizeParam.screenHeight = screenHeight
        // 默认4:3比例的底部Cover高度
        val bottomBarHeight = Math.max(
            screenHeight - calculateCameraPreviewMarginTop(CameraRatioType.PICTURE_RATIO_4_3) - calculateCameraPreviewHeight(
                CameraRatioType.PICTURE_RATIO_4_3
            ), ResourcesUtils.getDimension(R.dimen.camera_bottom_fragment_min_height).toInt()
        )
        BottomSizeParam.isMinBottomBar =
            bottomBarHeight <= BottomSizeParam.MinBottomBarHeight + DeviceUtils.dip2px(12f) //+12是兼容如果132dp作为最小 其实面板也要兼容小尺寸的问题，防止tab和按钮太挤的问题
        // 相机按钮BottomBar高度
        mBottomSizeParam.mBottomBarHeight = bottomBarHeight
        // Ar面板高度
        mBottomSizeParam.mArBarHeight =
            Math.max(bottomBarHeight + DeviceUtils.dip2px(44f), BottomSizeParam.MinArTabHeight)
        CameraConstant.CAMERA_BOTTOM_BAR_HEIGHT = bottomBarHeight //底部白色高度，用于拍后页。
        mBottomSizeParam.mEffectContentHeight = DeviceUtils.dip2px(97f) //默认基础效果面板内部保证的高度为97dp
        val cameraMarginBottom: Int //默认相机按钮收缩后 按钮margin底部的高度
        val cameraIconMarginBottom =
            ResourcesUtils.getDimension(R.dimen.camera_button_margin_bottom).toInt() //按钮中心偏移高度为10dp
        val cameraCenterHeight = DeviceUtils.dip2px(85f) //中心居中布局为85dp
        val effectTopBarHeight = DeviceUtils.dip2px(44f) //效果栏顶部高度
        val effectBottomButtonHeight = DeviceUtils.dip2px(40f) //效果界面中底部和相机按钮同一水平线的按钮高度
        val lookItemHeight = DeviceUtils.dip2px(78f) //look中itemHeight
        val cameraShrinkButtonHeight =
            if (DeviceUtils.isFullScreenDevice()) DeviceUtils.dip2px(54f) else DeviceUtils.dip2px(
                50f
            ) //相机收缩的高度
        val fixBottom =
            (cameraShrinkButtonHeight - effectBottomButtonHeight) / 2 //把按钮高度定位在和相机按钮同一水平线
        //获取效果界面面板高度
        //Android添加一个重要的原则 就是如果全面屏幕底部控件够 那么就扩大可操作的effectbar为相机bottomBar的高度
        //这种自适应的方式被UI否决了,但是综合来看 自适应的空间分布在长屏幕的机子明显更和谐 而且手势更舒服 三空间均匀分布尺寸 明显更和谐 by Bear
        //这边如果无边框 效果面板需要+圆角的16dp为了显示圆角
        val tempEffectBarHeight = bottomBarHeight + DeviceUtils.dip2px(16f)
        if (tempEffectBarHeight > BottomSizeParam.MinBottomFragmentHeight) {
            mBottomSizeParam.mEffectBarHeight = tempEffectBarHeight
            //三均匀分布
            val isBalanceSpace = false
            //是否是固定内容高度
            val isFixHeight = false
            if (isFixHeight) {
                //固定内容高度 93
                cameraMarginBottom =
                    tempEffectBarHeight - DeviceUtils.dip2px(50f) - mBottomSizeParam.mEffectContentHeight - cameraShrinkButtonHeight
            } else {
                //固定底部margin 34
                cameraMarginBottom = if (isBalanceSpace) {
                    (tempEffectBarHeight - effectTopBarHeight - lookItemHeight - cameraShrinkButtonHeight) / 3
                } else {
                    if (!DeviceUtils.isFullScreenDevice() || DeviceUtils.hasNavigationBar()) {
                        DeviceUtils.dip2px(10f)
                    } else {
                        DeviceUtils.dip2px(20f + 2f)
                    }
                }
                mBottomSizeParam.expandCameraMarginBottom = cameraMarginBottom
                //内容非固定 计算内容高度
                mBottomSizeParam.mEffectContentHeight =
                    tempEffectBarHeight - effectTopBarHeight - cameraShrinkButtonHeight - cameraMarginBottom
            }
        } else {
            //目前UI的稿子定的是固定高度的效果面板
            //AR面板的高度就是 效果面板 + 44dp
            // 效果栏bar的高度 全面屏幕223dp 非全面屏203dp
            mBottomSizeParam.mEffectBarHeight = BottomSizeParam.MinBottomFragmentHeight
            cameraMarginBottom =
                if (!DeviceUtils.isFullScreenDevice() || DeviceUtils.hasNavigationBar()) {
                    DeviceUtils.dip2px(10f)
                } else {
                    DeviceUtils.dip2px(20f + 2f)
                }
            mBottomSizeParam.expandCameraMarginBottom = cameraMarginBottom
            mBottomSizeParam.mEffectContentHeight =
                BottomSizeParam.MinBottomFragmentHeight - effectTopBarHeight - cameraShrinkButtonHeight - cameraMarginBottom
        }
        // 计算底部栏高度与底部fragment布局的高度差 让相机中心布局紧贴屏幕最底部
        mBottomSizeParam.mEffectCameraBtnTransY =
            (bottomBarHeight - cameraCenterHeight) / 2 + cameraIconMarginBottom
        // 紧贴最底部，按钮的marginBottom是有一个默认值的 计算之后需要做需要marginBottom的bottom值的偏差计算
        mBottomSizeParam.mEffectCameraBtnTransY += (cameraCenterHeight - cameraShrinkButtonHeight) / 2 - cameraMarginBottom
        mBottomSizeParam.mArCameraBtnTransY =
            (bottomBarHeight - cameraCenterHeight) / 2 + cameraIconMarginBottom + (cameraCenterHeight - cameraShrinkButtonHeight) / 2 - if (DeviceUtils.isFullScreenDevice()) DeviceUtils.dip2px(
                14f
            ) else DeviceUtils.dip2px(10f)
        // 计算底部白色遮罩距离
        mBottomSizeParam.mBottomCoverHeightOn11 =
            screenHeight - DeviceUtils.getScreenWidth() - mBottomSizeParam.mTopCoverHeight
        mBottomSizeParam.mBottomCoverHeightOn43 = bottomBarHeight
        // 效果界面的拍照按钮距离底部的高度计算
        mBottomSizeParam.mEffectButtonMarginBottom = fixBottom + cameraMarginBottom
        // 发送计算尺寸事件
        "mEffectContentHeight ${mBottomSizeParam.mEffectContentHeight}".LOGV_Camera()
        bottomSizeParamEvent.value = mBottomSizeParam
    }


    /**
     * 是否展示某种底部功能页面
     *
     * @param function
     * @return
     */
    fun isShow(function: GlowFunction): Boolean {
        return bottomFunctionChangeEvent.value == function
    }

    /**
     * 是否显示底部功能栏
     *
     * @return
     */
    fun hasBottomFunction(): Boolean {
        return bottomFunctionChangeEvent.value != null
    }

    /**
     * 显示目标状态
     * 展示需要的底部目标界面
     *
     * @param function 对应的功能界面
     * 如果function为null直接取消界面的显示
     */
    fun show(function: GlowFunction?, fromClick: Boolean = false) {
        // 记录上次的function
        if (fromClick && lastFunction == function) {
            show(null, false)
            return
        }
        lastFunction = function
        if (UIHelper.isMainThread()) {
            bottomFunctionChangeEvent.setValue(function)
        } else {
            bottomFunctionChangeEvent.postValue(function)
        }
    }

    /**
     * @Desc : 底部适配的参数集合
     * <AUTHOR> Bear - 2020/4/23
     */
    class BottomSizeParam {
        /**
         * 屏幕高度
         */
        var screenHeight = 0

        /**
         * 顶部Cover高度
         */
        var mTopCoverHeight = 0

        /**
         * 在Effect面板下按钮TransitionY
         */
        var mEffectCameraBtnTransY = 0

        /**
         * 在Ar面板下按钮的transitionY
         */
        var mArCameraBtnTransY = 0

        /**
         * Ar面板的高度
         */
        var mArBarHeight =
            (188f * DeviceUtils.getScreenWidth() / 375f + DeviceUtils.dip2px(84f)).toInt()

        /**
         * 效果栏结合
         * [com.commsource.camera.xcamera.cover.bottomFunction.effect.EffectFunction]
         * 的高度
         * AR面板的高度 目前也使用如此 update by csxiong
         * AR面板高度 mEffectBarHeight + ar顶部tab 44dp
         */
        var mEffectBarHeight = 0

        /**
         * 效果栏内部空间的高度
         */
        var mEffectContentHeight = 0

        /**
         * 效果按钮距离底部的距离
         */
        var mEffectButtonMarginBottom = 0

        /**
         * 相机拍照按钮,底部Bar高度
         */
        var mBottomBarHeight = 0

        /**
         * 底部包裹View的高度
         */
        var mBottomCoverHeightOn11 = 0
        var mBottomCoverHeightOn43 = 0

        /**
         * 获取面板底部安全高度
         * 就是按钮 + marginBotom的高度ss
        s
         * @return
         */
        val panelSafeBottom: Int
            get() = (mEffectButtonMarginBottom + if (DeviceUtils.isFullScreenDevice()) DeviceUtils.dip2fpx(
                65f
            ) else DeviceUtils.dip2fpx(60f)).toInt()

        //做动画缩小的时候，相机按钮的marginBottom
        var expandCameraMarginBottom: Int = 0

        companion object {
            /**
             * 是否是最小bar尺寸
             * 这边有个超小屏幕适配逻辑
             * 如果是超小屏幕[com.commsource.camera.xcamera.widget.CameraModeTab] transitionY 12dp 留出空间给主按钮
             * 拍后和同理
             */
            var isMinBottomBar = false

            /**
             * 底部BottomBar高度
             */
            val MinBottomBarHeight =
                ResourcesUtils.getDimension(R.dimen.camera_bottom_min_height).toInt()

            /**
             * 底部功能Function最低高度
             */
            var MinBottomFragmentHeight =
                ResourcesUtils.getDimension(R.dimen.camera_bottom_fragment_min_height_full_screen)
                    .toInt()

            /**
             * 最小Ar面板
             */
            var MinArTabHeight =
                (176f * DeviceUtils.getScreenWidth() / 375f + DeviceUtils.dip2px(84f)).toInt()

            init {
                if (DeviceUtils.hasNavigationBar() || !DeviceUtils.isFullScreenDevice()) {
                    MinBottomFragmentHeight =
                        ResourcesUtils.getDimension(R.dimen.camera_bottom_fragment_min_height)
                            .toInt()
                } else {
                    MinBottomFragmentHeight =
                        ResourcesUtils.getDimension(R.dimen.camera_bottom_fragment_min_height_full_screen)
                            .toInt()
                }
            }
        }
    }

    /**
     * 获取Bottombar高度
     *
     * @return
     */
    fun getBottomBarHeight(): Int {
        return if (mBottomSizeParam != null) {
            mBottomSizeParam!!.mBottomBarHeight
        } else 0
    }

}