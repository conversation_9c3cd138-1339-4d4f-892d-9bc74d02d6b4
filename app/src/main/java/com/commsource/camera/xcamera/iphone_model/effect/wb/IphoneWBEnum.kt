package com.commsource.camera.xcamera.iphone_model.effect.wb

import com.commsource.beautyplus.R
import com.google.firebase.analytics.FirebaseAnalytics


enum class IphoneWBEnum(
    val displayNameId: Int,
    val iconFontRes: Int,
    val defaultTemperature: Float,  //白平衡色温
    val defaultHue: Float, //白平衡色调
    val analyticsString: String
) {
    //手动
    Manual(
        displayNameId = R.string.v77120_B_30,
        iconFontRes = R.string.iphone_cam_icon_Manual,
        -0.1f,
        0.15f,
        "手动"
    ),

    //自动
    Auto(
        displayNameId = R.string.v77120_B_31,
        iconFontRes = R.string.iphone_cam_icon_Auto,
        0f,
        0f,
        "自动"
    ),

    //日光灯
    Daylight(
        displayNameId = R.string.v77120_B_34,
        iconFontRes = R.string.iphone_cam_icon_sunshine,
        0.07f,
        -0.1f,
        "日光"
    ),

    //阴天
    Cloudy(
        displayNameId = R.string.v77120_B_35,
        iconFontRes = R.string.iphone_cam_icon_cloud,
        0.2f,
        0.1f,
        "阴天"
    ),

    //白炽灯
    Incandescent(
        displayNameId = R.string.v77120_B_32,
        iconFontRes = R.string.iphone_cam_icon_wihte_light,
        -0.5f,
        -0.09f,
        "白炽灯"
    ),

    //荧光灯
    Fluorescent(
        displayNameId = R.string.v77120_B_33,
        iconFontRes = R.string.iphone_cam_icon_led,
        -0.20f,
        0.05f,
        "荧光灯"
    )

    ;

    companion object {
        val minTemperature: Pair<Int, Float> = Pair(2000, -0.7f)
        val maxTemperature: Pair<Int, Float> = Pair(8000, 0.3f)


        fun getFormatKValue(temperature: Float?): Int {
            temperature?.let {
                return ((it - minTemperature.second) / (maxTemperature.second - minTemperature.second) * (maxTemperature.first - minTemperature.first) + minTemperature.first).toInt()
            }
            return ((maxTemperature.first + minTemperature.first) / 2)
        }
    }
}