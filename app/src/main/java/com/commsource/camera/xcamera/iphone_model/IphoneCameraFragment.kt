package com.commsource.camera.xcamera.iphone_model

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.view.children
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.commsource.beautyplus.R
import com.commsource.beautyplus.router.RouterEntity
import com.commsource.beautyplus.router.RouterManager
import com.commsource.beautyplus.setting.abtest.ABTestDataEnum
import com.commsource.camera.common.BaseCameraFragment
import com.commsource.camera.common.CameraBundleExtras
import com.commsource.camera.common.CameraContainerViewModel
import com.commsource.camera.common.faceLiftForGlow
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.xcamera.DefaultCameraSizeConfig
import com.commsource.camera.xcamera.burst.BurstState
import com.commsource.camera.xcamera.iphone_model.cover.AppleProCover
import com.commsource.camera.xcamera.iphone_model.cover.CapturingCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneCaptureCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneFillLightCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneFloatAssistantCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneFunctionCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneGestureCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneHDVideoCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneLensTipCover
import com.commsource.camera.xcamera.iphone_model.cover.IphonePreviewCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneQuickSwitchCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneRadioCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneRecordingTopBarCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneSettingCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneStyleBoxCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneSuspendCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneTestTipsCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneTipsCover
import com.commsource.camera.xcamera.iphone_model.cover.IphoneTopBarCover
import com.commsource.camera.xcamera.iphone_model.hd.HighResolutionHelper
import com.commsource.camera.xcamera.iphone_model.render.CameraPrefilterRender
import com.commsource.camera.xcamera.iphone_model.render.CameraToneRender
import com.commsource.camera.xcamera.iphone_model.render.CameraWhiteBalanceRender
import com.commsource.camera.xcamera.iphone_model.transaction.IphoneCameraTransaction
import com.commsource.camera.xcamera.iphone_model.transaction.IphoneStyleTransaction
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneCameraNewViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneCaptureViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneFunctionViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneGestureViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneSettingViewModel
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.Meepo
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.AppTools
import com.commsource.util.BPLocationUtils
import com.commsource.util.DeviceLevelStrategy
import com.commsource.util.UIHelper
import com.commsource.util.text
import com.commsource.widget.dialog.delegate.PositiveButton
import com.commsource.widget.dialog.delegate.VideoPictureTips
import com.commsource.widget.dialog.delegate.XDialog
import com.commsource.widget.dialog.delegate.config.PagConfig
import com.meitu.common.AppContext
import com.meitu.library.media.camera.common.AspectRatioGroup
import com.meitu.library.media.camera.common.Facing
import com.meitu.library.media.camera.common.FlashMode
import com.meitu.library.media.camera.common.PreviewParams
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.camerasuite.camera.core.ARExtendType
import com.pixocial.camerasuite.camera.core.renders.CameraBeautyRender
import com.pixocial.camerasuite.camera.core.renders.CameraFilterRender
import com.pixocial.camerasuite.camera.core.renders.arrender.CameraARRender
import com.pixocial.camerasuite.camera.core.service.CameraCoreConfig
import com.pixocial.camerasuite.camera.core.service.config.CameraPreviewConfig
import com.pixocial.camerasuite.camera.core.service.config.CameraSizeConfig
import com.pixocial.camerasuite.commsource.camera.FpsRecorder
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.pixocial.framework.cover.CoverContainer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.libpag.PAGFile

class IphoneCameraFragment : BaseCameraFragment(), View.OnLayoutChangeListener {
    private val cameraContainerViewModel by lazy {
        ViewModelProvider(getSafeActivity())[CameraContainerViewModel::class.java]
    }
    private val iphoneCameraNewViewModel by lazy {
        ViewModelProvider(this)[IphoneCameraNewViewModel::class.java]
    }
    private val iphoneCaptureViewModel by lazy {
        ViewModelProvider(this)[IphoneCaptureViewModel::class.java]
    }
    private val iphoneFunctionViewModel by lazy {
        ViewModelProvider(this)[IphoneFunctionViewModel::class.java]
    }
    private val iphoneGestureViewModel by lazy {
        ViewModelProvider(this)[IphoneGestureViewModel::class.java]
    }
    private val iphoneSettingViewModel by lazy {
        ViewModelProvider(this)[IphoneSettingViewModel::class.java]
    }

    // 是否第一帧可用
    private var isFirstFrameAvailable = true

    // 是否套用默认效果
    private var isApplyDefaultEffect = false

    private var enterBrightness = 0.75f

    private var displayListener: DisplayManager.DisplayListener? = null

    override val coverContainer: CoverContainer by lazy { CoverContainer(requireContext()) }

    override fun createCameraCoreConfig(cameraModelName: String, bundle: Bundle?): CameraCoreConfig {
        val isTestA = Meepo.isInABTest(ABTestDataEnum.APPLE_HD_TEST_A.code)
        val isTestB = Meepo.isInABTest(ABTestDataEnum.APPLE_HD_TEST_B.code)
        val cameraId = if (IphoneModelCache.getIphoneModelCacheEntity().isFrontCamera()) 1 else 0
        val deviceLevel = DeviceLevelStrategy.getDeviceLevelForIphoneCamera()

        val cameraRenders = listOf(
            CameraWhiteBalanceRender(),
            CameraPrefilterRender(),
            CameraToneRender(),
            CameraFilterRender(),
            CameraBeautyRender().apply {
                this.isAppleMode = true
            },
            CameraARRender().apply {
                needHeadDetect = true
                need3DDetect = true
                registerApplierType(ARExtendType.HeadScale)
                registerApplierType(ARExtendType.CommonAR)
                registerApplierType(ARExtendType.Face3DLight)
                registerApplierType(ARExtendType.FaceLift)
                registerApplierType(ARExtendType.Makeup)
            }
        )
        val renders = if (deviceLevel == DeviceLevelStrategy.LOW) {
            cameraRenders.filterNot {
                it is CameraWhiteBalanceRender
            }
        } else {
            cameraRenders
        }
        val imageRenders = renders.map { it.cloneNew() }

        return CameraCoreConfig.Builder()
            .setDebug(true)
            .setFpsSuffix(FpsRecorder.SUFFIX_IPHONE)
            .setNeedRecordFps(true)
            .setForceApplyDefaultCameraParams(isTestA)
            .setCameraId(cameraId)
            .setFlashMode(
                if (IphoneModelCache.getIphoneModelCacheEntity().openLaterLight) FlashMode.TORCH else FlashMode.OFF
            )
            .setRenders(renders)
            .setImageRenders(imageRenders)
            .setCameraSizeConfig(
                if (isTestA || isTestB) {
                    appleCameraSizeConfig()
                } else {
                    defaultSizeConfig()
                }
            )
            .setCameraPreviewConfig(object : CameraPreviewConfig {
                override fun configPreviewParams(previewParams: PreviewParams, targetRatio: Int) {
                    previewParams.aspectRatio = when (targetRatio) {
                        CameraRatioType.PICTURE_RATIO_4_3 -> AspectRatioGroup.RATIO_4_3
                        CameraRatioType.PICTURE_RATIO_9_16 -> AspectRatioGroup.RATIO_16_9
                        CameraRatioType.PICTURE_RATIO_1_1 -> AspectRatioGroup.RATIO_1_1
                        CameraRatioType.PICTURE_RATIO_FULL -> AspectRatioGroup.FULL_SCREEN
                        else -> AspectRatioGroup.RATIO_4_3
                    }
                    previewParams.previewMarginTop = 0
                    previewParams.previewMarginLeft = 0
                    previewParams.previewMarginRight = 0
                    previewParams.previewMarginBottom = 0
                    previewParams.previewAlign = PreviewParams.ALIGN_CENTER
                }
            })
            .build(cameraModelName, IphoneModelCache.getIphoneModelCacheEntity().ratio)
    }

    override fun handleProtocol(bundle: Bundle?) {
        bundle?.getString(CameraBundleExtras.CAMERA_SOURCE, "其他")?.let {
            // 苹果模式 top_banner 应该是其他
            val source = if (it == "topbanner") "其他" else it
            IphoneModelCache.getIphoneModelCacheEntity().source = source
        }
        bundle?.getFloat(CameraBundleExtras.ZOOM_VALUE, 1f)?.let {
            iphoneCameraNewViewModel.initCurrentZoom(it)
        }

        val routerEntity = bundle?.getSerializable(RouterEntity.DEEP_LINK) ?: return
        routerEntity.takeIf { it is RouterEntity }?.let { webEntity ->
            val entity = webEntity as? RouterEntity ?: return
            entity.lastPathSegment?.let {
                if (!RouterManager.isIphoneProtocolPath(it)) {
                    return
                }
            }
            entity.getParameter("f_hd")?.let {
                if (it == "true") {
                    HighResolutionHelper.setAppleModeHD(true)
                }
            }
            entity.getParameter("f_resolution")?.let {
                if (it == "most") {
                    HighResolutionHelper.setAppleModeHD(true)
                    HighResolutionHelper.selectMostResolution = true
                }
            }
            entity.getParameter("feature")?.takeIf { it.isNotEmpty() }?.let { name ->
                iphoneCameraNewViewModel.getBeautyEntities()
                    .indexOfFirst { it.deeplinkName == name }.takeIf { it >= 0 }?.let {
                        IphoneModelCache.getIphoneModelCacheEntity().selectBeautyIndex = it
                        iphoneFunctionViewModel.show(IphoneFunction.BEAUTY)
                        iphoneFunctionViewModel.logFuncCategoryClick("美颜")
                    }
            }
        }
    }

    override fun logCameraBackEvent() {
        MTAnalyticsAgent.logEvent(
            MTAnalyticsConstant.EVENT_SELFIE_BACK,
            HashMap<String, String>(4).apply {
                when (iphoneCaptureViewModel.getMode()) {
                    CameraMode.MOVIE_MODE -> this["mode_a"] = "movie"
                    CameraMode.CAPTURE_MODE -> this["mode_a"] = "shoot"
                    CameraMode.VIDEO_MODE -> this["mode_a"] = "video"
                }
                this[MTAnalyticsConstant.KEY_BACK] = MTAnalyticsConstant.VALUE_KEY_BACK
                this[MTAnalyticsConstant.camera_mode] = MTAnalyticsConstant.MODE_IPHONE

            })

        IphoneModelCache.updateIphoneModelEntity()
        IphoneModelCache.closeCamera()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Meepo.enterTest(
            ABTestDataEnum.APPLE_HD_REF,
            ABTestDataEnum.APPLE_HD_TEST_A,
            ABTestDataEnum.APPLE_HD_TEST_B
        )
        if (IphoneModelCache.shouldShowRedDot()) {
            IphoneModelCache.consumeRedDot()
        }
        // 保持屏幕常量
        AppTools.setKeepScreenOn(activity?.window)

        handleProtocol(arguments)
        enterBrightness = activity?.window?.attributes?.screenBrightness ?: 0f
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return coverContainer.apply {
            // 权重 -1
            addCover(IphoneGestureCover())
            // 权重 0
            addCover(IphonePreviewCover())
            addCover(IphoneStyleBoxCover())
            addCover(IphoneTopBarCover())//顶部操作功能栏的Cover
            addCover(IphoneTipsCover())//提示
            addCover(IphoneFunctionCover())//风格等功能
            addCover(IphoneSuspendCover())//效果栏悬浮栏 主要是look、美颜滑杆、滤镜滑杆
            addCover(IphoneCaptureCover())//拍照按钮
            addCover(IphoneRecordingTopBarCover())
            addCover(IphoneQuickSwitchCover())//相机中快捷关闭
            addCover(IphoneRadioCover())//画幅
            addCover(IphoneHDVideoCover()) // 高清视频
            addCover(IphoneSettingCover())//顶部设置
            addCover(IphoneFillLightCover())//前置摄像头补光
            if (AppTools.isDebug()) {
                addCover(IphoneTestTipsCover())
            }
            addCover(IphoneFloatAssistantCover())//倒计时
            addCover(AppleProCover())
            addTransaction(IphoneStyleTransaction()) // 关联与UI无关的风格事务
            addTransaction(IphoneCameraTransaction())
            // 权重 1
            addCover(CapturingCover())
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        iphoneCameraNewViewModel.initAccessor(
            cameraContainerViewModel.cameraAccessor, viewLifecycleOwner
        )
        iphoneCameraNewViewModel.initPrefilter()
        coverContainer.bind(this)
        if (IphoneModelCache.shouldShowGuide()) {
            checkGuide {
                if (IphoneModelCache.showFuncAnim()) {
                    iphoneFunctionViewModel.showFuncAnim()
                    IphoneModelCache.hasShowFuncAnim()
                }
            }
            IphoneModelCache.hasShowGuide()
        } else if (IphoneModelCache.showLensTip()) {
            coverContainer.addCoverAndCommit(IphoneLensTipCover())
            iphoneFunctionViewModel.showLensTips()
        } else if (IphoneModelCache.showFuncAnim()) {
            iphoneFunctionViewModel.showFuncAnim()
            IphoneModelCache.hasShowFuncAnim()
        }

        view.addOnLayoutChangeListener(this)
        initEvent()
        initDisplayListener()

        iphoneCameraNewViewModel.updateRatioParams()
    }

    override fun onResume() {
        super.onResume()
        iphoneCameraNewViewModel.logCamAppr()
        if (!isApplyDefaultEffect) {
            isApplyDefaultEffect = true
            lifecycleScope.launch(Dispatchers.IO) {
                val beautyEntities = iphoneCameraNewViewModel.fetchDefaultEffectConfig()
                iphoneCameraNewViewModel.initBeautyEffect()
                iphoneCameraNewViewModel.applyBeautyEffect(beautyEntities)
                withContext(Dispatchers.Main) {
                    val index = IphoneModelCache.getIphoneModelCacheEntity().selectBeautyIndex
                    iphoneCameraNewViewModel.getBeautyEntities().elementAtOrNull(index)?.let {
                        iphoneCameraNewViewModel.notifyUseProFunState(it)
                    }
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        // 如果正在连拍则结束连拍
        iphoneCaptureViewModel.onPause()
        if (iphoneCaptureViewModel.isBurstTaking()) {
            iphoneGestureViewModel.restoreTakeByGestureEvent()
            iphoneCaptureViewModel.shutDownBurstTakePicture()
        }
        // 切换相机恢复亮度
        updateWindowBrightness(enterBrightness)
    }

    override fun onDestroy() {
        super.onDestroy()
        IphoneModelCache.closeCamera()
        removeDisplayListener()
        // 取消屏幕常亮
        activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onLayoutChange(
        v: View?,
        left: Int,
        top: Int,
        right: Int,
        bottom: Int,
        oldLeft: Int,
        oldTop: Int,
        oldRight: Int,
        oldBottom: Int
    ) {
        val root: View = coverContainer
        if (oldBottom - oldTop != root.height || oldRight - oldLeft != root.width) {
            DeviceUtils.setsHomeHeight(root.height)
            iphoneFunctionViewModel.initBottomSize(root.height)
            iphoneSettingViewModel.initFullScreenRect(right - left, root.height)
        }
    }

    private fun defaultSizeConfig(): CameraSizeConfig {
        return object : DefaultCameraSizeConfig() {
            override fun onPreviewScaleChanged(scale: Float) {
                if (isAdded) {
                    cameraContainerViewModel.cameraAccessor.setPreviewSizeScale(scale)
                } else {
                    doOnAttach {
                        cameraContainerViewModel.cameraAccessor.setPreviewSizeScale(scale)
                    }
                }
            }

            override fun getCameraFacing(): String {
                return if (IphoneModelCache.getIphoneModelCacheEntity().isFrontCamera()) {
                    Facing.FRONT
                } else {
                    Facing.BACK
                }
            }
        }
    }

    private fun appleCameraSizeConfig(): CameraSizeConfig {
        return object : AppleCameraSizeConfig() {
            override fun onPreviewScaleChanged(scale: Float) {
                if (isAdded) {
                    cameraContainerViewModel.cameraAccessor.setPreviewSizeScale(scale)
                } else {
                    doOnAttach {
                        cameraContainerViewModel.cameraAccessor.setPreviewSizeScale(scale)
                    }
                }
            }

            override fun getCameraFacing(): String {
                return if (IphoneModelCache.getIphoneModelCacheEntity().isFrontCamera()) {
                    Facing.FRONT
                } else {
                    Facing.BACK
                }
            }
        }
    }

    private fun initDisplayListener() {
        displayListener = object : DisplayManager.DisplayListener {
            override fun onDisplayAdded(displayId: Int) {}
            override fun onDisplayRemoved(displayId: Int) {}
            override fun onDisplayChanged(displayId: Int) {
                iphoneCameraNewViewModel.switchPictureRatio(iphoneCameraNewViewModel.getPictureRatio())
            }
        }

        val displayManager = context?.getSystemService(Context.DISPLAY_SERVICE) as? DisplayManager
        displayManager?.registerDisplayListener(displayListener, null)
    }

    private fun removeDisplayListener() {
        displayListener?.let {
            val displayManager =
                context?.getSystemService(Context.DISPLAY_SERVICE) as? DisplayManager
            displayManager?.unregisterDisplayListener(displayListener)
        }
    }

    private fun initEvent() {
        iphoneCameraNewViewModel.firstFrameLiveData?.observe(viewLifecycleOwner) {
            onFirstFrameAvailable(isFirstFrameAvailable)
            isFirstFrameAvailable = false
        }
        iphoneCameraNewViewModel.windowBrightnessLiveData.observe(viewLifecycleOwner) {
            updateWindowBrightness(it / 100f)
        }
        iphoneCaptureViewModel.getBurstState().observe(viewLifecycleOwner) {
            it?.let {
                when (it) {
                    BurstState.BURST_ING -> {
                        showCapturing(true)
                    }

                    BurstState.BURST_END -> {
                        showCapturing(false)
                    }
                }
            }
        }
    }

    private fun showCapturing(show: Boolean) {
        coverContainer.children.forEach {
            // 目前用 TAG 标记连拍时需要更新显示状态的 Cover
            (it.tag as? Boolean)?.let { flag ->
                val result = show xor flag
                it.visibility = if (result) View.VISIBLE else View.GONE
            }
        }
    }

    private fun onFirstFrameAvailable(isFirst: Boolean) {
        if (isFirst) {
            // 初始化默认的打光参数
            iphoneCameraNewViewModel.updateDefaultRelight()
            // 初始化默认的补光参数
            iphoneCameraNewViewModel.updateDefaultFillLight()
            // 初始化默认的风格参数
            iphoneCameraNewViewModel.updateDefaultStyle()
            // 初始化默认的白平衡参数
            iphoneCameraNewViewModel.updateDefaultWhiteBalance()
            // 初始化默认的滤镜参数
            iphoneCameraNewViewModel.changeDefaultFilter()

            coverContainer.postDelayed({
                if (AppTools.isFinishing(getSafeActivity())) {
                    return@postDelayed
                }
                UIHelper.runOnIdleTiming {
                    if (getSafeActivity().isFinishing) {
                        return@runOnIdleTiming
                    }

                    // 处理协议
                    handleProtocol(getSafeActivity().intent)
                }
            }, 250)
        }
    }

    private fun updateWindowBrightness(brightness: Float) {
        val layoutParams = activity?.window?.attributes
        layoutParams?.screenBrightness = brightness
        activity?.window?.attributes = layoutParams
    }

    /**
     * 显示引导视频教程
     */
    private fun checkGuide(action: (() -> Unit)) {
        val assets = requireActivity().assets
        XDialog {
            VideoPictureTips {
                val pathPag = if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    "iphone_model/iphone_model_guide_asia.pag"
                } else {
                    "iphone_model/iphone_model_guide_other.pag"
                }
                pagConfig = PagConfig(PAGFile.Load(assets, pathPag))
                content = arrayListOf(R.string.v77120_B_36.text())
                PositiveButton(text = R.string.try_it_now.text()) {
                    action.invoke()
                    it.dismiss()
                }

                cancelOutside = false
                cancelAble = true
                pagConfig?.clickPagClose = false
                onDismissListener = DialogInterface.OnDismissListener {
                    action.invoke()
                }
            }
        }.show()
    }

    private fun handleProtocol(intent: Intent?) {
        intent?.let { it ->
            it.getSerializableExtra(RouterEntity.DEEP_LINK)?.takeIf { it is RouterEntity }
                ?.let { webEntity ->
                    val entity = webEntity as RouterEntity
                    // 如果路径非空，判断是否有路径可以匹配，如果不能匹配判断是否是最新版本
                    if (!TextUtils.isEmpty(entity.lastPathSegment)) {
                        if (!RouterManager.isIphoneProtocolPath(entity.lastPathSegment!!)) {
                            return
                        }
                    }
                    iphoneCameraNewViewModel.handleProtocol(entity)
                }
        }
    }
}