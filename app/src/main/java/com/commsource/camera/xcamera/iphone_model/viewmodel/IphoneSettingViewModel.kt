package com.commsource.camera.xcamera.iphone_model.viewmodel

import android.app.Application
import android.graphics.Rect
import android.text.TextUtils
import android.view.animation.DecelerateInterpolator
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.util.XAnimator
import com.commsource.camera.xcamera.bean.LayoutShiftResult
import com.commsource.camera.xcamera.cover.BaseConfigViewModel
import com.commsource.camera.xcamera.cover.GestureLiveData
import com.commsource.camera.xcamera.iphone_model.IphoneModelCache
import com.commsource.camera.xcamera.usecase.CalculateCameraPreviewHeightUseCase
import com.commsource.camera.xcamera.usecase.CalculateCameraPreviewMarginTopUseCase
import com.commsource.camera.xcamera.usecase.CalculateLayoutShiftUseCase
import com.commsource.camera.xcamera.usecase.CheckCameraPreviewOverlapUseCase
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.LOGV_Camera
import com.commsource.util.UIHelper
import com.commsource.util.dp
import com.commsource.widget.DisplayExtension
import com.meitu.library.media.camera.common.FlashMode
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType


class IphoneSettingViewModel(application: Application) : BaseConfigViewModel(application) {

    companion object {

        private val calculateCameraPreviewHeightUseCase by lazy {
            CalculateCameraPreviewHeightUseCase()
        }

        private val checkCameraPreviewOverlap by lazy {
            CheckCameraPreviewOverlapUseCase(calculateCameraPreviewHeightUseCase)
        }

        private val calculateCameraPreviewMarginTop by lazy {
            CalculateCameraPreviewMarginTopUseCase(
                calculateCameraPreviewHeight = calculateCameraPreviewHeightUseCase,
                checkCameraPreviewOverlap = checkCameraPreviewOverlap
            )
        }

        /**
         * 顶部栏高度
         * 16:9 适配 在满足条件下，TopBar高度需要上移10dp
         */
        val TOP_BAR_HEIGHT by lazy {
            if (checkCameraPreviewOverlap()) 44.dp() else 54.dp
        }


        /**
         * 获取NotchPaddingTop
         */
        fun getCameraSafePaddingTop(): Int {
            return when {
                DisplayExtension.hasCutout() -> DeviceUtils.getStatusHeight()
                else -> 0
            }
        }

        /**
         * 获取安全的顶部距离
         * 这个尺寸提供使用
         */
        fun getNotchPaddingTop(): Int {
            return when {
                DisplayExtension.hasCutout() -> DeviceUtils.getStatusHeight()
                else -> 0
            }
        }

        /**
         * 计算相机预览框的核心方法 准确的计算 margin和机型的问题
         * 计算相机预览框的MarginTop问题
         * 计算相机预览框距离顶部的高度问题
         */
        fun calculateCameraPreviewMarginTop(ratio: Int?): Int =
            calculateCameraPreviewMarginTop(
                ratio = ratio,
                topBarHeight = TOP_BAR_HEIGHT
            )

        /**
         * 计算相机预览高度的核心方法 准确计算预览高度
         * 通过预览比例获取预览框的相机高度
         * 包括底层获取真实的预览尺寸高度也是用过这个方法
         */
        fun calculateCameraPreviewHeight(ratio: Int?): Int = calculateCameraPreviewHeightUseCase(ratio)

        /**
         * 获取相机TopBar底部的高度位置 也就是顶部topbar之下可视位置
         */
        fun calculateTopBarSize(ratio: Int?): Int {
            return Math.max(
                TOP_BAR_HEIGHT + getCameraSafePaddingTop(),
                calculateCameraPreviewMarginTop(ratio)
            )
        }
    }

    private val calculateLayoutShift by lazy {
        CalculateLayoutShiftUseCase(checkCameraPreviewOverlap)
    }

    val settingVisibleEvent by lazy { MutableLiveData<Boolean>() }

    /**
     * 是否显示网格线
     */
    val showGridLineEvent by lazy { MutableLiveData<Boolean>() }

    // 延迟拍摄类型
    private val _countDownTypeEvent = MutableLiveData<Int>()
    val countDownTypeEvent: LiveData<Int> = _countDownTypeEvent

    /**
     * 是否显示radio弹窗
     */
    val showRadioEvent by lazy { MutableLiveData<Boolean>() }

    /**
     * 闪光灯模式
     */
    val flashModeEvent by lazy { GestureLiveData<String>(FlashMode.OFF) }

    /**
     * 补光模式
     */
    val fillLightModeEvent by lazy { GestureLiveData<Boolean>(false) }

    /**
     * 变更闪关灯状态
     */
    fun changeFlashMode(isFrontCamera: Boolean, fromUser: Boolean) {
        if (isFrontCamera) {
            fillLightModeEvent.isFromUser = fromUser
            fillLightModeEvent.value = fillLightModeEvent.value != true
        } else {
            flashModeEvent.isFromUser = fromUser
            when (flashModeEvent.value) {
                FlashMode.OFF -> flashModeEvent.value = FlashMode.TORCH
                else -> flashModeEvent.value = FlashMode.OFF
            }
        }
    }

    fun isFlashTorch(): Boolean {
        return flashModeEvent.value == FlashMode.TORCH
    }

    /**
     * 是否显示radio弹窗
     */
    val showHDVideoSelectorEvent by lazy { MutableLiveData<Boolean>() }

    /**
     *
     * 触屏拍照
     */
    val touchPicEvent by lazy { MutableLiveData<Boolean>() }

    /**
     * 屏幕预览比例切换
     */
    val screenRatioChangeEvent by lazy { GestureLiveData<Int>(IphoneModelCache.getIphoneModelCacheEntity().ratio) }

    /**
     * 临时的相机可视区域矩形
     */
    private val tempCameraViewPortRect = Rect()

    /**
     * 正在改变的相机可视区域
     */
    private val changeCameraViewPortRect = Rect()

    // 预览尺寸、替换 onCameraPreviewSizeChange 使用
    private val _previewSizeLiveData = MutableLiveData<Rect>()
    val previewSizeLiveData: LiveData<Rect> get() = _previewSizeLiveData

    // 预览动画、替换 onCameraPreviewChangeListener 使用
    private val _previewAnimLiveData = MutableLiveData<Pair<Float, Rect>>()
    val previewAnimLiveData: LiveData<Pair<Float, Rect>> get() = _previewAnimLiveData

    /**
     * Topbar和底部模式选择栏是否需要偏移
     */
    private val _layoutShiftLiveData = MutableLiveData<LayoutShiftResult?>()
    val layoutShiftLiveData: LiveData<LayoutShiftResult?> = _layoutShiftLiveData

    /**
     * 预览比例切换
     */
    private var ratio: Int

    var bottomSizeParam = IphoneFunctionViewModel.BottomSizeParam()

    /**
     * 屏幕比例切换带来的改变
     */
    private val changeAnimator = XAnimator.ofFloat(0f, 1f)
        .duration(250)
        .interpolator(DecelerateInterpolator())
        .setAnimationListener(object : XAnimator.XAnimationListener {

            override fun onAnimationEnd(animation: XAnimator?) {
            }

            override fun onAnimationCancel(animation: XAnimator?) {
            }

            override fun onAnimationStart(animation: XAnimator?) {
            }

            override fun onAnimationUpdate(fraction: Float, value: Float) {
                val tempTop =
                    tempCameraViewPortRect.top + fraction * (cameraViewPortRect.top - tempCameraViewPortRect.top)
                val tempBottom =
                    tempCameraViewPortRect.bottom + fraction * (cameraViewPortRect.bottom - tempCameraViewPortRect.bottom)
                changeCameraViewPortRect.set(tempCameraViewPortRect)
                changeCameraViewPortRect.top = tempTop.toInt()
                changeCameraViewPortRect.bottom = tempBottom.toInt()
                // 通知相机预览比例窗口变化
                _previewAnimLiveData.postValue(Pair(fraction, changeCameraViewPortRect))
            }
        })

    init {
        //可视屏幕区域
        fullScreenRect.set(0, 0, DeviceUtils.getScreenWidth(), DeviceUtils.getScreenHeight())
        //计算相机预览的PortRect
        //获取默认的预览比例
        ratio = IphoneModelCache.getIphoneModelCacheEntity().ratio
        //记录首次ViewPort变化
        //首次变化不发送动画变更的通知
        changeCameraPreviewRatio(ratio, fromUser = false, withAnimation = false)

        _countDownTypeEvent.value = IphoneModelCache.getIphoneModelCacheEntity().openDown
        determineLayoutShift()
    }

    /**
     * 初始化全屏
     */
    fun initFullScreenRect(width: Int, height: Int) {
        fullScreenRect.set(0, 0, width, height)
        changeCameraPreviewRatio(this.ratio, fromUser = false, withAnimation = false)
    }

    /**
     * 提供变更屏幕比例
     */
    private fun updateCameraPreviewRatio(@CameraRatioType ratio: Int, fromUser: Boolean) {
        this.ratio = ratio
        changeCameraPreviewRatio(ratio, fromUser, true)
    }

    /**
     * 提供变更屏幕比例
     */
    fun updateCameraPreviewRatio(@CameraRatioType ratio: Int) {
        updateCameraPreviewRatio(ratio, true)
    }


    /**
     * 改变相机可视窗口
     */
    private fun changeCameraPreviewRatio(ratio: Int, fromUser: Boolean, withAnimation: Boolean) {
        //内部是一种策略 不同相机根据策略切换适配规则
        //保存旧屏幕位置
        screenRatioChangeEvent.isFromUser = fromUser
        screenRatioChangeEvent.value = ratio
        tempCameraViewPortRect.set(cameraViewPortRect)
        //计算新的预览尺寸
        calculateCameraVisibleSize(ratio, cameraViewPortRect)
        //通知相机预览比例窗口变化
        //发送屏幕比例的切换逻辑以及实现基础的回调实现
        _previewSizeLiveData.postValue(cameraViewPortRect)
        //发送动态逻辑
        if (withAnimation) {
            if (tempCameraViewPortRect.top != cameraViewPortRect.top || tempCameraViewPortRect.bottom != cameraViewPortRect.bottom) {
                changeAnimator.cancel()
                changeAnimator.start()
            }
        }
    }

    /**
     * 显示对应Setting
     */
    fun showSetting(isShow: Boolean) {
        // 如果状态相同不回调
        if (settingVisibleEvent.value == isShow) {
            return
        }
        if (settingVisibleEvent.value == null && !isShow) {
            return
        }
        if (UIHelper.isMainThread()) {
            settingVisibleEvent.value = isShow
        } else {
            settingVisibleEvent.postValue(isShow)
        }
    }

    fun showHDVideoSelector(isShow: Boolean) {
        if (showHDVideoSelectorEvent.value == isShow) {
            return
        }
        if (showHDVideoSelectorEvent.value == null && !isShow) {
            return
        }
        if (UIHelper.isMainThread()) {
            showHDVideoSelectorEvent.value = isShow
        } else {
            showHDVideoSelectorEvent.postValue(isShow)
        }
    }

    fun dismissAll() {
        showSetting(false)
        showRadio(false)
        showHDVideoSelector(false)
    }

    /**
     * 显示对应Setting
     */
    fun showRadio(isShow: Boolean) {
        // 如果状态相同不回调
        if (showRadioEvent.value == isShow) {
            return
        }
        if (showRadioEvent.value == null && !isShow) {
            return
        }
        if (UIHelper.isMainThread()) {
            showRadioEvent.value = isShow
        } else {
            showRadioEvent.postValue(isShow)
        }
    }

    /**
     * 是否显示radio
     */
    fun isShowRadio(): Boolean {
        return showRadioEvent.value != null && showRadioEvent.value!!
    }

    /**
     * 跟随画幅的view 需要向上移动的距离
     * view需要和屏幕底部重合
     */
    fun getFloatViewTranslateY(): Float {
//        var cameraVisibleRect = Rect()
//        calculateCameraVisibleSize(screenRatioChangeEvent.value!!, cameraVisibleRect)
//        val maxTranslateY = bottomSizeParam.mEffectBarHeight
//        var translateY = fullScreenRect.bottom - cameraVisibleRect.bottom
//        "translateY $translateY maxTranslateY $maxTranslateY".LOGV_Camera()
//        if (translateY < maxTranslateY) {
//            translateY = maxTranslateY
//        } 200
        return -getBottomPanelHeight().toFloat()
    }

    fun getBottomPanelHeight(): Int {
        val cameraVisibleRect = Rect()
        calculateCameraVisibleSize(screenRatioChangeEvent.value!!, cameraVisibleRect)
        val maxTranslateY = bottomSizeParam.mBottomBarHeight
        var translateY = fullScreenRect.bottom - cameraVisibleRect.bottom
        "translateY $translateY maxTranslateY $maxTranslateY".LOGV_Camera()
        if (translateY < maxTranslateY) {
            translateY = maxTranslateY
        }
        return translateY
    }

    /**
     * 计算相机可视区域
     */
    fun calculateCameraVisibleSize(ratio: Int, rect: Rect) {
        val top = calculateCameraPreviewMarginTop(ratio)
        val previewHeight = calculateCameraPreviewHeight(ratio)
        when (ratio) {
            CameraRatioType.PICTURE_RATIO_1_1 -> {
                val screenWidth = DeviceUtils.getScreenWidth()
                //1:1比例下 相机ViewPort
                rect.set(0, top, screenWidth, top + previewHeight)
            }

            CameraRatioType.PICTURE_RATIO_4_3 -> {
                val screenWidth = DeviceUtils.getScreenWidth()
                //3:4比例下 相机ViewPort
                rect.set(0, top, screenWidth, top + previewHeight)
            }

            CameraRatioType.PICTURE_RATIO_9_16 -> {
                val screenWidth = DeviceUtils.getScreenWidth()
                //9:16比例下 相机ViewPort
                rect.set(0, top, screenWidth, top + previewHeight)
            }

            CameraRatioType.PICTURE_RATIO_FULL -> {
                //全屏比例下 相机ViewPort
                rect.set(0, top, fullScreenRect.width(), fullScreenRect.height())
            }
        }
    }

    /**
     * 获取相机TopBar底部的高度位置 也就是顶部topbar之下可视位置
     */
    fun calculateTopBarSize(): Int {
        return Companion.calculateTopBarSize(ratio)
    }

    override fun addOnCameraPreviewChangeListener(onCameraPreviewChangeListener: OnCameraPreviewChangeListener) {
    }

    fun isFullScreen(): Boolean {
        return screenRatioChangeEvent.value == CameraRatioType.PICTURE_RATIO_FULL
    }

    fun is11CameraRatio(): Boolean {
        return screenRatioChangeEvent.value == CameraRatioType.PICTURE_RATIO_1_1
    }

    fun is43CameraRatio(): Boolean {
        return screenRatioChangeEvent.value == CameraRatioType.PICTURE_RATIO_4_3
    }

    fun is916CameraRatio(): Boolean {
        return screenRatioChangeEvent.value == CameraRatioType.PICTURE_RATIO_9_16
    }

    fun switchGridLine(): Boolean {
        val now = !IphoneModelCache.getIphoneModelCacheEntity().showGrid
        showGridLineEvent.postValue(now)
        IphoneModelCache.getIphoneModelCacheEntity().showGrid = now
        IphoneModelCache.updateIphoneModelEntity()
        return now
    }

    fun logEventOnClick(cameraMode: Int, iconName: String, iconState: String?) {
        val map = HashMap<String, String>(8)
        when (cameraMode) {
            CameraMode.CAPTURE_MODE -> map["mode_a"] = "shoot"
            CameraMode.MOVIE_MODE -> map["mode_a"] = "movie"
            else -> map["mode_a"] = "video"
        }
        map["camera_mode"] = "iphone"
        map["icon"] = iconName
        if (!TextUtils.isEmpty(iconState)) {
            map["点击后状态"] = iconState!!
        }
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIE_SET_CLICK, map)
    }

    fun changeCountDownType(): Int {
        val openDown = (IphoneModelCache.getIphoneModelCacheEntity().openDown + 1) % 3
        IphoneModelCache.getIphoneModelCacheEntity().openDown = openDown
        IphoneModelCache.updateIphoneModelEntity()
        _countDownTypeEvent.value = openDown
        return openDown
    }

    private fun determineLayoutShift() {
        _layoutShiftLiveData.value = calculateLayoutShift()
    }
}