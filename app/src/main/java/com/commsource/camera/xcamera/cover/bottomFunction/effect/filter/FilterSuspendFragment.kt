package com.commsource.camera.xcamera.cover.bottomFunction.effect.filter

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import com.commsource.beautyfilter.NewBeautyFilterManager
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.constants.FilterConstants
import com.commsource.beautyplus.databinding.FragmentFilterSuspendBinding
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.xcamera.cover.CameraCaptureViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.camera.xcamera.cover.bottomFunction.BottomFunction
import com.commsource.camera.xcamera.cover.bottomFunction.BottomInInterpolator
import com.commsource.camera.xcamera.cover.bottomFunction.BottomOutInterpolator
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.widget.XSeekBar
import com.meitu.library.util.device.DeviceUtils

/**
 * @Desc : 滤镜悬浮功能Fragment
 * <AUTHOR> Bear - 2020/5/7
 */
class FilterSuspendFragment : BaseBottomSubFragment() {
    /*
        滤镜Alpha记忆逻辑：
        - 无AR效果且Look无时，SP记忆最后一个选中滤镜的Alpha值
        - 任何情况下，内存临时记忆各个滤镜用户选中的Alpha值
        - 切换Look、切换AR时，重置内存临时记忆的各个滤镜用户选中的Alpha值
     */

    private val cameraFilterViewModel: CameraFilterViewModel by lazy {
        ViewModelProviders.of(mActivity as BaseActivity).get(CameraFilterViewModel::class.java)
    }

    /**
     * 用于获取当前模式。
     */
    private val cameraCaptureViewModel: CameraCaptureViewModel by lazy {
        ViewModelProviders.of(mActivity as BaseActivity).get(CameraCaptureViewModel::class.java)
    }

    lateinit var  mViewBinding:FragmentFilterSuspendBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        mViewBinding = FragmentFilterSuspendBinding.inflate(layoutInflater,container,false)
        return mViewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViewModel()
        initViews()
    }

    private fun initViewModel() {
        cameraFilterViewModel.applyFilterEvent.observe(viewLifecycleOwner, Observer { filterWrapper ->
            val filter = filterWrapper?.filter
            if (filter != null && filter.id != FilterConstants.ORIGINAL_ID) {
                mViewBinding.vSeekBar.setProgress(filter.userAlpha)
                // 因为高级美颜会改动到Alpha字段值，所以Alpha字段值不是原素材的默认值
                mViewBinding.vSeekBar.defaultPosition = filter.getDefaultAlpha() / 100f
            }
        })
    }

    private fun initViews() {
        mViewBinding.vSeekBar.addOnProgressChangeListener(object : XSeekBar.OnProgressChangeListener {

            override fun onPositionChange(progress: Int, leftDx: Float,fromUser:Boolean) {
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                cameraFilterViewModel.cacheCurrentFilterAlpha(progress)
                // 统计滑杆
                cameraFilterViewModel.applyFilterEvent.value?.let {
                    when (cameraCaptureViewModel.getMode()) {
                        CameraMode.MOVIE_MODE -> MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIEMOVIE_FILTER_SLIP, HashMap<String, String>(4).apply {
                            put("滑竿值", progress.toString())
                            putAll(NewBeautyFilterManager.filterInfoMap(it))
                        })
                        CameraMode.CAPTURE_MODE -> MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIE_FILTER_SLIP, HashMap<String, String>(4).apply {
                            put("滑竿值", progress.toString())
                            putAll(NewBeautyFilterManager.filterInfoMap(it))
                        })
                        CameraMode.VIDEO_MODE -> MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIEVIDEO_FILTER_SLIP, HashMap<String, String>(4).apply {
                            put("滑竿值", progress.toString())
                            putAll(NewBeautyFilterManager.filterInfoMap(it))
                        })
                    }

                }
            }

            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                cameraFilterViewModel.changeCurrentFilterAlpha(progress, fromUser)
            }

            override fun onStartTracking(progress: Int, leftDx: Float) {
            }
        })
    }

    override fun animateIn(action: () -> Unit) {
        super.animateIn(action)
        mViewBinding.root.animate().setListener(null).cancel()
        mViewBinding.root.translationY = DeviceUtils.dip2fpx(35f)
        mViewBinding.root.alpha = 0f
        mViewBinding.root.post {
            mViewBinding.root.animate().translationY(0f)
                    .alpha(1f)
                    .withLayer()
                    .setInterpolator(BottomInInterpolator())
                    .setDuration(BottomFunction.BOTTOM_DURATION)
                    .start()
        }
    }

    override fun animateOut(action: () -> Unit) {
        mViewBinding.root.animate().setListener(null).cancel()
        mViewBinding.root.post {
            mViewBinding.root.animate().setDuration(BottomFunction.BOTTOM_DURATION)
                    .withLayer()
                    .setInterpolator(BottomOutInterpolator())
                    .alpha(0f)
                    .translationY(DeviceUtils.dip2fpx(35f))
                    .setListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            super.onAnimationEnd(animation)
                            action.invoke()
                        }
                    })
                    .start()
        }
    }
}