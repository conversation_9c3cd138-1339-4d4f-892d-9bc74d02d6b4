package com.commsource.camera.xcamera.iphone_model.hd

import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.xcamera.CameraParams
import com.commsource.config.SelfieConfig
import com.meitu.library.media.camera.common.AspectRatio
import com.meitu.library.media.camera.common.AspectRatioGroup
import com.meitu.library.media.camera.common.CameraInfo
import com.meitu.library.media.camera.common.PictureSize
import com.meitu.library.media.camera.common.PreviewParams
import com.meitu.library.media.camera.common.PreviewSize
import com.meitu.library.media.renderarch.config.MTConfigPreviewParams
import kotlin.math.abs
import kotlin.math.min

open class IphoneCameraConfig : MTConfigPreviewParams() {

    private var cameraParams: CameraParams? = null

    fun setCameraParams(cameraParams: CameraParams?) {
        this.cameraParams = cameraParams
    }

    override fun configPreviewParams(aspectRatio: AspectRatio?): PreviewParams {
        val previewParams = PreviewParams(aspectRatio)
        cameraParams!!.updatePreviewParams(previewParams, cameraParams!!.pictureRatio)
        return previewParams
    }

    override fun configPictureSize(cameraInfo: CameraInfo): PictureSize {
        HighResolutionHelper.updateCameraHDSupport(cameraInfo.supportedPreviewSizes, cameraInfo.facing)

        val ratio = cameraInfo.currentAspectRatio
        val isCameraFullRatio =
            ratio === AspectRatioGroup.RATIO_16_9 || ratio === AspectRatioGroup.FULL_SCREEN
        var targetRadio = if (isCameraFullRatio) 16f / 9f else 4f / 3f
        val previewSize =
            CameraSizeUtil.getPreviewSize(cameraInfo.supportedPreviewSizes, targetRadio)
        if (isCameraFullRatio && abs((targetRadio - ((previewSize.width * 1.0f) / previewSize.height)).toDouble()) > 0.05f
        ) {
            targetRadio = 4f / 3f
        }
        val pictureSize = CameraSizeUtil.getPictureSize(
            cameraInfo.supportedPictureSizes, targetRadio
        )
        if (pictureSize == null) {
            return PictureSize(640, 480)
        }
        return pictureSize
    }

    override fun configPreviewSize(cameraInfo: CameraInfo, pictureSize: PictureSize?): PreviewSize {
        if (pictureSize == null) {
            return PreviewSize(640, 480)
        }

        val smallPreviewSize = CameraSizeUtil.getPreviewSize(
            cameraInfo.supportedPreviewSizes,
            pictureSize.width.toFloat() / pictureSize.height
        )
        val largePreviewSize = CameraSizeUtil.getPreviewSize(
            cameraInfo.supportedPreviewSizes,
            pictureSize.width.toFloat() / pictureSize.height,
            5000,
            5000
        )
        val hdSupport =
            largePreviewSize.width > smallPreviewSize.width || largePreviewSize.height > smallPreviewSize.height
        HighResolutionHelper.updateHDSupport(smallPreviewSize, largePreviewSize)

        var previewSize: PreviewSize? = null
        val targetRatio = pictureSize.width.toFloat() / pictureSize.height
        HighResolutionHelper.setAvailableResolutionList(
            cameraInfo.supportedPreviewSizes, targetRatio, cameraInfo.facing
        )

        previewSize = if (HighResolutionHelper.captureMode == CameraMode.CAPTURE_MODE) {
            if (SelfieConfig.isIphoneHdEnable() && hdSupport) {
                largePreviewSize
            } else {
                smallPreviewSize
            }
        } else {
            HighResolutionHelper.getVideoPreviewSize(cameraInfo.facing) ?: smallPreviewSize
        }

        if (previewSize == null) {
            previewSize = PreviewSize(640, 480)
        }

        val optPreviewSize: PreviewSize =
            HighResolutionHelper.getDefinedCameraPreviewSize((pictureSize.width * 1.0f) / pictureSize.height)
        val scale = optPreviewSize.height.toFloat() / previewSize.height
        onScalePreviewSize(min(scale, 1.0f))
        return previewSize
    }

    open fun onScalePreviewSize(scale: Float) {

    }
}