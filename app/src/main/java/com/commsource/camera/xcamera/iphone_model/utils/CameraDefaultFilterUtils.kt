package com.commsource.camera.xcamera.iphone_model.utils

import com.commsource.util.DeviceLevelStrategy
import com.commsource.util.LOGV_Camera
import com.commsource.util.phone.DeviceBrandTools


object CameraDefaultFilterUtils {
    val base = "camera/style/prefilter"
    fun getRefTexturePath(): String {
        val level = DeviceLevelStrategy.getDeviceLevelForIphoneCamera()
        val isXiaomiDevice = DeviceBrandTools.getInstance().isXiaomiDevice
        val isRedmiDevice = DeviceBrandTools.getInstance().isRedmiDevice
        val isOppo = DeviceBrandTools.getInstance().isOppo
        val isVivo = DeviceBrandTools.getInstance().isVivo
        val isSamsung = DeviceBrandTools.getInstance().isSamsung
        val tecnoItelInfinix = DeviceBrandTools.getInstance().isTecnoDevice || DeviceBrandTools.getInstance().isItelDevice || DeviceBrandTools.getInstance().isInfinixDevice
        // 三星高端机
        if (isSamsung && (level == DeviceLevelStrategy.VHIGH || level == DeviceLevelStrategy.HIGH)) {
            return "$base/samsung/high/"
        }
        // 三星中端机
        if (isSamsung && level == DeviceLevelStrategy.MID) {
            return "$base/samsung/mid/"
        }
        // 三星低端机
        if (isSamsung && level == DeviceLevelStrategy.LOW) {
            return "$base/samsung/low/"
        }
        val model = DeviceBrandTools.getInstance().redmiCode()
        // 添加测试代码
        "机型判断 isRedmiDevice = $isRedmiDevice model = $model".LOGV_Camera()
        // 红米高中端
        if (isRedmiDevice && (level == DeviceLevelStrategy.VHIGH || level == DeviceLevelStrategy.HIGH || level == DeviceLevelStrategy.MID)) {
            return "$base/redmi/high_mid/"
        }
        // 红米低端
        if (isRedmiDevice && level == DeviceLevelStrategy.LOW) {
            return "$base/redmi/low/"
        }

        // 小米 全品牌
        if (isXiaomiDevice) {
            return "$base/xiaomi/"
        }
        // 适配TECNO、itel 和Infinix，三个品牌全部机型
        if (tecnoItelInfinix) {
            return "$base/tecno_itel_infinix/"
        }

        // oppo 高中端
        if (isOppo && (level == DeviceLevelStrategy.VHIGH || level == DeviceLevelStrategy.HIGH || level == DeviceLevelStrategy.MID)) {
            return "$base/oppo/high_mid/"
        }
        // oppo 低端
        if (isOppo && level == DeviceLevelStrategy.LOW) {
            return "$base/oppo/low/"
        }
        // vivo 全品牌
        if (isVivo) {
            return "$base/vivo/"
        }

        return "$base/default/"
    }
}