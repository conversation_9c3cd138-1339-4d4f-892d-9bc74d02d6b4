package com.commsource.camera.xcamera.cover.window

import android.content.Intent
import android.graphics.Rect
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.Observer
import androidx.lifecycle.OnLifecycleEvent
import com.commsource.beautyplus.R
import com.commsource.beautyplus.armaterial.ArGiphyMaterialViewModel
import com.commsource.beautyplus.databinding.CoverH5Binding
import com.commsource.beautyplus.web.WebActivity
import com.commsource.beautyplus.web.WebConstant
import com.commsource.camera.mvp.annotation.CameraMode
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.camera.xcamera.BpCameraViewModel
import com.commsource.camera.xcamera.cover.AbsLazyCover
import com.commsource.camera.xcamera.cover.CameraCaptureViewModel
import com.commsource.camera.xcamera.cover.CameraConfigViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.BottomFunction
import com.commsource.camera.xcamera.cover.bottomFunction.BottomFunctionViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.effect.EffectFunctionViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.CameraFilterViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.effect.look.LookViewModel
import com.commsource.camera.xcamera.cover.bottomFunction.effect.makeup.MakeupViewModel
import com.commsource.camera.xcamera.cover.confirm.ConfirmViewModel
import com.commsource.config.SelfieConfig
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.MTFirebaseAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.*
import com.meitu.common.AppContext
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.net.NetUtils

/**
 * @Desc : 相机h5入口
 * <AUTHOR> Bear - 2020/4/26
 *
 */
class H5Cover : AbsLazyCover<CoverH5Binding>(), View.OnClickListener, LifecycleObserver {

    val mBottomFunctionViewModel by lazy { getViewModel(BottomFunctionViewModel::class.java) }

    val mCameraCaptureViewModel by lazy { getViewModel(CameraCaptureViewModel::class.java) }

    val mCameraConfigViewModel by lazy { getViewModel(CameraConfigViewModel::class.java) }

    val mBpCameraViewModel by lazy { getViewModel(BpCameraViewModel::class.java) }

    val arGiphyMaterialViewModel by lazy { getViewModel(ArGiphyMaterialViewModel::class.java) }

    val cameraFilterViewModel by lazy { getViewModel(CameraFilterViewModel::class.java) }

    val makeupViewModel by lazy { getViewModel(MakeupViewModel::class.java) }

    val lookViewModel by lazy { getViewModel(LookViewModel::class.java) }

    val effectFunctionViewModel by lazy { getViewModel(EffectFunctionViewModel::class.java) }

    val confirmViewModel by lazy { getViewModel(ConfirmViewModel::class.java) }

    /**
     * 是否有H5
     */
    var hasH5 = false

    /**
     * h5偏移位置
     */
    var h5TranslationYValuer: XAnimatorCalculateValuer = XAnimatorCalculateValuer()

    override fun onClick(v: View?) {
        when (v) {
            mViewBinding?.ivH5 -> {
                val intent = Intent(coverGroup.mActivity, WebActivity::class.java)
                intent.putExtra(
                    WebConstant.PARAMETER_URL,
                    SelfieConfig.getSelfieViewLink(AppContext.context)
                )
                coverGroup.mActivity.startActivity(intent)
                MTFirebaseAnalyticsAgent.logEvent(
                    coverGroup.mActivity,
                    MTAnalyticsConstant.EVENT_SELFIE_H5_CLK,
                    null
                )
                MTAnalyticsAgent.logEvent(MTAnalyticsConstant.EVENT_SELFIE_H5_CLK, null)
            }
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.cover_h5
    }

    override fun initView() {

    }

    override fun onLazyCreate() {
        if (!TextUtils.isEmpty(SelfieConfig.getSelfieViewIconLink(coverGroup.mActivity))
            && !TextUtils.isEmpty(SelfieConfig.getSelfieViewLink(coverGroup.mActivity)) && NetUtils.canNetworking(
                coverGroup.mActivity
            )
            && mBottomFunctionViewModel.newUserCoverVisibility.value == false

        ) {
            coverGroup.mActivity.lifecycle.addObserver(this)
            createCover()
            GlideProxy.with(coverGroup.mActivity)
                .load(SelfieConfig.getSelfieViewIconLink(coverGroup.mActivity))
                .into(mViewBinding?.ivH5)
            //BugFix 懒加载 如果手势很快点击 需要判断结合是否显示bottom来显示
            if (!mBottomFunctionViewModel.hasBottomFunction()) {
                mViewBinding?.ivH5?.visible()
            }
            mViewBinding?.ivH5?.setOnClickListener(this)
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIE_H5_IMP)
            hasH5 = true
        }
    }


    override fun initViewModel() {
        //屏幕比例切换
        mCameraConfigViewModel.screenRatioChangeEvent.observe(
            coverGroup.mActivity,
            Observer { ratio ->
                h5TranslationYValuer.mark(
                    mViewBinding?.ivH5?.translationY
                        ?: 0f,
                    getH5TranslationY(mCameraConfigViewModel.screenRatioChangeEvent.value)
                )
            })
        //底部监听
        mBottomFunctionViewModel.bottomSizeParamEvent.observe(coverGroup.mActivity, Observer {
            //h5入口显示高度
            val h5TranslationY =
                getH5TranslationY(mCameraConfigViewModel.screenRatioChangeEvent.value)
            h5TranslationYValuer.to(h5TranslationY)
            mViewBinding?.ivH5?.translationY = h5TranslationY
        })

        //底部function切换
        mBottomFunctionViewModel.bottomFunctionChangeEvent.observe(coverGroup.mActivity, Observer {
            when (it) {
                null -> {
                    UIHelper.runOnUiThreadDelay({ showH5(true) }, BottomFunction.BOTTOM_DURATION)
                }

                else -> showH5(false)
            }
        })

        //拍照模式改变
        mCameraCaptureViewModel.cameraCaptureModeEvent.observe(coverGroup.mActivity, Observer {
            //h5入口判断
            when (it) {
                CameraMode.VIDEO_MODE,
                CameraMode.MOVIE_MODE,
                CameraMode.CREATE_MONTAGE_MODE -> {
                    showH5(false)
                }

                else -> {
                    showH5(true)
                }
            }
        })

        //视频状态 导致 H5入口改变
        mCameraCaptureViewModel.cameraVideoChangeEvent.observe(coverGroup.mActivity, Observer {
            when (it) {
                CameraCaptureViewModel.START_RECORDING,
                CameraCaptureViewModel.STOP_RECORDING,
                CameraCaptureViewModel.RECORDING -> {
                    showH5(false)
                }

                else -> {
                    showH5(true)
                }
            }
        })

        //Giphy删除
        arGiphyMaterialViewModel.isTouchingArGiphyEvent.observe(coverGroup.mActivity, Observer {
            it?.let {
                if (it) {
                    showH5(false)
                } else {
                    showH5(true)
                }
            }
        })

        // 展示点击遮照。
        mBpCameraViewModel.showClickBarrierEvent.observe(coverGroup.mActivity, Observer {
            if (it) {
                mViewBinding?.vClickBarrier?.visible()
            } else {
                mViewBinding?.vClickBarrier?.gone()
            }
        })

        //退出拍后
        confirmViewModel.exitEvent.observe(coverGroup.mActivity, Observer {
            if (hasH5 && mViewBinding?.ivH5?.isVisible == true) {
                MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIE_H5_IMP)
            }
        })

        mBottomFunctionViewModel.newUserCoverVisibility.observe(coverGroup.mActivity) {
            if (it == true) {
                showH5(false)
            }
        }
    }

    override fun onCameraVisibleSizeChange(cameraViewPort: Rect, fraction: Float) {
        super.onCameraVisibleSizeChange(cameraViewPort, fraction)
        mViewBinding?.ivH5?.translationY = h5TranslationYValuer.calculateValue(fraction)
    }

    override fun onViewRotationChange(rotation: Float, fraction: Float) {
        super.onViewRotationChange(rotation, fraction)
//        mViewBinding?.ivH5?.rotation = rotation
    }

    /**
     * 通过当前比例获取h5的translationY
     */
    private fun getH5TranslationY(ratio: Int?): Float {
        return -when (ratio) {
            CameraRatioType.PICTURE_RATIO_1_1 -> {
//                var rect = Rect()
                val previewHeight = CameraConfigViewModel.calculateCameraPreviewHeight(ratio)
                val marginTop = CameraConfigViewModel.calculateCameraPreviewMarginTop(ratio)
//                mCameraConfigViewModel.calculateCameraVisibleSize(ratio, rect)
                //计算的高度是全屏到1：1比例下 底部栏相夹的高度/2 直接加入
                var height =
                    (mCameraConfigViewModel.fullScreenRect.bottom - (previewHeight + marginTop) - (mBottomFunctionViewModel.bottomSizeParam.mBottomBarHeight / 2 + DeviceUtils.dip2fpx(
                        45f
                    )))
                //1:1比例下 位移
                (mBottomFunctionViewModel.bottomSizeParam.mBottomBarHeight / 2 + 45.dp() + (height - 40.dp()) / 2)
            }

            CameraRatioType.PICTURE_RATIO_4_3 -> {
                (mBottomFunctionViewModel.bottomSizeParam.mBottomBarHeight + 14.dpf())
            }

            CameraRatioType.PICTURE_RATIO_9_16 -> {
                (mBottomFunctionViewModel.bottomSizeParam.mBottomBarHeight / 2 + 45.dpf() + 14.dpf())
            }

            CameraRatioType.PICTURE_RATIO_FULL -> {
                (mBottomFunctionViewModel.bottomSizeParam.mBottomBarHeight / 2 + 45.dpf() + 14.dpf())//(80+5 +  17)/2
            }

            else -> {
                0f
            }
        }.coerceAtLeast(mBottomFunctionViewModel.bottomSizeParam.mBottomBarHeight.toFloat() + 14.dp)
    }

    /**
     * 显示H5入口
     */
    fun showH5(isShow: Boolean) {
        if (AppTools.isFinishing(coverGroup.mActivity)) {
            return
        }
        val canShow = isShow && mBottomFunctionViewModel.newUserCoverVisibility.value == false
        if (hasH5) {
            if (canShow) {
                if (!mBottomFunctionViewModel.hasBottomFunction()
                    && !mCameraCaptureViewModel.isCreateMontageMode()
                    && !mCameraCaptureViewModel.isVideoMode()
                    && !mCameraCaptureViewModel.isMovieMode()
                    && !mCameraCaptureViewModel.isRecording()
                    && !mBpCameraViewModel.isVideoRecording()
                ) {

                    mViewBinding?.ivH5?.visible()
                } else {
                    mViewBinding?.ivH5?.gone()
                }
            } else {
                mViewBinding?.ivH5?.gone()
            }
        }
    }

    /**
     * 监听物理案件点击。
     */
    override fun onDispatchPhysicKeyEvent(event: KeyEvent?): Boolean {
        if (mViewBinding?.vClickBarrier?.visibility == View.VISIBLE) {
            // 禁止界面点击时，同时静止掉所有操作。
            return true
        }
        return super.onDispatchPhysicKeyEvent(event)
    }

    var isFirst = true

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun onResume() {
        if (isFirst) {
            isFirst = false
            return
        }
        if (hasH5 && mViewBinding?.ivH5?.isVisible == true) {
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIE_H5_IMP)
        }
    }

}