package com.commsource.camera.xcamera.stamp_model.cover

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.content.Intent
import android.graphics.Color
import android.graphics.Rect
import android.net.Uri
import android.provider.Settings
import android.text.TextUtils
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.lifecycle.Observer
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverStampTipsBinding
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.xcamera.cover.AbsLazyCover
import com.commsource.camera.xcamera.cover.CameraCaptureViewModel
import com.commsource.camera.xcamera.cover.CameraConfigViewModel.Companion.TOP_BAR_HEIGHT
import com.commsource.camera.xcamera.cover.CameraConfigViewModel.Companion.getCameraSafePaddingTop
import com.commsource.camera.xcamera.stamp_model.LocationManager
import com.commsource.camera.xcamera.stamp_model.viewmodel.StampCameraViewModel
import com.commsource.camera.xcamera.stamp_model.viewmodel.StampSettingViewModel
import com.commsource.camera.xcamera.stamp_model.viewmodel.StampTipsViewModel
import com.commsource.util.ViewUtils
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.setMarginTop
import com.commsource.util.string
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.DisplayExtension
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.purchases.common.utils.AndroidFileUtil.getPackageName
import kotlin.math.max


class StampTipsCover : AbsLazyCover<CoverStampTipsBinding>() {

    private val tipsViewModel: StampTipsViewModel by lazy { getViewModel(StampTipsViewModel::class.java) }

    private val cameraCaptureViewModel by lazy { getViewModel(CameraCaptureViewModel::class.java) }
    private val stampSettingViewModel by lazy { getViewModel(StampSettingViewModel::class.java) }
    private val stampCameraViewModel by lazy { getViewModel(StampCameraViewModel::class.java) }
    override fun getLayoutId(): Int {
        return R.layout.cover_stamp_tips
    }

    override fun onLazyCreate() {
        createCover()
        //相机提示
        tipsViewModel.tipsEvent.observe(coverGroup.mActivity, Observer { tips ->
            if (TextUtils.isEmpty(tips.first)) {
                //隐藏对应提示信息
            } else {
                //显示对应提示
                showTips(tips.first, tips.second)
            }
        })

        // 普通提示。
        tipsViewModel.floatingTipsEvent.observe(coverGroup.mActivity, Observer { tips ->
            if (!TextUtils.isEmpty(tips)) {
                showFloatingTips(tips, null, tipsViewModel.floatingTipsEvent.isLeftToRight)
            }
        })

        //提示
        tipsViewModel.multiFloatingTipsEvent.observe(coverGroup.mActivity, Observer {
            it?.let {
                showFloatingTips(
                        it.first,
                        it.second,
                        tipsViewModel.multiFloatingTipsEvent.isLeftToRight
                )
            }
        })

        tipsViewModel.showLocationAllowTipsEvent.observe(coverGroup.mActivity, Observer {
            if (it) {
                mViewBinding?.locatioinTips?.visible()
            } else {
                mViewBinding?.locatioinTips?.gone()
            }
        })

        tipsViewModel.showNetWorkTipsEvent.observe(coverGroup.mActivity, Observer {
            if (it) {
                mViewBinding?.tvNetWorkTips?.text = R.string.v77140_B_3.text() +","+ R.string.v77140_B_4.text()
                mViewBinding?.netWorkTips?.visible()
            } else {
                mViewBinding?.netWorkTips?.gone()
            }
        })

        stampSettingViewModel.screenRatioChangeEvent.observe(coverGroup.mActivity, Observer {
            var cameraVisibleRect = Rect()
            stampSettingViewModel.calculateCameraVisibleSize(it, cameraVisibleRect)
            val marginTop = cameraVisibleRect.top
            val finalMarginTop = max(marginTop, TOP_BAR_HEIGHT + getCameraSafePaddingTop())
            ViewUtils.setMarginTop(mViewBinding?.locatioinTips, (finalMarginTop + 10.dpf).toInt())
            ViewUtils.setMarginTop(mViewBinding?.netWorkTips, (finalMarginTop + 10.dpf).toInt())
        })
    }

    override fun initView() {
        mViewBinding?.tvTips?.setMarginTop(if (DisplayExtension.hasCutout()) 190.dp() + DeviceUtils.getStatusHeight() else 190.dp())
        mViewBinding?.tvSetting?.setOnClickListener {
            if (!LocationManager.isLocationEnabled(coverGroup.mActivity)) {
                val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                coverGroup.mActivity.startActivity(intent)
            } else {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts("package", coverGroup.mActivity.applicationContext.packageName, null)
                intent.setData(uri)
                coverGroup.mActivity.startActivity(intent)
            }


        }
        mViewBinding?.tvRetry?.setOnClickListener {
            stampCameraViewModel.stampTextFill()
        }
    }

    override fun initViewModel() {
        cameraCaptureViewModel.cameraCaptureModeEvent.observe(coverGroup.mActivity, Observer {
            when (it) {
                CameraMode.CAPTURE_MODE -> {
                    mViewBinding?.tvTips?.setMarginTop(if (DisplayExtension.hasCutout()) 190.dp() + DeviceUtils.getStatusHeight() else 190.dp())
                }

                else -> {
                    mViewBinding?.tvTips?.setMarginTop(if (DisplayExtension.hasCutout()) 140.dp() + DeviceUtils.getStatusHeight() else 140.dp())
                }
            }
        })
    }

    /**
     * 显示提示信息
     */
    private fun showTips(tips: String, duration: Long) {
        createCover()
        mViewBinding!!.tvTips.animate().setStartDelay(0).setListener(null).cancel()
        mViewBinding!!.tvTips.visible()
        mViewBinding!!.tvTips.text = tips
        mViewBinding!!.tvTips.animate()
                .alpha(1f)
                .setStartDelay(0)
                .setDuration(200)
                .setListener(object : AnimatorListenerAdapter() {

                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        mViewBinding!!.tvTips.animate().setListener(null)
                                .alpha(0f)
                                .setStartDelay(duration)
                                .setDuration(200)
                                .start()
                    }
                })
                .start()
    }

    /**
     * 显示悬浮Tips
     */
    private fun showFloatingTips(
            tips: String?,
            subTips: String?,
            leftToRight: Boolean = true,
            colorStr: String? = null
    ) {
        createCover()
        tips?.let {
            if (TextUtils.isEmpty(colorStr)) {
                mViewBinding!!.flColor.gone()
            } else {
                mViewBinding!!.flColor.visible()
                mViewBinding!!.flColor.delegate.backgroundColor = Color.parseColor(colorStr)
            }
            mViewBinding!!.rlFloating.animate().setStartDelay(0).setListener(null).cancel()
            mViewBinding!!.rlFloating.visible()
            mViewBinding!!.rlFloating.alpha = 0f
            mViewBinding!!.tvFloating.text = tips
            mViewBinding!!.tvFloatingSub.run {
                if (subTips.isNullOrEmpty()) {
                    gone()
                } else {
                    visible()
                    text = subTips
                }
            }
            mViewBinding!!.rlFloating.translationX =
                    if (leftToRight) (-DeviceUtils.dip2px(30f)).toFloat() else (DeviceUtils.dip2px(30f)).toFloat()
            mViewBinding!!.rlFloating.animate()
                    .alpha(1f)
                    .setStartDelay(0)
                    .translationX(0f)
                    .setInterpolator(DecelerateInterpolator())
                    .setDuration(1000)
                    .setListener(object : AnimatorListenerAdapter() {

                        override fun onAnimationEnd(animation: Animator) {
                            super.onAnimationEnd(animation)
                            mViewBinding!!.rlFloating.animate().setListener(null)
                                    .alpha(0f)
                                    .translationX(
                                            if (leftToRight) 30.dpf() else (-30).dpf()
                                    )
                                    .setInterpolator(AccelerateInterpolator())
                                    .setDuration(1000)
                                    .start()
                        }
                    })
                    .start()
        }
    }

}