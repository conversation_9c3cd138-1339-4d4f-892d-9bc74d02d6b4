package com.commsource.camera.xcamera.glow_model.cover

import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverGlowTextLayoutBinding
import com.commsource.camera.xcamera.glow_model.GlowCameraViewModel
import com.pixocial.framework.cover.AbstractViewBindingCover

class GlowTextCover : AbstractViewBindingCover<CoverGlowTextLayoutBinding>() {

    private val viewModel by lazy { fragmentViewModel(GlowCameraViewModel::class) }

    override fun getLayoutId() = R.layout.cover_glow_text_layout

    override fun onViewDidLoad() {

    }

    override fun onBindViewModel() {

//        viewModel.cameraEffectManager
//            .applyAREffect(
//                CameraArCustomKey.AR,
//                "/storage/emulated/0/Android/data/com.commsource.beautyplus/files/ar_material/ar_file/500016/ar/configuration.plist"
//            )
//            .commit()
//        var count = 0
//        viewBinding.txt.setOnClickListener {
//            if (count % 2 == 0) {
//                viewModel.cameraEffectManager.setArEffectEnable(CameraArCustomKey.AR, false)
//                    .commit()
//            } else {
//                viewModel.cameraEffectManager.setArEffectEnable(CameraArCustomKey.AR, true)
//                    .commit()
//            }
//            count++
//
//            viewModel.cameraEffectManager
//                .applyAREffect(
//                    CameraArCustomKey.MATERIAL2,
//                    "armaterial/ar_debug/configuration.plist"
//                )
//                .commit()
//
//            viewModel.cameraEffectManager
//                .applyAREffect(
//                    CameraArCustomKey.FACE_SHAPE,
//                    "newcameraeffect/ARRemodel_Face/configuration_ziran.plist"
//                )
//                .updateFaceLift(CameraArCustomKey.FACE_SHAPE, CameraBeautyType.Type_Face_Basic, 1f)
//                .commit()
//
//            viewModel.cameraEffectManager
//                .applyBeautyEffect(
//                    "newcameraeffect/bp_newBeautyEffect/configuration_beauty_filter.plist",
//                    "newcameraeffect/bp_newBeautyEffect/configuration_reshape_filter_jawlineRetouch.plist"
//                )
//                .updateBeautyEffect(CameraBeautyType.Type_Skin_HD, 1f)
//                .commit()
//
//
//            viewModel.cameraEffectManager
//                .applyAREffect(
//                    CameraArCustomKey.TYPE_LIP_STICK,
//                    "makeup_material/BP_LIP_00000002/ar/configuration.plist"
//                )
//                .updateMakeup(CameraArCustomKey.TYPE_LIP_STICK, 1f)
//                .commit()
//
//            viewModel.cameraEffectManager
//                .applyAREffect(
//                    CameraArCustomKey.TYPE_TRIMMING,
//                    "makeup_material/BP_CON_00000002/ar/configuration.plist"
//                )
//                .updateMakeup(CameraArCustomKey.TYPE_TRIMMING, 1f)
//                .commit()
//
//            viewModel.cameraEffectManager
//                .applyFilter("filter_internal/BP_FIL_00000509/filter/filterConfig.plist")
//                .updateFilter(1f)
//                .commit()

//            viewModel.cameraEffectManager
//                .applyAREffect(
//                    CameraArCustomKey.FACE,
//                    "newcameraeffect/ARRemodel_Face/configuration.plist"
//                )
//                .updateHead(1f)
//                .commit()
//        }
    }
}