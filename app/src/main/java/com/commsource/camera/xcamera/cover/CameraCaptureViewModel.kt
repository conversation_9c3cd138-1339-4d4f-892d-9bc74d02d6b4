package com.commsource.camera.xcamera.cover

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.camera.mvp.CameraConstant
import com.commsource.camera.mvp.annotation.CameraMode
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.bean.PositionModel

/**
 * @Desc : 相机拍照ViewModel
 * <AUTHOR> Bear - 2020/4/24
 *
 * [isMode]TODO 我们需要定义模式的概念 {视频 拍照 电影}是一种模式 {蒙太奇}一种模式 {证件照}也是一种模式
 * 这是我们传递模式的ViewModel 保证各个子Cover能正确的保证Model获取判断正确
 *
 *
 */
class CameraCaptureViewModel : AndroidViewModel {

    companion object {
        //未开始录制状态
        val NORMAL = 0

        //开始录制
        val START_RECORDING = 1

        //录制中
        val RECORDING = 2

        //停止录制
        val STOP_RECORDING = 3
    }

    /**
     * 相机模式切换事件
     * 1.电影模式
     * 2.视频模式
     * 3.拍照模式
     * 4.创建蒙太奇模式
     * 5.快拍模式（外部调用相机获取一张图片）
     *
     */
    val cameraCaptureModeEvent by lazy { NoStickLiveData<Int>() }

    /**
     * 视频录制切换事件
     */
    val cameraVideoChangeEvent by lazy { MutableLiveData<Int>() }

    /**
     * 上一次的模式
     */
    var lastMode: Int? = null

    /**
     * 重置相机按钮
     */
    val cameraResetEvent by lazy { MutableLiveData<Boolean>() }

    /**
     * 是否H5应用AR逻辑
     *
     * 美颜跟AR面板入口都屏蔽
     */
    val h5ArEvent by lazy { MutableLiveData<Boolean>(false) }

    /**
     * 是否H5应用Look逻辑
     *
     * look面板要展示，且不能切换
     */
    val h5LookEvent by lazy { MutableLiveData<Boolean>(false) }

    /**
     * 截屏监听 livedata
     */
    var screenShotEvent = NoStickLiveData<Boolean>()

    constructor(application: Application) : super(application) {
    }

    /**
     * 更新相机拍照模式
     */
    fun updateMode(@CameraMode mode: Int) {
        lastMode = cameraCaptureModeEvent.value
        cameraCaptureModeEvent.value = mode
    }

    fun initModeIfNull(@CameraMode mode: Int) {
        if (cameraCaptureModeEvent.value == null) {
            updateMode(mode)
        }
    }

    /**
     * 还原上次的模式
     */
    fun restoreLastMode() {
        lastMode?.let {
            updateMode(it)
        }
    }

    /**
     * 是否是电影模式
     */
    fun isVideoMode(): Boolean {
        return isMode(CameraMode.VIDEO_MODE)
    }

    /**
     * 是否是拍照模式
     */
    fun isCaptureMode(): Boolean {
        return isMode(CameraMode.CAPTURE_MODE)
    }

    /**
     * 是否是电影模式
     */
    fun isMovieMode(): Boolean {
        return isMode(CameraMode.MOVIE_MODE)
    }

    /**
     * 是否是创建蒙太奇模式
     */
    fun isCreateMontageMode(): Boolean {
        return isMode(CameraMode.CREATE_MONTAGE_MODE)
    }

    /**
     * 是否是快拍模式
     */
    fun isInAppCaptureMode(): Boolean {
        return isMode(CameraMode.IN_APP_CAPTURE_MODE)
    }

    /**
     * 判断处于某种模式
     */
    private fun isMode(@CameraMode mode: Int): Boolean {
        cameraCaptureModeEvent.value?.let {
            return it == mode
        }
        return false
    }

    /**
     * 是否在录制中 分段录制也被计算在对应的UI中
     */
    fun isRecording(): Boolean {
        when (cameraVideoChangeEvent.value) {
            START_RECORDING,
            RECORDING -> {
                return@isRecording true
            }
        }
        return false
    }

    /**
     * 是否在分段录制中
     * 包括录制停止也计算中
     */
    fun isInMultiRecording(): Boolean {
        when (cameraVideoChangeEvent.value) {
            START_RECORDING,
            RECORDING,
            STOP_RECORDING -> {
                return@isInMultiRecording true
            }
        }
        return false
    }

    /**
     * 获取当前模式
     */
    fun getMode(): Int {
        cameraCaptureModeEvent.value?.let {
            return it
        }
        return CameraMode.CAPTURE_MODE
    }

    /**
     * 获取当前模式的统计String。
     */
    fun getModeStatisticString(): String {
        when (getMode()) {
            CameraMode.CAPTURE_MODE -> return "拍摄"
            CameraMode.MOVIE_MODE -> return "电影"
            CameraMode.VIDEO_MODE -> return "视频"
        }
        return "拍摄"
    }

    /**
     * 保存快拍模式
     */
    fun saveIsInAppCaptureMode(isInAppCaptureMode: Boolean) {
        if (isInAppCaptureMode) {
            cameraCaptureModeEvent.value = CameraMode.IN_APP_CAPTURE_MODE
        }
    }

    fun addSpm(obj: Any?, isSelfie: Boolean) {
        obj?.let {
            val model = PositionModel()
            model.pageId = obj::class.java.simpleName
            model.setPageObject(this)
            model.content = "相机"
            model.page = if (isSelfie) "1002_01" else "1002_02"
            SPMManager.instance.pushSpm(model)
        }
    }

    /**
     * 统计
     */
    fun logCameraApprEvent(ratioType: Int?) {
        val hashMap = HashMap<String, String>().apply {
            this["比例"] = when (ratioType) {
                CameraRatioType.PICTURE_RATIO_1_1 -> {
                    "1:1"
                }

                CameraRatioType.PICTURE_RATIO_FULL -> {
                    "full"
                }

                CameraRatioType.PICTURE_RATIO_9_16 -> {
                    "9:16"
                }

                else -> {
                    "3:4"
                }
            }
        }

        // 统计默认滤镜
        when (cameraCaptureModeEvent.value) {
            CameraMode.VIDEO_MODE -> {
                MTAnalyticsAgent.logEvent(MTAnalyticsConstant.EVENT_SELFIE_VIDEO, hashMap)
            }

            CameraMode.CAPTURE_MODE, CameraMode.IN_APP_CAPTURE_MODE -> {
                hashMap.putAll(SPMManager.instance.getCurrentSpmInfo())
                MTAnalyticsAgent.logEvent(MTAnalyticsConstant.SELFIE_APPR, hashMap)
            }

            CameraMode.MOVIE_MODE -> {
                MTAnalyticsAgent.logEvent(MTAnalyticsConstant.MOVIE_APPR, hashMap)
            }
        }
    }

}