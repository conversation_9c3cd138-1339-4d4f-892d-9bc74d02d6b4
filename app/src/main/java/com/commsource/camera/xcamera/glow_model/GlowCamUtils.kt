package com.commsource.camera.xcamera.glow_model

import java.util.Calendar

object GlowCamUtils {
    /**
     * 获取当天凌晨12点的时间戳
     */
    fun getMidnightTimestamp(): Long {
        val calendar = Calendar.getInstance()

        // 设置时间为当天凌晨12点
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)

        return calendar.timeInMillis  // 返回时间戳（毫秒）
    }
}