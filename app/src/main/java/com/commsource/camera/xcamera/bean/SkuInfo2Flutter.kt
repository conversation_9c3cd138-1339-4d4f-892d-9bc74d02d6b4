package com.commsource.camera.xcamera.bean

import java.io.Serializable


class SkuInfo2Flutter : Serializable {
    /**
     * SKU ID
     */
    var productIdentifier: String? = null

    /**
     * 价格
     */
    var price: Double? = null

    /**
     * 价格符号，如￥
     */
    var localSymbol: String? = null

    /**
     * 本地格式化后的价格
     */
    var localPrice: String? = null
}


class SkuProductInfo : Serializable {
    var products: ArrayList<SkuInfo2Flutter>? = null
}
