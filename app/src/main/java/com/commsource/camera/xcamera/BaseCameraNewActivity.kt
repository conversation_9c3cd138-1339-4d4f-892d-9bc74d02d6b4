package com.commsource.camera.xcamera

import android.Manifest
import android.os.Build
import android.os.Bundle
import androidx.annotation.IntDef
import androidx.annotation.UiThread
import androidx.core.app.ActivityCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautyplus.ActivityStackManager
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.R
import com.commsource.beautyplus.router.RouterEntity
import com.commsource.beautyplus.router.UriConstant
import com.commsource.camera.mvp.CameraConstant
import com.commsource.camera.xcamera.cover.CoverContainer
import com.commsource.camera.xcamera.cover.tips.TipsViewModel
import com.commsource.config.ApplicationConfig
import com.commsource.config.SelfieConfig
import com.commsource.easyeditor.utils.opengl.GlThread
import com.commsource.util.AppTools
import com.commsource.util.GoogleSeriveUtils
import com.commsource.util.PermissionUitl
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.ipermission.PermissionProcess
import com.commsource.util.ipermission.PermissionResult
import com.commsource.util.print
import com.commsource.widget.dialog.common.ADialog
import com.meitu.common.AppContext
import com.meitu.common.utils.ToastUtils
import com.meitu.library.media.camera.hub.MTCameraHub
import com.meitu.library.util.Debug.Debug

/**
 *
 * Created on 2020/3/27
 * <AUTHOR>
 */
abstract class BaseCameraNewActivity : BaseActivity() {

    companion object {
        const val TAG = "NewCameraActivity"
    }

    @IntDef(
        CameraStatus.NONE,
        CameraStatus.CREATED,
        CameraStatus.START,
        CameraStatus.RESUME,
        CameraStatus.PAUSE,
        CameraStatus.STOP,
        CameraStatus.DESTROY
    )
    @Target(AnnotationTarget.FIELD)
    @Retention(AnnotationRetention.SOURCE)
    annotation class CameraStatus {
        companion object {
            const val NONE = -1
            const val CREATED = 0
            const val START = 1
            const val RESUME = 2
            const val PAUSE = 3
            const val STOP = 4
            const val DESTROY = 5
        }
    }

    /**
     * 相机生命周期 目的是为了成对的调用相机生命周期
     */
    @CameraStatus
    private var cameraStatus: Int = CameraStatus.NONE

    lateinit var bpCameraViewModel: BpCameraViewModel

    lateinit var coverContainer: CoverContainer

    lateinit var viewModelProvider: ViewModelProvider

    /**
     * 是否套用默认效果
     */
    private var isApplyDefaultEffect = false

    /**
     * 懒加载是否检查一次
     */
    private var isLazyInit = false

    /**
     * 第一帧预览可见
     */
    var isFirstFrameAvaliable = true

    /**
     * 是否是证件照功能
     */
    var isPhotoID = false


    var requestPermissionOnStart: Boolean = true

    /**
     * 初始化
     */
    @UiThread
    protected abstract fun onInit(cameraHub: MTCameraHub)

    /**
     * 懒加载Init
     */
    protected abstract fun onLazyInit()

    /**
     * 套用默认效果
     */
    @UiThread
    protected abstract fun onApplyDefaultEffect()

    /**
     * 第一帧预览可见
     * @param isFirst 是否是相机首次打开预览可见
     */
    @GlThread
    protected abstract fun onFirstFrameAvaliable(isFirst: Boolean)

    /**
     * 获取相机布局控件ID
     */
    protected abstract fun getCameraLayoutId(): Int

    /**
     * 获取自动对焦控件ID
     */
    protected abstract fun getFocusViewId(): Int

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isApplyDefaultEffect = false
        viewModelProvider = ViewModelProvider(this)
        coverContainer = CoverContainer(this)
        setContentView(coverContainer)
        if (!AppTools.checkSoExist()) {
            return
        }

        // 相机初始化
        bpCameraViewModel = getViewModel(BpCameraViewModel::class.java)
        bpCameraViewModel.apply {
            onCameraAddNode { cameraBuilder ->
                "cameraEffectManager被创建:${bpCameraViewModel.hashCode()}".print(CoverContainer.TAG)
                setCameraEffectManager(
                    CameraEffectManager(
                        cameraBuilder,
                        getLifecycleEglEngine(),
                        isPhotoID
                    )
                )
            }
            initCamera(this@BaseCameraNewActivity, getFocusViewId()).let {
                onInit(it)
            }
            resetRenderOrder(true)
            firstFrameEvent.observe(this@BaseCameraNewActivity) {
                onFirstFrameAvaliable(isFirstFrameAvaliable)
                isFirstFrameAvaliable = false
            }

            onCreate(savedInstanceState)
        }
        cameraStatus = CameraStatus.CREATED
        Debug.d(TAG, "onCreate")
    }

    override fun onStart() {
        super.onStart()
        if (requestPermissionOnStart) {
            executeRequestPermission()
        }
        if (cameraStatus == CameraStatus.CREATED || cameraStatus == CameraStatus.STOP || cameraStatus == CameraStatus.DESTROY) {
            bpCameraViewModel.onStart()
            cameraStatus = CameraStatus.START
        }
        Debug.d(TAG, "onStart:$cameraStatus")
    }

    open fun isClassicCamera(): Boolean {
        return false
    }

    private fun isVideoModeFromProtocol(): Boolean {
        intent?.let { it ->
            it.getSerializableExtra(RouterEntity.DEEP_LINK)?.takeIf { it is RouterEntity }
                ?.let { webEntity ->
                    val entity = webEntity as RouterEntity
                    // 如果路径非空，判断是否有路径可以匹配，如果不能匹配判断是否是最新版本
                    if (entity.lastPathSegment == UriConstant.PATH_M_VIDEO) {
                        return true
                    }
                }
        }
        return false
    }

    /**
     * 执行请求权限
     * 我们在onstart请求权限
     *
     * 部分在onResume才加入coverContainer的cover无法分发到这个回调 需要注意
     */
    private fun executeRequestPermission() {
        val isSupportVersion = Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
        var storagePermission = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_EXTERNAL_STORAGE
        } else {//Android 14 延后去判断
            Manifest.permission.READ_MEDIA_IMAGES
        }
        IProcessHandler(this)
            .execute(object : PermissionProcess(
                ArrayList<String>().apply {
                    add(Manifest.permission.CAMERA)
                    if (!isClassicCamera()) {
                        addAll(PermissionUitl.getPartialMediaPermissionsWhenAndroid14())
                    }
                    if (SelfieConfig.isFirstOpenAudioPermission(AppContext.context) && (!isClassicCamera() || isVideoModeFromProtocol())) {
                        add(Manifest.permission.RECORD_AUDIO)
                        SelfieConfig.setIsFirstOpenAudioPermission(AppContext.context, false)
                    }
                }) {
                override fun onPermissionResult(
                    results: List<PermissionResult>?,
                    isRequestResult: Boolean
                ) {
                    results?.let {
                        var cameraResult = it.find { it.permission == Manifest.permission.CAMERA }
                        if (cameraResult?.fromRequest == true) {
                            CameraNewActivity.isEnter = false
                        }
                        //非必要权限
                        var audioResult =
                            it.find { it.permission == Manifest.permission.RECORD_AUDIO }
                        var storageResult = it.find { it.permission == storagePermission }
                        if (storageResult?.isSuccess == false) {//Android 14可能没有这个权限,但是有READ_MEDIA_VISUAL_USER_SELECTED权限
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                                var hasReadMediaVisualUserSelected =
                                    it.find { curPermission -> curPermission.permission == Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED }
                                if (hasReadMediaVisualUserSelected?.isSuccess == true) {
                                    storageResult = hasReadMediaVisualUserSelected
                                    storagePermission =
                                        Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED
                                }
                            }
                        }
                        when {
                            //如果都成功了 直接CoverContainer拦截发送
                            cameraResult?.isSuccess == true && storageResult?.isSuccess == true -> {
                                coverContainer.dispatchPermissionResult(results, isRequestResult)
                            }
                            //如果用户 选择了拒绝不再提示 跳转setting
                            cameraResult?.isSuccess == false && !ActivityCompat.shouldShowRequestPermissionRationale(
                                this@BaseCameraNewActivity,
                                cameraResult.permission
                            ) -> {
                                if (!GoogleSeriveUtils.isSupportGP(this@BaseCameraNewActivity) && !isSupportVersion) {
                                    ADialog.showNoGPError(this@BaseCameraNewActivity)
                                    return
                                }
                                ADialog.showCameraOrStorageErrorAgain(
                                    this@BaseCameraNewActivity,
                                    true
                                )
                            }
                            //如果用户 选择了拒绝不再提示 跳转setting
                            storageResult?.isSuccess == false && !ActivityCompat.shouldShowRequestPermissionRationale(
                                this@BaseCameraNewActivity,
                                storageResult.permission
                            ) -> {
                                if (!GoogleSeriveUtils.isSupportGP(this@BaseCameraNewActivity) && !isSupportVersion) {
                                    ADialog.showNoGPError(this@BaseCameraNewActivity)
                                    return
                                }
                                ADialog.showCameraOrStorageErrorAgain(
                                    this@BaseCameraNewActivity,
                                    true
                                )
                            }
                            //如果两个重要权限不成功
                            cameraResult?.isSuccess == false && storageResult?.isSuccess == false -> {
                                ADialog.showPermissionError(
                                    this@BaseCameraNewActivity,
                                    CameraConstant.PERMISSION_CAMERA_ERROR,
                                    arrayOf(Manifest.permission.CAMERA, storagePermission)
                                )
                            }
                            //如果存储权限不通过
                            storageResult?.isSuccess == false -> {
                                ADialog.showPermissionError(
                                    this@BaseCameraNewActivity,
                                    CameraConstant.PERMISSION_CAMERA_ERROR,
                                    arrayOf(storagePermission)
                                )
                            }
                            //如果相机权限不通过
                            cameraResult?.isSuccess == false -> {
                                ADialog.showPermissionError(
                                    this@BaseCameraNewActivity,
                                    CameraConstant.PERMISSION_CAMERA_ERROR,
                                    arrayOf(Manifest.permission.CAMERA)
                                )
                            }
                        }
                    }
                }
            })
    }

    override fun onResume() {
        super.onResume()
        if (cameraStatus == CameraStatus.START || cameraStatus == CameraStatus.PAUSE) {
            bpCameraViewModel.onResume()
            cameraStatus = CameraStatus.RESUME
        } else if (cameraStatus != CameraStatus.RESUME) {
            bpCameraViewModel.onStart()
            bpCameraViewModel.onResume()
            cameraStatus = CameraStatus.RESUME
        }
        if (!isLazyInit) {
            isLazyInit = true
            onLazyInit()
        }
        if (!isApplyDefaultEffect) {
            isApplyDefaultEffect = true
            onApplyDefaultEffect()
        }
        Debug.d(TAG, "onResume:$cameraStatus")
    }

    override fun onPause() {
        if (cameraStatus == CameraStatus.RESUME) {
            bpCameraViewModel.onPause()
            cameraStatus = CameraStatus.PAUSE
        }
        super.onPause()
        Debug.d(TAG, "onPause:$cameraStatus")
    }

    override fun onStop() {
        super.onStop()
        if (cameraStatus == CameraStatus.PAUSE) {
            bpCameraViewModel.onStop()
            cameraStatus = CameraStatus.STOP
        }
        Debug.d(TAG, "onStop:$cameraStatus")
    }

    override fun onDestroy() {
        super.onDestroy()
        if (cameraStatus == CameraStatus.PAUSE) {
            bpCameraViewModel.onStop()
        } else if (cameraStatus != CameraStatus.STOP) {
            bpCameraViewModel.onPause()
            bpCameraViewModel.onStop()
        }
        bpCameraViewModel.onDestroy()
        cameraStatus = CameraStatus.DESTROY
        Debug.d(TAG, "onDestroy:$cameraStatus")
    }

    fun <K : ViewModel> getViewModel(clazz: Class<K>): K {
        var oldTime = System.currentTimeMillis()
        val viewModel = viewModelProvider[clazz]
        "${System.currentTimeMillis() - oldTime} : ${javaClass.simpleName} create -> ${clazz.simpleName}".print(
            CoverContainer.TAG
        )
        return viewModel
    }

    /**
     * 请求权限回调
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        bpCameraViewModel.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }
}