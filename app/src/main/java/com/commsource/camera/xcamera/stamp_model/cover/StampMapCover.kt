//package com.commsource.camera.xcamera.stamp_model.cover
//
//import android.Manifest
//import android.content.pm.PackageManager
//import android.location.Address
//import android.location.Geocoder
//import android.os.Build
//import androidx.core.app.ActivityCompat
//import androidx.core.content.ContextCompat
//import androidx.lifecycle.Observer
//import com.commsource.beautyplus.R
//import com.commsource.beautyplus.databinding.CoverStampMapBinding
//import com.commsource.camera.xcamera.cover.AbsCover
//import com.commsource.camera.xcamera.iphone_model.utils.map.CoordinateConvert
//import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneSettingViewModel
//import com.commsource.util.LOGV_Camera
//import com.commsource.util.dp
//import com.commsource.util.setMarginBottom
//import com.google.android.gms.location.FusedLocationProviderClient
//import com.google.android.gms.location.LocationCallback
//import com.google.android.gms.location.LocationRequest
//import com.google.android.gms.location.LocationResult
//import com.google.android.gms.location.LocationServices
//import com.google.android.gms.location.Priority
//import com.google.android.gms.maps.CameraUpdateFactory
//import com.google.android.gms.maps.GoogleMap
//import com.google.android.gms.maps.SupportMapFragment
//import com.google.android.gms.maps.model.LatLng
//import com.google.android.gms.maps.model.MarkerOptions
//
//class StampMapCover : AbsCover<CoverStampMapBinding>()  {
//
//    companion object {
//        const val TAG = "StampMapCover"
//    }
//
//    private val PERMISSIONS_REQUEST_ACCESS_FINE_LOCATION = 1
//    private var locationPermissionGranted = false
//    private lateinit var fusedLocationProviderClient: FusedLocationProviderClient
//    private lateinit var locationCallback: LocationCallback
//    private lateinit var locationRequest: LocationRequest
//    private var initMap = false
//
//    private val iphoneSettingViewModel by lazy {
//        getViewModel(IphoneSettingViewModel::class.java)
//    }
//
//    override fun getLayoutId(): Int {
//        return R.layout.cover_stamp_map
//    }
//
//    override fun initView() {
//        getLactionPermission()
//        fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(coverGroup.mActivity)
//
//        locationRequest =
//            LocationRequest.Builder(Priority.PRIORITY_BALANCED_POWER_ACCURACY,1000).apply {
//                setMinUpdateIntervalMillis(5000)
//            }.build()
//
//        locationCallback = object : LocationCallback() {
//            override fun onLocationResult(locationResult: LocationResult) {
//                locationResult ?: return
//                for (location in locationResult.locations) {
//                    // 更新位置信息
//                    val latitude = location.latitude
//                    val longitude = location.longitude
//                    val altitude = location.altitude
//                    // 使用纬度和经度进行其他操作
//                    "btj latitude:$latitude, longitude:$longitude altitude:$altitude".LOGV_Camera()
//                    if (!initMap) {
//                        val geocoder = Geocoder(coverGroup.mActivity)
//                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
//                            geocoder.getFromLocation(latitude, longitude, 1) {
//                                "btj addresss1:${it[0].featureName}".LOGV_Camera()
//                            }
//                        } else {
//                            val addresss = geocoder.getFromLocation(44.61360220148978, -110.56261998217425,20)
//                            // 遍历所有地址
//                            if (addresss != null) {
//                                for (address in addresss) {
//                                    "btj addresss3:${address}".LOGV_Camera()
//                                }
//                            }
//                        }
//
//                        CoordinateConvert.wgs84ToGcj02(latitude, longitude).let {
//                            "btj wgs84ToGcj02 lat:${it.first}, lon:${it.second}".LOGV_Camera()
//                            val mapFragment = coverGroup.mActivity.supportFragmentManager
//                                .findFragmentById(R.id.mapView) as SupportMapFragment
//                            val chinaLat = it.first
//                            val chinaLon = it.second
//                            mapFragment.getMapAsync() {
//                                it.mapType = GoogleMap.MAP_TYPE_HYBRID
//                                it.addMarker(MarkerOptions().position(LatLng(chinaLat, chinaLon)).title("Marker"))
//                                it.moveCamera(CameraUpdateFactory.newLatLngZoom(LatLng(chinaLat, chinaLon), 15f))
//                            }
//                        }
//                        initMap = true
//                    }
//                }
//            }
//        }
//
//       if (locationPermissionGranted) {
//           if (ActivityCompat.checkSelfPermission(
//                   coverGroup.mActivity,
//                   Manifest.permission.ACCESS_FINE_LOCATION
//               ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
//                   coverGroup.mActivity,
//                   Manifest.permission.ACCESS_COARSE_LOCATION
//               ) != PackageManager.PERMISSION_GRANTED
//           ) {
//               return
//           }
//           fusedLocationProviderClient.requestLocationUpdates(locationRequest, locationCallback, null)
//       }
//    }
//
//    override fun initViewModel() {
//        iphoneSettingViewModel.screenRatioChangeEvent.observe(coverGroup.mActivity, Observer {
//            mViewBinding.mapView.setMarginBottom(iphoneSettingViewModel.getBottomPanelHeight() + 12.dp)
//        })
//    }
//
//    private fun getLactionPermission() {
//        if (ContextCompat.checkSelfPermission(coverGroup.mActivity,
//                Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
//            locationPermissionGranted = true
//            "btj 有权限".LOGV_Camera()
//        } else {
//            ActivityCompat.requestPermissions(coverGroup.mActivity,
//                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
//                PERMISSIONS_REQUEST_ACCESS_FINE_LOCATION)
//        }
//    }
//
//
//
//
//}