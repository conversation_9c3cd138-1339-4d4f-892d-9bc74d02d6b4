package com.commsource.camera.xcamera.retro.cover

import android.hardware.Camera
import com.commsource.ad.DailyMembershipUnlocker
import com.commsource.beautyplus.BuildConfig
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverRetroTopBarBinding
import com.commsource.camera.common.CameraContainerViewModel
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.xcamera.retro.RetroCameraViewModel
import com.commsource.camera.xcamera.retro.logic.RetroBottomFuncViewModel
import com.commsource.camera.xcamera.retro.logic.RetroCaptureViewModel
import com.commsource.camera.xcamera.retro.logic.RetroSettingViewModel
import com.commsource.camera.xcamera.retro.logic.RetroStyleViewModel
import com.commsource.config.SubscribeConfig
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.statistics.trace.TraceManager
import com.commsource.statistics.trace.TraceModule
import com.commsource.util.VerifyMothod
import com.commsource.util.common.ProcessUtil
import com.commsource.util.coroutine.launchMain
import com.commsource.util.gone
import com.commsource.util.hapticVirtualKey
import com.commsource.util.setMarginTop
import com.commsource.util.visible
import com.pixocial.framework.common.fragment.WindowViewModel
import com.pixocial.framework.cover.AbstractViewBindingCover

class RetroTopBarCover : AbstractViewBindingCover<CoverRetroTopBarBinding>() {

    private val containerViewModel by lazy { activityViewModel(CameraContainerViewModel::class) }
    private val windowViewModel by lazy { fragmentViewModel(WindowViewModel::class) }
    private val captureViewModel by lazy { fragmentViewModel(RetroCaptureViewModel::class) }
    private val cameraViewModel by lazy { fragmentViewModel(RetroCameraViewModel::class) }
    private val styleViewModel by lazy { fragmentViewModel(RetroStyleViewModel::class) }
    private val settingViewModel by lazy { fragmentViewModel(RetroSettingViewModel::class) }
    private val bottomFuncViewModel by lazy { fragmentViewModel(RetroBottomFuncViewModel::class) }

    private var isEnableBeauty = true
    private var isEnableReshape = true
    private var isEnableMakeup = true

    override fun getLayoutId() = R.layout.cover_retro_top_bar

    override fun onViewDidLoad() {
        if (cameraViewModel.cameraAccessor.fetchNowCameraConfig().cameraId == Camera.CameraInfo.CAMERA_FACING_FRONT) {
            viewBinding.ifvSwitchCamera.tag = "前"
        } else {
            viewBinding.ifvSwitchCamera.tag = "后"
        }

        //TODO 测试 记得删除
        if (BuildConfig.DEBUG) {
            viewBinding.tvBeauty.setOnClickListener {
                isEnableBeauty = !isEnableBeauty
                styleViewModel.enableBeautyEffectOnly(
                    cameraViewModel.cameraEffectManager,
                    isEnableBeauty
                )
            }

            viewBinding.tvReshape.setOnClickListener {
                isEnableReshape = !isEnableReshape
                styleViewModel.enableReshpeEffectOnly(
                    cameraViewModel.cameraEffectManager,
                    isEnableReshape
                )
            }

            viewBinding.tvMakeup.setOnClickListener {
                isEnableMakeup = !isEnableMakeup
                styleViewModel.enableMakeupEffectOnly(
                    cameraViewModel.cameraEffectManager,
                    isEnableMakeup
                )
            }
        }
        viewBinding.rlTopBar.gone()

        viewBinding.mIvBack.setOnClickListener {
            if (ProcessUtil.isProcessing()) {
                return@setOnClickListener
            }

            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.EVENT_SELFIE_BACK,
                HashMap<String, String>(4).apply {
                    when (captureViewModel.captureModeEvent.value) {
                        CameraMode.VIDEO_MODE -> this["mode_a"] = "video"
                        else -> this["mode_a"] = "shoot"
                    }
                    this["返回类型"] = "左上角返回"
                    this["camera_mode"] = "film_cam"
                })

            TraceManager.pop(TraceModule.CAMERA)
            coverContainer.attachFragment.activity?.finish()
            VerifyMothod.doOutAnimationEx(coverContainer.attachFragment.activity)
        }

        viewBinding.ifvSettingMore.setOnClickListener {
            if (ProcessUtil.isProcessing()) {
                return@setOnClickListener
            }

            settingViewModel.showOrHideMoreSetting(true)
        }

        viewBinding.ifvSwitchCamera.setOnClickListener {
            if (ProcessUtil.isProcessing()) {
                return@setOnClickListener
            }

            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.film_cam_category_clk,
                "category",
                "镜头切换"
            )
            if (cameraViewModel.cameraAccessor.switchCamera()) {
                containerViewModel.previewCoverEnabledEvent.value = false
            }

            it.hapticVirtualKey()
            if (cameraViewModel.cameraAccessor.fetchNowCameraConfig().cameraId == Camera.CameraInfo.CAMERA_FACING_FRONT) {
                it.tag = "前"
            } else {
                it.tag = "后"
            }
        }
    }

    override fun onBindViewModel() {
        windowViewModel.windowCompatEvent.observe(lifecycleOwner) {
            viewBinding.rlTopBar.visible()
            viewBinding.rlTopBar.setMarginTop(topMargin = it.safeRect.top)
        }

        captureViewModel.fetchVideoRecordingEvent()?.observe(lifecycleOwner) {
            if (captureViewModel.isVideoRecording()) {
                viewBinding.rlTopBar.gone()
            } else {
                viewBinding.rlTopBar.visible()
            }
        }

        cameraViewModel.getApplyEffectEvent().observe(lifecycleOwner) {
            refreshLimitTimes()
        }

        captureViewModel.albumAddImageEvent.observe(lifecycleOwner) {
            // 消耗限免次数
            styleViewModel.consumeLimitTimes()
            refreshLimitTimes()
        }

        // 这种是拍后页保存过后刷新次数
        styleViewModel.refreshRvEvent.observe(lifecycleOwner) {
            refreshLimitTimes()
        }

        launchMain {
            cameraViewModel.deviceOrientationFlow.collect {
                viewBinding.mIvBack.rotation = it
                viewBinding.ifvSettingMore.rotation = it
                viewBinding.ifvSwitchCamera.rotation = it
            }
        }
    }

    private fun refreshLimitTimes() {
        val unSubscribe =
            !SubscribeConfig.isSubValid() && !DailyMembershipUnlocker.hasUserEarnReward()
        if (!unSubscribe) {
            viewBinding.tvLimitTimes.gone()
            return
        }


        val times = styleViewModel.limitTimes()

        if (times > 0 && !styleViewModel.isNeedPay()) {
            viewBinding.tvLimitTimes.visible()
            viewBinding.tvLimitTimes.text = "Free Uses：$times"
        } else {
            viewBinding.tvLimitTimes.gone()
        }
    }
}