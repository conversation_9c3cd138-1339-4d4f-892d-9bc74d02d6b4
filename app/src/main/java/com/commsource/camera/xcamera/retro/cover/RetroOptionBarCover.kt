package com.commsource.camera.xcamera.retro.cover

import android.hardware.Camera
import androidx.lifecycle.Observer
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverRetroOptionBarBinding
import com.commsource.camera.common.CameraContainerViewModel
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.xcamera.retro.RetroCameraConfig
import com.commsource.camera.xcamera.retro.RetroCameraViewModel
import com.commsource.camera.xcamera.retro.getRadio
import com.commsource.camera.xcamera.retro.logic.RetroCaptureViewModel
import com.commsource.camera.xcamera.retro.logic.RetroSettingViewModel
import com.commsource.camera.xcamera.retro.logic.RetroStyleViewModel
import com.commsource.camera.xcamera.retro.logic.ZoomType
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.V
import com.commsource.util.common.ProcessUtil
import com.commsource.util.coroutine.launchIO
import com.commsource.util.coroutine.launchMain
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.hapticVirtualKey
import com.commsource.util.invisible
import com.commsource.util.resColor
import com.commsource.util.safeObserve
import com.commsource.util.set
import com.commsource.util.setMarginCompat
import com.commsource.util.setSize
import com.commsource.util.text
import com.commsource.util.visible
import com.meitu.library.media.camera.common.Facing
import com.meitu.library.media.camera.common.FlashMode
import com.meitu.library.util.device.DeviceUtils
import com.meitu.room.database.DBHelper
import com.pixocial.camerasuite.commsource.camera.mvp.annotation.CameraRatioType
import com.pixocial.framework.cover.AbstractViewBindingCover
import kotlinx.coroutines.suspendCancellableCoroutine

/**
 * @Description: 相机操作Bar
 *
 * @author: vinvince  @Date: 2025年02月14日 10:45
 */
class RetroOptionBarCover : AbstractViewBindingCover<CoverRetroOptionBarBinding>() {

    private val captureViewModel by lazy { fragmentViewModel(RetroCaptureViewModel::class) }
    private val retroStyleViewModel by lazy { fragmentViewModel(RetroStyleViewModel::class) }
    private val cameraViewModel by lazy { fragmentViewModel(RetroCameraViewModel::class) }
    private val settingViewModel by lazy { fragmentViewModel(RetroSettingViewModel::class) }
    private val containerViewModel by lazy { activityViewModel(CameraContainerViewModel::class) }

    private var cameraName = ""

    override fun getLayoutId(): Int {
        return R.layout.cover_retro_option_bar
    }

    override fun onViewDidLoad() {
        viewBinding.ifvBeautify.tag = "关"
        viewBinding.ifvWatermark.tag = "关"
        viewBinding.ifvFunction.tag = "关"

        viewBinding.ifvZoom.setOnClickListener {
            if (ProcessUtil.isProcessing()) {
                return@setOnClickListener
            }

            if (captureViewModel.isVideoMode()) {
                settingViewModel.showToastTip(R.string.v7100_B_62.text())
                return@setOnClickListener
            }

            settingViewModel.changeZoomType(captureViewModel.captureModeEvent.value!!)
            viewBinding.ifvZoom.hapticVirtualKey()
        }

        viewBinding.ifvFlash.setOnClickListener {
            if (ProcessUtil.isProcessing()) {
                return@setOnClickListener
            }

            if (captureViewModel.isVideoMode() && cameraViewModel.cameraAccessor.fetchConfigNotifier()?.cameraFacingChangeEventEvent?.value == Facing.FRONT) {
                settingViewModel.showToastTip(R.string.v7100_B_61.text())
                return@setOnClickListener
            }

            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.film_cam_category_clk, "category", "闪光灯"
            )

            val supportFlashModes =
                cameraViewModel.cameraAccessor.fetchConfigNotifier()?.supportFlashModesEvent?.value
            val canSetFlash =
                supportFlashModes?.isNotEmpty() == true && supportFlashModes.contains(FlashMode.OFF) && supportFlashModes.contains(
                    FlashMode.TORCH
                )

            val isFrontCamera =
                cameraViewModel.cameraAccessor.fetchConfigNotifier()?.cameraFacingChangeEventEvent?.value == Facing.FRONT
            val flashMode = if (isFrontCamera) {
                RetroCameraConfig.getString(RetroCameraConfig.KEY_FRONT_FLASH, FlashMode.OFF)
            } else {
                RetroCameraConfig.getString(RetroCameraConfig.KEY_BACK_FLASH, FlashMode.OFF)
            }

            if (canSetFlash && !isFrontCamera) {
                if (flashMode == FlashMode.TORCH) {
                    cameraViewModel.cameraAccessor.switchFlashMode(FlashMode.OFF)
                    updateFlashUi(FlashMode.OFF)
                    settingViewModel.showToastTip(tipText = R.string.v79000_A_25.text())
                    RetroCameraConfig.putValue(RetroCameraConfig.KEY_BACK_FLASH, FlashMode.OFF)
                } else {
                    cameraViewModel.cameraAccessor.switchFlashMode(FlashMode.TORCH)
                    updateFlashUi(FlashMode.TORCH)
                    settingViewModel.showToastTip(tipText = R.string.v79000_A_24.text())
                    RetroCameraConfig.putValue(RetroCameraConfig.KEY_BACK_FLASH, FlashMode.TORCH)
                }
            } else if (isFrontCamera) {
                if (flashMode == FlashMode.ON) {
                    settingViewModel.showToastTip(tipText = R.string.v79000_A_25.text())
                    updateFlashUi(FlashMode.OFF)
                    RetroCameraConfig.putValue(RetroCameraConfig.KEY_FRONT_FLASH, FlashMode.OFF)
                } else {
                    settingViewModel.showToastTip(tipText = R.string.v79000_A_24.text())
                    updateFlashUi(FlashMode.ON)
                    RetroCameraConfig.putValue(RetroCameraConfig.KEY_FRONT_FLASH, FlashMode.ON)
                }
            }

            it.hapticVirtualKey()
        }

        viewBinding.ifvBeautify.setOnClickListener {
            if (ProcessUtil.isProcessing()) {
                return@setOnClickListener
            }

            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.film_cam_category_clk, "category", "美颜")

            val beautyEnable = !(settingViewModel.beautySwitchEvent.value ?: true)
            retroStyleViewModel.enableBeautyEffect(
                cameraViewModel.cameraEffectManager,
                beautyEnable
            )
            if (beautyEnable) {
                viewBinding.ifvBeautify.setTextColor(R.color.color_ff5353.resColor())
                settingViewModel.showToastTip(tipText = R.string.v79000_C_8.text())
                viewBinding.ifvBeautify.tag = "开"
            } else {
                viewBinding.ifvBeautify.setTextColor(R.color.Gray_E.resColor())
                viewBinding.ifvBeautify.tag = "关"
                settingViewModel.showToastTip(tipText = R.string.v79000_C_9.text())
            }
            settingViewModel.beautySwitchEvent.value = beautyEnable
            RetroCameraConfig.putValue(RetroCameraConfig.KEY_BEAUTY_SWITCH, beautyEnable)

            it.hapticVirtualKey()
        }

        viewBinding.ifvWatermark.setOnClickListener {
            if (ProcessUtil.isProcessing()) {
                return@setOnClickListener
            }
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.film_cam_category_clk, "category", "水印")
            val newStatus =
                !RetroCameraConfig.getWatermarkSwitch(retroStyleViewModel.currentCamera?.materialId)
            /// 启用或者关闭水印效果
            retroStyleViewModel.enableWaterMask(cameraViewModel.cameraEffectManager, newStatus)
            if (newStatus) {
                viewBinding.ifvWatermark.setTextColor(R.color.color_ff5353.resColor())
                viewBinding.ifvWatermark.tag = "开"
                val isOnlyApplyOnTakeAfter = retroStyleViewModel.isOnlyApplyOnTakeAfter()
                settingViewModel.showToastTip(tipText = if (isOnlyApplyOnTakeAfter) R.string.v7100_B_52.text() else R.string.v7100_B_55.text())
            } else {
                viewBinding.ifvWatermark.setTextColor(R.color.Gray_E.resColor())
                viewBinding.ifvWatermark.tag = "关"
                settingViewModel.showToastTip(tipText = R.string.v7100_B_56.text())
            }
            RetroCameraConfig.setWatermarkSwitch(newStatus)

            it.hapticVirtualKey()
        }

        viewBinding.ifvFunction.setOnClickListener {
            if (ProcessUtil.isProcessing()) {
                return@setOnClickListener
            }

            val isDoubleExposure = retroStyleViewModel.currentCamera?.specialFeatures == 1
            val captureMode = captureViewModel.captureModeEvent.value
            if (isDoubleExposure && captureMode == CameraMode.VIDEO_MODE) {
                settingViewModel.showToastTip(R.string.v7100_B_53.text())
                return@setOnClickListener
            }

            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.film_cam_category_clk, "category", "双重曝光"
            )

            retroStyleViewModel.currentCamera?.let { camera ->
                val nowDoubleExport = !camera.shouldDoubleExport
                camera.shouldDoubleExport = nowDoubleExport
                captureViewModel.shouldDoubleExport.set(nowDoubleExport)
                launchIO {
                    DBHelper.dataBase.duffleFilmDao.update(camera)
                }
                if (nowDoubleExport) {
                    settingViewModel.showToastTip(tipText = R.string.v7100_B_11.text())
                    viewBinding.ifvFunction.setTextColor(R.color.color_ff5353.resColor())
                    viewBinding.ifvFunction.tag = "开"
                } else {
                    settingViewModel.showToastTip(tipText = R.string.v7100_B_12.text())
                    viewBinding.ifvFunction.tag = "关"
                    viewBinding.ifvFunction.setTextColor(R.color.Gray_E.resColor())

                    if (captureViewModel.shouldResetCaptureData()) {
                        captureViewModel.resetCaptureData()
                    }
                }
            }

            it.hapticVirtualKey()
        }
    }

    override fun onBindViewModel() {
        containerViewModel.cameraPreviewPositionEvent.observe(lifecycleOwner) {
            it?.let {
                viewBinding.rlOptionBar.visible()
                viewBinding.rlOptionBar.post {
                    val minSize = 16.dpf() * 2f + 40.dpf() * 5f
                    if (it.width < minSize) {
                        viewBinding.rlOptionBar.setSize(width = (DeviceUtils.getScreenWidth() - 16.dp() * 2))
                    } else {
                        viewBinding.rlOptionBar.setSize(width = it.width)
                    }
                    viewBinding.rlOptionBar.setMarginCompat(top = it.top + it.height + 11f.dp())
                }
            }
        }

        captureViewModel.fetchVideoRecordingEvent()?.observe(lifecycleOwner) {
            if (captureViewModel.isVideoRecording()) {
                viewBinding.rlOptionBar.invisible()
            } else {
                viewBinding.rlOptionBar.visible()
            }
        }

        settingViewModel.zoomTypeEvent.observe(lifecycleOwner) {
            if (it == ZoomType.Camera) {
                viewBinding.ifvZoom.tag = "1"
                viewBinding.ifvZoom.setTextColor(R.color.Gray_E.resColor())
            } else {
                viewBinding.ifvZoom.tag = "0"
                viewBinding.ifvZoom.setTextColor(R.color.color_ff5353.resColor())
            }
        }

        captureViewModel.captureModeEvent.observe(lifecycleOwner) {
            checkDoubleFunction()
            checkFlashEnable()

            if (it == CameraMode.VIDEO_MODE) {
                viewBinding.ifvZoom.alpha = 0.5f
                viewBinding.ifvZoom.isNeedPressState = false

                if (RetroCameraConfig.getInt(
                        RetroCameraConfig.KEY_ZOOM_TYPE,
                        ZoomType.Camera
                    ) == ZoomType.Digital
                ) {
                    settingViewModel.changeZoomType(it, false)
                }
            } else {
                viewBinding.ifvZoom.alpha = 1f
                viewBinding.ifvZoom.isNeedPressState = true
            }
        }

        cameraViewModel.cameraAccessor.fetchConfigNotifier()?.run {
            cameraIdEvent.observe(lifecycleOwner) {
                launchIO {
                    RetroCameraConfig.putValue(
                        RetroCameraConfig.KEY_CAMERA_ID,
                        cameraViewModel.cameraAccessor.fetchNowCameraConfig().cameraId
                    )
                }
            }

            supportFlashModesEvent.observe(lifecycleOwner) {
                val canSetFlash =
                    it?.isNotEmpty() == true && it.contains(FlashMode.OFF) && it.contains(FlashMode.TORCH)
                if (canSetFlash && cameraFacingChangeEventEvent.value == Facing.BACK) {
                    viewBinding.ifvFlash.visible()
                    viewBinding.space4.visible()
                } else if (cameraFacingChangeEventEvent.value == Facing.FRONT) {
                    viewBinding.ifvFlash.visible()
                    viewBinding.space4.visible()
                } else {
                    viewBinding.ifvFlash.gone()
                    viewBinding.space4.gone()
                }
            }

            flashModeEvent.observe(lifecycleOwner) {
                val supportFlashModes = supportFlashModesEvent.value
                val canSetFlash =
                    supportFlashModes?.isNotEmpty() == true && supportFlashModes.contains(FlashMode.OFF) && supportFlashModes.contains(
                        FlashMode.TORCH
                    )

                val isFrontCamera = cameraFacingChangeEventEvent.value == Facing.FRONT
                if (canSetFlash && !isFrontCamera) {
                    updateFlashUi(it)
                } else {
                    val flashMode = if (isFrontCamera) {
                        RetroCameraConfig.getString(
                            RetroCameraConfig.KEY_FRONT_FLASH,
                            FlashMode.OFF
                        )
                    } else {
                        RetroCameraConfig.getString(RetroCameraConfig.KEY_BACK_FLASH, FlashMode.OFF)
                    }
                    updateFlashUi(flashMode)
                }
            }

            firstFrameEvent.observe(lifecycleOwner) {
                switchFlash() // 切换摄像头，重新开启闪光灯
                checkFlashEnable()
            }
        }

        launchMain {
            cameraViewModel.deviceOrientationFlow.collect {
                viewBinding.ifvFlash.rotation = it
                viewBinding.ifvWatermark.rotation = it
                viewBinding.ifvBeautify.rotation = it
                viewBinding.ifvFunction.rotation = it
                viewBinding.ifvZoom.rotation = it
            }
        }

        retroStyleViewModel.applyCameraEvent.observe(lifecycleOwner) {
            cameraName = it.materialName ?: ""
            val accessor = cameraViewModel.cameraAccessor
            val shouldSwitchRadio =
                accessor.fetchNowCameraConfig().cameraRatio != retroStyleViewModel.currentCamera?.paintingSize?.getRadio()
            "切换比例 ${
                it.paintingSize.getRadio()
            }  ${accessor.fetchNowCameraConfig().cameraRatio} shouldSwitchRadio $shouldSwitchRadio".V()
            initStyle(shouldSwitchRadio)

            if (it.specialFeatures == 1) {
                viewBinding.ifvFunction.visible()
                viewBinding.space3.visible()
                if (it.shouldDoubleExport) {
                    captureViewModel.shouldDoubleExport.set(true)
                    viewBinding.ifvFunction.setTextColor(R.color.color_ff5353.resColor())
                    viewBinding.ifvFunction.tag = "开"
                } else {
                    captureViewModel.shouldDoubleExport.set(false)
                    viewBinding.ifvFunction.setTextColor(R.color.Gray_E.resColor())
                    viewBinding.ifvFunction.tag = "关"
                }
            } else {
                viewBinding.ifvFunction.gone()
                viewBinding.space3.gone()
                viewBinding.ifvFunction.tag = "关"
                captureViewModel.shouldDoubleExport.set(false)
            }

            if (RetroCameraConfig.getWatermarkSwitch(it.materialId)) {
                viewBinding.ifvWatermark.tag = "开"
                viewBinding.ifvWatermark.setTextColor(R.color.color_ff5353.resColor())
            } else {
                viewBinding.ifvWatermark.tag = "关"
                viewBinding.ifvWatermark.setTextColor(R.color.Gray_E.resColor())
            }

            if (RetroCameraConfig.getBoolean(RetroCameraConfig.KEY_BEAUTY_SWITCH, true)) {
                viewBinding.ifvBeautify.setTextColor(R.color.color_ff5353.resColor())
                viewBinding.ifvBeautify.tag = "开"
            } else {
                viewBinding.ifvBeautify.setTextColor(R.color.Gray_E.resColor())
                viewBinding.ifvBeautify.tag = "关"
            }

            checkDoubleFunction()

        }

        cameraViewModel.cameraAccessor.fetchConfigNotifier()?.ratioChangeEvent?.safeObserve(
            lifecycleOwner
        ) {
            if (it != null) {
                RetroCameraConfig.setLastRadio(it)
            }
        }
    }

    private fun initStyle(sSRadio: Boolean) {
        launchMain {
            if (sSRadio) {
                switchRadio()
            }

            switchFlash()
        }
    }

    private suspend fun switchRadio(): Boolean =
        suspendCancellableCoroutine { coroutine ->
            cameraViewModel.cameraAccessor.let {
                it.switchPictureRatio(getRadio())
                it.fetchConfigNotifier()?.ratioChangeEvent?.let { event ->
                    val observer = object : Observer<Int> {
                        override fun onChanged(value: Int) {
                            coroutine.resumeWith(Result.success(true))
                            event.removeObserver(this)
                        }
                    }
                    event.observe(lifecycleOwner, observer)
                }
            }
        }

    private fun switchFlash() {
        var canSetFlash = false
        cameraViewModel.cameraAccessor.fetchConfigNotifier()?.supportFlashModesEvent?.value?.let {
            canSetFlash =
                it.isNotEmpty() == true && it.contains(FlashMode.OFF) && it.contains(FlashMode.TORCH)
        }

        val isFrontCamera =
            cameraViewModel.cameraAccessor.fetchNowCameraConfig().cameraId == Camera.CameraInfo.CAMERA_FACING_FRONT
        val flashMode = if (isFrontCamera) {
            RetroCameraConfig.getString(RetroCameraConfig.KEY_FRONT_FLASH, FlashMode.OFF)
        } else {
            RetroCameraConfig.getString(RetroCameraConfig.KEY_BACK_FLASH, FlashMode.OFF)
        }
        if (canSetFlash && !isFrontCamera) {
            cameraViewModel.cameraAccessor.switchFlashMode(flashMode)
        }
        updateFlashUi(flashMode) // 刷新闪光灯UI
    }

    private fun updateFlashUi(flashMode: String) {
        when (flashMode) {
            FlashMode.TORCH -> {
                viewBinding.ifvFlash.text = R.string.filmcam_tool_flash_always.text()
                viewBinding.ifvFlash.setTextColor(R.color.color_ff5353.resColor())
                viewBinding.ifvFlash.tag = "开"
            }

            FlashMode.ON -> {
                viewBinding.ifvFlash.text = R.string.filmcam_tool_flash.text()
                viewBinding.ifvFlash.setTextColor(R.color.color_ff5353.resColor())
                viewBinding.ifvFlash.tag = "开"
            }

            else -> {
                viewBinding.ifvFlash.text = R.string.filmcam_tool_flash.text()
                viewBinding.ifvFlash.setTextColor(R.color.Gray_E.resColor())
                viewBinding.ifvFlash.tag = "关"
            }
        }
    }

    private fun getRadio() = if (retroStyleViewModel.currentCamera?.paintingSize == 1) {
        CameraRatioType.PICTURE_RATIO_3_2
    } else {
        CameraRatioType.PICTURE_RATIO_4_3
    }

    private fun checkDoubleFunction() {
        val isDoubleExposure = retroStyleViewModel.currentCamera?.specialFeatures == 1
        val captureMode = captureViewModel.captureModeEvent.value
        if (isDoubleExposure && captureMode == CameraMode.VIDEO_MODE) {
            viewBinding.ifvFunction.alpha = 0.5f
            viewBinding.ifvFunction.tag = "关"
            viewBinding.ifvFunction.setTextColor(R.color.Gray_E.resColor())
            viewBinding.ifvFunction.isNeedPressState = false
        } else {
            viewBinding.ifvFunction.alpha = 1.0f
            viewBinding.ifvFunction.tag = "开"
            if (isDoubleExposure && retroStyleViewModel.currentCamera?.shouldDoubleExport == true) {
                viewBinding.ifvFunction.setTextColor(R.color.color_ff5353.resColor())
            } else {
                viewBinding.ifvFunction.setTextColor(R.color.Gray_E.resColor())
            }
            viewBinding.ifvFunction.isNeedPressState = true
        }
    }

    private fun checkFlashEnable() {
        var canUseFlash = true
        if (captureViewModel.isVideoMode() && cameraViewModel.cameraAccessor.fetchConfigNotifier()?.cameraFacingChangeEventEvent?.value == Facing.FRONT) {
            canUseFlash = false
        }
        if (canUseFlash) {
            val isFrontCamera =
                cameraViewModel.cameraAccessor.fetchConfigNotifier()?.cameraFacingChangeEventEvent?.value == Facing.FRONT
            val flashMode = if (isFrontCamera) {
                RetroCameraConfig.getString(RetroCameraConfig.KEY_FRONT_FLASH, FlashMode.OFF)
            } else {
                RetroCameraConfig.getString(RetroCameraConfig.KEY_BACK_FLASH, FlashMode.OFF)
            }
            if (flashMode == FlashMode.OFF) {
                viewBinding.ifvFlash.setTextColor(R.color.Gray_E.resColor())
            } else {
                viewBinding.ifvFlash.setTextColor(R.color.color_ff5353.resColor())
            }
            viewBinding.ifvFlash.alpha = 1.0f
            viewBinding.ifvFlash.isNeedPressState = true
        } else {
            viewBinding.ifvFlash.setTextColor(R.color.Gray_E.resColor())
            viewBinding.ifvFlash.alpha = 0.5f
            viewBinding.ifvFlash.isNeedPressState = false
        }
    }
}