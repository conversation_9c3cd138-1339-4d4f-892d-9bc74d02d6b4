package com.commsource.camera.xcamera.stamp_model.api

import com.commsource.beautyplus.BuildConfig
import com.commsource.config.ApplicationConfig
import com.commsource.config.SubscribeConfig
import com.commsource.homev3.entity.AiCreativeEntity
import com.commsource.homev3.entity.BannerEntity
import com.commsource.homev3.entity.HomeConfigEntity
import com.commsource.homev3.entity.KingkongEntity
import com.commsource.statistics.Meepo
import com.commsource.util.BPLocationUtils
import com.commsource.util.LanguageUtil
import com.commsource.util.MtOkHttpRequestUtil
import com.meitu.common.AppContext
import com.meitu.http.HttpResult
import com.meitu.http.kotex.Get
import com.meitu.http.kotex.Host
import com.meitu.http.kotex.KingkongHost
import com.meitu.http.kotex.Parameter
import com.meitu.http.kotex.XSign
import com.meitu.http.kotex.addAppUserIdParameter
import com.meitu.http.kotex.syncResponse
import java.io.Serializable
import java.util.TimeZone

class WeatherApi {

    /**
     * 获取天气数据
     * @param lat 纬度
     * @param lon 经度
     * @param lang 语言
     */
    fun fetchWeatherData(lat: Double, lon: Double): WeatherDataEntity? {
        return Get<HttpResult<WeatherDataEntity>>("/beautyplus/api/v1/weather/current") {
            Host(KingkongHost)
            Parameter {
                this["lat"] = lat
                this["lon"] = lon
                this["language"] = LanguageUtil.getLanguage(AppContext.context, true)
                addAppUserIdParameter()
            }
            XSign(
                if (ApplicationConfig.isTestMaterialEnvironment()) {
                    "61shpk0ti2pcq30pskmqdb20"
                } else {
                    "c0ernn4rlvh9yr2kyugikftz"
                }, if (ApplicationConfig.isTestMaterialEnvironment()) {
                    "ntks9kqnsjlq381zbtq3unjgls7bc3zc"
                } else {
                    "mxdrkl5xzxprlrjao7ukj8cm0qjxp49t"
                }
            )
        }.responseOnBackground().synRequest().syncResponse {}?.data
    }

    data class WeatherDataEntity(
        val celsius: Float? = null,
        val fahrenheit: Float? = null,
        var humidity: Float? = null,
        var weather: WeatherData? = null
    ) : Serializable

    data class WeatherData(
        var id: Int? = null,
        var main: String? = null,
        var description: String? = null,
        var icon: String? = null
    ) : Serializable
}