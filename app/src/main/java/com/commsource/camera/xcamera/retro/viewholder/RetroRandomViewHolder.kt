package com.commsource.camera.xcamera.retro.viewholder

import android.content.Context
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemRetroRandomMaterialBinding
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.setSize
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.bind.setWidth
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder
import com.meitu.room.database.DBHelper
import com.pixocial.business.duffle.repo.film.FilmMaterial
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

class RetroRandomViewHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<String>(context, parent, R.layout.item_retro_random_material) {

    val viewBinding = ItemRetroRandomMaterialBinding.bind(itemView)

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<String>?,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        item?.entity?.run {
            viewBinding.tvName.textSize = 10f
            viewBinding.tvName.text = R.string.v7100_B_58.text()
        }

        if (item?.isSelect == true) {
            viewBinding.textBg.visible()
        } else {
            viewBinding.textBg.gone()
        }
    }

    override fun onViewDetached() {
        super.onViewDetached()
        scope.cancel()
    }
}