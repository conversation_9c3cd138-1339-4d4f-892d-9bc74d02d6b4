package com.commsource.camera.xcamera.idcard

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import androidx.lifecycle.Observer
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverFillLightBinding
import com.commsource.camera.xcamera.BpCameraViewModel
import com.commsource.camera.xcamera.cover.AbsCover
import com.commsource.camera.xcamera.cover.CameraConfigViewModel
import com.commsource.util.gone
import com.commsource.util.visible

/**
 * @Desc : 补光Cover
 * <AUTHOR> Bear - 2020/5/12
 */
class IDCardFillLightCover : AbsCover<CoverFillLightBinding>() {

    val bpCameraViewModel by lazy { getViewModel(BpCameraViewModel::class.java) }

    val confitViewModel by lazy { getViewModel(CameraConfigViewModel::class.java) }

    override fun getLayoutId(): Int {
        return R.layout.cover_fill_light
    }

    override fun initView() {
        //处理前置补光动画
        //监听拍照状态变化 在拍照前显示白底

        bpCameraViewModel.beforeCaptureEvent.observe(coverGroup.mActivity, Observer {
            it?.takeIf { bpCameraViewModel.isFrontCamera() && confitViewModel.isFillLightON() }
                ?.let {
                    mViewBinding.mVFillLight.animate().setListener(null).setStartDelay(0).cancel()
                    mViewBinding.mVFillLight.alpha = 1f
                    mViewBinding.mVFillLight.visible()
                    mViewBinding.mVFillLight.animate().alpha(0f).setDuration(10)
                        .setListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationCancel(animation: Animator) {
                                super.onAnimationCancel(animation)
                                mViewBinding.mVFillLight.gone()
                            }

                            override fun onAnimationEnd(animation: Animator) {
                                super.onAnimationEnd(animation)
                                mViewBinding.mVFillLight.gone()
                            }
                        })
                        .setStartDelay(50)
                        .start()
                }
        })
    }

    override fun initViewModel() {
    }
}