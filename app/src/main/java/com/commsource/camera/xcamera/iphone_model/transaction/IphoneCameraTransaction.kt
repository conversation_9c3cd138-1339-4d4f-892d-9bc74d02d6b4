package com.commsource.camera.xcamera.iphone_model.transaction

import com.commsource.beautyplus.router.RouterEntity
import com.commsource.beautyplus.router.UriConstant
import com.commsource.camera.mvp.annotation.CameraMode
import com.commsource.camera.xcamera.iphone_model.IphoneFunction
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneCameraNewViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneCaptureViewModel
import com.commsource.camera.xcamera.iphone_model.viewmodel.IphoneFunctionViewModel
import com.pixocial.framework.cover.AbstractTransaction
import java.util.Locale

/**
 * @Desc : 拍照、录制视频相关 相关事务
 */
class IphoneCameraTransaction : AbstractTransaction() {
    private val iphoneCameraViewModel by lazy { fragmentViewModel(IphoneCameraNewViewModel::class) }
    private val cameraCaptureViewModel by lazy { fragmentViewModel(IphoneCaptureViewModel::class) }
    private val functionViewModel by lazy { fragmentViewModel(IphoneFunctionViewModel::class) }

    override fun onAttachTransaction() {
        iphoneCameraViewModel.protocolLiveData.observe(lifecycleOwner) {
            onHandleProtocol(it)
        }
    }

    override fun onDetachTransaction() {

    }

    override fun onWindowFocusChanged(hasWindowFocus: Boolean) {

    }

    private fun onHandleProtocol(webEntity: RouterEntity) {
        when (webEntity.lastPathSegment) {
            UriConstant.PATH_IPHONE_M_VIDEO -> {
                //视频模式
                cameraCaptureViewModel.updateMode(CameraMode.VIDEO_MODE)
            }

            UriConstant.PATH_IPHONE_M_SELFIE -> {
                //自拍模式
                cameraCaptureViewModel.updateMode(CameraMode.CAPTURE_MODE)
            }
        }
        webEntity.getParameter("type")?.let {
            when (it.lowercase(Locale.ROOT)) {
                "styles" -> {
                    functionViewModel.show(IphoneFunction.STYLE)
                    functionViewModel.logFuncCategoryClick("风格")
                }

                "lighting" -> {
                    functionViewModel.show(IphoneFunction.RELIGHT)
                    functionViewModel.logFuncCategoryClick("打光")
                }

                "white_balance" -> {
                    functionViewModel.show(IphoneFunction.WHITE_BALANCE)
                    functionViewModel.logFuncCategoryClick("白平衡")
                }

                "fill_light" -> {
                    functionViewModel.show(IphoneFunction.FILL_LIGHT)
                }
            }
        }
    }
}