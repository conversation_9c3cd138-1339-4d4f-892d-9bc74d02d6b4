package com.commsource.camera.param;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 妆容type
 * <p>
 * Create by vinvince On 2018/8/5 14:51
 */
@Retention(RetentionPolicy.SOURCE)
public @interface MakeupType {
    /**
     * 调试用的AR配置（例如打印人脸信息）
     */
    int TYPE_DEBUG = 0x00;

    /**
     * 美肤
     */
    int TYPE_BEAUTY_SKIN = 0x01;
    /**
     * 整妆
     */
    int TYPE_MAKEUP = 0x02;
    /**
     * 口红
     */
    int TYPE_LIP_STICK = 0x03;
    /**
     * 眉毛
     */
    int TYPE_EYE_BROW = 0x04;
    /**
     * 美瞳
     */
    int TYPE_BEAUTY_PUPIL = 0x05;
    /**
     * 眼线
     */
    int TYPE_EYE_LINER = 0x06;
    /**
     * 眼影
     */
    int TYPE_EYE_SHADOW = 0x07;
    /**
     * 双眼皮
     */
    int TYPE_EYELID = 0x08;
    /**
     * 睫毛
     */
    int TYPE_EYE_LASH = 0x09;
    /**
     * 卧蚕
     */
    int TYPE_EYE_SMILES = 0x17;
    /**
     * 腮红
     */
    int TYPE_BLUSH = 0x0a;
    /**
     * 修容
     */
    int TYPE_TRIMMING = 0x0b;
    /**
     * 美型
     */
    int TYPE_FACE_LIFT = 0x0c;
    /**
     * 身体检测
     */
    int TYPE_SEGMENT = 0x0d;
    /**
     * 头发
     */
    int TYPE_HAIR = 0x0e;
    /**
     * 衣服
     */
    int TYPE_CLOTHES = 0x0f;
    /**
     * 耳朵
     */
    int TYPE_EAR = 0x10;
    /**
     * 头部
     */
    int TYPE_HEAD = 0x11;
    /**
     * 眼部
     */
    int TYPE_EYE = 0x12;
    /**
     * 面部
     */
    int TYPE_FACIAL = 0x13;
    /**
     * 氛围
     */
    int TYPE_ATMOSPHERE = 0x14;
    /**
     * 套装
     */
    int TYPE_SUIT = 0x15;

    /**
     * 雀斑
     */
    int TYPE_BEAUTY_FRECKLE = 0x16;

    /**
     * 装饰
     */
    int TYPE_BEAUTY_DECORATION = 0x18;

    /**
     * 眼线
     */
    int TYPE_BEAUTY_EYELINER = 0x19;

    /**
     * 通用AR
     */
    int TYPE_NORMAL_AR = 0x90;
    /**
     * 通用背景
     */
    int TYPE_NORMAL_BG = 0x91;

    /**
     * 身体
     */
    int TYPE_BODY_LIFT = 0x93;

    /**
     * AR通用美型
     */
    int TYPE_AR_BEAUTY = 0x94;

    /**
     * Style风格模式的普通AR
     */
    int TYPE_NORMAL_AR_IN_STYLE = 0x95;

    /**
     * 眼妆：包含眼影、眼线、眼线、美瞳、双眼皮、睫毛。
     * {@link #TYPE_EYE_SHADOW}
     * {@link #TYPE_EYE_LINER}
     * {@link #TYPE_EYELID}
     * {@link #TYPE_EYE_LASH}
     * <p>
     * 目前线上没有包装美妆的类型
     */
    @Deprecated
    int TYPE_EYE_MAKEUP = 0x16;

    int TYPE_FR = 0x78;

    int TYPE_FAT_FACE = 0x79;

    /**
     * 胡子
     */
    int TYPE_BEARD = 0x7a;

    /**
     * 滤镜中的AR
     */
    int TYPE_FILTER_AR = 0x96;

    /**
     * 背景光
     */
    int TYPE_BACKGROUND_LIGHT = 0x97;

    /**
     * 氛围光
     */
    int TYPE_AMBIENCE_LIGHT = 0x98;

    /**
     * 面部光
     */
    int TYPE_FACE_LIGHT = 0x99;


    // v77180 开始。相机的美型功能有很多，
    // 需要加载多份配置文件。

    // 注意一下，我这边直接按照顺序。是直接判断落入0x101 - 0x112
    // 就算新美颜/美型效果。太多了，简单处理
    int TYPE_NORMAL_FACE_LIFT = 0x100;  // 旧版本通用脸形 = 256


    int TYPE_NEW_BEAUTY_SHAPE_1 = 0x101; // 3D 直鼻、丰额头、苹果肌、提拉、鼻雕和凸嘴矫正
    int TYPE_NEW_BEAUTY_SHAPE_2 = 0x102; // 其他美型通用配置
    int TYPE_NEW_BEAUTY_SHAPE_5 = 0x103; // 面部折叠在用
    int TYPE_NEW_BEAUTY_SHAPE_6 = 0x104; // 脸长在用
    int TYPE_NEW_BEAUTY_SHAPE_7 = 0x105; // 3D 小翘鼻
    int TYPE_NEW_BEAUTY_SHAPE_8 = 0x106; // 3D 驼峰鼻
    // 这6个配置和上面6个配置其实是一样的。只是因为业务上需要实现效果叠加
    // 所以这边就不能共用一份配置，需要重复加载一次。
    int TYPE_FACE_NEW_BEAUTY_SHAPE_1 = 0x107; // 脸型 -（3D 直鼻、丰额头、苹果肌）等专属
    int TYPE_FACE_NEW_BEAUTY_SHAPE_5 = 0x108; // 脸型-面部折叠 专属
    int TYPE_FACE_NEW_BEAUTY_SHAPE_6 = 0x109; // 脸型-脸长    专属
    int TYPE_FACE_NEW_BEAUTY_SHAPE_7 = 0x10A; // 脸型-经典   专属
    int TYPE_FACE_NEW_BEAUTY_SHAPE_8 = 0x10B; // 脸型-原生    专属
    int TYPE_FACE_NEW_BEAUTY_SHAPE_9 = 0x10C; // 脸型-清瘦    专属
    int TYPE_FACE_NEW_BEAUTY_SHAPE_10 = 0x10D; // 脸型-幼短   专属
    int TYPE_FACE_NEW_BEAUTY_SHAPE_11 = 0x10E; // 脸型-圆润    专属
    int TYPE_FACE_NEW_BEAUTY_SHAPE_12 = 0x11F; // 脸型-棱角    专属

}
