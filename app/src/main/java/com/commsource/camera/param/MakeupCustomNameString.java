package com.commsource.camera.param;

/*
* AR-SDK妆容对应的CustomName，实质上是AR配置里每一个妆容part对应的CustomName
* */
public interface MakeupCustomNameString {
    /**
     * 唇妆
     */
    String NAME_MOUTH = "MUFACE_MOUTH";

    /**
     * 眉毛
     */
    String NAME_BROW = "MUFACE_EYEBROW";

    /**
     * 腮红
     */
    String NAME_CHEEK = "MUFACE_CHEEK";

    /**
     * 眼线
     * 备注：因为历史原因，保留了两个Key，
     * 优先使用 EYELINER ，建议废弃 EYELINE.
     */
    String NAME_EYELINE = "MUFACE_EYELINE";
    String NAME_EYELINER = "MUFACE_EYELINER";

    /**
     * 眼影
     */
    String NAME_EYESHADOW = "MUFACE_EYESHADOW";

    /**
     * 眼瞳
     */
    String NAME_EYEPUPIL = "MUFACE_EYEPUPIL";

    /**
     * 睫毛
     */
    String NAME_EYELASH = "MUFACE_EYELASH";

    /*
    * 修容（五官立体）
    * */
    String NAME_HIGHLIGHT = "MUFACE_HIGHLIGHT";


    /*
     * 卧蚕
     * */
    String NAME_WOCAN = "MUFACE_WOCAN";

    /*
     * 染发
     * */
    String NAME_HAIRCOLOR = "MUFACE_HAIRCOLOR";

    /*
     * 雀斑
     * */
    String NAME_FRECKLE = "MUFACE_FRECKLE";

    /*
     * 设计师自定义为妆容
     * */
    String NAME_DESIGNER_DEFINED = "MUFACE_DESIGNER_DEFINED";
}
