package com.commsource.search_common.repo

import androidx.room.*
import com.commsource.search_common.entity.KeyWordInfo
import com.meitu.room.dao.IDataResource

@Dao
interface KeyWordDao : IDataResource<KeyWordInfo, String> {

    @Query("select * from KeyWordInfo where type = :key")
    override fun loadEntity(key: String?): KeyWordInfo?

    @Query("select type from KeyWordInfo")
    override fun loadKeys(): MutableList<String>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    override fun insert(entity: KeyWordInfo)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    override fun insertAll(list: Array<KeyWordInfo>)

    @Update
    override fun updateAll(list: Array<KeyWordInfo>)

    @Update
    override fun update(entity: KeyWordInfo)

    @Delete
    override fun delete(entity: KeyWordInfo)

    //删除所有
    @Query("delete from KeyWordInfo")
    fun deleteAll()

    @Query("delete from KeyWordInfo where type =:type")
    fun deleteAllByKey(type: String)

    @Delete
    override fun deleteAll(list: Array<KeyWordInfo>)

    @Query("select * from KeyWordInfo")
    fun loadAll(): MutableList<KeyWordInfo>

    @Query("select * from KeyWordInfo")
    fun loadAllEnableEntity(): MutableList<KeyWordInfo>


    @Query("select * from KeyWordInfo where type =:type")
    fun loadAllEntityWithType(type: String): List<KeyWordInfo>

    @Query("delete from KeyWordInfo where type =:type")
    fun deleteAllEntityWithType(type: String)

    @Query("select * from KeyWordInfo where type =(:type)")
    fun loadAllEntityWithTypes(type: List<String>): List<KeyWordInfo>

    @Query("select COUNT(*) from KeyWordInfo where type =:type")
    fun entityCount(type: String): Long


}