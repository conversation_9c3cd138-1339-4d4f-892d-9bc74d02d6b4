package com.commsource.search_common.viewmodel

import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.text.BidiFormatter
import androidx.core.text.TextDirectionHeuristicsCompat
import androidx.databinding.ObservableField
import com.commsource.beautyplus.R
import com.commsource.library_mvvm.bind.command.BindingCommand
import com.commsource.library_mvvm.bind.command.BindingConsumer
import com.commsource.search_common.SearchConst
import com.commsource.search_common.StringCutUtil
import com.commsource.search_common.entity.KeyWordInfo
import com.commsource.search_common.holder.TrendingItemHolder
import com.commsource.search_common.repo.KeyWordRepo
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.store.filter.search.TagFlowLayout
import com.commsource.util.LOGV
import com.commsource.util.RTLTool
import com.commsource.util.coroutine.CloseableCoroutineScope
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.setMarginCompat
import com.commsource.util.visible
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SearchHotViewModel(
    val searchWord: ObservableField<String>,//搜索关键字
    val type: String,//轮播词类型
    val from: Int,//轮播词类型
    val limit: Int = 10,//轮播词个数
    private val wordLenLimit: Int = 24,//最多显示几个数字
) {


    val viewModelScope = CloseableCoroutineScope(SupervisorJob() + Dispatchers.IO)

    val visibility = ObservableField(View.GONE)

    private var tagFlowLayout: TagFlowLayout? = null

    private val hotWords = mutableListOf<String>()

    val getHistoryPanel = BindingCommand<TagFlowLayout>(BindingConsumer {
        tagFlowLayout = it
    })

    fun loadView(shouldSPM: Boolean) {
        viewModelScope.launch {
            var result = KeyWordRepo.fetchDataWithType(type)
            val uniqueList: List<KeyWordInfo> = ArrayList<KeyWordInfo>(HashSet<KeyWordInfo>(result))//少数情况，会出现重复的数据，这里过一下去除重复
            result = uniqueList

            limit.let { result.takeIf { it.isNotEmpty() }?.let { result = result.take(10) } }
            result = result.sortedBy {
                it.orderNo
            }
            "type $type 热词 ${result.size}".LOGV()
            withContext(Dispatchers.Main) {
                if (result.isEmpty()) {
                    visibility.set(View.GONE)
                } else {
                    visibility.set(View.VISIBLE)
                    tagFlowLayout?.apply {
                        removeAllViews()
                        result.forEach { keyInfo ->
                            val itemView = LayoutInflater.from(context)
                                .inflate(R.layout.item_trending_new_holer, null)
                            val tv = itemView.findViewById<TextView>(R.id.tvTrending)
                            val newTag = itemView.findViewById<ImageView>(R.id.new_Icon)
                            val hotTag = itemView.findViewById<ImageView>(R.id.hot_Icon)
                            tv.apply {
                                maxLines = 1
                                setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
                                gravity = Gravity.CENTER
                                setBackgroundResource(R.drawable.radius_37_graye)
                                setPadding(10.dp, 0, 10.dp, 0)

                                text = if (RTLTool.isLayoutRtl()) {
                                    val infoStr = StringCutUtil.formatText(keyInfo.name, wordLenLimit)
                                    BidiFormatter.getInstance().unicodeWrap(
                                        "\u202A" + infoStr, TextDirectionHeuristicsCompat.RTL
                                    )
                                } else {
                                    StringCutUtil.formatText(keyInfo.name, wordLenLimit)
                                }
                                this.setOnClickListener { view ->
                                    "点击 $text".LOGV()
                                    searchWord.set(keyInfo.name)
                                    clickTrendingWord(keyInfo.name)

                                }
                            }

                            if (keyInfo.label?.find { it.lowercase() == TrendingItemHolder.LABEL_HOT } != null) {
                                hotTag.visible()
                                hotTag.setMarginCompat(
                                    start = tv.paint.measureText(tv.text.toString()).toInt() + 10.dp
                                )
                            } else {
                                hotTag.gone()
                            }

                            if (keyInfo.label?.find { it.lowercase() == TrendingItemHolder.LABEL_NEW } != null) {
                                newTag.visible()
                                newTag.setMarginCompat(
                                    start = tv.paint.measureText(tv.text.toString()).toInt() + 10.dp
                                )
                            } else {
                                newTag.gone()
                            }
                            hotWords.add(keyInfo.name)
                            if (shouldSPM) {
                                showTrendingWord(keyInfo.name)
                            }
                            addView(
                                itemView
                            )
                        }
                    }
                }
            }
        }
    }

    /**
     * 弹窗点击空白处，需要埋点
     */
    fun trendWord() {
        hotWords.forEach {
            showTrendingWord(it)
        }
    }

    /**
     * 热搜词曝光
     */
    private fun showTrendingWord(keyWord: String) {
        MTAnalyticsAgent.logEvent(
            MTAnalyticsConstant.trending_word_imp,
            HashMap<String, String>(4).apply {
                if (from == SearchConst.ENTRANCE_STORE) {
                    put(MTAnalyticsConstant.source, MTAnalyticsConstant.shop_page)
                } else if (from == SearchConst.ENTRANCE_FUNCTION) {
                    put(MTAnalyticsConstant.source, MTAnalyticsConstant.func_page)
                } else if (from == SearchConst.HOME) {
                    put(MTAnalyticsConstant.source, MTAnalyticsConstant.home_page_search)
                } else if (from == SearchConst.MINI_APP) {
                    put(MTAnalyticsConstant.source, MTAnalyticsConstant.miniapp_page_search)
                }


                if (type == KeyWordInfo.TYPE_STICKERS) {
                    put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.sticker)
                } else if (type == KeyWordInfo.TYPE_FILTERS) {
                    put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.filter)
                } else if (type == KeyWordInfo.TYPE_TEMPLATES) {
                    put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.template)
                } else if (type == KeyWordInfo.TYPE_ALL) {
                    put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.all)
                } else if (type == KeyWordInfo.TYPE_MINIAPP) {
                    put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.miniapp)
                } else if (type == KeyWordInfo.TYPE_TEXT_TEMPLATES) {
                    put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.text)
                } else if (type == KeyWordInfo.TYPE_BRUSHES) {
                    put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.brush)
                }
                put(MTAnalyticsConstant.word_content, keyWord)
            })
    }


    /**
     * 热搜词点击/搜索框搜索内容
     */
    fun clickTrendingWord(keyWord: String) {
        val map = HashMap<String, String>(4).apply {
            if (from == SearchConst.ENTRANCE_STORE) {
                put(MTAnalyticsConstant.source, MTAnalyticsConstant.shop_page)
            } else if (from == SearchConst.ENTRANCE_FUNCTION) {
                put(MTAnalyticsConstant.source, MTAnalyticsConstant.func_page)
            } else if (from == SearchConst.HOME) {
                put(MTAnalyticsConstant.source, MTAnalyticsConstant.home_page_search)
            } else if (from == SearchConst.MINI_APP) {
                put(MTAnalyticsConstant.source, MTAnalyticsConstant.miniapp_page_search)
            }

            if (type == KeyWordInfo.TYPE_STICKERS) {
                put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.sticker)
            } else if (type == KeyWordInfo.TYPE_FILTERS) {
                put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.filter)
            } else if (type == KeyWordInfo.TYPE_TEMPLATES) {
                put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.template)
            } else if (type == KeyWordInfo.TYPE_ALL) {
                put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.all)
            } else if (type == KeyWordInfo.TYPE_MINIAPP) {
                put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.miniapp)
            } else if (type == KeyWordInfo.TYPE_TEXT_TEMPLATES) {
                put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.text)
            } else if (type == KeyWordInfo.TYPE_BRUSHES) {
                put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.brush)
            }
            put(MTAnalyticsConstant.word_content, keyWord)
        }
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.trending_word_clk, map)
    }
}