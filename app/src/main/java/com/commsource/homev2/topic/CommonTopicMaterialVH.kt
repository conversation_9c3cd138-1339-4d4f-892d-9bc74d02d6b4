package com.commsource.homev2.topic

import android.content.Context
import android.text.TextUtils
import android.util.TypedValue
import android.view.ViewGroup
import android.widget.ImageView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHomev2TopicMaterialBinding
import com.commsource.duffle.formula.BACompareView
import com.commsource.homev2.entity.BannerType
import com.commsource.homev2.entity.RecommendMaterial
import com.commsource.homev2.entity.TagType
import com.commsource.studio.DecorateConstant
import com.commsource.util.ResourcesUtils
import com.commsource.util.gone
import com.commsource.util.setCornerRadius
import com.commsource.util.setMarginCompat
import com.commsource.util.toColor
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.pixocial.androidx.core.extension.dp


/**
 * 初始化的时候指定Size。
 */
class CommonTopicMaterialVH(context: Context, parent: ViewGroup) :
    TopicBaseVH<RecommendMaterial>(context, parent, R.layout.item_homev2_topic_material) {

    val binding = ItemHomev2TopicMaterialBinding.bind(itemView)

    override fun onViewHolderCreated() {
        val layoutParams = binding.bgRoot.layoutParams
        getContainerSize()?.let {
            layoutParams.width = it.first as Int
            layoutParams.height = it.second as Int
        }
        binding.bgRoot.layoutParams = layoutParams
    }

    override fun onBindViewHolder(position: Int, item: BaseItem<RecommendMaterial>, payloads: List<Any>?) {
        super.onBindViewHolder(position, item, payloads)
        val width = getContainerSize()?.first as Int
        val height = getContainerSize()?.second as Int

        item.entity.let {
            if (it.isPaid == DecorateConstant.NEED_PAID) {
                binding.ivIap.visible()
            } else {
                binding.ivIap.gone()
            }

            binding.ivTag.visible()
            when (it.tag) {
                TagType.HOT -> binding.ivTag.setImageResource(R.drawable.corner_hot_large)
                TagType.NEW -> binding.ivTag.setImageResource(R.drawable.corner_new_large)
                else -> binding.ivTag.gone()
            }
        }

        // 默认占位色
        val placeHolderColor = getSpecifyBgColor() ?: ResourcesUtils.getColor(R.color.Gray_PlaceHolder)
        // 真实的底色。
        val targetColor = if (TextUtils.isEmpty(item.entity.color)) item.entity.commonColor else item.entity.color
        val color = targetColor?.toColor(placeHolderColor) ?: placeHolderColor
        binding.bgRoot.setBackgroundColor(placeHolderColor)
        cornerRadius()?.let {
            binding.rootView.radius = it
            binding.border.setCornerRadius(it)
        }
        // 内边距
        containerPadding()?.let { binding.baCompareView.setInternalPadding(it) }
        binding.materialTitle.gone()
        (if (item.entity.parentBannerType == BannerType.TYPE_HPB_H5_TOPIC) {
            item.entity?.title?.takeIf { it.isNotEmpty() }?.let {
                if (isArticleContent()) {
                    binding.materialTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
                    binding.materialTitle.setMarginCompat(10.dp, 0, 10.dp, 8.dp)
                }
                binding.materialTitle.visible()
                binding.materialTitle.text = item.entity?.title
            }
            item.entity.coverImage
        } else {
            item.entity.thumbnail
        })?.let {
            containerPadding()?.let {
                binding.baCompareView.setInternalPadding(it)
            }
            val scaleType = scaleType() ?: ImageView.ScaleType.CENTER_CROP
            binding.baCompareView.setAfterImageLoadedListener(object : BACompareView.OnAfterImageLoadListener {
                override fun onAfterImageLoaded() {
                    binding.bgRoot.setBackgroundColor(color)
                }
            })
            binding.baCompareView.prepare(
                urlAfter = it,
                urlBefore = if (!TextUtils.isEmpty(item.entity.originalImg)) item.entity.originalImg else null,
                targetW = width,
                targetH = height,
                type = scaleType,
                urlAfterError = it,
                urlBeforeError = if (!TextUtils.isEmpty(item.entity.originalImg)) item.entity.originalImg else null
            )
        }
    }
}