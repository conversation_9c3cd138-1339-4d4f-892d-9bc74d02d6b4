package com.commsource.homev2.topic

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentTopicMixBinding
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.widget.FastCenterScrollLayoutManager
import com.commsource.homev2.entity.MixMaterials
import com.commsource.util.ViewUtils
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.resColor
import com.commsource.util.setMarginTop
import com.commsource.util.setRTL
import com.commsource.widget.DisplayExtension
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.AppBarLayout.LayoutParams.SCROLL_FLAG_NO_SCROLL
import com.google.android.material.appbar.AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL
import com.meitu.library.util.device.DeviceUtils
import kotlin.math.abs

class MixTopicFragment : BaseFragment() {

    private val binding by lazy { FragmentTopicMixBinding.inflate(layoutInflater) }

    private val viewModel by lazy { ViewModelProvider(ownerActivity)[TopicViewModel::class.java] }

    private val baseAdapter by lazy { BaseRecyclerViewAdapter(ownerActivity) }

    private var isFirstEnter: Boolean = true

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        viewModel.bannerEntityInfo?.let {
            it.mixContent?.let {
                initialTabLayout(it)
                initialMaterialsRv(it)
            }
        }
    }


    private fun initialTabLayout(mixEntities: List<MixMaterials>) {
        viewModel.curCommonUIInfo?.headInfo?.let {
            // 状态栏高度
            val statusBarHeight = if (DisplayExtension.hasCutout()) DeviceUtils.getStatusHeight() else 0
            // tab 高度
            val tabBarHeight = 35.dp
            // 标题栏高度
            val titleBarHeight = 44.dp
            val barTopMargin = statusBarHeight + titleBarHeight
            val topSpaceHeight = it.height - barTopMargin - tabBarHeight
            if (mixEntities.size <= 1) {
                binding.tabBar.gone()
                ViewUtils.setHeight(binding.topSpace, it.height - 5.dp)
            } else {
                ViewUtils.setHeight(binding.topSpace, topSpaceHeight)
                binding.tabBar.setMarginTop(barTopMargin)
                binding.tabBar.apply {
                    layoutManager =
                        FastCenterScrollLayoutManager(
                            ownerActivity
                        )
                    baseAdapter.addTag(TopicTagVH.DARK_MODE, viewModel.isDarkMode)
                    adapter = baseAdapter
                    baseAdapter.setSingleItemEntities(mixEntities, TopicTagVH::class.java, true)
                    baseAdapter.setCurrentSelectPosition(0)
                    baseAdapter.setOnEntityClickListener(MixMaterials::class.java) { pos, _ ->
                        binding.vp2.setCurrentItem(pos, false)
                        true
                    }
                    addItemDecoration(object : ItemDecoration() {
                        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                            when (parent.getChildAdapterPosition(view)) {
                                0 -> outRect.setRTL(16.dp(), 0, 10.dp(), 0)
                                parent.adapter!!.itemCount - 1 -> outRect.setRTL(0, 0, 10.dp(), 0)
                                else -> outRect.setRTL(0, 0, 10.dp(), 0)
                            }
                        }
                    })
                }
            }
        }

    }

    private fun initialMaterialsRv(mixEntities: List<MixMaterials>) {
        binding.vp2.adapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int {
                return mixEntities.size
            }

            override fun createFragment(position: Int): Fragment {
                return MixTopicSubMaterialFragment().apply {
                    arguments = Bundle().apply {
                        putInt(MixTopicSubMaterialFragment.POS, position)
                    }
                }
            }
        }
        binding.vp2.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                baseAdapter.setCurrentSelectPosition(position)
                binding.tabBar.smoothScrollToPosition(position)
                if (!isFirstEnter) {
                    (baseAdapter.currentSelectEntity as? MixMaterials)?.let {
                        viewModel.logOnTabClk(it.materialType)
                    }
                }
                isFirstEnter = false
            }
        })

        binding.appBar.addOnOffsetChangedListener { _, verticalOffset ->
            viewModel.notifyYOffsetEvent.value = verticalOffset.toFloat()
            viewModel.notifyCheckVisibleEvent.value = true
            // 直接变更颜色
            viewModel.curCommonUIInfo?.let {
                if (abs(verticalOffset) >= binding.appBar.totalScrollRange) {
                    binding.tabBar.setBackgroundColor(it.titleInfo.rootBgColor)
                } else {
                    binding.tabBar.setBackgroundColor(R.color.transparent.resColor())
                }
            }
        }

        viewModel.disableScrollEvent.observe(viewLifecycleOwner) { isDisable ->
            (binding.topSpace.layoutParams as? AppBarLayout.LayoutParams)?.let {
                it.scrollFlags = if (isDisable) SCROLL_FLAG_NO_SCROLL else SCROLL_FLAG_SCROLL
                binding.topSpace.layoutParams = it
            }
        }
    }
}