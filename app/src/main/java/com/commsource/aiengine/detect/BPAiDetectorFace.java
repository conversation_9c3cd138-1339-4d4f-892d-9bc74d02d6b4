package com.commsource.aiengine.detect;

import static com.meitu.mtlab.MTAiInterface.MTAiEngineMode.IMAGE;
import static com.meitu.mtlab.MTAiInterface.MTAiEngineMode.VIDEO;
import static com.meitu.mtlab.MTAiInterface.MTAiEngineMode.VIDEO_IMAGE;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.FaceDetectorMode.IMAGE_FA;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.FaceDetectorMode.IMAGE_FD_FA;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.FaceDetectorMode.VIDEO_FD_FA_FAST;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.FaceDetectorMode.VIDEO_FD_FA_NORMAL;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_3DFA;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_AGE_SEA;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_DL3D;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_EYELID;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_FACE;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_GENDER;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_HEAD;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_LIPS;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_MOUTH_AND_PARSING;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_NECK;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_PARSING_HEAVY;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_PART;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_POSEESTIMATION;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_RACE;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_REFINE_EYE;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_REFINE_MOUTH;
import static com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption.MT_FACE_ENABLE_VISIBILITY;
import static com.meitu.mtlab.MTAiInterface.common.MTAiEngineType.MTAIENGINE_MODEL_FACE_NECK;
import static com.meitu.mtlab.MTAiInterface.common.MTAiEngineType.MTAIENGINE_MODEL_FACE_NECK_CONFIG;

import android.util.Log;

import com.commsource.util.DeviceLevelAdapter;
import com.meitu.common.AppContext;
import com.meitu.library.util.io.FileUtils;
import com.meitu.mtlab.MTAiInterface.MTAiEngineMode;
import com.meitu.mtlab.MTAiInterface.MTAiEngineResult;
import com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFace;
import com.meitu.mtlab.MTAiInterface.MTFaceModule.MTFaceOption;
import com.meitu.mtlab.MTAiInterface.MeituAiEngine;
import com.meitu.mtlab.MTAiInterface.common.MTAiEngineEnableOption;
import com.meitu.mtlab.MTAiInterface.common.MTAiEngineFrame;
import com.meitu.mtlab.MTAiInterface.common.MTAiEngineRet;
import com.meitu.mtlab.MTAiInterface.common.MTAiEngineType;

import java.io.File;
import java.nio.ByteBuffer;

/**
 * User: Zhou
 * Date: 2020-07-02
 * Description: AI引擎人脸+项链点检测器
 */
public class BPAiDetectorFace extends BPAiDetectorBase {

    public static final String TAG = BPAiDetectorFace.class.getSimpleName() + "_mtai";

    public static final String BPAI_FACE_ENABLE_KEY = "face_faceEnable"; // 人脸检测
    public static final String BPAI_FACE_REFINE_KEY = "face_refineEnable"; // 眼睛+嘴巴级联开关
    public static final String BPAI_FACE_GENDER_KEY = "face_genderEnable"; // 性别检测
    public static final String BPAI_FACE_AGE_KEY = "face_ageEnable"; // 年龄检测
    public static final String BPAI_FACE_RACE_KEY = "face_raceEnable"; // 种族检测
    public static final String BPAI_FACE_POSE_KEY = "face_poseEstimateInterval"; // 姿态预估
    public static final String BPAI_FACE_VISIBLE_KEY = "face_visibleEnable"; // 可见性
    public static final String BPAI_FACE_NECK_KEY = "face_neckEnable"; // 项链检测
    public static final String BPAI_FACE_MASK_KEY = "face_maskEnable"; // 嘴唇mask
    public static final String BPAI_FACE_PART_KEY = "face_partEnable"; // 局部人脸点（无人脸五官信息）
    public static final String BPAI_FACE_FACEMASK_KEY = "face_faceMaskEnable"; // 全脸mask
    public static final String BPAI_FACE_HEAD_KEY = "face_faceHeadEnable"; // 头部点
    public static final String BPAI_FACE_3DFA_KEY = "face_face3DFAEnable"; // 3dfa,获取MTDL3DFace的系数结果
    public static final String BPAI_FACE_EYELID_KEY = "face_faceEyelidEnable"; // 单双眼皮检测
    private static final String CONFIG_XML_PATH = "necklace_config/config.xml"; // 内置asset的路径
    private boolean mHasSetNecklaceModel = false; // 是否已经设置项链模型
    private boolean mLastAsyncFd; // 记录最后一次fd同步异步开关，用于强制设置FD之后的还原操作

    /**
     * 通过外部的引擎实例和检测配置创建实例，本实例引用持有外部这2个变量
     *
     * @param engine       引擎实例
     * @param mode         检测模式
     * @param detectOption 检测配置
     */
    public BPAiDetectorFace(MeituAiEngine engine, @MTAiEngineMode.Params int mode,
                            MTAiEngineEnableOption detectOption) {
        super(engine, mode, detectOption);
        mRegisterOption = new MTFaceOption();
        MTFaceOption faceOption = (MTFaceOption) mRegisterOption;
        if (mode == IMAGE) {
            faceOption.mode = IMAGE_FD_FA;
        } else if (mode == VIDEO_IMAGE || mode == VIDEO) {
            if (DeviceLevelAdapter.isLowDevice()) {
                // 低端机 - 快速模式
                faceOption.mode = VIDEO_FD_FA_FAST;
            } else {
                // 中高端机器 - 普通模式
                faceOption.mode = VIDEO_FD_FA_NORMAL;
            }
        }
    }

    /**
     * 设置人脸检测模式
     *
     * @param mode 人脸检测模式
     */
    public void setFaceDetectMode(@MTFaceOption.FaceDetectorMode.Params int mode) {
        if (mRegisterOption instanceof MTFaceOption) {
            MTFaceOption faceOption = (MTFaceOption) mRegisterOption;
            if (faceOption.mode != mode) {
                faceOption.mode = mode;
                mNeedRegister = true;
                if (mIsNeedPrintLog) {
                    Log.i(TAG, "setFaceDetectMode: 外部设置人脸检测模式：" + mode);
                }
            }
        }
    }

    /**
     * 获取当前的人脸检测模式
     *
     * @return 当前人脸检测模式
     */
    public @MTFaceOption.FaceDetectorMode.Params int getFaceDetectMode() {
        MTFaceOption faceOption = (MTFaceOption) mRegisterOption;
        return faceOption.mode;
    }

    /**
     * 设置模型文件夹路径 (目前人脸模型均通过Maven引入，故无需再单独设置路径)
     *
     * @param folderPath 模型文件夹路径
     * @param isInAsset  是否通过Asset加载
     */
    @Override
    @Deprecated
    public void setModelFolderPath(String folderPath, boolean isInAsset) {

    }

    /**
     * 设置项链点模型路径
     */
    public void setNeckModelPath(String folderPath, boolean inAsset) {
        String necklaceModelPath = folderPath + File.separatorChar + "MTNeck_ptr_model.manis";
        if (FileUtils.isFileExist(necklaceModelPath) || inAsset) {
            mEngine.setSingleModelPath(MTAIENGINE_MODEL_FACE_NECK_CONFIG, CONFIG_XML_PATH);
            mEngine.setSingleModelPath(MTAIENGINE_MODEL_FACE_NECK, necklaceModelPath);
            mHasSetNecklaceModel = true;
            if (mIsNeedPrintLog) {
                Log.i(TAG, "setModelFolderPath: 设置项链模型 成功！！");
            }
        } else {
            Log.i(TAG, "setNeckModelFolderPath: model file not exist!! path = " + necklaceModelPath);
        }
    }

    /**
     * 设置最大人脸数量
     */
    public void setMaxFaceNum(int num) {
        if (mDetectOption != null) {
            mDetectOption.faceOption.maxFaceNum = num;
        }
    }

    /**
     * 设置引擎开关配置
     *
     * @param key    配置的关键字，都以BPAI_开头
     * @param config 引擎开关配置
     */
    public void setEngineConfig(String key, Object config) {
        // 人脸
        if (BPAI_FACE_ENABLE_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_FACE) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_FACE;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_FACE;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 眼睛+嘴巴级联
        if (BPAI_FACE_REFINE_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin =
                            (registerOption.option & (MT_FACE_ENABLE_REFINE_EYE | MT_FACE_ENABLE_REFINE_MOUTH)) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= (MT_FACE_ENABLE_REFINE_EYE | MT_FACE_ENABLE_REFINE_MOUTH);
                        } else {
                            registerOption.option &= ~(MT_FACE_ENABLE_REFINE_EYE | MT_FACE_ENABLE_REFINE_MOUTH);
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 性别
        if (BPAI_FACE_GENDER_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_GENDER) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_GENDER;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_GENDER;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 年龄
        if (BPAI_FACE_AGE_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_AGE_SEA) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_AGE_SEA;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_AGE_SEA;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 种族
        if (BPAI_FACE_RACE_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_RACE) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_RACE;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_RACE;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 姿态预估
        if (BPAI_FACE_POSE_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_POSEESTIMATION) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_POSEESTIMATION;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_POSEESTIMATION;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 嘴唇mask
        if (BPAI_FACE_MASK_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean canUseMouthMask = registerOption.mode == IMAGE_FA || registerOption.mode == IMAGE_FD_FA
                            || (registerOption.option & (MT_FACE_ENABLE_REFINE_EYE | MT_FACE_ENABLE_REFINE_MOUTH)) != 0;
                    enable = enable && canUseMouthMask;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_LIPS) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_LIPS;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_LIPS;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 全脸mask
        if (BPAI_FACE_FACEMASK_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean canUseFaceMask = registerOption.mode == IMAGE_FA || registerOption.mode == IMAGE_FD_FA
                            || (registerOption.option & (MT_FACE_ENABLE_REFINE_EYE | MT_FACE_ENABLE_REFINE_MOUTH)) != 0;
                    enable = enable && canUseFaceMask;
                    boolean enableOrigin = (registerOption.option & (MT_FACE_ENABLE_PARSING_HEAVY | MT_FACE_ENABLE_MOUTH_AND_PARSING)) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= (MT_FACE_ENABLE_PARSING_HEAVY | MT_FACE_ENABLE_MOUTH_AND_PARSING);
                        } else {
                            registerOption.option &= ~(MT_FACE_ENABLE_PARSING_HEAVY | MT_FACE_ENABLE_MOUTH_AND_PARSING);
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 局部人脸点
        if (BPAI_FACE_PART_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_PART) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_PART;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_PART;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 可见性
        if (BPAI_FACE_VISIBLE_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_VISIBILITY) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_VISIBILITY;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_VISIBILITY;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 项链点
        if (BPAI_FACE_NECK_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config && mHasSetNecklaceModel;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_NECK) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_NECK;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_NECK;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 头部点
        if (BPAI_FACE_HEAD_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_HEAD) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_HEAD;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_HEAD;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        // 3DFA
        if (BPAI_FACE_3DFA_KEY.contentEquals(key)) {
            if (config instanceof Boolean) {
                boolean enable = (Boolean) config;
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableOrigin = (registerOption.option & MT_FACE_ENABLE_3DFA) != 0;
                    if (enable != enableOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_3DFA;
                            registerOption.option |= MT_FACE_ENABLE_DL3D;
                            registerOption.meshGeneration = true;
                            registerOption.threeDFaModelType = MTFaceOption.MTFace3DFaModelType.MT_FACE_3DFA;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_3DFA;
                            registerOption.option &= ~MT_FACE_ENABLE_DL3D;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

        if (BPAI_FACE_EYELID_KEY.contains(key)) {
            if (config instanceof Boolean) {
                boolean enable = ((Boolean) config);
                if (mRegisterOption instanceof MTFaceOption) {
                    MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
                    boolean enableNetOrigin = (registerOption.option & MT_FACE_ENABLE_EYELID) != 0;
                    if (enable != enableNetOrigin) {
                        if (enable) {
                            registerOption.option |= MT_FACE_ENABLE_EYELID;
                        } else {
                            registerOption.option &= ~MT_FACE_ENABLE_EYELID;
                        }
                        mNeedRegister = true;
                    }
                }
            }
        }

    }

    /**
     * 注册检测器
     *
     * @return 是否注册成功
     */
    @Override
    public boolean registerModule() {
        if (!mNeedRegister) {
            return mIsRegisterSuccess;
        }
        if (mEngine != null) {
            mIsRegisterSuccess = mEngine.registerModule(MTAiEngineType.FACE_MODULE, mRegisterOption,
                    AppContext.getContext().getAssets()) == MTAiEngineRet.SUCCESS;
            if (!mIsRegisterSuccess) {
                MTFaceOption faceOption = (MTFaceOption) mRegisterOption;
                Log.i(TAG, "registerModule: 注册人脸 faceOption = " + Long.toBinaryString(faceOption.option) + " mode =  "
                        + faceOption.mode + " isRegisterSuccess = " + mIsRegisterSuccess);
            }
            if (mIsNeedPrintLog) {
                MTFaceOption faceOption = (MTFaceOption) mRegisterOption;
                Log.i(TAG, "registerModule: 注册人脸 faceOption = " + Long.toBinaryString(faceOption.option) + " mode =  "
                        + faceOption.mode + " isRegisterSuccess = " + mIsRegisterSuccess);
            }
            mNeedRegister = false;
        }
        return mIsRegisterSuccess;
    }

    /**
     * 注销检测器
     *
     * @return 是否注销成功
     */
    @Override
    public boolean unregisterModule() {
        if (!mIsRegisterSuccess) {
            return false;
        }
        boolean isUnRegisterSuccess = false;
        if (mEngine != null) {
            isUnRegisterSuccess = mEngine.unregisterModule(MTAiEngineType.FACE_MODULE) == MTAiEngineRet.SUCCESS;
            if (!isUnRegisterSuccess) {
                Log.e(TAG, "unregisterFace fail, mRegisterOption = " + mRegisterOption);
            }
            if (mIsNeedPrintLog) {
                MTFaceOption faceOption = (MTFaceOption) mRegisterOption;
                Log.i(TAG, "unregisterModule: 注销人脸 faceOption = " + Long.toBinaryString(faceOption.option) + " mode =  "
                        + faceOption.mode + " isUnRegisterSuccess = " + isUnRegisterSuccess);
            }
            mNeedRegister = true;
        }
        return isUnRegisterSuccess;
    }

    /**
     * 同步注册配置到检测配置中去，在注册完成后，检测之前，需要做这一步操作
     */
    @Override
    protected void syncRegisterToDetect() {
        if (mRegisterOption instanceof MTFaceOption) {
            MTFaceOption registerOption = (MTFaceOption) mRegisterOption;
            mDetectOption.faceOption.option = registerOption.option;
            mDetectOption.faceOption.meshGeneration = registerOption.meshGeneration;
            mDetectOption.faceOption.threeDFaModelType = registerOption.threeDFaModelType;
            if (mIsNeedPrintLog) {
                Log.i(TAG,
                        "syncRegisterToDetect:同步注册人脸 enableDetectFace = "
                                + Long.toBinaryString(mDetectOption.faceOption.option) + ",registerOption.option = "
                                + Long.toBinaryString(registerOption.option));
            }
        }
    }

    @Override
    protected void beforeDetect(MTAiEngineFrame engineFrame) {
        mLastAsyncFd = mDetectOption.faceOption.asyncFd;
        if (engineFrame.firstFrame) {
            mDetectOption.faceOption.asyncFd = false;
        }
    }

    @Override
    protected void afterDetect(MTAiEngineFrame engineFrame, MTAiEngineResult engineResult) {
        mDetectOption.faceOption.asyncFd = mLastAsyncFd;
        if (engineResult != null && engineResult.faceResult != null && engineResult.faceResult.faces != null) {
            for (MTFace face : engineResult.faceResult.faces) {
                if (face.lipMaskData != null) {
                    ByteBuffer byteBufferClone = ByteBuffer.allocateDirect(face.lipMaskData.capacity());
                    byteBufferClone.rewind();
                    byteBufferClone.put(face.lipMaskData);
                    face.lipMaskData.rewind();
                    byteBufferClone.flip();
                    face.lipMaskData = byteBufferClone;
                }
            }
        }
    }
}
