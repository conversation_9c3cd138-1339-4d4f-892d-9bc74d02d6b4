package com.commsource.repository.child.makeup

import androidx.annotation.Keep
import com.commsource.util.V
import com.google.gson.annotations.SerializedName
import com.meitu.template.bean.FileJson

/**
 * @Desc : 美妆基本数据JsonBean
 * <AUTHOR> Bear - 6/29/21
 */
open class MakeupJsonMaterial : Material() {

    @SerializedName("file")
    var file: FileJson? = null

    /**
     * 0否 1是
     */
    @SerializedName("is_gl3")
    var isGl3: Int = 0

    /**
     * 1-点击下载，2-wifi自动下载，3-自动下载
     */
    @SerializedName("download_type")
    var downloadType: Int = 1

    @SerializedName("cust_metadata")
    var custMetaData: CustMetaData? = null

    override fun toMaterial(): MakeupMaterial {
        return super.toMaterial().apply {
            isSupportGl3 = isGl3
            url = file?.url
            configType = 2
            styleConfig = file?.url
            downloadType = <EMAIL>
            prompt = <EMAIL>?.prompt
            styleId = <EMAIL>?.styleId
            type = <EMAIL>?.type
            reference = <EMAIL>?.reference
            <EMAIL>?.isAiMode?.run {
                isAiMode = this
            }
            <EMAIL>?.unableAdjustAlpha?.run {
                unableAdjustAlpha = this
            }

        }
    }

    @Keep
    data class CustMetaData(
        val prompt: String,
        val styleId: String,
        val type: String,
        val reference: String,
        val isAiMode: Boolean,
        val unableAdjustAlpha: Boolean,
    )
}