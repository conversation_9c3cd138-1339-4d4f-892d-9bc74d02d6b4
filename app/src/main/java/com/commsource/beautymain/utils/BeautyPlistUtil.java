package com.commsource.beautymain.utils;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.commsource.beautymain.data.SoftFocusEntity;
import com.commsource.beautymain.data.TonesEntity;
import com.meitu.common.AppContext;
import com.meitu.library.util.Debug.Debug;
import com.meitu.media.util.plist.Array;
import com.meitu.media.util.plist.Dict;
import com.meitu.media.util.plist.PList;
import com.meitu.media.util.plist.PListXMLHandler;
import com.meitu.media.util.plist.PListXMLParser;

import android.graphics.Color;

/**
 * 一键美颜特效解析类
 *
 * <AUTHOR> on 2016/2/22.
 */
public class BeautyPlistUtil {

    private static Map<String, List<SoftFocusEntity>> sEffectMap;

    public static ArrayList<TonesEntity> loadTones(String assetsPath) {
        ArrayList<TonesEntity> tonesList = new ArrayList<TonesEntity>();
        PListXMLParser parser = new PListXMLParser();
        // 基于SAX的实现
        PListXMLHandler handler = new PListXMLHandler();
        parser.setHandler(handler);
        InputStream ins = null;

        try {
            ins = AppContext.getContext().getAssets().open(assetsPath);
            parser.parse(ins);
            PList actualPList = ((PListXMLHandler) parser.getHandler()).getPlist();

            Array tonesArray = (Array) actualPList.getRootElement();
            if (tonesArray != null && tonesArray.size() > 0) {
                for (int i = 0; i < tonesArray.size(); i++) {
                    tonesList.add(getTonesEntity((Dict) tonesArray.get(i)));
                }
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (ins != null) {
                try {
                    ins.close();
                } catch (IOException ee) {
                    Debug.w(ee);
                }
            }
        }
        return tonesList;
    }

    private static TonesEntity getTonesEntity(Dict dict) {
        TonesEntity tonesEntity = new TonesEntity();
        // 肤色ID
        com.meitu.media.util.plist.Integer tonesId = dict.getConfigurationInteger("ID");
        if (tonesId != null) {
            tonesEntity.setTonesId(tonesId.getValue());
        }
        // 肤色
        com.meitu.media.util.plist.String color = dict.getConfiguration("TonesColor");
        if (color != null) {
            tonesEntity.setTonesColor(Color.parseColor(color.getValue()));
        }
        // 肤色透明度
        com.meitu.media.util.plist.Integer tonesAlpha = dict.getConfigurationInteger("TonesAlpha");
        if (tonesAlpha != null) {
            tonesEntity.setTonesAlpha(tonesAlpha.getValue());
        }
        // 肤色统计ID
        com.meitu.media.util.plist.String staticsId = dict.getConfiguration("StaticsID");
        if (staticsId != null) {
            tonesEntity.setTonesStaticsId(staticsId.getValue());
        }
        return tonesEntity;
    }

    private static List<SoftFocusEntity> loadDefocusEntitiesFromCache(String assetsPath) {
        if (sEffectMap == null) {
            return null;
        }

        List<SoftFocusEntity> defocusEntityList = sEffectMap.get(assetsPath);
        List<SoftFocusEntity> cloneDefocusEntityList = null;
        if (defocusEntityList != null) {
            cloneDefocusEntityList = new ArrayList<>();
            for (SoftFocusEntity defocusEntity : defocusEntityList) {
                cloneDefocusEntityList.add(defocusEntity.copy());
            }
        }

        return cloneDefocusEntityList;
    }

    public static List<SoftFocusEntity> loadDefocusEntities(String assetsPath) {
        List<SoftFocusEntity> defocusEntityList = loadDefocusEntitiesFromCache(assetsPath);
        if (defocusEntityList != null) {
            return defocusEntityList;
        }

        defocusEntityList = new ArrayList<>();

        PListXMLParser parser = new PListXMLParser();
        PListXMLHandler handler = new PListXMLHandler();
        parser.setHandler(handler);
        InputStream ins = null;

        try {
            ins = AppContext.getContext().getAssets().open(assetsPath);
            parser.parse(ins);
            PList actualPList = ((PListXMLHandler) parser.getHandler()).getPlist();

            Array defocusEntitys = (Array) actualPList.getRootElement();
            if (defocusEntitys != null && defocusEntitys.size() > 0) {
                for (int i = 0; i < defocusEntitys.size(); i++) {
                    Dict entityDict = (Dict) defocusEntitys.get(i);
                    SoftFocusEntity defocusEntity = new SoftFocusEntity();

                    // ID
                    com.meitu.media.util.plist.Integer id = entityDict.getConfigurationInteger("DefocusId");
                    if (id != null) {
                        defocusEntity.mEffectId = id.getValue().intValue();
                    }

                    // 透明度
                    com.meitu.media.util.plist.Integer level = entityDict.getConfigurationInteger("DefocusLevel");
                    if (level != null) {
                        defocusEntity.mEffectIntensity = level.getValue().intValue();
                    }

                    // 效果最大值
                    com.meitu.media.util.plist.Integer maxLevel = entityDict.getConfigurationInteger("MaxLevel");
                    if (maxLevel != null) {
                        defocusEntity.mMaxIntensity = maxLevel.getValue().intValue();
                    }

                    // 效果最小值
                    com.meitu.media.util.plist.Integer minLevel = entityDict.getConfigurationInteger("MinLevel");
                    if (minLevel != null) {
                        defocusEntity.mMinIntensity = minLevel.getValue().intValue();
                    }

                    // 封面
                    com.meitu.media.util.plist.String previewRes = entityDict.getConfiguration("PreviewRes");
                    if (previewRes != null) {
                        defocusEntity.mPreviewRes = previewRes.getValue();
                    }

                    // 统计ID
                    com.meitu.media.util.plist.String staticsId = entityDict.getConfiguration("StaticsID");
                    if (staticsId != null) {
                        defocusEntity.mStaticsId = staticsId.getValue();
                    }

                    // 光斑值
                    final com.meitu.media.util.plist.Integer defValue = new com.meitu.media.util.plist.Integer();
                    defValue.setValue(5);
                    com.meitu.media.util.plist.Integer gamaLevel =
                        entityDict.getConfigurationIntegerWithDefault("gamma", defValue);
                    if (gamaLevel != null) {
                        defocusEntity.gamma = gamaLevel.getValue().intValue();
                    }
                    // 依赖模型
                    com.meitu.media.util.plist.String kernel = entityDict.getConfiguration("Kernel");
                    if (kernel != null) {
                        defocusEntity.Kernel = kernel.getValue();
                    }
                    defocusEntityList.add(defocusEntity);
                }
            }
        } catch (Exception e) {
            Debug.w(e);
        } finally {
            if (ins != null) {
                try {
                    ins.close();
                } catch (IOException ee) {
                    Debug.w(ee);
                }
            }
        }

        if (sEffectMap == null) {
            sEffectMap = new HashMap<>(4);
        }

        sEffectMap.put(assetsPath, defocusEntityList);

        return loadDefocusEntities(assetsPath);
    }
}
