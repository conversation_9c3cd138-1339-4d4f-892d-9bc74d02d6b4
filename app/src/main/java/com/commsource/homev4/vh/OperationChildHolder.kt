package com.commsource.homev4.vh

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.util.Log
import android.view.ViewGroup
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHomev4OperationChildBinding
import com.commsource.home.HomeLayoutUtils
import com.commsource.homev4.entity.HomeFuncEntity
import com.commsource.util.FileDownloader
import com.commsource.util.GlideProxy
import com.commsource.util.gone
import com.commsource.util.resColor
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder
import com.pixocial.videokit.PlaySource
import com.pixocial.videokit.decoder.OnVideoEventListener
import com.pixocial.videokit.decoder.PlayerEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class OperationChildHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<HomeFuncEntity.HomeFuncItem>(
        context,
        parent,
        R.layout.item_homev4_operation_child
    ) {

    private val binding by lazy {
        ItemHomev4OperationChildBinding.bind(itemView)
    }

    // 视频下载任务列表
    private val videoTasks = mutableListOf<String>()

    // 视频播放监听器
    private val videoListener = object : OnVideoEventListener {
        override fun onPlayerEventChange(state: Int) {
            when (state) {
                PlayerEvent.playerReady -> {
                    Log.d("OperationChildHolder", "Video player ready")
                }
                PlayerEvent.startRender -> {
                    Log.d("OperationChildHolder", "Video start render")
                }
                PlayerEvent.playerError -> {
                    Log.e("OperationChildHolder", "Video player error")
                }
            }
        }
    }

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<HomeFuncEntity.HomeFuncItem>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)

        if (item.entity.isVideoType()) {
            // 视频格式
            binding.videoContainer.visible()
            binding.ivThumb.gone()

            // 准备视频封面
            prepareVideoCover(item.entity)

            // 异步下载视频
            prepareVideo(item.entity)
        } else {
            // 图片格式
            binding.ivThumb.visible()
            binding.videoContainer.gone()
            GlideProxy.with(mContext)
                .load(item.entity?.icon)
                .placeHolder(R.color.White.resColor().toDrawable())
                .requestOptions(HomeLayoutUtils.OPTIONS_TOP_BANNER)
                .into(binding.ivThumb)
        }
    }

    /**
     * 准备视频封面
     */
    private fun prepareVideoCover(entity: HomeFuncEntity.HomeFuncItem) {
        val imageUrl = entity.icon
        binding.videoContainer.prepareVideoCover(
            coverPath = imageUrl,
            isAssets = false,
            placeHolder = R.color.White.resColor().toDrawable()
        )
    }

    /**
     * 异步准备视频
     */
    private fun prepareVideo(entity: HomeFuncEntity.HomeFuncItem) {
        entity.videoUrl?.let { url ->
            // 首先检查本地是否已有缓存
            val localVideoPath = FileDownloader.getLocalVideoPathOrNull(url, "OperationVideo", null)
            if (localVideoPath?.lowercase()?.endsWith(".mp4") == true) {
                Log.d("OperationChildHolder", "Using cached video: $localVideoPath")
                attachVideo(localVideoPath)
                return
            }

            // 如果没有缓存，则下载
            val key = url
            if (!videoTasks.contains(key)) {
                videoTasks.add(key)
                (mContext as? FragmentActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
                    try {
                        val videoPath = FileDownloader.downloadVideoFile(url, "OperationVideo")
                        withContext(Dispatchers.Main) {
                            videoTasks.remove(key)
                            if (videoPath?.lowercase()?.endsWith(".mp4") == true) {
                                Log.d("OperationChildHolder", "Video downloaded successfully: $videoPath")
                                attachVideo(videoPath)
                            } else {
                                Log.d("OperationChildHolder", "Video path invalid: $videoPath")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("OperationChildHolder", "Video download failed", e)
                        withContext(Dispatchers.Main) {
                            videoTasks.remove(key)
                        }
                    }
                }
            }
        }
    }

    /**
     * 激活视频播放
     */
    private fun attachVideo(videoPath: String) {
        binding.videoContainer.extraVideoListener = videoListener
        binding.videoContainer.play(
            playSource = PlaySource.LocalPlaySource(videoPath),
            isLazy = true,
            isLoop = true,
            volume = 0f,
            singleMode = false
        )
    }

    /**
     * 开始视频播放
     */
    fun startVideo() {
        if (binding.videoContainer.isAttachVideo()) {
            binding.videoContainer.requestStart()
        }
    }

    /**
     * 暂停视频播放
     */
    private fun pauseVideo() {
        if (binding.videoContainer.isAttachVideo()) {
            binding.videoContainer.seekTo(0)
            binding.videoContainer.requestPause()
        }
    }

    /**
     * 分离视频
     */
    private fun detachVideo() {
        binding.videoContainer.detach()
    }

    override fun onViewDetached() {
        super.onViewDetached()
        pauseVideo()
    }

    override fun onViewRecycler() {
        super.onViewRecycler()
        binding.videoContainer.extraVideoListener = null
        detachVideo()
        binding.videoContainer.gone()
        binding.ivThumb.gone()
    }
}

/**
 * 判断是否为视频类型
 */
private fun HomeFuncEntity.HomeFuncItem.isVideoType(): Boolean {
    return videoUrl?.lowercase()?.endsWith(".mp4") == true
}