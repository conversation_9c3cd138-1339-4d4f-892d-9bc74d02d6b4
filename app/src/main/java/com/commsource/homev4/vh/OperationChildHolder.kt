package com.commsource.homev4.vh

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHomev4OperationChildBinding
import com.commsource.home.HomeLayoutUtils
import com.commsource.homev4.entity.HomeFuncEntity
import com.commsource.util.GlideProxy
import com.commsource.util.resColor
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

// FIXME: @wrf 参考 ItemThumbnailHolder
class OperationChildHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<HomeFuncEntity.HomeFuncItem>(
        context,
        parent,
        R.layout.item_homev4_operation_child
    ) {

    private val binding by lazy {
        ItemHomev4OperationChildBinding.bind(itemView)
    }

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<HomeFuncEntity.HomeFuncItem>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)

        binding.ivThumb.visible()
        GlideProxy.with(mContext)
            .load(item.entity?.icon)
            .placeHolder(ColorDrawable(R.color.White.resColor()))
            .requestOptions(HomeLayoutUtils.OPTIONS_TOP_BANNER)
            .into(binding.ivThumb)
    }
}