package com.commsource.homev4.vh

import android.content.Context
import android.view.ViewGroup
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHomev4OperationChildBinding
import com.commsource.home.HomeLayoutUtils
import com.commsource.homev4.entity.HomeFuncEntity
import com.commsource.util.GlideProxy
import com.commsource.util.VideoDownloadManager
import com.commsource.util.gone
import com.commsource.util.logV
import com.commsource.util.resColor
import com.commsource.util.setSize
import com.commsource.util.toAspectRatio
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder
import com.pixocial.androidx.core.extension.dp
import com.pixocial.videokit.PlaySource

class OperationChildHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<HomeFuncEntity.HomeFuncItem>(
        context,
        parent,
        R.layout.item_homev4_operation_child
    ) {

    private val binding by lazy {
        ItemHomev4OperationChildBinding.bind(itemView)
    }

    private var videoPath: String? = null

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<HomeFuncEntity.HomeFuncItem>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        videoPath = null
//        "$this onBindViewHolder".logV("AAAAA")
        // 设置Item 的宽高
        val ratio = item.entity?.ratio?.toAspectRatio() ?: 1f
        val width = (152.dp * ratio).toInt()
        val height = 152.dp
        binding.cvThumbnail.setSize(width = width, height = height)
        binding.videoContainer.gone()

        // 显示图片或者视频缩略图
        GlideProxy.with(mContext)
            .load(item.entity?.icon)
            .placeHolder(R.color.White.resColor().toDrawable())
            .requestOptions(HomeLayoutUtils.OPTIONS_TOP_BANNER)
            .into(binding.ivThumb)
        if (item.entity.isVideoType()) {
            // 视频格式，下载视频然后播放
            prepareVideo(item.entity)
        }
    }

    /**
     * 异步准备视频
     */
    private fun prepareVideo(entity: HomeFuncEntity.HomeFuncItem) {
        VideoDownloadManager.download(
            scope = (mContext as? FragmentActivity)?.lifecycleScope ?: return,
            url = entity.videoUrl ?: return,
            moduleName = "OperationVideo",
            md5 = "",
            onSuccess = {
                videoPath = it
                attachVideo(it)
            },
            onError = {}
        )
    }

    /**
     * 激活视频播放
     */
    private fun attachVideo(videoPath: String) {
        binding.videoContainer.visible()
        "${binding.videoContainer.hashCode()} start play".logV("AAAAA")
        binding.videoContainer.play(
            playSource = PlaySource.LocalPlaySource(videoPath),
            isLazy = true,
            isLoop = true,
            volume = 0f,
            singleMode = false
        )
    }

    override fun onViewDetached() {
        super.onViewDetached()
//        "$this onViewDetached".logV("AAAAA")
        item?.entity?.videoUrl?.let {
            VideoDownloadManager.cancel(it)
        }
        binding.videoContainer.gone()
        if (binding.videoContainer.isAttachVideo()) {
            binding.videoContainer.requestPause()
        }
    }

    override fun onAttach() {
        super.onAttach()
//        "$this onAttach".logV("AAAAA")
        videoPath?.let {
            attachVideo(it)
        }
    }

    override fun onViewRecycler() {
        super.onViewRecycler()
        if (binding.videoContainer.isAttachVideo()) {
            binding.videoContainer.detach()
        }
    }
}

/**
 * 判断是否为视频类型
 */
private fun HomeFuncEntity.HomeFuncItem.isVideoType(): Boolean {
    return videoUrl?.lowercase()?.endsWith(".mp4") == true
}