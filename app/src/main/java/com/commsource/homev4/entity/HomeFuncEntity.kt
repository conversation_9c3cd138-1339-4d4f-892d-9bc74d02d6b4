package com.commsource.homev4.entity

import com.google.gson.annotations.SerializedName

class HomeFuncEntity {
    @SerializedName("id")
    val id: String? = null

    @SerializedName("func_bar_id")
    val funcBarId: String? = null

    @SerializedName("func_bar_type")
    var funcBarType: Int = 0

    @SerializedName("name")
    val name: String? = null

    @SerializedName("min_version")
    val minVersion: String? = null

    @SerializedName("max_version")
    val maxVersion: String? = null

    @SerializedName("func_list")
    val funcItems: List<HomeFuncItem>? = null

    class HomeFuncItem {
        @SerializedName("id")
        val id: String? = null

        @SerializedName("func_id")
        val funcId: String? = null

        @SerializedName("name")
        val name: String? = null

        @SerializedName("icon")
        val icon: String? = null

        @SerializedName("video_url")
        val videoUrl: String? = null

        @SerializedName("jump_url")
        val jumpUrl: String? = null

        @SerializedName("start_time")
        val startTime: Long = 0L

        @SerializedName("end_time")
        val endTime: Long = 0L
    }
}