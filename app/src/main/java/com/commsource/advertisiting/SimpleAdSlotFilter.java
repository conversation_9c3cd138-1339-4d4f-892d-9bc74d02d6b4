package com.commsource.advertisiting;

import com.commsource.beautyplus.R;
import com.commsource.config.SubscribeConfig;
import com.commsource.util.ResourcesUtils;
import com.meitu.hwbusinesskit.core.bean.AdSlot;
import com.meitu.hwbusinesskit.core.bean.Platform;
import com.meitu.hwbusinesskit.core.utils.AdSlotFilter;
import com.meitu.hwbusinesskit.core.utils.TestLog;
import com.meitu.library.util.device.DeviceUtils;

import android.content.Context;
import android.os.Build;

/**
 * Create by vinvince On 2019-06-21 15:32
 */
public class SimpleAdSlotFilter implements AdSlotFilter {

    private Context mContext;
    private final String mWebViewVersion;

    public SimpleAdSlotFilter(Context context) {
        this.mContext = context;
        mWebViewVersion = DeviceUtils.getWebViewVersion(context);
    }

    @Override
    public boolean filter(AdSlot adSlot) {
        return false;
    }

    @Override
    public boolean filter(String adSlotId, Platform platform) {
        if (mContext == null) {
            return false;
        }
        // 4、5、6系统屏蔽Admob激励视频和插屏广告。7.0、7.1系统webview版本号为空，同样屏蔽。
        if (Build.VERSION.SDK_INT <= 23 || isEmptyWebViewVersion()) {
            if ((Platform.PLATFORM_ADMOB.equals(platform.getPlatform())
                    || Platform.PLATFORM_ADX.equals(platform.getPlatform())
                    || Platform.PLATFORM_DFP.equals(platform.getPlatform()))
                    && (AdSlot.TYPE_INTERSTITIAL.equals(platform.getType())
                    || AdSlot.TYPE_REWARDED_VIDEO.equals(platform.getType()))) {
                TestLog.log("filter_internal " + adSlotId + ": " + platform.getPlatform() + "," + platform.getType());
                return true;
            }
        }

        // 订阅用户 开屏广告位 过滤掉非MT广告平台的
        if (SubscribeConfig.isSubValid() && ResourcesUtils.getString(R.string.ad_slot_launch_ad).equals(adSlotId) && !Platform.PLATFORM_MT.equals(platform.getPlatform())) {
            return true;
        }

        return filterIfNeed(platform);
    }

    private boolean filterIfNeed(Platform platform) {
        return false;

    }

    private boolean isEmptyWebViewVersion() {
        return (Build.VERSION.SDK_INT == 24 || Build.VERSION.SDK_INT == 25)
                && ("empty".equals(mWebViewVersion) || "0.0.0.1".equals(mWebViewVersion));
    }
}
