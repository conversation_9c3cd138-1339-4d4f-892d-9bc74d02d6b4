package com.commsource.advertisiting.video;

import android.content.Context;
import android.text.TextUtils;
import com.commsource.home.entity.AdContent;
import com.commsource.statistics.MTAnalyticsAgent;
import com.commsource.statistics.MTFirebaseAnalyticsAgent;
import com.commsource.statistics.constant.MTFirebaseConstant;
import com.commsource.util.AppTools;
import com.meitu.common.AppContext;
import com.meitu.hwbusinesskit.core.bean.AdData;
import com.meitu.hwbusinesskit.core.bean.Platform;

import java.util.HashMap;

/*********************************************
 * Author: lbh 2017/1/22
 * ********************************************
 * Version: 版本
 * Author: lbh
 * Changes: 更新点
 * ********************************************
 * <AUTHOR>
 */
public class BeautyPlusAdvertStatistics {
    private static final String AD_SLOT_ID_AD_BANNER_ALBUM = "ad_banner_album";
    private static final String AD_SLOT_ID_AD_BANNER_SELFIESAVE = "ad_banner_selfiesave";
    private static final String AD_SLOT_ID_BUSINESS_LAUNCH_AD = "ad_app_open";

    /**
     * 使用激励视频滤镜保存照片/视频埋点
     * @param filterThemeId 滤镜主题ID
     * @param filterId 滤镜ID
     */
    public static void logRvFilterSaveStatistics(int filterThemeId, int filterId) {

    }

    /**
     * 激励视频滤镜埋点
     * @param eventName 事件名
     * @param filterThemeId 滤镜主题ID
     * @param source 来源（即从哪一步跳转到激励视频滤镜）
     */
    public static void logRvFilterStatistics(String eventName, int filterThemeId, String source) {
        if (TextUtils.isEmpty(eventName)) {
            return;
        }
        HashMap<String, String> hashMap = new HashMap<>(4);
        hashMap.put(MTFirebaseConstant.KEY_ID, String.valueOf(filterThemeId));
        if (!TextUtils.isEmpty(source)) {
            hashMap.put(MTFirebaseConstant.KEY_SOURCE, source);
        }
        MTFirebaseAnalyticsAgent.logEvent(AppContext.getContext(), eventName, hashMap);
        MTAnalyticsAgent.logEvent(eventName, hashMap);
    }

    /**
     * 统计广告展示
     */
    public static void logAdShowStatistics(String eventName, AdData adData, String additionalKey, String value) {
        if (TextUtils.isEmpty(eventName)) {
            return;
        }

        final String platformName = getStatisticsPlatformName(adData.getPlatform());
        final String tag = adData.getTag();
        final String mediationPlatform = adData.getSubPlatform();
        final String adType = adData.getAdType();
        final Context context = AppContext.getContext();

        HashMap<String, String> hashMap = new HashMap<>(4);
        hashMap.put("channel", AppTools.getChannelName(context));
        hashMap.put("platform", platformName);
        if (!TextUtils.isEmpty(adType)) {
            hashMap.put("source", adType);
        }
        if (!TextUtils.isEmpty(tag)) {
            hashMap.put("tag", tag);
        }
        if (!TextUtils.isEmpty(adData.getAdId())) {
            hashMap.put("ID", adData.getAdId());
        }
        if (!TextUtils.isEmpty(mediationPlatform)) {
            hashMap.put("subPlatform", mediationPlatform);
        }

        if (!TextUtils.isEmpty(additionalKey) && !TextUtils.isEmpty(value)) {
            hashMap.put(additionalKey, value);
        }
        MTAnalyticsAgent.logEvent(eventName, hashMap);
        MTFirebaseAnalyticsAgent.logEvent(context, eventName, hashMap);
    }

    /**
     * 统计广告展示
     */
    public static void logHomeAdEvent(String eventName, int position, AdContent adContent) {
        if (TextUtils.isEmpty(eventName) || adContent == null || adContent.getAdData()==null) {
            return;
        }

        final String platformName = getStatisticsPlatformName( adContent.getAdData().getPlatform());
        final String adType = adContent.getAdData().getAdType();
        final Context context = AppContext.getContext();

        HashMap<String, String> hashMap = new HashMap<>(8);
        hashMap.put("platform", platformName);
        if (!TextUtils.isEmpty(adType)) {
            hashMap.put("type", adType);
        }
        if (!TextUtils.isEmpty(adContent.getAdSlotId())) {
            hashMap.put("slotID", adContent.getAdSlotId());
        }
        if (position != -1) {
            hashMap.put("pos", position + "");
        }

        MTAnalyticsAgent.logEvent(eventName, hashMap);
        MTFirebaseAnalyticsAgent.logEvent(context, eventName, hashMap);
    }

    /**
     * 统计广告点击
     */
    public static void logAdClickStatistics(String eventName, AdData adData) {
        if (TextUtils.isEmpty(eventName)) {
            return;
        }

        final String platformName = getStatisticsPlatformName(adData.getPlatform());
        final String tag = adData.getTag();
        final String adType = adData.getAdType();
        final String mediationPlatform = adData.getSubPlatform();
        final Context context = AppContext.getContext();

        HashMap<String, String> hashMap = new HashMap<>(4);
        hashMap.put("channel", AppTools.getChannelName(context));
        hashMap.put("platform", platformName);
        if (!TextUtils.isEmpty(adType)) {
            hashMap.put("source", adType);
        }
        if (!TextUtils.isEmpty(tag)) {
            hashMap.put("tag", tag);
        }
        if (!TextUtils.isEmpty(adData.getAdId())) {
            hashMap.put("ID", adData.getAdId());
        }
        if (!TextUtils.isEmpty(mediationPlatform)) {
            hashMap.put("subPlatform", mediationPlatform);
        }

        MTAnalyticsAgent.logEvent(eventName, hashMap);
        MTFirebaseAnalyticsAgent.logEvent(context, eventName, hashMap);
    }

    /**
     * 获取统计平台名
     *
     * @param platform 广告平台
     * @return 返回广告名
     */
    public static String getStatisticsPlatformName(String platform) {
        if (TextUtils.isEmpty(platform)) {
            return BeautyPlusAdvertConstant.AD_STATISTICS_PLATFORM_NAME_UNKNOWN;
        }
        if (platform.equalsIgnoreCase(Platform.PLATFORM_ADMOB)) {
            return BeautyPlusAdvertConstant.AD_STATISTICS_PLATFORM_NAME_ADMOB;
        } else if (platform.equalsIgnoreCase(Platform.PLATFORM_ADX)) {
            return BeautyPlusAdvertConstant.AD_STATISTICS_PLATFORM_NAME_ADX;
        } else if (platform.equalsIgnoreCase(Platform.PLATFORM_MT)) {
            return BeautyPlusAdvertConstant.AD_STATISTICS_PLATFORM_NAME_MT;
        } else if (platform.equalsIgnoreCase(Platform.PLATFORM_DFP)) {
            return BeautyPlusAdvertConstant.AD_STATISTICS_PLATFORM_NAME_DFP;
        } else {
            return platform;
        }
    }
}
