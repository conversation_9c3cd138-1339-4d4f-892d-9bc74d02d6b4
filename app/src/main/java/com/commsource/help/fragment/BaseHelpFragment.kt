package com.commsource.help.fragment

import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.camera.xcamera.cover.bottomFunction.BottomInInterpolator
import com.meitu.library.util.device.DeviceUtils

/**
 * author: admin
 * Date: 2022/10/26
 * Des:
 */
open class BaseHelpFragment : BaseFragment() {


    open fun animateIn(action: () -> Unit) {
        view?.run {
            animate().setListener(null).cancel()
            translationX = DeviceUtils.getScreenWidth().toFloat()
            animate().translationX(0f)
                .setInterpolator(BottomInInterpolator())
                .setDuration(350)
                .withEndAction {
                    action.invoke()
                }
                .start()
        }
    }

    open fun animateOut(action: () -> Unit) {
        view?.run {
            animate().setListener(null).cancel()
            translationX = 0f
            animate().translationX(DeviceUtils.getScreenWidth().toFloat())
                .setInterpolator(BottomInInterpolator())
                .setDuration(350)
                .withEndAction {
                    action.invoke()
                }
                .start()
        }
    }

    fun animateTransHide() {
        view?.run {
            animate().setListener(null).cancel()
            translationX = 0f
            animate().translationX(-DeviceUtils.getScreenWidth().toFloat())
                .setInterpolator(BottomInInterpolator())
                .setDuration(350)
                .start()
        }
    }

    fun animateAlphaHide() {
        view?.run {
            animate().setListener(null).cancel()
            alpha = 1f
            animate().alpha(0f)
                .setInterpolator(BottomInInterpolator())
                .setDuration(350)
                .start()
        }
    }

    open fun animateShow() {
        view?.run {
            animate().setListener(null).cancel()
            animate().translationX(0f)
                .alpha(1f)
                .setInterpolator(BottomInInterpolator())
                .setDuration(350)
                .start()
        }
    }

    open fun onBackPressed(): Boolean {
        return false
    }
}