package com.commsource.help.viewholder

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.util.resColor
import com.commsource.util.setRTL

class HelpArticleItemDecoration(
    val divideLineMarginLeft: Int = 0,
    val divideLineMarginRight: Int = 0,
    val showLastDivideLine: Boolean = false
) : RecyclerView.ItemDecoration() {

    private val lineHeight = 1f

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val pos = parent.getChildAdapterPosition(view)
        var bottom = 0
        if (pos != parent.adapter?.itemCount) {
            bottom = lineHeight.toInt()
        }
        outRect.setRTL(0, 0, 0, bottom)
    }

    private val bounds = Rect()

    private val paint = Paint().apply {
        color = (R.color.Gray_Dashline).resColor()
        style = Paint.Style.FILL_AND_STROKE
        strokeWidth = lineHeight
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)
        c.save()
        val left: Float = divideLineMarginLeft.toFloat()
        val right: Float = parent.width.toFloat() - divideLineMarginRight
        val childCount = if (showLastDivideLine) {
            parent.childCount
        } else {
            parent.childCount - 1
        }
        for (i in 0 until childCount) {
            val child = parent.getChildAt(i)
            parent.getDecoratedBoundsWithMargins(child, bounds)
            val bottom: Float = bounds.bottom + child.translationY
            val top: Float = bottom - lineHeight
            c.drawLine(left, top, right, top, paint)
        }
        c.restore()
    }
}