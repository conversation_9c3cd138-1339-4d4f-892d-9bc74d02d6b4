package com.commsource.help

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.BeautyPlusApplication
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ActivityTicketBinding
import com.commsource.util.dp
import com.commsource.util.setSize
import com.commsource.util.text
import com.commsource.widget.dialog.common.ADialog
import com.commsource.widget.title.ImageAction

class TicketActivity : BaseActivity() {
    private val binding by lazy { ActivityTicketBinding.inflate(layoutInflater) }

    private val helpModel by lazy {
        ViewModelProvider(
            application as BeautyPlusApplication,
            ViewModelProvider.AndroidViewModelFactory.getInstance(application)
        )[HelpCenterViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        binding.xtb.apply {
            setTitleBg(resources.getColor(R.color.transparent))
            val backAction = object : ImageAction(R.drawable.edit_icon_navbar_back) {
                override fun onActionClick(view: View) {
                    onBackPressed()
                }
            }
            addAction(backAction)
            backAction.mIv?.setSize(20.dp, 20.dp)

            val closeAction = object : ImageAction(R.drawable.ic_close) {
                override fun onActionClick(view: View) {
                    showExitPromptDialog(true)
                }
            }
            addAction(closeAction, Gravity.END or Gravity.CENTER_VERTICAL)
            closeAction.mIv?.setSize(20.dp, 20.dp)
        }

        val fragment = TicketFragment().apply {
            arguments = Bundle().apply {
                putString(
                    TicketFragment.KEY_TICKET,
                    intent.getStringExtra(TicketFragment.KEY_TICKET)
                )
            }
        }

        supportFragmentManager.beginTransaction()
            .add(R.id.fragment_container, fragment)
            .commit()

        initViewModel()
    }

    private fun initViewModel() {
        helpModel.backToSettingEvent.observe(this) {
            // 和点击X通用的逻辑，就不新增字段了
            setResult(RESULT_OK, Intent().apply {
                putExtra(HelpActivity.KEY_BACK_FROM, HelpActivity.BACK_FROM_CLOSE_BTN)
            })
            finish()
        }
        helpModel.backHomeEvent.observe(this) {
            setResult(RESULT_OK, Intent().apply {
                putExtra(HelpActivity.KEY_BACK_FROM, HelpActivity.BACK_FROM_TICKET_EXPLORE)
            })
            finish()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        showExitPromptDialog(false)
    }

    private fun showExitPromptDialog(isFromCloseBtn: Boolean) {
        ADialog.showWithParam(R.string.v79000_E_6.text(), R.string.exit.text(), {
            it.dismiss()
            if (isFromCloseBtn) {
                setResult(RESULT_OK, Intent().apply {
                    putExtra(HelpActivity.KEY_BACK_FROM, HelpActivity.BACK_FROM_CLOSE_BTN)
                })
            }
            finish()
        }, R.string.dialog_no.text())
    }

}