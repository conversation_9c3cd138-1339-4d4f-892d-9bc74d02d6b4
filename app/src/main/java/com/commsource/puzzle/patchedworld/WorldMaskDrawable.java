package com.commsource.puzzle.patchedworld;

import java.util.LinkedList;

import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * <NAME_EMAIL> on 2017/7/8.
 *
 * <AUTHOR>
 */

public class WorldMaskDrawable extends Drawable {
    @SuppressWarnings("unused")
    private static final String TAG = WorldMaskDrawable.class.getSimpleName();

    /** 绑定的PatchedWorld对象 */
    protected PatchedWorld mPatchedWorld;
    /** 用于指示部件可改变边的Drawable */
    public IAlterableRectDrawable mPatchesAlterableIndicatorDrawable;

    public WorldMaskDrawable(@NonNull PatchedWorld patchedWorld) {
        mPatchedWorld = patchedWorld;
        mPatchesAlterableIndicatorDrawable = new SideOverStrikingDrawable();
    }

    @Override
    public void draw(@NonNull Canvas canvas) {
        // 根部件
        VisualPatch rootPatch = mPatchedWorld.getRootPatch();
        // 根部件在画布世界中的边界
        Rect rootPatchBoundInCanvas = new Rect();

        float scaleRootPatchToFitCanvas = 1.0f;
        int worldExtendMode = mPatchedWorld.getWorldExtendMode();
        // 居中适配根部件到画布中
        if (worldExtendMode == PatchedWorld.WORLD_IMMUTABLE || worldExtendMode == PatchedWorld.WORLD_XY_EXTENSIBLE) {
            scaleRootPatchToFitCanvas =
                rootPatch.centeredMatchInTargetWorld(canvas.getWidth(), canvas.getHeight(), rootPatchBoundInCanvas);
        }
        // 适配宽度到画布中
        else if (worldExtendMode == PatchedWorld.WORLD_Y_EXTENSIBLE) {
            scaleRootPatchToFitCanvas =
                PatchedWorld.calculateAndScaleRectToMatchWidthInRect(rootPatch.getIntrinsicWidth(), canvas.getWidth(),
                    mPatchedWorld.getWorldBottomPatch(), rootPatchBoundInCanvas);
        }

        synchronized (mPatchedWorld.getLayeredPatches()) {
            // 按照层级顺序绘制各部件
            LinkedList<VisualPatch> patchesToDraw = mPatchedWorld.getLayeredPatches();
            for (VisualPatch patch : patchesToDraw) {
                if (patch != null) {
                    Rect patchBoundInCanvas =
                        patch.resolveBoundInOtherWorld(rootPatchBoundInCanvas, scaleRootPatchToFitCanvas);
                    // 部件边界被选中且没有被拖拽绘制选中框
                    if (patch.isSelected() && !patch.isDragging()) {
                        // 边界可调节时优先绘制边界可调节指示（覆盖轮廓指示）
                        if (patch.isBoundaryAlterable()) {
                            // 优先选择各部件自行制定的可调节指示drawable, 如果未指定，选择整个World统一指定的可调节指示drawable
                            IAlterableRectDrawable alterableIndicatorDrawable =
                                patch.getAlterableSidesIndicatorDrawable() != null
                                    ? patch.getAlterableSidesIndicatorDrawable() : mPatchesAlterableIndicatorDrawable;
                            if (alterableIndicatorDrawable != null) {
                                alterableIndicatorDrawable.drawAlterableIndicator(canvas, patchBoundInCanvas,
                                    patch.neighborDistribution[0], patch.neighborDistribution[1],
                                    patch.neighborDistribution[2], patch.neighborDistribution[3]);
                            }
                        }
                        // 边界不可调节时绘制普通的轮廓线指示
                        else {
                            OutlineDrawable outlineDrawable = patch.getPatchOutlineDrawable();
                            if (outlineDrawable != null && patch.willDrawOutlineWhenSelected()) {
                                outlineDrawable.draw(canvas, patchBoundInCanvas);
                            }
                        }
                    }

                    // 侵入指示可见时绘制
                    OutlineDrawable invadedDrawable = patch.getPatchInvadedDrawable();
                    if (invadedDrawable != null && invadedDrawable.isVisible()) {
                        invadedDrawable.draw(canvas, patchBoundInCanvas);
                    }
                }
            }
        }
    }

    @Override
    public void setAlpha(@IntRange(from = 0, to = 255) int alpha) {
    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {
    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }
}
