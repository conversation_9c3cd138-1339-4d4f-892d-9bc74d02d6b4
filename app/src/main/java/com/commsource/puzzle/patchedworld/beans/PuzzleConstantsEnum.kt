package com.commsource.puzzle.patchedworld.beans

import com.commsource.beautyplus.R

enum class PuzzleConstantsEnum(var rationBean: RationBean,
                               //按比例计算单位像素
                               val ratioPx: Int,
                               //比例的组合模式
                               var rationPairFraction: Pair<Int,Int>) {

    RATIO_1_1(RationBean("1:1", R.string.if_ee_crop_1_1, 1500, 1500), 1500,Pair(1,1)),
    RATIO_3_4(RationBean("3:4", R.string.if_ee_crop_3_4, 1200, 1600), 400,Pair(3,4)),
    RATIO_4_5(RationBean("4:5", R.string.if_ee_crop_4_5, 1200, 1500), 300,Pair(4,5)),
    RATIO_9_16(RationBean("9:16", R.string.if_ee_crop_9_16, 900, 1600), 100,Pair(9,16)),
    RATIO_4_3(RationBean("4:3", R.string.if_ee_crop_4_3, 1600, 1200), 400,Pair(4,3)),
    RATION_5_4(<PERSON><PERSON><PERSON><PERSON>("5:4", R.string.if_ee_crop_5_4, 1500, 1200), 300,Pair(5,4)),
    RATIO_16_9(RationBean("16:9", R.string.if_ee_crop_16_9, 1600, 900), 100,Pair(16,9)),

    //经尺寸模式计算后换算出的比例对象
    RATIO_SIZE(RationBean("", R.string.if_ee_crop_1_1, 1500, 1500), 1500,Pair(1,1));


    fun getAspectRatio(): Float {
        return if (rationBean.ratioWidth.toFloat() == 0f || rationBean.ratioHeight.toFloat() == 0f) {
            1f
        } else rationBean.ratioWidth.toFloat() / rationBean.ratioHeight.toFloat()
    }

    companion object {

        val puzzleRatioList = arrayListOf(
                RATIO_1_1,
                RATIO_3_4,
                RATIO_4_5,
                RATIO_9_16,
                RATIO_4_3,
                RATION_5_4,
                RATIO_16_9
        )

        fun getFinalStandardRatioList(): List<RationBean> = puzzleRatioList.map {
            it.rationBean
        }

    }


}