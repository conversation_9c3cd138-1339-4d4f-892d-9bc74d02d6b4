package com.commsource.puzzle.patchedworld.viewmodel;

import android.app.Application;
import android.content.Intent;
import android.graphics.Bitmap;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.commsource.ad.ADCache;
import com.commsource.beautyplus.base.BaseVm;
import com.commsource.beautyplus.router.UriConstant;
import com.commsource.beautyplus.util.PathUtil;
import com.commsource.config.ApplicationConfig;
import com.commsource.puzzle.patchedworld.beans.PuzzleBean;
import com.commsource.puzzle.patchedworld.beans.PuzzleConstantsEnum;
import com.commsource.puzzle.patchedworld.beans.RationBean;
import com.commsource.puzzle.patchedworld.beans.SizeBeanEnum;
import com.commsource.puzzle.patchedworld.beans.SizeCountryEnum;
import com.commsource.puzzle.patchedworld.factor.PuzzleEntityFactory;
import com.commsource.puzzle.patchedworld.frame.PuzzleActivity;
import com.commsource.puzzle.patchedworld.imageware.ImagePipelineWarehouse;
import com.commsource.share.UserMediaInfo;
import com.commsource.statistics.MTAnalyticsAgent;
import com.commsource.statistics.constant.MTAnalyticsConstant;
import com.commsource.studio.ImageStudioViewModel;
import com.commsource.util.BPImageSaveUtils;
import com.commsource.util.BPLocationUtils;
import com.commsource.util.MediaSaver;
import com.commsource.util.SDCardUtil;
import com.commsource.util.ThreadExecutor;
import com.commsource.util.thread.AbstractNamedRunnable;
import com.meitu.common.AppContext;
import com.meitu.library.util.bitmap.BitmapUtils;
import com.meitu.library.util.device.DeviceUtils;
import com.pixocial.library.albumkit.media.MediaInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Pair;

/**
 * Created by meitu on 2018/12/18.
 */

public class PuzzleViewModel extends BaseVm {

    private int FROM_CAPTURE_CONFIRM = ImageStudioViewModel.FROM_CAPTURE_CONFIRM;

    private MutableLiveData<List<MediaInfo>> mImageInfos;

    private MutableLiveData<Boolean> mImageErrorTip;

    private MutableLiveData<ImagePipelineWarehouse.WarehouseConfig> mImageConfig;

    private MutableLiveData<List<PuzzleBean>> mPuzzleList;

    private MutableLiveData<PuzzleBean> mCurrentPuzzle;

    private MutableLiveData<SizeBeanEnum> mCurrentPuzzleSize;

    private MutableLiveData<Boolean> sizeNeedRefreshCanvasSave;

    private MutableLiveData<RationBean> mCurrentPuzzleRatio;

    private MutableLiveData<List<RationBean>> mInitRatioUI;

    private MutableLiveData<List<SizeBeanEnum>> mInitSizeUI;

    private MutableLiveData<List<Pair<Integer, Bitmap>>> imagePipeWareInitOkEvent = new MutableLiveData<>();

    private MutableLiveData<List<Pair<Integer, Bitmap>>> replacePhotoEvent = new MutableLiveData<>();

    private MutableLiveData<Boolean> saveEvent = new MutableLiveData<>();

    private MutableLiveData<Boolean> memoryNotEnoughEvent = new MutableLiveData<>();

    private MutableLiveData<UserMediaInfo> saveResultEvent = new MutableLiveData<>();

    private MutableLiveData<Boolean> loadingEvent = new MutableLiveData<>();

    private MutableLiveData<Integer> currentPuzzleConfigIndex = new MutableLiveData<>();

    private boolean isImageWarePrepared = false;

    private String savedStitchImagePath;

    private int mImageNumber;

    private PuzzleEntityFactory mPuzzleEntityFactory;


    private boolean isDefaultSelectSrtip = false;

    private String homeFuncID;

    private int puzzleFrom;

    public PuzzleViewModel(@NonNull Application application) {
        super(application);
    }


    public MutableLiveData<Boolean> getSaveEvent() {
        return saveEvent;
    }

    public MutableLiveData<Boolean> getMemoryNotEnoughEvent() {
        return memoryNotEnoughEvent;
    }

    public MutableLiveData<Integer> getCurrentPuzzleConfigIndex() {
        return currentPuzzleConfigIndex;
    }

    public String getHomeFuncID() {
        return homeFuncID;
    }

    public void setHomeFuncID(String homeFuncID) {
        this.homeFuncID = homeFuncID;
    }

    public MutableLiveData<List<Pair<Integer, Bitmap>>> getReplacePhotoEvent() {
        return replacePhotoEvent;
    }

    public MutableLiveData<Boolean> getLoadingEvent() {
        return loadingEvent;
    }

    public MutableLiveData<List<Pair<Integer, Bitmap>>> getImagePipeWareInitOkEvent() {
        return imagePipeWareInitOkEvent;
    }

    public MutableLiveData<UserMediaInfo> getSaveResultEvent() {
        return saveResultEvent;
    }

    public boolean isDefaultSelectSrtip() {
        return isDefaultSelectSrtip;
    }

    public boolean isFromCamera() {
        return FROM_CAPTURE_CONFIRM == puzzleFrom;
    }

    public void initData(Intent intent) {
        List<MediaInfo> caImageInfoList =
                (List<MediaInfo>) intent.getSerializableExtra(PuzzleActivity.KEY_PUZZLE_DATA);
        isDefaultSelectSrtip = UriConstant.PATH_F_PICSRTIP.equals(intent.getStringExtra(PuzzleActivity.KEY_PUZZLE_MODE));

        puzzleFrom = intent.getIntExtra(PuzzleActivity.KEY_PUZZLE_FROM, -1);

        mPuzzleEntityFactory = new PuzzleEntityFactory();
        if (caImageInfoList != null && !caImageInfoList.isEmpty()) {
            getImageInfos().setValue(caImageInfoList);
            mImageNumber = caImageInfoList.size();
            // 初始化分类数据
            initPuzzleRatioData();
            initPuzzleSizeData();
            // 初始化素材
            List<PuzzleBean> mPuzzleList = mPuzzleEntityFactory.createPuzzleMaterials(mImageNumber);
            getPuzzleList().setValue(mPuzzleList);
            // 初始化图片库
            initImageWareData();
        }

    }

    public MutableLiveData<List<MediaInfo>> getImageInfos() {
        if (mImageInfos == null) {
            mImageInfos = new MutableLiveData<>();
        }
        return mImageInfos;
    }

    /**
     * 初始化比例UI数据
     */
    public void initPuzzleRatioData() {
        List<RationBean> puzzleRatioBeans = new ArrayList<>();
        puzzleRatioBeans.addAll(PuzzleConstantsEnum.Companion.getFinalStandardRatioList());
        getInitRatioUI().postValue(puzzleRatioBeans);
    }

    /**
     * 初始化尺寸数据，需要根据ip地址/分国家区分
     */
    public void initPuzzleSizeData() {
        List<SizeBeanEnum> puzzleRatioBeans = null;
        //美国地区
        if (BPLocationUtils.isUS(AppContext.context)) {
            puzzleRatioBeans = SizeCountryEnum.USSizeData.getSizeEnums();
        } else if (BPLocationUtils.isJapan(AppContext.context)) {
            //日本地区
            puzzleRatioBeans = SizeCountryEnum.JapanSizeData.getSizeEnums();
        } else if (BPLocationUtils.isEuropeanStandardAreas(AppContext.context)) {
            //欧洲地区
            puzzleRatioBeans = SizeCountryEnum.EuropeanSizeData.getSizeEnums();
        } else {
            //默认为美国
            puzzleRatioBeans = SizeCountryEnum.USSizeData.getSizeEnums();
        }
        getInitSizeUI().postValue(puzzleRatioBeans);

    }


    /**
     * 初始化图片仓库
     */
    public void initImageWareData() {
        List<MediaInfo> imageInfos = getImageInfos().getValue();
        if (imageInfos != null && !imageInfos.isEmpty()) {
            final ImagePipelineWarehouse.WarehouseConfig config = new ImagePipelineWarehouse.WarehouseConfig();
//            int memoryLevel = ImagePipelineWarehouse.getMemoryLevel();
//            int picMaxSize;
//            if (memoryLevel == ImagePipelineWarehouse.MEMORY_LEVEL_EXTRA_LOW) {
//                picMaxSize = ImagePipelineWarehouse.MAX_SIZE_FOR_PREVIEW_PHOTO_FOR_EXTRA_LOW_MEMORY_DEVICE;
//            } else if (memoryLevel == ImagePipelineWarehouse.MEMORY_LEVEL_LOW) {
//                picMaxSize = ImagePipelineWarehouse.MAX_SIZE_FOR_PREVIEW_PHOTO_FOR_LOW_MEMORY_DEVICE;
//            } else {
//                picMaxSize = ImagePipelineWarehouse.MAX_SIZE_FOR_PREVIEW_PHOTO_FOR_NORMAL_DEVICE;
//            }
            // 最长边直接改为这个。拼接那边也需要使用。
            config.picMaxWidthSize = (int) (DeviceUtils.getScreenWidth() * 0.9f);
            config.picMaxHeightSize = config.picMaxWidthSize * 5;
            // 一张照片使用同步方式加载，多张照片使用异步方式加载
            config.loadImageAsync = imageInfos.size() != 1;
            // 开启内存拷贝优化, 如果导入的照片已经存在内存中，会直接做内存拷贝，避免磁盘IO操作
            config.preferCopyImageInMemory = true;
            config.cacheDir = PathUtil.getPuzzleTempDir();
            int selectNumber = imageInfos.size();
            List<String> imagePaths = new ArrayList<>();
            if (selectNumber == 1) {
                for (int i = 0; i < 2; i++) {
                    String url = imageInfos.get(0).getMediaPath();
                    if (!TextUtils.isEmpty(url)) {
                        imagePaths.add(url);
                    } else {
                        // 加载图片资源失败
                        getLoadImageError().setValue(true);
                    }
                }
            } else {
                for (int i = 0; i < imageInfos.size(); i++) {
                    String url = imageInfos.get(i).getMediaPath();
                    if (!TextUtils.isEmpty(url)) {
                        imagePaths.add(url);
                    } else {
                        // 加载图片资源失败
                        getLoadImageError().setValue(true);
                    }
                }

            }
            config.uriList = imagePaths;
            isImageWarePrepared = true;
            getInitImageData().setValue(config);

        } else {
            // 加载图片资源失败
            getLoadImageError().setValue(true);
        }
    }

    public MutableLiveData<ImagePipelineWarehouse.WarehouseConfig> getInitImageData() {
        if (mImageConfig == null) {
            mImageConfig = new MutableLiveData<>();
        }
        return mImageConfig;

    }


    /**
     * @param puzzleBean
     */
    public void onSelectPuzzle(PuzzleBean puzzleBean, int postition, boolean click) {

        if (!isImageWarePrepared || puzzleBean == null) {

            return;
        }
        if (click) {
            MTAnalyticsAgent.logEvent(MTAnalyticsConstant.PUZZLE_TEMPLATE, MTAnalyticsConstant.PUZZLE_TYPE,
                    puzzleBean.getName());
        }

        // 要对配置文件进行解析，耗时操作
        ThreadExecutor.executeFastTask(new AbstractNamedRunnable("onSelectPuzzle") {
            @Override
            public void execute() {
                // 加载配置文件
                puzzleBean.loadPatchWorld();
                getCurrentPuzzle().postValue(puzzleBean);

            }
        });

    }

    /**
     * @return
     */
    public MutableLiveData<PuzzleBean> getCurrentPuzzle() {
        if (mCurrentPuzzle == null) {
            mCurrentPuzzle = new MutableLiveData<>();
        }
        return mCurrentPuzzle;
    }


    public MutableLiveData<SizeBeanEnum> getCurrentPuzzleSize() {
        if (mCurrentPuzzleSize == null) {
            mCurrentPuzzleSize = new MutableLiveData<>();
        }
        return mCurrentPuzzleSize;
    }

    public MutableLiveData<Boolean> getCurrentSizeSave() {
        if (sizeNeedRefreshCanvasSave == null) {
            sizeNeedRefreshCanvasSave = new MutableLiveData<>();
        }
        return sizeNeedRefreshCanvasSave;
    }


    public MutableLiveData<RationBean> getCurrentPuzzleRatio() {
        if (mCurrentPuzzleRatio == null) {
            mCurrentPuzzleRatio = new MutableLiveData<>();
        }
        return mCurrentPuzzleRatio;
    }


    /**
     * 提示图片加载失败
     *
     * @return
     */
    public MutableLiveData<Boolean> getLoadImageError() {
        if (mImageErrorTip == null) {
            mImageErrorTip = new MutableLiveData<>();
        }
        return mImageErrorTip;

    }

    public MutableLiveData<List<PuzzleBean>> getPuzzleList() {
        if (mPuzzleList == null) {
            mPuzzleList = new MutableLiveData<>();
        }
        return mPuzzleList;
    }

    /**
     * 初始化比例UI
     *
     * @return
     */
    public MutableLiveData<List<RationBean>> getInitRatioUI() {
        if (mInitRatioUI == null) {
            mInitRatioUI = new MutableLiveData<>();
        }
        return mInitRatioUI;
    }

    /**
     * 初始化尺寸UI
     */
    public MutableLiveData<List<SizeBeanEnum>> getInitSizeUI() {
        if (mInitSizeUI == null) {
            mInitSizeUI = new MutableLiveData<>();
        }
        return mInitSizeUI;
    }


    /**
     * 替换拼图结果
     *
     * @param index
     * @param caImageInfo
     */
    public void replacePhoto(int index, MediaInfo caImageInfo) {
        List<MediaInfo> imageInfos = getImageInfos().getValue();
        if (imageInfos != null && !imageInfos.isEmpty()) {
            if (index >= 0 && index < imageInfos.size()) {
                imageInfos.set(index, caImageInfo);
            }
        }
    }

    public void clearSavePath() {
        savedStitchImagePath = null;
    }

    public boolean isStitchSaved() {
        return !TextUtils.isEmpty(savedStitchImagePath);
    }


    public void saveStitchImageToDisk(Bitmap bitmap, int imageCount) {
        if (!BitmapUtils.isAvailableBitmap(bitmap)) {
            saveResultEvent = null;
            return;
        }

        if (!SDCardUtil.checkSD(50)) {
            //保存失败弹窗。
            memoryNotEnoughEvent.postValue(true);
            return;
        }
        // 允许重复保存
//        if (!TextUtils.isEmpty(savedStitchImagePath)) {
//            saveResultEvent.postValue(null);
//            return;
//        }
        loadingEvent.setValue(true);
        ThreadExecutor.executeSlowTask(new AbstractNamedRunnable("SAVE-STITCH-IMAGE") {
            @Override
            public void execute() {
                String targetPath = PathUtil.getOrgPicPath();
                boolean saveResult = false;
                if (ApplicationConfig.getSaveOptimizeSwitch()) {
                    saveResult = MediaSaver.saveImageToSDCard(bitmap, targetPath, "拼图", 0, Bitmap.CompressFormat.JPEG, 100);
                } else {
                    saveResult = BPImageSaveUtils.saveImageWithExif(bitmap, 0, targetPath, "拼图");
                }
                if (saveResult) {
                    Map<String, String> map = new HashMap<>(4);
                    map.put(MTAnalyticsConstant.PUZZLE_COUNT, String.valueOf(imageCount));
                    map.put("type", "拼接");
                    map.put("clk_times", ADCache.INSTANCE.getPuzzleSaveCount() + "");
                    String homeFuncId = getHomeFuncID();
                    if (homeFuncId != null) {
                        map.put("source", "首页推荐");
                    }
                    MTAnalyticsAgent.logEvent(MTAnalyticsConstant.PUZZLE_SAVE, map);
                }
                loadingEvent.postValue(false);
                UserMediaInfo mediaInfo = new UserMediaInfo();
                mediaInfo.setMediaPath(targetPath);
                mediaInfo.setWidth(bitmap.getWidth());
                mediaInfo.setHeight(bitmap.getHeight());
                mediaInfo.setType(UserMediaInfo.TYPE_IMAGE);
                saveResultEvent.postValue(saveResult ? mediaInfo : null);
                savedStitchImagePath = targetPath;
                bitmap.recycle();
            }
        });
    }


}
