package com.commsource.puzzle.patchedworld.beans

enum class SizeBeanEnum(
        val id: Int,
        /** 宽度*/
        val sizeWidth: Float,
        /** 高度*/
        val sizeHeight: Float,
        /** 单位*/
        val uint: String,
        /** 展示名称res。*/
        val nameRes: String,
        /** 统计名称*/
        val statisticName: String,
) {
    US_0(0, 3.5f, 5f, "inch", "3.5 X 5 inch", "3.5X5in"),
    US_1(1, 4f, 6f, "inch", "4 X 6 inch", "4X6in"),
    US_2(2, 5f, 7f, "inch", "5 X 7 inch", "7X7in"),
    US_3(3, 8f, 10f, "inch", "8 X 10 inch", "8X10in"),
    US_4(4, 11f, 14f, "inch", "11 X 14 inch", "11X14in"),

    EURO_0(5, 9f, 13f, "cm", "9 X 13 cm", "9X13cm"),
    EURO_1(6, 10f, 15f, "cm", "10 X 15 cm", "10X15cm"),
    EURO_2(7, 13f, 18f, "cm", "13 X 18 cm", "13X18cm"),
    EURO_3(8, 15f, 21f, "cm", "15 X 21 cm", "15X21cm"),
    EURO_4(9, 20f, 30f, "cm", "20 X 30 cm", "20X30cm"),
    EURO_5(10, 105f, 148f, "mm", "A6", "105X148mm"),
    EURO_6(11, 148f, 210f, "mm", "A5", "148X210mm"),

    JAPAN_0(12, 89f, 127f, "mm", "L版", "89X127mm"),
    JAPAN_1(13, 127f, 178f, "mm", "2L版", "127X178mm"),
    JAPAN_2(14, 102f, 152f, "mm", "KG版", "102X152mm"),
    JAPAN_3(15, 210f, 297f, "mm", "A4版", "210X297mm"),
    JAPAN_4(16, 303f, 242f, "mm", "八切サイズ ", "303X242mm"),
    JAPAN_5(17, 379f, 288f, "mm", "四切サイズ", "379X288mm"), ;


}