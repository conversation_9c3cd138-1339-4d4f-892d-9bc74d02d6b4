package com.commsource.puzzle.patchedworld;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

import com.commsource.beautyplus.R;
import com.commsource.util.ResourcesUtils;
import com.meitu.library.util.Debug.Debug;
import com.meitu.library.util.bitmap.BitmapUtils;
import com.meitu.library.util.io.StreamUtils;


import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.os.Parcel;
import android.text.TextUtils;

/**
 * 图片部件
 * <p>
 * <NAME_EMAIL> on 2016/10/27.
 *
 * <AUTHOR>
 */
public class ImagePatch extends VisualPatch {
    private static final String TAG = "ImagePatch";

    /**
     * 主图像
     */
    private transient Bitmap image;
    /**
     * mask图像
     */
    private transient Bitmap maskBitmap;
    /**
     * mask文件路径
     */
    private String maskPath;
    /**
     * 正片叠底图片路径
     */
    private String multiplyImagePath;
    /**
     * 正片叠底图片是否支持跟随主图像一起旋转
     */
    private boolean rotateMultiplyImageIfNeed = true;
    /**
     * 图像的缩放类型
     */
    private ScaleType scaleType = ScaleType.FIT_XY;
    /**
     * 图像类型 {@link ImageType}
     */
    private ImageType imageType = ImageType.COMMON;
    /**
     * 图像是否水平镜像翻转
     */
    private boolean imageHorizontallyFlipped = false;
    /**
     * 图像是否竖直镜像翻转
     */
    private boolean imageVerticallyFlipped = false;
    /**
     * 图像是否有正角度旋转
     */
    private boolean imageRightAngleRotated = false;
    /**
     * 图像的正角度旋转角度
     */
    private int imageRightAngleRotateDegree = 0;
    /**
     * 标记图像是否有衬底
     */
    private boolean hasSubstrate = false;
    /**
     * 图像如果使用衬底，衬底超出图像的宽度
     */
    private int substrateOutset = 0;
    /**
     * 滤镜素材id
     */
    private long filterMaterialId;
    /**
     * 默认滤镜素材id
     */
    private long defaultFilterMaterialId;
    /**
     * 滤镜素材文件路径
     */
    private String filterFilePath;
    /**
     * 特效透明度 (0.0f - 1.0f)
     */
    private float filterAlpha = 1.0f;
    /**
     * 使用启用滤镜
     */
    private boolean filterEnabled = true;
    /**
     * 天气颜色（可选）
     */
    private int weatherIconColor = ResourcesUtils.getColor(R.color.transparent);
    /**
     * 高度是否动态计算
     */
    private final boolean isHeightResolvedDynamic;

    /**
     * 标记部件是否担任注头或者注脚，若是，则无法移动其位置
     */
    private boolean isHeadOrFooting;

    public ImagePatch(VisualPatch patch) {
        super(patch);
        // 如果固有高度在构建时等于0，表明高度需要在后置逻辑中动态计算
        this.isHeightResolvedDynamic = intrinsicHeight == 0;
    }

    public ImagePatch(Builder builder) {
        super(builder);
        // 如果固有高度在构建时等于0，表明高度需要在后置逻辑中动态计算
        this.isHeightResolvedDynamic = intrinsicHeight == 0;
        this.scaleType = builder.scaleType;
        this.maskPath = builder.maskPath;
        this.multiplyImagePath = builder.multiplyImagePath;
        this.filterMaterialId = this.defaultFilterMaterialId = builder.defaultFilterMaterialId;
        this.filterFilePath = builder.filterFilePath;
        this.filterAlpha = builder.filterAlpha;
        this.imageType = builder.imageType;
        this.weatherIconColor = builder.weatherIconColor;
        this.rotateMultiplyImageIfNeed = builder.rotateMultiplyImageIfNeed;
        this.isHeadOrFooting = builder.isHeadOfFooting;
        this.hasSubstrate = builder.hasSubstrate;
        this.substrateOutset = builder.substrateOutset;
    }

    @SuppressWarnings("unused")
    protected ImagePatch(Parcel in) {
        super(in);
        // 如果固有高度在构建时等于0，表明高度需要在后置逻辑中动态计算
        this.isHeightResolvedDynamic = intrinsicHeight == 0;
    }

    /**
     * 根据ScaleType动态计算宽高信息
     */
    public void resolveDimensionsByScaleType() {
        if (!BitmapUtils.isAvailableBitmap(image)) {
            return;
        }
        // 图像的宽高比
        float imageH2WRatio = image.getHeight() * 1.0f / image.getWidth();
        if (getScaleType() == ImagePatch.ScaleType.MATCH_WIDTH) {
            if (getIntrinsicHeight() == 0) {
                setIntrinsicHeight((int) Math.ceil(imageH2WRatio * getIntrinsicWidth()));
            }

            setBoundaryWithLinkedFieldsCorrected(position.x, position.y, position.x + getIntrinsicWidth(),
                    position.y + getIntrinsicHeight());
        }
    }

    public boolean isHeightResolvedDynamic() {
        return isHeightResolvedDynamic;
    }

    public int getImageWidth() {
        return image != null ? image.getWidth() : 0;
    }

    public int getImageHeight() {
        return image != null ? image.getHeight() : 0;
    }

    public ScaleType getScaleType() {
        return scaleType;
    }

    public ImagePatch setScaleType(ScaleType scaleType) {
        this.scaleType = scaleType;
        return this;
    }

    public int getWeatherIconColor() {
        return weatherIconColor;
    }

    public boolean isHeadOrFooting() {
        return isHeadOrFooting;
    }

    public boolean hasSubstrate() {
        return hasSubstrate;
    }

    public int getSubstrateOutset() {
        return substrateOutset;
    }

    public ImagePatch setImage(Bitmap image) {
        recycleManagedBitmap(this.image);
        this.image = image;
        if (imageRightAngleRotated) {
            rotateRightAngleImpl(imageRightAngleRotateDegree);
        }
        if (imageHorizontallyFlipped || imageVerticallyFlipped) {
            flipImageImpl(imageHorizontallyFlipped, imageVerticallyFlipped);
        }

        return this;
    }

    protected void setImageInner(Bitmap image) {
        this.image = image;
    }

    @Override
    public void loadManagedBitmapAndDrawables(Context context) {
        super.loadManagedBitmapAndDrawables(context);

        if (!TextUtils.isEmpty(maskPath)) {
            if (maskPath.startsWith(File.separator)) {
                maskBitmap = BitmapFactory.decodeFile(maskPath);
            } else {
                InputStream ins = null;
                try {
                    ins = context.getAssets().open(maskPath);
                    maskBitmap = BitmapFactory.decodeStream(ins);
                } catch (Exception e) {
                    Debug.e(TAG, e);
                } finally {
                    StreamUtils.close(ins);
                }
            }
        }
    }

    @Override
    public void releaseManagedBitmapAndDrawables() {
        super.releaseManagedBitmapAndDrawables();

        recycleManagedBitmap(this.image);
        recycleManagedBitmap(maskBitmap);
    }

    public Bitmap getMaskBitmap() {
        return maskBitmap;
    }

    public ImagePatch setMaskPath(String maskPath) {
        this.maskPath = maskPath;
        return this;
    }

    public String getMaskPath() {
        return maskPath;
    }

    public ImagePatch setMultiplyImagePath(String multiplyImagePath) {
        this.multiplyImagePath = multiplyImagePath;
        return this;
    }

    public ImagePatch setRotateMultiplyImageIfNeed(boolean rotateMultiplyImageIfNeed) {
        this.rotateMultiplyImageIfNeed = rotateMultiplyImageIfNeed;
        return this;
    }

    public String getMultiplyImagePath() {
        return multiplyImagePath;
    }

    public boolean rotateMultiplyImageIfNeed() {
        return rotateMultiplyImageIfNeed;
    }

    public Bitmap getImage() {
        return image;
    }

    /**
     * 水平镜像翻转图像
     */
    public void horizontalFlipImage() {
        if (flipImageImpl(true, false)) {
            imageHorizontallyFlipped = !imageHorizontallyFlipped;
        }
    }

    /**
     * 竖直镜像翻转图像
     */
    public void verticalFlipImage() {
        if (flipImageImpl(false, true)) {
            imageVerticallyFlipped = !imageVerticallyFlipped;
        }
    }

    /**
     * 顺时针旋转图像90度
     */
    public void rotateImageRightAngleClockwise() {
        if (rotateRightAngleImpl(90)) {
            imageRightAngleRotateDegree += 90;
            imageRightAngleRotateDegree = imageRightAngleRotateDegree % 360;

            imageRightAngleRotated = (imageRightAngleRotateDegree != 0);
        }
    }

    /**
     * 镜像翻转图像
     *
     * @param flipHorizontal 水平镜像翻转
     * @param flipVertical   竖直镜像翻转
     * @return 是否翻转成功
     */
    private boolean flipImageImpl(boolean flipHorizontal, boolean flipVertical) {
        if (BitmapUtils.isAvailableBitmap(image)) {
            Matrix matrix = new Matrix();
            matrix.postScale(flipHorizontal ? -1 : 1, flipVertical ? -1 : 1, image.getWidth() / 2,
                    image.getHeight() / 2);
            image = Bitmap.createBitmap(image, 0, 0, image.getWidth(), image.getHeight(), matrix, true);

            return true;
        }

        return false;
    }

    /**
     * 旋转图像给定正角度（90度倍角)
     *
     * @param rightAngleToRotate 正角度（90度倍角)
     * @return 是否旋转成功
     */
    private boolean rotateRightAngleImpl(int rightAngleToRotate) {
        if (BitmapUtils.isAvailableBitmap(image)) {
            Matrix matrix = new Matrix();
            matrix.postRotate(rightAngleToRotate);
            image = Bitmap.createBitmap(image, 0, 0, image.getWidth(), image.getHeight(), matrix, true);

            return true;
        }

        return false;
    }

    /**
     * 获取是否竖直镜像翻转了图像
     *
     * @return 是否竖直镜像翻转了图像
     */
    public boolean isImageVerticallyFlipped() {
        return imageVerticallyFlipped;
    }

    /**
     * 设置图像是否进行了竖直镜像翻转
     *
     * @param imageVerticallyFlipped 图像是否进行了竖直镜像翻转
     */
    public void setImageVerticallyFlipped(boolean imageVerticallyFlipped) {
        this.imageVerticallyFlipped = imageVerticallyFlipped;
    }

    /**
     * 获取是否水平镜像翻转了图像
     *
     * @return 是否水平镜像翻转了图像
     */
    public boolean isImageHorizontallyFlipped() {
        return imageHorizontallyFlipped;
    }

    /**
     * 设置图像是否进行了水平镜像翻转
     *
     * @param imageHorizontallyFlipped 图像是否进行了水平镜像翻转
     */
    public void setImageHorizontallyFlipped(boolean imageHorizontallyFlipped) {
        this.imageHorizontallyFlipped = imageHorizontallyFlipped;
    }

    /**
     * 获取图像是否进行了正角位旋转
     *
     * @return 图像是否进行了正角位旋转
     */
    public boolean isImageRightAngleRotated() {
        return imageRightAngleRotated;
    }

    /**
     * 获取图像的正角度旋转角度
     *
     * @return 图像的正角度旋转角度
     */
    public int getImageRightAngleRotateDegree() {
        return imageRightAngleRotateDegree;
    }

    /**
     * 设置图像的正角位旋转情况
     *
     * @param imageRightAngleRotated true表示做了正角位旋转
     * @param rotateDegree           在imageRightAngleRotated传入true时此值指定旋转的正角位，false时无效
     *                               只接受0, +90, -90及180
     */
    public void setImageRightAngleRotated(boolean imageRightAngleRotated, int rotateDegree) {
        this.imageRightAngleRotated = imageRightAngleRotated;
        if (imageRightAngleRotated) {
            this.imageRightAngleRotateDegree = rotateDegree;
        } else {
            this.imageRightAngleRotateDegree = 0;
        }
    }

    /**
     * 重置图像的镜像翻转和旋转值
     */
    public void resetFlipAndRotate() {
        imageRightAngleRotateDegree = 0;
        imageRightAngleRotated = false;
        imageHorizontallyFlipped = false;
        imageVerticallyFlipped = false;
    }

    public long getFilterMaterialId() {
        return filterMaterialId;
    }

    public void setFilterMaterialId(long filterMaterialId) {
        this.filterMaterialId = filterMaterialId;
    }

    @SuppressWarnings("unused")
    public long getDefaultFilterMaterialId() {
        return this.defaultFilterMaterialId;
    }

    public void setDefaultFilterMaterialId(long defaultFilterMaterialId) {
        this.defaultFilterMaterialId = defaultFilterMaterialId;
    }

    public void setFilterFilePath(String filterFilePath) {
        this.filterFilePath = filterFilePath;
    }

    public String getFilterFilePath() {
        return this.filterFilePath;
    }

    public boolean filterDifferFromDefault() {
        return this.defaultFilterMaterialId != this.filterMaterialId;
    }

    public float getFilterAlpha() {
        return this.filterAlpha;
    }

    public void setFilterAlpha(float filterAlpha) {
        this.filterAlpha = filterAlpha;
    }

    public void enableFilter(boolean enable) {
        filterEnabled = enable;
    }

    public boolean isFilterEnabled() {
        return filterEnabled;
    }

    public ImageType getImageType() {
        return imageType;
    }

    public enum ScaleType {
        /**
         * 类似ImageView.ScaleType#FIT_XY
         */
        FIT_XY(1),
        /**
         * 类似ImageView.ScaleType#CENTER_CROP
         * <p>
         * 保持图像的宽高比缩放直到图像的两边等于或者大于patch的对应边
         * 图像在patch中的部分是居中的
         */
        CENTER_CROP(6),
        /**
         * 类似ImageView.ScaleType#CENTER_INSIDE
         * <p>
         * 保持图像的宽高比缩放直到图像的两边等于或者小于patch的对应边
         * 图像在patch中居中
         */
        CENTER_INSIDE(7),

        /**
         * 宽度固定，高度根据图片宽度计算
         */
        MATCH_WIDTH(8),

        /**
         * 长宽比不接近于1:1时使用匹配最长边（CENTER_INSIDE），当长宽比接近1:1时进行scale四边都露出白边（海报边框白边框需求）
         */
        SCALE_SQUARE(9);

        // TODO: 实现更多适配方式 {@link ImageView.ScaleType}

        ScaleType(int scaleType) {
            this.scaleType = scaleType;
        }

        final int scaleType;
    }

    public enum ImageType {
        /**
         * 普通图片
         */
        COMMON(0),
        /**
         * 动态天气图标
         */
        @SuppressWarnings("unused")
        DYNAMIC_WEATHER_ICON(1);

        ImageType(int typeInt) {
            this.typeInt = typeInt;
        }

        public static ImageType enumOf(int typeInt) {
            for (ImageType imageType : ImageType.values()) {
                if (imageType.typeInt == typeInt) {
                    return imageType;
                }
            }
            return COMMON;
        }

        final int typeInt;
    }



    public static class Builder extends VisualPatch.Builder {
        private String maskPath;
        private String multiplyImagePath;
        private boolean rotateMultiplyImageIfNeed = true;
        private long defaultFilterMaterialId;
        private String filterFilePath;
        private float filterAlpha = 1.0f;
        private ScaleType scaleType = ScaleType.FIT_XY;
        private ImageType imageType = ImageType.COMMON;
        private int weatherIconColor = 0x00000000;
        private boolean isHeadOfFooting;
        private boolean hasSubstrate = false;
        private int substrateOutset = 0;

        public Builder() {
            super();
        }

        public Builder(int worldWidth, int worldHeight) {
            super(worldWidth, worldHeight);
        }

        public Builder(int worldWidth, int worldHeight, int width, int height) {
            super(worldWidth, worldHeight, width, height);
        }

        public Builder setScaleType(ScaleType scaleType) {
            this.scaleType = scaleType;
            return this;
        }

        public Builder setMaskPath(String maskPath) {
            this.maskPath = maskPath;
            return this;
        }

        public Builder setMultiplyImagePath(String multiplyImagePath) {
            this.multiplyImagePath = multiplyImagePath;
            return this;
        }

        public Builder setRotateMultiplyImageIfNeed(boolean rotateMultiplyImageIfNeed) {
            this.rotateMultiplyImageIfNeed = rotateMultiplyImageIfNeed;
            return this;
        }

        public Builder setDefaultFilterMaterialId(long filterMaterialId) {
            this.defaultFilterMaterialId = filterMaterialId;
            return this;
        }

        public Builder setFilterFilePath(String filterFilePath) {
            this.filterFilePath = filterFilePath;
            return this;
        }

        public Builder setFilterAlpha(float filterAlpha) {
            this.filterAlpha = filterAlpha;
            return this;
        }

        public Builder setImageType(ImageType imageType) {
            this.imageType = imageType;
            return this;
        }

        public Builder setWeatherIconColor(int weatherIconColor) {
            this.weatherIconColor = weatherIconColor;
            return this;
        }

        public Builder setHasSubstrate(boolean hasSubstrate) {
            this.hasSubstrate = hasSubstrate;
            return this;
        }

        public Builder setSubstrateOutset(int outset) {
            this.substrateOutset = outset;
            return this;
        }

        public void setHeadOfFooting(boolean headOfFooting) {
            this.isHeadOfFooting = headOfFooting;
        }

        public ImagePatch create() {
            return new ImagePatch(this);
        }
    }

}
