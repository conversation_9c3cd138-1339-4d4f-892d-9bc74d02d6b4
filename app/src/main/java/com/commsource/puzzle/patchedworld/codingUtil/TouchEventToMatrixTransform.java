package com.commsource.puzzle.patchedworld.codingUtil;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.CallSuper;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TouchEvent事件到矩阵变换的转换器，使用它可以不必自己解析MotionEvent事件并映射到View的Matrix变换,
 * 只需要创建转换器并为转换器指定要变换的目标Matrix, 然后让转换器接收MotionEvent即可。
 * <p>
 * 已经实现的转换器包括:
 * 平移{@link TouchEventToTranslate}、旋转{@link TouchEventToRotate}、缩放{@link TouchEventToScale}
 * <p>
 * 转换器可以自由组合使用{@link TouchEventToMatrixTransform#appendPrecursoryTransform(TouchEventToMatrixTransform)}
 * 各种变换分别提供了一些可配置的参数和特性
 * <p>
 * Created by lcb@meitu on 2016/11/2.
 *
 * <AUTHOR> Lin
 */
public abstract class TouchEventToMatrixTransform {
    private static final String TAG = TouchEventToMatrixTransform.class.getSimpleName();

    /**
     * 列表，前置的TouchEvent到矩阵变换的转换器
     */
    protected List<TouchEventToMatrixTransform> mPrecursoryTransforms;
    /**
     * 矩阵变换任务, 是一个矩阵变换约束到矩阵的映射表
     */
    protected List<MatrixTransform> mMatrixTransformAssignments = new ArrayList<>();
    /**
     * 矩阵变换与其对应的平移动画生成器映射, 避免重复创建
     */
    protected Map<MatrixTransform, TranslationAnimatorGenerator> mTranslationAnimatorGenerators = new HashMap<>(4);
    /**
     * 矩阵变换与其对应的缩放动画生成器映射, 避免重复创建
     */
    protected Map<MatrixTransform, ScaleAnimatorGenerator> mScaleAnimatorGenerators = new HashMap<>(4);

    /**
     * 矩阵变换应用的目标视图
     */
    private WeakReference<View> viewRef;
    /**
     * 标记当前转换器是否是主要的、发起者的转换器，每个转换器独立运作时都是主要的，
     * 当它们被作为前置转换器链接到当前转换器时会被置为非主要的
     */
    private boolean mIsMajor = true;
    /**
     * 是否使用单指来旋转
     */
    protected boolean mUseSingleTouchToRotate = false;
    /**
     * 是否使用单指来缩放
     */
    protected boolean mUseSingleTouchToScale = false;
    /**
     * 是否使用累积型的缩放，如果设置为true, 则单指旋缩放是通过每次ACTION_MOVE事件之间的距离增量来累积应用缩放，
     * 否则可称为重置型，每次ACTION_MOVE总是基于初始点来计算缩放值，这个区别只适用于单指来缩放；
     * 对于双指缩放，基于已经被广泛接受的手势语义定义，总是使用累积型
     */
    protected boolean mUseAccumulativeScale = DEFAULT_SCALE_ACCUMULATIVE;
    private static final boolean DEFAULT_SCALE_ACCUMULATIVE = false;

    /**
     * 是否使用累积型的旋转，如果设置为true, 则单指旋转是通过每次ACTION_MOVE事件之间的角度增量来累积应用缩放，
     * 否则可称为重置型，每次ACTION_MOVE总是基于初始点来计算角度值，这个区别只适用于单指来旋转；
     * 对于双指旋转，基于已经被广泛接受的手势语义定义，总是使用累积型
     */
    protected boolean mUseAccumulativeRotate = DEFAULT_ROTATE_ACCUMULATIVE;
    private static final boolean DEFAULT_ROTATE_ACCUMULATIVE = false;

    /**
     * 标记是否需要在新的变换之前重置变换，即是否重置型变换
     */
    private boolean mNeedRestoreBeforeNewTransform =
            (!mUseAccumulativeRotate && mUseSingleTouchToRotate) || (!mUseAccumulativeScale && mUseSingleTouchToScale);

    /**
     * 是否使用动画来展示矩阵回弹
     */
    private boolean mShouldAnimateRebound = true;
    /**
     * 默认的回弹动画持续时间
     */
    private static final long DEFAULT_REBOUND_ANIMATION_DURATION = 150L;

    /**
     * 单指操作的主手指的id, 记录这个id的意义在保持单指轨迹的一致性， 如果UP时发现此时的手指与DOWN时的不一致，
     * 不做单指编辑的响应以防止参数跳变（因为另外一根手指的坐标与初始那根手指的坐标不同）
     */
    protected int mSingleTouchMajorPointerId = -1;

    /**
     * TouchEventToMatrixTransform构造器
     */
    public TouchEventToMatrixTransform() {
        this(null);
    }

    /**
     * TouchEventToMatrixTransform构造器
     */
    public TouchEventToMatrixTransform(@Nullable View view) {
        mPrecursoryTransforms = new ArrayList<>();
        viewRef = view != null ? new WeakReference<>(view) : new WeakReference<>(null);
    }

    /**
     * 设置是否是主要的转换器
     *
     * @param isMajor 是否主要的转换器
     */
    public void setIsMajor(boolean isMajor) {
        mIsMajor = isMajor;
    }

    /**
     * 设置是否使用单指来旋转和缩放
     *
     * @param singleTouchToRotate 是否使用单指来旋转
     * @param singleTouchToScale  是否使用单指来缩放
     */
    public void setUseSingleTouchToRotateAndScale(boolean singleTouchToRotate, boolean singleTouchToScale) {
        setUseSingleTouchToRotateAndScale(singleTouchToRotate, DEFAULT_ROTATE_ACCUMULATIVE, singleTouchToScale,
                DEFAULT_SCALE_ACCUMULATIVE);
    }

    /**
     * 设置是否使用单指来旋转和缩放, 同时指定是否使用累积型的旋转和缩放模式
     *
     * @param singleTouchToRotate   是否使用单指来旋转
     * @param useAccumulativeRotate 是否使用累积型的旋转模式
     * @param singleTouchToScale    是否使用单指来缩放
     * @param useAccumulativeScale  是否使用累积型的缩放模式
     */
    public void setUseSingleTouchToRotateAndScale(boolean singleTouchToRotate, boolean useAccumulativeRotate,
                                                  boolean singleTouchToScale, boolean useAccumulativeScale) {
        mUseSingleTouchToRotate = singleTouchToRotate;
        mUseAccumulativeRotate = useAccumulativeRotate;
        mUseSingleTouchToScale = singleTouchToScale;
        mUseAccumulativeScale = useAccumulativeScale;
        mNeedRestoreBeforeNewTransform =
                (!mUseAccumulativeRotate && mUseSingleTouchToRotate) || (!mUseAccumulativeScale && mUseSingleTouchToScale);
        if (mPrecursoryTransforms != null && !mPrecursoryTransforms.isEmpty()) {
            for (int i = mPrecursoryTransforms.size() - 1; i >= 0; i--) {
                TouchEventToMatrixTransform precursor = mPrecursoryTransforms.get(i);
                if (precursor != null) {
                    precursor.setUseSingleTouchToRotateAndScale(mUseSingleTouchToRotate, mUseAccumulativeRotate,
                            mUseSingleTouchToScale, mUseAccumulativeScale);
                }
            }
        }
    }

    /**
     * 设置是否使用动画回弹
     * 如果指定了动画回弹，则解析到touch到矩阵变换如果产生了回弹，解析方法能够返回这个回弹的逐帧版本，
     * 可以供外部做动画演示这个变换修正，比如回弹，吸附功能等
     * <p>
     * {@link MatrixTransformAnimatorGenerator TouchEventToMatrixTransform#parseTouch(MotionEvent, TouchEditHelperF.TouchState)}
     * {@link MatrixTransformAnimatorGenerator}
     *
     * @param shouldAnimateRebound 是否使用逐帧修正
     */
    public TouchEventToMatrixTransform setShouldAnimateRebound(boolean shouldAnimateRebound) {
        mShouldAnimateRebound = shouldAnimateRebound;
        return this;
    }

    /**
     * 判定当前的转换器中是否已经包含给定的矩阵变换
     *
     * @param matrixTransform 矩阵变换
     * @return 是否已经包含给定的矩阵变换
     */
    public boolean containsMatrixTransform(MatrixTransform matrixTransform) {
        return mMatrixTransformAssignments.contains(matrixTransform);
    }

    void assignMatrixTransformInner(@NonNull MatrixTransform transform) {
        if (!containsMatrixTransform(transform)) {
            mMatrixTransformAssignments.add(transform);
            View view = viewRef != null ? viewRef.get() : null;
            if (mShouldAnimateRebound && view != null) {
                if (transform.constraint.shouldReboundToLimitedScale()) {
                    mScaleAnimatorGenerators.put(transform, new ScaleAnimatorGenerator(transform, view));
                }

                if (transform.constraint.shouldKeepTargetFilledInViewport()) {
                    mTranslationAnimatorGenerators.put(transform, new TranslationAnimatorGenerator(transform, view));
                }
            }
        }
    }

    /**
     * 分配矩阵变换的任务
     *
     * @param transform 变换任务
     */
    public TouchEventToMatrixTransform assignMatrixTransform(@NonNull MatrixTransform transform) {
        assignMatrixTransformInner(transform);

        if (mPrecursoryTransforms != null && !mPrecursoryTransforms.isEmpty()) {
            for (int i = mPrecursoryTransforms.size() - 1; i >= 0; i--) {
                TouchEventToMatrixTransform precursor = mPrecursoryTransforms.get(i);
                if (precursor != null) {
                    precursor.assignMatrixTransformInner(transform);
                }
            }
        }

        return this;
    }

    /**
     * 保存当前的矩阵变换及其约束的状态
     */
    public void saveTransform() {
        for (MatrixTransform matrixTransform : mMatrixTransformAssignments) {
            if (matrixTransform != null) {
                matrixTransform.save();
            }
        }
    }

    /**
     * 还原当前的矩阵变换及其约束的状态
     */
    public void restoreTransform(boolean restoreOnlyScale) {
        for (MatrixTransform matrixTransform : mMatrixTransformAssignments) {
            if (matrixTransform != null) {
                matrixTransform.restore();
            }
        }
    }

    /**
     * 增加前置的touch事件到矩阵变换的转换器
     *
     * @param precursoryTransform 前置的转换器
     * @return 当前touch事件到矩阵变换的转换器
     */
    public TouchEventToMatrixTransform appendPrecursoryTransform(TouchEventToMatrixTransform precursoryTransform) {
        for (MatrixTransform matrixTransform : mMatrixTransformAssignments) {
            precursoryTransform.assignMatrixTransformInner(matrixTransform);
        }
        mPrecursoryTransforms.add(precursoryTransform);
        precursoryTransform.setIsMajor(false);
        return this;
    }

    /**
     * 将Touch事件解析为矩阵变换，如果变换后产生了修正并且以动画的方式进行，将返回动画集合
     *
     * @param event      MotionEvent事件
     * @param touchState TouchState
     * @return 回弹动画，如果变换之后产生了回弹并且配置了使用动画演示回弹，则返回一个返回动画列表
     * {@link TouchEventToMatrixTransform#setShouldAnimateRebound(boolean)}
     */
    public List<AnimatorSet> parseTouch(@NonNull MotionEvent event, @NonNull TouchEditHelperF.TouchState touchState) {
        return parseTouch(event, touchState, mShouldAnimateRebound ? DEFAULT_REBOUND_ANIMATION_DURATION : 0L);
    }

    /**
     * 将Touch事件解析为矩阵变换，如果变换后产生的矩阵修正是以动画的方式进行，将返回Duration的动画集合
     *
     * @param event             MotionEvent事件
     * @param touchState        TouchState
     * @param animationDuration 逐帧调节的帧数
     * @return 逐帧调节，如果变换之后产生了后续的调节，比如回弹，吸附等功能，可能将这些调节的变换保存在这些逐帧调节对象中
     * {@link TouchEventToMatrixTransform#setShouldAnimateRebound(boolean)}
     */
    public @Nullable
    List<AnimatorSet> parseTouch(@NonNull MotionEvent event,
                                 @NonNull TouchEditHelperF.TouchState touchState, long animationDuration) {
        switch (event.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                parseActionDown(event, touchState);
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
                parseActionPointerDown(event, touchState);
                break;
            case MotionEvent.ACTION_MOVE:
                parseActionMove(event, touchState);
                break;
            case MotionEvent.ACTION_POINTER_UP:
                parseActionPointerUp(event, touchState);
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                return parseActionEnd(event, touchState, mShouldAnimateRebound ? animationDuration : 0L);
            default:
        }

        return null;
    }

    /**
     * 判断给定的Touch事件是否有被指定类型的变换认领
     *
     * @param event          MotionEvent事件
     * @param touchState     TouchState
     * @param transformClazz 矩阵变换类型
     * @return true表示给定touch state有被指定类型的变换认领；否则表示给定touch state没有被指定类型的变换认领
     */
    public boolean claimTouch(@NonNull MotionEvent event, @NonNull TouchEditHelperF.TouchState touchState,
                              Class<? extends TouchEventToMatrixTransform> transformClazz) {
        if (transformClazz.isInstance(this)) {
            return claimTouch(event, touchState);
        }

        for (TouchEventToMatrixTransform transform : mPrecursoryTransforms) {
            if (transformClazz.isInstance(transform)) {
                return transform.claimTouch(event, touchState);
            }
        }

        return false;
    }

    /**
     * 认领Touch事件用于矩阵变换
     *
     * @param event MotionEvent事件
     * @return true表示给定touch state将用于矩阵变换；否则表示给定touch state不会用于矩阵变换
     */
    protected abstract boolean claimTouch(@NonNull MotionEvent event, @NonNull TouchEditHelperF.TouchState touchState);

    /**
     * 解析{@link MotionEvent#ACTION_DOWN}事件并用于矩阵变换
     *
     * @param event      MotionEvent事件
     * @param touchState TouchState
     * @return 是否使用事件对矩阵做了变换
     */
    @CallSuper
    protected boolean parseActionDown(@NonNull MotionEvent event, @NonNull TouchEditHelperF.TouchState touchState) {
        boolean transformed = false;

        if (mPrecursoryTransforms != null && !mPrecursoryTransforms.isEmpty()) {
            for (int i = mPrecursoryTransforms.size() - 1; i >= 0; i--) {
                TouchEventToMatrixTransform precursor = mPrecursoryTransforms.get(i);
                if (precursor != null) {
                    if (precursor.parseActionDown(event, touchState)) {
                        transformed = true;
                    }
                }
            }
        }

        mSingleTouchMajorPointerId = event.getPointerId(0);

        // 如果当前转换器是主要的，在Touch开始(ACTION_DOWN时)保存当前的变换状态
        // 保存下来的变换状态可以在Touch结束(ACTION_UP或者ACTION_CANCEL时)用作参考状态，对期间产生的变换做还原，过滤或者修正
        if (mIsMajor) {
            saveTransform();
        }

        return transformed;
    }

    /**
     * 解析{@link MotionEvent#ACTION_POINTER_DOWN}事件并用于矩阵变换
     *
     * @param event      MotionEvent事件
     * @param touchState TouchState
     */
    @CallSuper
    protected void parseActionPointerDown(@NonNull MotionEvent event, @NonNull TouchEditHelperF.TouchState touchState) {
        if (mPrecursoryTransforms != null && !mPrecursoryTransforms.isEmpty()) {
            for (int i = mPrecursoryTransforms.size() - 1; i >= 0; i--) {
                TouchEventToMatrixTransform precursor = mPrecursoryTransforms.get(i);
                if (precursor != null) {
                    precursor.parseActionPointerDown(event, touchState);
                }
            }
        }
    }

    /**
     * 解析{@link MotionEvent#ACTION_MOVE}事件并用于矩阵变换
     *
     * @param event      MotionEvent事件
     * @param touchState TouchState
     * @return 是否使用事件对矩阵做了变换
     */
    @CallSuper
    protected boolean parseActionMove(@NonNull MotionEvent event, @NonNull TouchEditHelperF.TouchState touchState) {
        boolean transformed = false;

        // 如果采用重置型变换
        // 则每次ACTION_MOVE时还原到之前保存的初始状态，然后再应用新的变换
        if (mNeedRestoreBeforeNewTransform && mIsMajor) {
            restoreTransform(mUseAccumulativeRotate);
        }

        if (mPrecursoryTransforms != null && !mPrecursoryTransforms.isEmpty()) {
            for (int i = mPrecursoryTransforms.size() - 1; i >= 0; i--) {
                TouchEventToMatrixTransform precursor = mPrecursoryTransforms.get(i);
                if (precursor != null) {
                    if (precursor.parseActionMove(event, touchState)) {
                        transformed = true;
                    }
                }
            }
        }

        return transformed;
    }

    /**
     * 解析{@link MotionEvent#ACTION_POINTER_UP}事件并用于矩阵变换
     *
     * @param event      MotionEvent事件
     * @param touchState TouchState
     */
    @CallSuper
    protected void parseActionPointerUp(@NonNull MotionEvent event, @NonNull TouchEditHelperF.TouchState touchState) {

        if (mPrecursoryTransforms != null && !mPrecursoryTransforms.isEmpty()) {
            for (int i = mPrecursoryTransforms.size() - 1; i >= 0; i--) {
                TouchEventToMatrixTransform precursor = mPrecursoryTransforms.get(i);
                if (precursor != null) {
                    precursor.parseActionPointerUp(event, touchState);
                }
            }
        }
    }

    /**
     * @param event             MotionEvent事件
     * @param touchState        TouchState
     * @param animationDuration 如果采用动画处理矩阵回弹的话，动画持续的时间
     * @return 如果发生了矩阵回弹并且回弹使用动画展示，返回动画列表，列表中的动画在参数指定的时长内同时执行
     */
    @CallSuper
    protected final List<AnimatorSet> parseActionEnd(@NonNull MotionEvent event,
                                                     @NonNull TouchEditHelperF.TouchState touchState, @IntRange(from = 0L) long animationDuration) {
        List<AnimatorSet> revisionAnimations = mShouldAnimateRebound ? new ArrayList<>() : null;
        for (MatrixTransform matrixTransform : mMatrixTransformAssignments) {
            MatrixTransform.Constraint constraint = matrixTransform.constraint;
            boolean animateRebound = (mShouldAnimateRebound && viewRef.get() != null && animationDuration > 0L);

            animateRebound &= constraint.shouldAnimateRebound();
            AnimatorSet animatorSet = animateRebound ? new AnimatorSet().setDuration(animationDuration) : null;
            Animator scaleAnimator = null;
            Animator translationAnimator = null;
            float preexistScale = MatrixUtil.getScale(matrixTransform.matrix);
            float targetScale = preexistScale;

            if (constraint.shouldReboundToLimitedScale()) {
                float reboundMaxScale = constraint.getReboundMaxScale();
                float reboundMinScale = constraint.getReboundMinScale();

                if (preexistScale > reboundMaxScale) {
                    targetScale = reboundMaxScale;
                } else if (preexistScale < reboundMinScale) {
                    targetScale = reboundMinScale;
                }
            }

            if (targetScale != preexistScale) {
                if (animateRebound) {
                    ScaleAnimatorGenerator scaleAnimatorGenerator = mScaleAnimatorGenerators.get(matrixTransform);
                    scaleAnimator = scaleAnimatorGenerator != null
                            ? scaleAnimatorGenerator.config(targetScale).generateAnimator(null) : null;
                }

                if (scaleAnimator == null) {
                    matrixTransform.postScale(targetScale / preexistScale);
                }

                matrixTransform.updatePivotSittingBound();
            }

            if (constraint.shouldKeepTargetFilledInViewport()) {
                float[] reboundTranslation = new float[2];

                constraint.reviseTranslationInSittingBound(0, 0, reboundTranslation);

                if (reboundTranslation[0] != 0 || reboundTranslation[1] != 0) {
                    if (animateRebound) {
                        TranslationAnimatorGenerator translationAnimatorGenerator =
                                mTranslationAnimatorGenerators.get(matrixTransform);

                        translationAnimator = translationAnimatorGenerator != null ? translationAnimatorGenerator
                                .config(reboundTranslation[0], reboundTranslation[1]).generateAnimator(null) : null;
                    }

                    if (translationAnimator == null) {
                        matrixTransform.postTranslate(reboundTranslation[0], reboundTranslation[1]);
                    }
                }
            }

            if (revisionAnimations != null && animatorSet != null
                    && (scaleAnimator != null || translationAnimator != null)) {
                revisionAnimations.add(animatorSet);
                if (scaleAnimator != null && translationAnimator != null) {
                    animatorSet.playTogether(scaleAnimator, translationAnimator);
                } else {
                    if (scaleAnimator == null) {
                        animatorSet.play(translationAnimator);
                    } else {
                        animatorSet.play(scaleAnimator);
                    }
                }
                animatorSet.start();
            }

        }

        return revisionAnimations;
    }

    /**
     * 解析Touch结束事件的前置操作
     *
     * @param event      MotionEvent事件
     * @param touchState TouchState
     */
    @CallSuper
    protected void beforeParseActionEnd(@NonNull MotionEvent event, @NonNull TouchEditHelperF.TouchState touchState) {
        if (mPrecursoryTransforms != null && !mPrecursoryTransforms.isEmpty()) {
            for (int i = mPrecursoryTransforms.size() - 1; i >= 0; i--) {
                TouchEventToMatrixTransform precursor = mPrecursoryTransforms.get(i);
                if (precursor != null) {
                    precursor.beforeParseActionEnd(event, touchState);
                }
            }
        }
    }

}
