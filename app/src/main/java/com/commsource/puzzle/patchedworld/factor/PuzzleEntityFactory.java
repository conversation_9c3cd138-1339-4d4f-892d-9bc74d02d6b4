package com.commsource.puzzle.patchedworld.factor;

import com.commsource.puzzle.patchedworld.beans.PuzzleBean;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by meitu on 2018/12/20.
 * <AUTHOR> 
 */

public class PuzzleEntityFactory {
    private static final String ID_PUZZLE_ROOT = "zip30019";

    private static final String PATH_PARENT = "puzzle/3001";

    /**
     * 根据数量的不同，来生成不同的拼图素材
     * @param count
     * @return
     */
    public List<PuzzleBean> createPuzzleMaterials(int count) {

        List<PuzzleBean> materialList = new ArrayList<>();
        if (count > 0) {
            if (count == 1) {
                materialList.addAll(getCount1List());
            } else {
                for (int i = 1; i <= 5; i++) {
                    PuzzleBean puzzleBean = new PuzzleBean();
                    puzzleBean.setContentPath(PATH_PARENT + File.separator + ID_PUZZLE_ROOT + "0" + count + "00" + i);
                    puzzleBean.setName(count + "-" + (i));
                    materialList.add(puzzleBean);
                }
            }

        }
        return materialList;
    }

    /**
     * 一张图片的时候特殊处理
     * @return
     */
    public List<PuzzleBean> getCount1List() {
        PuzzleBean puzzle1 = new PuzzleBean();
        puzzle1.setContentPath(PATH_PARENT + File.separator + "zip3001902001");
        puzzle1.setName("1-1");
        PuzzleBean puzzle2 = new PuzzleBean();
        puzzle2.setContentPath(PATH_PARENT + File.separator + "zip3001902002");
        puzzle2.setName("1-2");

        PuzzleBean puzzle3 = new PuzzleBean();
        puzzle3.setContentPath(PATH_PARENT + File.separator + "zip3001904001");
        puzzle3.setName("1-3");

        PuzzleBean puzzle4 = new PuzzleBean();
        puzzle4.setContentPath(PATH_PARENT + File.separator + "zip3001909001");
        puzzle4.setName("1-4");

        List<PuzzleBean> puzzleBeans = new ArrayList<>();
        puzzleBeans.add(puzzle1);
        puzzleBeans.add(puzzle2);
        puzzleBeans.add(puzzle3);
        puzzleBeans.add(puzzle4);
        return puzzleBeans;

    }

}
