package com.commsource.puzzle.patchedworld.imageware.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import android.text.format.Formatter;

import com.meitu.common.AppContext;
import com.meitu.library.util.Debug.Debug;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;

/**
 * Created by wrs on 2016/12/6.
 * 内存工具类
 */

public class MemoryUtil {

    private static final String TAG = "Debug_" + "MemoryUtil";
    private static int mTotalMemory;
    private static ActivityManager.MemoryInfo memoryInfo=new ActivityManager.MemoryInfo();
    private static final StringBuilder mBuilder=new StringBuilder();

    // FIXME：硬件的配置低中高端的界定随着时间推移应当有所更新
    // 并且，根据配置边界来区分的逻辑随着功能的资源使用情况，算法优化程度也应当更新

    /**
     * 是否低端机型（内存小于1G或者系统版本低于4.4）
     * @param ramSize 内存，单位KB
     * @return 是否低端机
     */
    public static boolean isLowMachine(int ramSize) {
        return Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT || ramSize < 1024;
    }

    /**
     * 是否中端机型(非低端机但内存低于1.5G)
     * @param ramSize 内存，单位KB
     * @return 是否中端机
     */
    public static boolean isMediumMachine(int ramSize) {
        return !isLowMachine(ramSize) && ramSize < 1536;
    }

    /**
     * 是否高端机(内存大于2G)
     *
     * @param ramSize 内存，单位KB
     * @return 是否高端机
     */
    public static boolean isHighMachine(int ramSize) {
        return ramSize > 2048;
    }

    /**
     * 是否Ultra机型(内存大于3G)
     *
     * @param ramSize 内存，单位KB
     * @return 是否Ultra机型
     */
    public static boolean isUltraMachine(int ramSize) {
        return ramSize > 3072;
    }

    /**
     * 获取总内存
     * @return 单位MB
     */
    public static int getTotalMemory() {
        if (mTotalMemory > 0) {
            return mTotalMemory;
        }
        int mTotal = 0;
        // /proc/meminfo读出的内核信息进行解释
        String path = "/proc/meminfo";
        String content = null;
        BufferedReader br = null;
        try {
            br = new BufferedReader(new FileReader(path), 8);
            String line;
            if ((line = br.readLine()) != null) {
                content = line;
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        if (content != null) {
            // beginIndex
            int begin = content.indexOf(':');
            // endIndex
            int end = content.indexOf('k');
            // 截取字符串信息

            content = content.substring(begin + 1, end).trim();
            mTotal = Integer.parseInt(content);
            Debug.d(TAG, ">>>total memory=" + mTotal + "KB");
        }
        mTotalMemory = mTotal / 1024;
        return mTotalMemory;
    }
    public static  String getCurrentRuntimeMemoryInfo() {
        ActivityManager manager = (ActivityManager) AppContext.getContext().getSystemService(Context.ACTIVITY_SERVICE);
        manager.getMemoryInfo(memoryInfo);
        mBuilder.delete(0, mBuilder.length());
        String availMem = Formatter.formatFileSize(AppContext.getContext(), memoryInfo.availMem);
        String totalMem = Formatter.formatFileSize(AppContext.getContext(), memoryInfo.totalMem);
        mBuilder.append(availMem);
        mBuilder.append("/");
        mBuilder.append(totalMem);
        return mBuilder.toString();
    }
}
