package com.commsource.airepair

import com.commsource.config.ApplicationConfig
import com.commsource.util.common.SpTableName
import com.commsource.util.common.SPConfig
import com.meitu.common.AppContext

object VideoAiRepairConfig : SPConfig(AppContext.context, SpTableName.VideoAiRepairConfig) {


    /**
     *  是否有已经完成处理的记录
     */
    private const val AIREPAIR_HAS_PROCESSED_RECORD = "AIREPAIR_HAS_PROCESSED_RECORD"
    fun isHasProcessedRecord(): Boolean {
        return getBoolean(AIREPAIR_HAS_PROCESSED_RECORD, false)
    }

    fun setHasProcessedRecord(isHas: Boolean) {
        putValue(AIREPAIR_HAS_PROCESSED_RECORD, isHas)
    }


    /**
     * 用户选中Ai 服务协议。
     */
    private const val USER_CHECK_PROTOCOL = "USER_CHECK_PROTOCOL"
    fun isUserCheckedProtocol(): Bo<PERSON><PERSON> {
        return getBoolean(USER_CHECK_PROTOCOL, false)
    }

    fun setUserCheckedProtocol(isChecked: Boolean) {
        putValue(USER_CHECK_PROTOCOL, isChecked)
    }


    /**
     *  是否可以免费使用Ai 修复
     */
    private const val AIREPAIR_CAN_FREE_USE = "AIREPAIR_CAN_FREE_USE"
    fun isCanFreeUse(): Boolean {
        return getBoolean(AIREPAIR_CAN_FREE_USE, true)
    }

    fun setCanFreeUse(isFree: Boolean) {
        putValue(AIREPAIR_CAN_FREE_USE, isFree)
    }


    private const val SHOW_AI_REPAIR_NEW_ICON = "SHOW_AI_REPAIR_NEW_ICON"
    fun setShowAiRepairNewIcon() {
        putValue(SHOW_AI_REPAIR_NEW_ICON, false)
    }

    fun isShowAiRepairNewIcon(): Boolean {
        return !ApplicationConfig.isNewUser(AppContext.context)
                && getBoolean(SHOW_AI_REPAIR_NEW_ICON, true)
    }


    private var record: VideoAiRepairRecord? = null
    fun setNeedDelOldRecord(rec: VideoAiRepairRecord?) {
        record = rec
    }

    fun getNeedDelOldRecord(): VideoAiRepairRecord? {
        return record
    }
}