package com.commsource.airepair

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.MotionEvent
import android.widget.FrameLayout
import android.widget.TextView
import com.commsource.beautymain.widget.gesturewidget.GestureDetectorPro
import com.commsource.beautyplus.R
import com.commsource.util.RTLTool
import com.commsource.util.ResourcesUtils
import com.commsource.util.dpf
import com.commsource.util.resColor
import com.pixocial.androidx.core.extension.dp
import kotlin.math.abs

class LabelSeekBar : FrameLayout {

    // 滑块的半径
    private val thumbSize: Float

    // seekBar 高度
    private val seekBarHeight: Int


    // 进度条的颜色
    private val progressColor: Int

    // seekBar 背景色
    private val seekBarColor: Int

    // 滑块位置
    private var thumbPos = 0f

    // 是否吸附到最近的位置。
    private var isAdsorptionToNearestPos = true

    // 滑杆上小圆点的的半径
    private val indexIndicatorRadius = 2.dp

    private val labelViews = mutableListOf<TextView>()
    private val labels: MutableList<Label> = mutableListOf()


    var onLabelSelect: ((label: Label) -> Unit)? = null
    private var curSelectLabel: Label? = null

    private val paint = Paint().apply {
        color = Color.WHITE
    }

    constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, 0)
    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : super(context, attrs, defStyleAttr) {
        val typeArray = context.obtainStyledAttributes(attrs, R.styleable.LabelSeekBar)
        thumbSize = typeArray.getDimension(R.styleable.LabelSeekBar_LabelthumbRadius, 10.dpf)
        seekBarHeight = typeArray.getDimension(R.styleable.LabelSeekBar_Labelseekbar_height, 10.dpf).toInt()
        progressColor = typeArray.getColor(R.styleable.LabelSeekBar_LabelprogressColor, R.color.white.resColor())
        seekBarColor = typeArray.getColor(R.styleable.LabelSeekBar_LabelseekBarColor, R.color.Gray_Background_4.resColor())
        typeArray.recycle()
    }

    private val gestureDetector = GestureDetectorPro(context,
        object : GestureDetectorPro.SimpleOnGestureListener() {
            override fun onMajorFingerDown(downEvent: MotionEvent): Boolean {
                thumbPos = downEvent.x
                invalidate()
                return true
            }

            override fun onMajorScroll(
                downEvent: MotionEvent?,
                moveEvent: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                thumbPos = moveEvent.x
                invalidate()
                return true
            }

            override fun onMajorFingerUp(upEvent: MotionEvent): Boolean {
                // 做一次吸附逻辑。
                if (isAdsorptionToNearestPos) {
                    val result = getNearestPos(thumbPos)
                    thumbPos = result.first
                    result.second?.let {
                        curSelectLabel = it
                        onLabelSelect?.invoke(it)
                    }
                    invalidate()
                }
                return true
            }
        })

    private fun initialLabel(list: List<Label>) {
        // 默认选中的Label
        curSelectLabel = list.elementAtOrNull(0)
        list.forEach {
            labelViews.add(TextView(context).apply {
                gravity = Gravity.CENTER
                setTextColor(Color.WHITE)
                setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
                setText(it.textRes)
                it.iconRes?.let {
                    setCompoundDrawablesRelativeWithIntrinsicBounds(ResourcesUtils.getDrawable(it), null, null, null)
                }
                compoundDrawablePadding = it.iconPadding
            }.also {
                addView(
                    it, LayoutParams(
                        LayoutParams(
                            LayoutParams.WRAP_CONTENT,
                            LayoutParams.WRAP_CONTENT
                        )
                    )
                )
            })
        }
    }

    /**
     * 设置滑杆的标签。
     */
    fun setLabels(list: List<Label>) {
        labels.clear()
        labels.addAll(list)
        initialLabel(list)
        requestLayout()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (labelViews.isNotEmpty()) {
            val topSpace = thumbSize * 2 + 8.dp
            val size = MeasureSpec.getSize(widthMeasureSpec)
            val textViewHeight = labelViews.maxOf { it.measuredHeight }
            setMeasuredDimension(size, (topSpace + textViewHeight).toInt())
            thumbPos = if (RTLTool.isLayoutRtl()) width.toFloat() else 0f
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        val topMargin = 8.dp
        // 设置TextView的位置。
        if (labels.isNotEmpty()) {
            labelViews.forEachIndexed { index, textView ->
                var tempLeft: Int
                val tempTop: Int
                if (RTLTool.isLayoutRtl()) {
                    tempLeft = width - (width * index / (labelViews.lastIndex)) - textView.width
                    tempTop = (thumbSize * 2 + topMargin).toInt()
                } else {
                    tempLeft = width * index / (labelViews.lastIndex)
                    tempTop = (thumbSize * 2 + topMargin).toInt()
                }
                when (index) {
                    0 -> textView.layout(tempLeft, tempTop, tempLeft + textView.width, tempTop + textView.height)
                    labelViews.lastIndex -> {
                        val targetLeft = if (RTLTool.isLayoutRtl()) {
                            tempLeft + textView.width
                        } else {
                            tempLeft - textView.width
                        }
                        val targetRight = targetLeft + textView.width
                        textView.layout(
                            targetLeft, tempTop, targetRight, tempTop + textView.height
                        )
                    }

                    else -> {
                        val targetLeft = if (RTLTool.isLayoutRtl()) {
                            (tempLeft + textView.width / 2.0f).toInt()
                        } else {
                            (tempLeft - textView.width / 2.0f).toInt()
                        }
                        textView.layout(targetLeft, tempTop, targetLeft + textView.width, tempTop + textView.height)
                    }
                }
            }
        }
    }

    override fun dispatchDraw(canvas: Canvas) {
        super.dispatchDraw(canvas)
        // 绘制滑杆
        paint.color = seekBarColor
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = seekBarHeight.toFloat()
        canvas.drawLine(0f, thumbSize, width.toFloat(), thumbSize, paint)

        // 绘制进度。
        paint.color = progressColor
        if (RTLTool.isLayoutRtl()) {
            canvas.drawLine(width.toFloat(), thumbSize, thumbPos, thumbSize, paint)
        } else {
            canvas.drawLine(0f, thumbSize, thumbPos, thumbSize, paint)
        }
        paint.style = Paint.Style.FILL

        // 绘制小圆点
        for (index in 0..labelViews.lastIndex) {
            val indexPos = (if (RTLTool.isLayoutRtl()) {
                width - width / labelViews.lastIndex * index
            } else {
                width / labelViews.lastIndex * index
            }).coerceAtLeast(indexIndicatorRadius)
                .coerceAtMost(width - indexIndicatorRadius)
            if (RTLTool.isLayoutRtl()) {
                if (thumbPos <= indexPos) {
                    paint.color = progressColor
                } else {
                    paint.color = seekBarColor
                }
            } else {
                if (thumbPos >= indexPos) {
                    paint.color = progressColor
                } else {
                    paint.color = seekBarColor
                }
            }
            canvas.drawCircle(indexPos.toFloat(), thumbSize, indexIndicatorRadius.toFloat(), paint)
        }

        // 绘制滑块
        paint.color = progressColor
        val centerPos = thumbPos.coerceAtLeast(thumbSize).coerceAtMost(width - thumbSize)
        canvas.drawCircle(centerPos, thumbSize, thumbSize, paint)
    }

    /**
     * 找到距离滑块最近的小圆点位置。
     */
    private fun getNearestPos(srcPos: Float): Pair<Float, Label?> {
        var diffTmp = Float.MAX_VALUE
        var targetPos = 0f
        var targetLabel: Label? = null
        for (temp in 0..labelViews.lastIndex) {
            val indexPos = (width.toFloat() / labelViews.lastIndex * temp)
            val diff = abs(srcPos - indexPos)
            if (diff <= diffTmp) {
                diffTmp = diff
                targetPos = indexPos
                targetLabel = labels.elementAtOrNull(temp)
            }
        }
        return Pair(targetPos, targetLabel)
    }


    override fun onTouchEvent(event: MotionEvent?): Boolean {
        return gestureDetector.onTouchEvent(event)
    }

    fun selectLabel(label: Label?) {
        label ?: return
        labels.indexOf(label).takeIf { it >= 0 }?.let {
            thumbPos = ((it.toFloat() / labels.lastIndex) * width)
            curSelectLabel = label
            onLabelSelect?.invoke(label)
            invalidate()
        }
    }

    fun curSelectLabel(): Label? {
        return curSelectLabel
    }

    data class Label(val iconRes: Int?, val textRes: Int, val value: Int, val iconPadding: Int = 0)

}