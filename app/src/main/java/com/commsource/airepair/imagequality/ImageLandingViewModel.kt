package com.commsource.airepair.imagequality

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import com.commsource.airepair.imagequality.repo.ImageRepairRecordRepo
import com.commsource.airepair.imagequality.util.ImageRepairLog
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.home.work.aigenerate.BaseAiGenerateRecord
import com.commsource.util.set
import com.meitu.library.hwanalytics.spm.SPMManager

typealias ImageSceneEntities = Pair<List<ImageSceneEntity>, List<ImageSceneEntity>>

class ImageLandingViewModel : ViewModel() {
    private val _scenesLiveData = MutableLiveData<ImageSceneEntities>()
    val scenesLiveData: LiveData<ImageSceneEntities> get() = _scenesLiveData

    private val _sceneLiveData = MutableLiveData<ImageSceneEntity>()
    val sceneLiveData: LiveData<ImageSceneEntity> get() = _sceneLiveData

    private val _tryLiveData = NoStickLiveData<String>()
    val tryLiveData: LiveData<String> get() = _tryLiveData

    val recordLiveData: LiveData<List<BaseAiGenerateRecord>> get() = ImageRepairRecordRepo.recordListFlow.asLiveData()

    private var currentScene: ImageSceneEntity? = null

    init {
        _scenesLiveData.set(buildScene())
    }

    fun sceneProtocol(scene: String?) {
        val scenes = _scenesLiveData.value ?: return
        val allScenes = scenes.first + scenes.second
        val defaultScene = allScenes.firstOrNull {
            it.sceneEnum.code == scene
        } ?: allScenes.firstOrNull()
        defaultScene?.let {
            updateScene(it)
        }
    }

    /**
     * 更新场景
     *
     */
    fun updateScene(entity: ImageSceneEntity) {
        if (currentScene == entity) return

        currentScene = entity
        _sceneLiveData.value = entity
    }

    fun tryRepair() {
        currentScene?.let {
            _tryLiveData.value = it.sceneEnum.code
        }
    }

    fun logEnterRepair() {
        ImageRepairLog.logEnterRepair(spmInfo)
    }

    fun logClick() {
        currentScene?.let { ImageRepairLog.logClickTry(it.sceneEnum.code, spmInfo) }
    }

    private val spmInfo = mutableMapOf<String, String>()

    fun cacheSpmInfo() {
        spmInfo.clear()
        spmInfo.putAll(SPMManager.instance.getCurrentSpmInfo())
    }
}