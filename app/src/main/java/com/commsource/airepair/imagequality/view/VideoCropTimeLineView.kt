package com.commsource.airepair.imagequality.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.LinearInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.OverScroller
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.commsource.beautymain.widget.gesturewidget.GestureDetectorPro
import com.commsource.util.dp
import com.commsource.util.dpf
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.library.albumkit.media.MediaInfo
import com.pixocial.library.albumkit.media.MediaType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.abs
import kotlin.math.ceil
import com.commsource.airepair.easytimeline.VideoClipRender
import com.commsource.airepair.easytimeline.VideoFrameHelper
import kotlin.math.max
import kotlin.math.min

/**
 * 视频裁剪时间轴控件
 * 基于EasyTimeLineView，但去除了时间刻度尺功能
 */
class VideoCropTimeLineView : View {

    // 简单定一些高度变量 - 去除了TimeRulerHeight
    companion object {
        const val LimitMinDuration = 1000L
        val DefaultStartOffset = -(55.dpf)
        val VideoFrameHeight = 52.dp
        val DefaultCropWidth = DeviceUtils.getScreenWidth() - 110.dpf
    }

    private val scroller by lazy { OverScroller(context) }
    private val gestureDetector: GestureDetectorPro

    /**
     * 默认的初始绘制偏移位置。
     */
    private val defaultStartOffset = DefaultStartOffset

    /**
     * 水平滚动的距离
     */
    private var horizontalOffset = defaultStartOffset

    /**
     * 可以滚动的范围
     */
    private val scrollRange = floatArrayOf(0f, 0f)

    private var frameCount: Float = 0f
    private var videoDuration: Int = 0

    /**
     * 视频帧缩略图宽高
     */
    private val frameRect = Rect(0, 0, VideoFrameHeight, VideoFrameHeight)

    /**
     * 视频帧缩略图渲染器
     */
    private val clipRender = VideoClipRender()

    /**
     * 视频帧解析助手
     */
    private val helper = VideoFrameHelper()

    /**
     *  SeekTime改变接口
     */
    var onSeekTimeChangeCallback: ((seekTime: Float, force: Boolean) -> Unit)? = null

    var onPlayStateCallback: ((isPause: Boolean) -> Unit)? = null

    /**
     * 裁剪范围变化回调，返回裁剪视频的长度（秒）
     */
    var onCropRangeChangeCallback: ((cropDurationInSeconds: Int) -> Unit)? = null

    /**
     * 播放动画
     */
    private var playAnim: ValueAnimator? = null

    /**
     * 智能扩展动画
     */
    private var smartExpansionAnim: ValueAnimator? = null

    /**
     * 是否正在执行智能扩展动画
     */
    private var isSmartExpanding = false

    var isPauseState = false
        internal set

    /**
     * 是否需要隐藏时间轴
     */
    private var isNeedHideTimeLine = false

    /**
     * 最小限制时间
     */
    private var limitMinCropDuration: Long = LimitMinDuration

    private val framePool = CopyOnWriteArrayList<Bitmap>()

    private val cropperRender by lazy {
        // 使用简化的裁剪器渲染器，去除时间显示背景
        // 垂直偏移调整为8dp（时间轴线延伸高度）
        val lineExtension = 8.dp
        SimpleCropperRender(VideoFrameHeight, lineExtension.toFloat())
    }

    private val timeLineRender by lazy {
        // 使用简化的时间轴渲染器，只绘制时间轴线
        // 控件高度现在等于时间轴线的绘制高度
        val lineExtension = 8.dp
        SimpleTimeLineRender((VideoFrameHeight + lineExtension * 2).toFloat())
    }

    /**
     * 视频帧解析Job
     */
    private var parseJob: Job? = null

    /**
     * 默认裁剪时长
     */
    private var defaultCropDuration: Long = 1000

    private var videoUrl: String? = null

    /**
     * 用户是否操作过
     * 用户只要操作过就算是。不用重置。
     */
    var isUserOpted: Boolean = false
        internal set

    /**
     * 用户是否正在操作时间组件
     */
    private var isUserOptTimeView = false

    /**
     *  是否触发Fling 操作
     */
    private var isUserOccurFling = false

    private val gestureListener = object : GestureDetectorPro.SimpleOnGestureListener() {

        var optCropper: Boolean? = null //true 左边 false 右边
        var optTimeLine = false
        var optVideoClip = false
        var isValidOpt = false
        
        override fun onMajorFingerDown(downEvent: MotionEvent?): Boolean {
            val x = downEvent?.x ?: 0f
            val y = downEvent?.y ?: 0f
            // 取消智能扩展动画（如果正在进行）
            cancelSmartExpansionAnimation()
            // 重置变量
            isNeedHideTimeLine = false
            isUserOptTimeView = true
            isUserOccurFling = false
            optCropper = null
            optTimeLine = false
            optVideoClip = false
            isValidOpt = false
            // 是否要操作裁剪区域
            optCropper = when {
                cropperRender.isTouchInLeftCropHandle(x, y) -> true
                cropperRender.isTouchInRightCropHandle(x, y) -> false
                else -> null
            }

            // 是否要操作时间轴
            if (optCropper == null) {
                optTimeLine = timeLineRender.isTouchInTimeLine(x, y)
            }

            // 是否滑动视频片段
            if (optCropper == null && !optTimeLine) {
                optVideoClip = true
            }
            return true
        }

        override fun onMajorScroll(
            downEvent: MotionEvent?,
            moveEvent: MotionEvent?,
            distanceX: Float, distanceY: Float
        ): Boolean {
            // 取消播放
            if (optCropper != null || optTimeLine || optVideoClip) {
                isValidOpt = true
                cancelPlay()
            }
            // 修改位置
            when {
                optCropper != null -> {
                    isUserOpted = true
                    isNeedHideTimeLine = true
                    fixCropperHandlePos(optCropper!!, distanceX)
                    timeLineRender.updateTimeLineRange(cropperRender.cropStartPos, cropperRender.cropEndPos)
                    timeLineRender.offsetTo(0f, cropperRender.cropEndPos - cropperRender.cropStartPos)
                    val time = getTimeInPos(if (optCropper == true) cropperRender.cropStartPos else cropperRender.cropEndPos)
                    onSeekTimeChangeCallback?.invoke(time, true)
                    // 通知裁剪范围变化
                    notifyCropRangeChanged()
                }

                optTimeLine -> {
                    timeLineRender.offsetBy(-distanceX, (cropperRender.cropEndPos - cropperRender.cropStartPos))
                    val time = getTimeInPos(timeLineRender.getCurrentTimeLinePos())
                    onSeekTimeChangeCallback?.invoke(time, true)
                }

                optVideoClip -> {
                    isUserOpted = true
                    isNeedHideTimeLine = true
                    val tempOffset = horizontalOffset + distanceX
                    horizontalOffset = tempOffset.coerceAtLeast(scrollRange[0])
                        .coerceAtMost(scrollRange[1])
                    val time = getTimeInPos(timeLineRender.getCurrentTimeLinePos())
                    onSeekTimeChangeCallback?.invoke(time, false)
                }
            }
            invalidate()
            return true
        }

        override fun onFlingFromLeftToRight(
            downEvent: MotionEvent,
            upEvent: MotionEvent,
            velocityX: Float, velocityY: Float
        ): Boolean {
            // 只有时间轴片段才能触发Fling
            if (optVideoClip) {
                isUserOccurFling = true
                isNeedHideTimeLine = true
                scroller.fling(
                    horizontalOffset.toInt(), 0, -velocityX.toInt(),
                    0, scrollRange[0].toInt(),
                    scrollRange[1].toInt(), 0, 0
                )
                invalidate()
            }
            return true
        }

        override fun onFlingFromRightToLeft(
            downEvent: MotionEvent?,
            upEvent: MotionEvent,
            velocityX: Float, velocityY: Float
        ): Boolean {
            if (optVideoClip) {
                isUserOccurFling = true
                isNeedHideTimeLine = true
                scroller.fling(
                    horizontalOffset.toInt(), 0,
                    -velocityX.toInt(), 0,
                    scrollRange[0].toInt(),
                    scrollRange[1].toInt(),
                    0, 0
                )
                invalidate()
            }
            return true
        }

        override fun onMajorFingerUp(upEvent: MotionEvent?): Boolean {
            isNeedHideTimeLine = false
            isUserOptTimeView = false
            if (isValidOpt) {
                if (optTimeLine) {
                    startPlay(timeLineRender.getCurrentTimeLinePos())
                } else if (optVideoClip || optCropper != null) {
                    startPlay(cropperRender.cropStartPos)
                }
            }
            invalidate()
            return true
        }
    }

    constructor(ctx: Context) : this(ctx, null)
    constructor(ctx: Context, attr: AttributeSet?) : this(ctx, attr, 0)
    constructor(ctx: Context, attr: AttributeSet?, defStyleAttr: Int) : super(ctx, attr, defStyleAttr) {
        gestureDetector = GestureDetectorPro(ctx, gestureListener)
    }

    fun initialWithMediaInfo(
        mediaInfo: MediaInfo?,
        cropTime: Long,
        limitMinCropDuration: Long
    ) {
        mediaInfo ?: return

        this.videoUrl = mediaInfo.mediaPath
        this.videoDuration = mediaInfo.duration.toInt()
        this.limitMinCropDuration = limitMinCropDuration.coerceAtLeast(LimitMinDuration).coerceAtMost(cropTime)
        this.defaultCropDuration = cropTime
        // 初始化
        frameCount = DefaultCropWidth / defaultCropDuration * videoDuration / frameRect.width()

        clipRender.apply { realDrawFrameCount = frameCount }

        (context as? LifecycleOwner)?.lifecycleScope?.launch(Dispatchers.IO) {
            cropperRender.initialCropperRender(DefaultCropWidth, defaultCropDuration)
        }

        timeLineRender.initialTimelineRender(videoDuration.toLong())
        post {
            startParseFrame()
            // 通知初始裁剪范围
            notifyCropRangeChanged()
        }
    }

    private fun startParseFrame() {
        parseJob = (context as? LifecycleOwner)?.lifecycleScope?.launch(Dispatchers.IO) {
            val needFrameCount = ceil(frameCount).toInt()
            var hasStartedPlay = false
            helper.parseVideoFrame(videoUrl!!, needFrameCount, VideoFrameHeight) {
                framePool.add(it)
                // 解析10张之后自动开始播放，但只触发一次
                if (!hasStartedPlay && (framePool.size == needFrameCount || framePool.size == 10)) {
                    hasStartedPlay = true
                    post { startPlay() }
                }
                postInvalidate()
            }
        }
    }

    private fun updateScrollRange() {
        scrollRange[0] = -cropperRender.cropStartPos
        scrollRange[1] = frameCount * frameRect.width().toFloat() - cropperRender.cropEndPos
    }

    private fun updateCropperRange(width: Float) {
        cropperRender.cropStartPos = abs(defaultStartOffset)
        cropperRender.cropEndPos = width - cropperRender.cropStartPos
        timeLineRender.updateTimeLineRange(cropperRender.cropStartPos, cropperRender.cropEndPos)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val width = MeasureSpec.getSize(widthMeasureSpec)
        // 计算高度：与时间轴线的实际绘制高度保持一致
        // 时间轴线实际绘制范围：(paddingTop - 8dp) 到 (paddingTop + frameHeight + 8dp)
        // 控件高度 = 时间轴线绘制高度 = frameHeight + 2 * lineExtension
        val lineExtension = 8.dp
        val height = frameRect.height() + lineExtension * 2
        setMeasuredDimension(width, height)
        updateCropperRange(width.toFloat())
        updateScrollRange()
    }

    override fun onDraw(canvas: Canvas) {
        if (framePool.isEmpty()) return
        
        // 调整绘制位置：控件现在从时间轴线顶部开始
        // 时间轴线延伸8dp，所以视频帧从8dp位置开始绘制
        val lineExtension = 8.dp
        val videoFrameDrawTop = lineExtension.toFloat()
        
        // 绘制视频帧
        clipRender.drawVideoFrame(
            canvas, framePool,
            videoFrameDrawTop,
            horizontalOffset
        )
        
        // 绘制裁剪器
        cropperRender.drawCropper(canvas, horizontalOffset, scrollRange[1])
        
        // 绘制时间轴线
        if (!isNeedHideTimeLine) {
            timeLineRender.drawTimeLine(
                canvas,
                cropperRender.cropStartPos,
                cropperRender.cropEndPos
            )
        }
    }

    /**
     * 调整抵达最小时长的位置，支持智能边界扩展
     */
    private fun fixCropperHandlePos(
        isOptLeftHandle: Boolean,
        distanceX: Float
    ) {
        val minOffset = DefaultCropWidth / defaultCropDuration * limitMinCropDuration
        val maxCropWidth = DefaultCropWidth // 最大可裁剪区域宽度就是DefaultCropWidth

        if (isOptLeftHandle) {
            val realPos = cropperRender.cropStartPos - distanceX
            val minValue = (-horizontalOffset).coerceAtLeast(abs(DefaultStartOffset)).coerceAtMost(abs(scrollRange[0]))
            val newStartPos = realPos.coerceAtLeast(minValue).coerceAtMost(cropperRender.cropEndPos - minOffset)

            // 检查是否触达左边界且满足智能扩展条件
            if (shouldTriggerSmartExpansion(isOptLeftHandle, newStartPos, realPos)) {
                performSmartExpansion(isOptLeftHandle, newStartPos, maxCropWidth)
            } else {
                cropperRender.cropStartPos = newStartPos
            }
        } else {
            val realPos = cropperRender.cropEndPos - distanceX
            val maxValue = (cropperRender.cropEndPos + scrollRange[1] - horizontalOffset)
                .coerceAtMost((DefaultCropWidth - DefaultStartOffset))
            val newEndPos = realPos.coerceAtLeast(cropperRender.cropStartPos + minOffset).coerceAtMost(maxValue)

            // 检查是否触达右边界且满足智能扩展条件
            if (shouldTriggerSmartExpansion(isOptLeftHandle, newEndPos, realPos)) {
                performSmartExpansion(isOptLeftHandle, newEndPos, maxCropWidth)
            } else {
                cropperRender.cropEndPos = newEndPos
            }
        }
        // 更新一下滚动位置
        updateScrollRange()
    }

    /**
     * 检查是否应该触发智能边界扩展
     *
     * @param isOptLeftHandle 是否操作左手柄
     * @param constrainedPos 受约束后的位置
     * @param desiredPos 期望的位置
     * @return 是否应该触发智能扩展
     */
    private fun shouldTriggerSmartExpansion(
        isOptLeftHandle: Boolean,
        constrainedPos: Float,
        desiredPos: Float
    ): Boolean {
        // 条件0: 不在智能扩展动画期间
        if (isSmartExpanding) {
            return false
        }

        // 条件1: 手柄已移动到其最大可移动范围的边界位置
        val isAtBoundary = abs(constrainedPos - desiredPos) > 0.1f

        // 条件1.5: 检查拖拽方向，只有向外扩展时才触发智能扩展
        // 左手柄向右拖拽（缩小）或右手柄向左拖拽（缩小）时不应触发智能扩展
        val isExpandingDirection = if (isOptLeftHandle) {
            // 左手柄：期望位置小于约束位置，说明是向左拖拽（扩展方向）
            desiredPos < constrainedPos
        } else {
            // 右手柄：期望位置大于约束位置，说明是向右拖拽（扩展方向）
            desiredPos > constrainedPos
        }

        // 条件2: 在手柄移动方向上，视频内容边界外仍有未显示的视频帧
        val hasMoreContent = if (isOptLeftHandle) {
            // 左手柄：检查左侧是否还有视频内容
            horizontalOffset > scrollRange[0]
        } else {
            // 右手柄：检查右侧是否还有视频内容
            horizontalOffset < scrollRange[1]
        }

        // 条件3: 当前裁剪区域长度 < 最大可裁剪区域（defaultCropDuration对应的长度）
        // 使用即将更新的位置计算裁剪宽度，而不是使用当前的cropperRender值
        val currentCropWidth = if (isOptLeftHandle) {
            cropperRender.cropEndPos - constrainedPos
        } else {
            constrainedPos - cropperRender.cropStartPos
        }
        val maxCropWidth = DefaultCropWidth // defaultCropDuration对应的最大裁剪宽度就是DefaultCropWidth
        val canExpand = currentCropWidth < maxCropWidth

        val shouldTrigger = isAtBoundary && isExpandingDirection && hasMoreContent && canExpand

        return shouldTrigger
    }

    /**
     * 执行智能边界扩展（带动画）
     *
     * @param isOptLeftHandle 是否操作左手柄
     * @param boundaryPos 边界位置
     * @param maxCropWidth 最大裁剪宽度
     */
    private fun performSmartExpansion(
        isOptLeftHandle: Boolean,
        boundaryPos: Float,
        maxCropWidth: Float
    ) {
        val currentCropWidth = cropperRender.cropEndPos - cropperRender.cropStartPos
        val remainingExpandSpace = maxCropWidth - currentCropWidth

        val scrollDistance = if (isOptLeftHandle) {
            // 左手柄到达左边界：向右滚动视频，向右扩展右手柄
            val availableScrollDistance = horizontalOffset - scrollRange[0]
            // 还需要考虑右手柄扩展后是否会超出屏幕边界
            val maxRightPos = DefaultCropWidth + abs(DefaultStartOffset)
            val maxRightExpansion = maxRightPos - cropperRender.cropEndPos
            min(min(availableScrollDistance, remainingExpandSpace), maxRightExpansion)
        } else {
            // 右手柄到达右边界：向左滚动视频，向左扩展左手柄
            val availableScrollDistance = scrollRange[1] - horizontalOffset
            // 还需要考虑左手柄扩展后是否会超出屏幕边界
            val minLeftPos = abs(DefaultStartOffset)
            val maxLeftExpansion = cropperRender.cropStartPos - minLeftPos
            min(min(availableScrollDistance, remainingExpandSpace), maxLeftExpansion)
        }

        if (scrollDistance > 0) {
            startSmartExpansionAnimation(isOptLeftHandle, boundaryPos, scrollDistance)
        }
    }

    /**
     * 开始智能扩展动画
     *
     * @param isOptLeftHandle 是否操作左手柄
     * @param boundaryPos 边界位置
     * @param scrollDistance 滚动距离
     */
    private fun startSmartExpansionAnimation(
        isOptLeftHandle: Boolean,
        boundaryPos: Float,
        scrollDistance: Float
    ) {
        // 取消之前的动画
        cancelSmartExpansionAnimation()

        // 暂停播放
        val wasPlaying = !isPauseState
        if (wasPlaying) {
            pausePlay()
        }

        // 设置动画状态
        isSmartExpanding = true

        // 记录初始值
        val startHorizontalOffset = horizontalOffset
        val startCropStartPos = cropperRender.cropStartPos
        val startCropEndPos = cropperRender.cropEndPos

        // 计算目标值
        val targetHorizontalOffset = if (isOptLeftHandle) {
            startHorizontalOffset - scrollDistance
        } else {
            startHorizontalOffset + scrollDistance
        }

        val targetCropStartPos = if (isOptLeftHandle) {
            // 左手柄扩展时，左手柄保持在边界位置
            boundaryPos
        } else {
            // 右手柄扩展时，左手柄向左移动，扩展距离等于滚动距离
            startCropStartPos - scrollDistance
        }

        val targetCropEndPos = if (isOptLeftHandle) {
            // 左手柄扩展时，右手柄向右移动，扩展距离等于滚动距离
            startCropEndPos + scrollDistance
        } else {
            // 右手柄扩展时，右手柄保持在边界位置
            boundaryPos
        }

        // 创建动画
        smartExpansionAnim = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = 250L // 250ms动画时长
            interpolator = DecelerateInterpolator()

            addUpdateListener { animator ->
                val progress = animator.animatedValue as Float

                // 插值计算当前值
                horizontalOffset = startHorizontalOffset + (targetHorizontalOffset - startHorizontalOffset) * progress
                cropperRender.cropStartPos = startCropStartPos + (targetCropStartPos - startCropStartPos) * progress
                cropperRender.cropEndPos = startCropEndPos + (targetCropEndPos - startCropEndPos) * progress

                // 更新时间轴范围
                timeLineRender.updateTimeLineRange(cropperRender.cropStartPos, cropperRender.cropEndPos)

                // 重绘界面
                invalidate()

                // 通知时间变化
                val currentHandlePos = if (isOptLeftHandle) cropperRender.cropStartPos else cropperRender.cropEndPos
                val time = getTimeInPos(currentHandlePos)
                onSeekTimeChangeCallback?.invoke(time, true)

                // 通知裁剪范围变化
                notifyCropRangeChanged()
            }

            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 动画结束处理
                    isSmartExpanding = false
                    updateScrollRange()

                    // 如果之前在播放，恢复播放
                    if (wasPlaying) {
                        startPlay(cropperRender.cropStartPos)
                    }

                }

                override fun onAnimationCancel(animation: Animator) {
                    // 动画取消处理
                    isSmartExpanding = false
                    updateScrollRange()

                }
            })
        }

        smartExpansionAnim?.start()
    }

    /**
     * 取消智能扩展动画
     */
    private fun cancelSmartExpansionAnimation() {
        smartExpansionAnim?.let { anim ->
            if (anim.isRunning) {
                anim.cancel()
            }
            smartExpansionAnim = null
        }
    }

    /**
     * 获取对应位置的时间点
     *
     * @param pos 对应位置
     * @return
     */
    private fun getTimeInPos(pos: Float): Float {
        val totalRulerWidth = DefaultCropWidth / (defaultCropDuration.toFloat() / videoDuration)
        return (pos + horizontalOffset) / totalRulerWidth * videoDuration
    }

    /**
     * 通知裁剪范围变化
     */
    private fun notifyCropRangeChanged() {
        // 使用与SimpleCropperRender.drawTime()相同的计算逻辑
        val cropDurationMs = ((cropperRender.cropEndPos - cropperRender.cropStartPos) / DefaultCropWidth * defaultCropDuration).toLong()
        // 最小为1秒
        val cropDurationSeconds = max((cropDurationMs / 1000.0).toInt(), 1)
        onCropRangeChangeCallback?.invoke(cropDurationSeconds)
    }

    fun startPlay(startPos: Float = cropperRender.cropStartPos) {
        if (videoDuration == 0 || isUserOptTimeView || !scroller.isFinished) return

        cancelPlay()
        //开始播放动画
        val endPos = cropperRender.cropEndPos
        val animDuration = ((defaultCropDuration / DefaultCropWidth)
                * (cropperRender.cropEndPos - startPos)).toLong()
        playAnim = ValueAnimator.ofFloat(startPos, endPos).apply {
            interpolator = LinearInterpolator()
            duration = animDuration
            addUpdateListener {
                val value = (it.animatedValue as Float)
                val time = getTimeInPos(value)
                timeLineRender.offsetTo(
                    value - cropperRender.cropStartPos,
                    (endPos - cropperRender.cropStartPos)
                )
                invalidate()
                onSeekTimeChangeCallback?.invoke(time, false)
            }
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    startPlay()
                }
            })
        }
        // 每次开始前先取消动画
        playAnim?.start()
        if (isPauseState) {
            onPlayStateCallback?.invoke(false)
        }
        isPauseState = false
    }

    private fun cancelPlay() {
        if (playAnim == null) return
        playAnim?.removeAllUpdateListeners()
        playAnim?.removeAllListeners()
        playAnim?.cancel()
        playAnim = null
    }

    fun pausePlay() {
        if (!isPauseState) {
            isPauseState = true
            onPlayStateCallback?.invoke(isPauseState)
            cancelPlay()
            invalidate()
        }
    }

    fun resumePlay() {
        if (isPauseState) {
            isPauseState = false
            onPlayStateCallback?.invoke(isPauseState)
            startPlay(timeLineRender.getCurrentTimeLinePos())
        }
    }

    @Override
    override fun computeScroll() {
        super.computeScroll()
        // Fling 停止的时候重新触发自动播放
        if (isUserOccurFling && scroller.isFinished) {
            isNeedHideTimeLine = false
            isUserOccurFling = false
            startPlay()
        } else if (scroller.computeScrollOffset()) {
            isNeedHideTimeLine = true
            horizontalOffset = scroller.currX.toFloat()
            invalidate()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        return gestureDetector.onTouchEvent(event)
    }

    suspend fun cropVideo(srcMediaInfo: MediaInfo, outputPath: String, onProgressChanged: ((progress: Double) -> Unit)? = null): MediaInfo? {
        val startTime = getTimeInPos(cropperRender.cropStartPos)
        val endTime = getTimeInPos(cropperRender.cropEndPos)
        return if (helper.cropVideo(videoUrl!!, outputPath, startTime, endTime, onProgressChanged)) {
            MediaInfo().apply {
                mediaPath = outputPath
                duration = (endTime - startTime).toLong()
                mediaType = MediaType.VIDEO
                width = srcMediaInfo.width
                height = srcMediaInfo.height
                orientation = srcMediaInfo.orientation
            }
        } else {
            null
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 释放资源
        cancelPlay()
        cancelSmartExpansionAnimation()
        parseJob?.cancel()
        timeLineRender.releaseRender()
        framePool.forEach { it.recycle() }
    }
}