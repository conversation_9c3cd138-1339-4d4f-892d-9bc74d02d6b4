package com.commsource.share.facebook

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.graphics.BitmapFactory
import android.net.Uri
import com.commsource.share.common.SnsUtil
import com.facebook.FacebookSdk
import com.facebook.share.internal.ShareFeedContent
import com.facebook.share.model.*
import com.facebook.share.widget.ShareDialog
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream

object ShareHelper {
    /**
     * 分享链接
     */
    fun shareLink(
        context: Context,
        link: String,
        hashtag: String? = null,
        quote: String? = null
    ): Boolean {
        val content =
            ShareLinkContent.Builder()
                .setContentUrl(Uri.parse(link))
                .setShareHashtag(ShareHashtag.Builder().setHashtag(hashtag).build())
                .setQuote(quote)
                .build()
        return share(context, content)
    }

    /**
     *通过磁盘url分享图片，接口支持多张，支持追加文案
     */
    fun sharePhotosUrl(context: Context, path: List<String>, hashtag: String? = null): Boolean {
        val content =
            SharePhotoContent.Builder()
                .setPhotos(
                    path.map { image ->
                        SharePhoto.Builder()
                            .setImageUrl(
                                SnsUtil.getShareUri(context, image)
                            )
                            .build()
                    })
                .setShareHashtag(ShareHashtag.Builder().setHashtag(hashtag).build())
                .build()
        return share(context, content)
    }

    /**
     * 分享视频,仅支持单个视频，支持追加文案
     */
    fun shareVideosUrl(context: Context, videoPath: String, hashtag: String? = null):Boolean{
        val content =
            ShareVideoContent.Builder()
                .setVideo(
                    ShareVideo.Builder().setLocalUrl(SnsUtil.getShareUri(context, videoPath))
                        .build()
                )
                .setShareHashtag(ShareHashtag.Builder().setHashtag(hashtag).build())
                .build()
        return share(context, content)
    }

    fun sharePhotos(
        context: Context,
        drawableIds: List<Int>,
        hashtag: String? = null,
    ): Boolean {
        val content =
            SharePhotoContent.Builder()
                .setPhotos(
                    drawableIds.map { image ->
                        SharePhoto.Builder()
                            .setBitmap(
                                BitmapFactory.decodeResource(
                                    FacebookSdk.getApplicationContext().resources,
                                    image
                                )
                            )
                            .build()
                    })
                .setShareHashtag(ShareHashtag.Builder().setHashtag(hashtag).build())
                .build()
        return share(context, content)
    }

    fun shareVideo(
        context: Context,
        videoResId: Int,
        hashtag: String? = null,
    ): Boolean {
        val content =
            ShareVideoContent.Builder()
                .setVideo(
                    ShareVideo.Builder().setLocalUrl(getResourceUri(resourceId = videoResId))
                        .build()
                )
                .setShareHashtag(ShareHashtag.Builder().setHashtag(hashtag).build())
                .build()
        return share(context, content)
    }

    fun share(
        context: Context,
        content: ShareContent<*, *>,
        mode: ShareDialog.Mode = ShareDialog.Mode.AUTOMATIC
    ): Boolean {
        val activity = getActivity(context) ?: return false
        ShareDialog(activity).show(content, mode)
        return true
    }

    fun getResourceUri(resourceId: Int): Uri {
        val dst = File.createTempFile("temp", null, FacebookSdk.getApplicationContext().cacheDir)
        val input = FacebookSdk.getApplicationContext().resources.openRawResource(resourceId)
        val out: OutputStream = FileOutputStream(dst)

        // Transfer bytes from input to out
        val buf = ByteArray(1024)
        var len: Int
        while (input.read(buf).also { len = it } > 0) {
            out.write(buf, 0, len)
        }
        input.close()
        out.close()
        return Uri.fromFile(dst)
    }

    fun getActivity(context: Context): Activity? {
        var ctx = context
        while (ctx is ContextWrapper) {
            if (ctx is Activity) {
                return ctx
            }
            ctx = ctx.baseContext
        }
        return null
    }
}