package com.commsource.duffle.sticker.search.sticker

import android.net.Uri
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.router.Router
import com.commsource.beautyplus.router.UriConstant
import com.commsource.duffle.sticker.DuffleType
import com.commsource.duffle.sticker.StiCategory
import com.commsource.duffle.sticker.Sticker
import com.commsource.duffle.sticker.StickerRepo
import com.commsource.home.NewHomeViewModel
import com.commsource.library_mvvm.bind.command.BindingCommand
import com.commsource.library_mvvm.bind.command.BindingConsumer
import com.commsource.search_common.entity.KeyWordInfo
import com.commsource.search_common.entity.SearchInfo
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.DecorateConstant
import com.commsource.search_common.viewmodel.BaseSearchViewModel
import com.commsource.studio.sticker.StickerViewModel
import com.commsource.search_common.holder.SearchEmptyHolder
import com.commsource.search_common.holder.SearchRecommendTitleHolder
import com.commsource.studio.sticker.search.SearchTickerDataItemDecoration
import com.commsource.studio.sticker.search.SearchTickerItemHolder
import com.commsource.studio.sticker.search.entity.StickerData
import com.commsource.util.ErrorNotifier
import com.commsource.util.LOGV
import com.commsource.util.common.ProcessUtil
import com.commsource.util.coroutine.launch
import com.commsource.util.set
import com.commsource.widget.recyclerview.PreLoadRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.util.net.NetUtils
import com.scwang.smartrefresh.layout.SmartRefreshLayout
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.ArrayList

class StickerSearchListViewModel(
    val ownerActivity: FragmentActivity,
    val stickerViewModel: StickerViewModel,
    type: String,
    from: Int,//商店页 或者 功能按钮
    val shouldRecommend: Boolean,//是否需要 相关推荐
) : BaseSearchViewModel(
    ownerActivity.application,
    type,
    from,
) {


    var refreshLayout: SmartRefreshLayout? = null


    val getRefreshLayout = BindingCommand<SmartRefreshLayout>(BindingConsumer {
        refreshLayout = it
        refreshLayout?.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {

            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                loadMore<StickerData>(SearchTickerItemHolder::class.java)
            }
        })
    })

    val getRecyclerView = BindingCommand<RecyclerView>(BindingConsumer {
        searchAdapter = PreLoadRecyclerViewAdapter(it.context, onPreload = {
            "预加载".LOGV()
            loadMore<StickerData>(SearchTickerItemHolder::class.java)
        }).apply {
            setOnEntityClickListener(SearchInfo::class.java) { position, entity ->
                if (type == KeyWordInfo.TYPE_STICKERS) {
                    (entity.linkObj as? Sticker)?.let {
                        it.stickerApplyFrom = MTAnalyticsConstant.shop_page
                        stickerViewModel.searchClickSticker.value = it
                    }
                } else {
                    effectSticker(entity)
                }
                clickEvent(entity.linkObj as Sticker, searchWord.get())
                true
            }

        }

        it.layoutManager = GridLayoutManager(it.context, 5).apply {
            spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when {
                        searchAdapter.getItemViewType(position)
                                == SearchRecommendTitleHolder::class.java.hashCode() ||
                                searchAdapter.getItemViewType(position)
                                == SearchEmptyHolder::class.java.hashCode() -> 5

                        else -> 1
                    }
                }
            }
        }
        it.addItemDecoration(SearchTickerDataItemDecoration())
        it.adapter = searchAdapter
    })

    override fun getLimit() = 60

    override fun getNetType() = DuffleType.STI

    override fun onAddMaterial(info: SearchInfo) {

    }


    fun effectSticker(entity: SearchInfo): Boolean {
        //1.若是商店页点击配方或贴纸，跳转到修图页面应用它
        //2。a.若是功能页点击配方，取消面板应用配方,记录页面状态。b.若是点击贴纸，折叠面板应用贴纸
        //检查无网
        // 无网络提示。
        if (ProcessUtil.isProcessing()) {
            return false
        }

        if (!NetUtils.canNetworking(AppContext.context) && (entity.linkObj !is StiCategory)) {
            ErrorNotifier.showNetworkErrorToast()
            return false
        }

        (entity.linkObj as? Sticker)?.run {
            when {
                !needDownload() -> {
                    if (type == KeyWordInfo.TYPE_STICKERS) {
                        stickerViewModel.searchClickApplySticker.value =
                            Pair(searchWord.get(), this)
                    } else {//全局搜索进入
                        ownerActivity?.run {

                            ViewModelProvider(this as FragmentActivity)[NewHomeViewModel::class.java].run {
                                searchApplyEvent.set(
                                    Triple(
                                        entity.keyId,
                                        "BP_cat_STI_SCH",
                                        "搜索，${entity.keyId}"
                                    )
                                )
                            }
                            Router.startRouter(this, Uri.Builder().apply {
                                scheme(UriConstant.BEAUTYPLUS_SCHEME)
                                authority(UriConstant.HOST_P_EDIT)
                                appendPath(UriConstant.PATH_F_STICKERS)
                                appendQueryParameter(UriConstant.KEY_PARAM_CONTENT, entity.keyId)
                                appendQueryParameter(UriConstant.FROM_SEARCH, "from_search")
                            }.build().toString())
                        }
                    }

                }


                !NetUtils.canNetworking(AppContext.context) -> ErrorNotifier.showNetworkErrorToast()
                else -> {
                    stickerViewModel.shouldAutoApplySticker = this
                    StickerRepo.downloadOneSticker(
                        sticker = this, isHighPriority = false,
                        downloadForm = DecorateConstant.DOWNLOAD_FROM_SEARCH
                    )
                }
            }
        }
        return true
    }


    fun initData(
        hasShowMaterial: ArrayList<String>,
        key: String
    ) {
        searchWord.set(key)
        launch({
            withContext(Dispatchers.IO) {
                pageIndex = 2

                hasShowMaterial.forEach {
                    searchShowMap[it] = it
                }

                val dataList = mutableListOf<SearchInfo>()
                StickerSearchListFragment.materials.forEach {
                    dataList.add(it)
                }
                StickerSearchListFragment.materials.clear()

                hasMore = StickerSearchListFragment.hasMoreSticker
                recommendHasMore = shouldRecommend
                hasShowForYou = false

                if (!hasMore && recommendHasMore) {
                    flashRv(dataList, false, SearchTickerItemHolder::class.java)
                    val recommendJob = recommendMaterial<StickerData>(key, false)
                    val recommendList = recommendJob.await()
                    dataList.forEach {
                        allDataList[it.keyId!!] = it
                    }
                    recommendList.forEach {
                        allDataList[it.keyId!!] = it
                    }
                    "贴纸数据 ${dataList.size}  推荐数据 ${recommendList.size}".LOGV()
                    flashRv(
                        recommendList,
                        isLoadMore = true,
                        SearchTickerItemHolder::class.java,
                        showForYou = recommendList.isNotEmpty(),
                    )


                } else {
                    flashRv(dataList, false, SearchTickerItemHolder::class.java)
                    dataList.forEach {
                        allDataList[it.keyId!!] = it
                    }
                }

                withContext(Dispatchers.Main) {
                    refreshLayout?.setEnableLoadMore(hasMore || recommendHasMore)
                }
            }
        })
    }

    override fun onLoadFinish() {
        refreshLayout?.finishLoadMore()
        refreshLayout?.setEnableLoadMore(hasMore || recommendHasMore)
        "是否有更多数据  ${hasMore || recommendHasMore}".LOGV()
    }


    /******************* 统计 **********************/
    fun clickEvent(sticker: Sticker, keyWord: String? = null) {
        val map = HashMap<String, String>().apply {
            if (sticker.isSpecialSticker()) {
                this[MTAnalyticsConstant.KEY_STICKER_CATEGORY_ID] = sticker.specialCategory!!
            } else {
                put(MTAnalyticsConstant.KEY_STICKER_CATEGORY_ID, sticker.categoryId)
            }
            sticker.stickerApplyFrom?.takeIf { it.isNotEmpty() }
                ?.let { source -> put(MTAnalyticsConstant.source, source) }
            val id = if (sticker.isCustom()) Sticker.getCustomStiLogId() else sticker.materialId
            put(MTAnalyticsConstant.KEY_STICKER_ID, id)
            if (type == KeyWordInfo.TYPE_STICKERS) {
                put(MTAnalyticsConstant.source, MTAnalyticsConstant.shop_page)
            } else {
                put(MTAnalyticsConstant.source, MTAnalyticsConstant.home_page_search)
            }
            keyWord?.let { this["word_content"] = it }
            putAll(SPMManager.instance.getCurrentSpmInfo())
        }
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.EVENT_BEAU_CLK_STICKER_USE, map)
    }

}