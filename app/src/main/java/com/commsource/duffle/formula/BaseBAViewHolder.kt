package com.commsource.duffle.formula

import android.content.Context
import android.view.View
import android.view.ViewGroup
import com.commsource.homev2.BaseHomev2ModuleVH
import com.commsource.widget.list.XItem

open class BaseBAViewHolder<T>(context: Context, parent: ViewGroup, layoutId: Int) :
    BaseHomev2ModuleVH<T>(context, parent, layoutId) {

    override fun onBindViewHolder(position: Int, item: XItem<T>, payloads: List<Any>?) {

    }

    open fun isBaResourceLoaded(): Boolean {
        return false
    }

    open fun getBaCompareView(): View {
        return itemView
    }

    open fun isBaEnable(): Boolean {
        return false
    }

    open fun setBACompareAnimState(isStart: Boolean) {

    }

}