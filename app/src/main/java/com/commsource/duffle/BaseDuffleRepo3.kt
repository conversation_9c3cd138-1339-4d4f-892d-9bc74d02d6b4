package com.commsource.duffle

import androidx.lifecycle.LifecycleOwner
import com.commsource.beautyplus.R
import com.commsource.duffle.sticker.DuffleType
import com.commsource.duffle.sticker.LoadMoreOffsetRecorder
import com.commsource.duffle.sticker.MaterialReqType
import com.commsource.duffle.sticker.ReqType
import com.commsource.studio.DecorateConstant
import com.commsource.util.OnlineLocalIntegrationUtils
import com.commsource.util.ResourcesUtils
import com.commsource.util.print
import com.commsource.util.text
import com.meitu.http.kotex.api
import com.meitu.library.util.net.NetUtils
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.Serializable

/**
 * 顶层Duffle 仓库。
 *
 *  主要封装一些子仓库共用的一些方法
 */
abstract class BaseDuffleRepo3<TC : DuffleCategory<TC, M>, M : DuffleMaterial<M, TC>> {
    /**
     * 当前仓库Tag
     */
    abstract val tag: String

    /**
     * 当前仓库版本
     */
    abstract val version: Int

    /**
     * 一级分类的数据库
     */
    abstract val tcDao: DuffleCateDao<TC>?

    /**
     * 三级素材的数据库
     */
    abstract val materialDao: DuffleDao<M>?

    /**
     * DuffleV4 分页请求接口
     */
    protected val duffleApi = DuffleV4Api::class.java.api()

    /**
     * 数据同步器
     */
    open val materialSynchronizer: DuffleEntitySynchronizer<M>? = null

    abstract suspend fun onRepoVersionChange(oldVersion: Int, newVersion: Int)

    private suspend fun requestCategoriesInternal(
        @ReqType reqType: Int,
        typeId: Int,
        clz: Class<TC>,
        cursorKey: String,
        parentCategoryID: String? = null,
        limit: Int = 40,
        isLoadMore: Boolean = true,
        recorder: LoadMoreOffsetRecorder,
        delivery: Int? = 1
    ): Pair<Int?, List<TC>?>? {
        return suspendCancellableCoroutine {
            // 用于分页请求的CursorKey
            var isReachMaxData =
                if (isLoadMore) recorder.reachMaxSignMap[cursorKey] ?: false else false
            val tempOffset = if (isLoadMore) (recorder.offsetMap[cursorKey] ?: 0) else 0

            if (isReachMaxData || !NetUtils.canNetworking()) {
                it.resumeWith(Result.success(null))
            } else {
                val result = when (reqType) {
                    // 分页拉取顶层分类获取
                    ReqType.TOP_CATEGORY -> duffleApi.requestTopCategory(
                        typeId,
                        clz,
                        tempOffset,
                        limit, delivery = delivery
                    )
                    // 分页拉取运营位下的二级包
                    ReqType.LAST_LEVEL, ReqType.HOT, ReqType.PRO, ReqType.FEATURED -> {
                        ">>>>>>> 执行拉取全部的二级分类 tempOffset>>>>>${tempOffset}".print("yyp")
                        duffleApi.requestSubCateByTag(
                            reqType,
                            typeId,
                            clz,
                            tempOffset,
                            limit,
                            delivery = delivery
                        )
                    }
                    // 指定父分类分页拉取子分类。
                    // 指定父分类获取包的方式，可能需要调整。目前这个方式是全量获取的。因为接口还没有支持。
                    ReqType.SEARCH -> {
                        if (parentCategoryID != null) {
                            duffleApi.requestSubCateById(
                                parentCategoryID,
                                typeId,
                                clz,
                                tempOffset,
                                limit, delivery = delivery
                            )
                        } else {
                            null
                        }
                    }

                    else -> null
                }
                isReachMaxData = (result?.second?.size ?: 0) < limit
                recorder.reachMaxSignMap[cursorKey] = isReachMaxData
                recorder.offsetMap[cursorKey] = result?.first ?: 0
                it.resumeWith(Result.success(result))
            }
        }
    }


    private suspend fun requestMaterialInternal(
        @MaterialReqType reqType: Int,
        typeId: Int,
        cursorKey: String,
        clz: Class<M>? = null,
        clz2: Class<out TC>? = null,
        parentId: String? = null,
        limit: Int = 40,
        isLoadMore: Boolean = true,
        upLevel: Int = 0,
        isRequestAll: Boolean = true,
        withSub: Boolean? = null,
        recorder: LoadMoreOffsetRecorder,
        delivery: Int? = 1
    ): Pair<Int?, List<M>?>? {
        return suspendCancellableCoroutine {
            // 用于分页请求的CursorKey
            var isReachMaxData =
                if (isLoadMore) recorder.reachMaxSignMap[cursorKey] ?: false else false
            val tempOffset = if (isLoadMore) (recorder.offsetMap[cursorKey] ?: 0) else 0
            if (isReachMaxData || !NetUtils.canNetworking()) {
                it.resumeWith(Result.success(null))
            } else {
                val result = when (reqType) {
                    MaterialReqType.HOT, MaterialReqType.PRO, MaterialReqType.FEATURED, MaterialReqType.DEFAULT -> {
                        duffleApi.requestMaterialsByTag(
                            reqType, typeId, clz!!,
                            tempOffset, limit, upLevel, withSub = withSub, delivery = delivery
                        )
                    }

                    else -> {
                        if (isRequestAll) {
                            duffleApi.requestAllMaterialsInOneCate(
                                parentId!!,
                                typeId,
                                clz2!!,
                                withSub = withSub,
                                delivery = delivery
                            )
                        } else {
                            duffleApi.requestAllMaterialsInOneCate(
                                parentId!!,
                                typeId,
                                clz2!!,
                                withSub = withSub,
                                delivery = delivery
                            )
                            //  API还有问题。全都走全量拉取。
                            //  duffleApi.requestMaterialsInOneCate(parentId!!, typeId, clz2!!, tempOffset, limit)
                        }
                    }
                }
                isReachMaxData = (result.second?.size ?: 0) < limit
                recorder.reachMaxSignMap[cursorKey] = isReachMaxData
                recorder.offsetMap[cursorKey] = result.first ?: 0
                it.resumeWith(Result.success(result))
            }
        }
    }

    /**
     * 比较特殊的接口，直接查询多个分类下的素材。
     * 适用场景：配方、滤镜列表这类的
     */
    private suspend fun requestMaterialInCategoriesInternal(
        list: List<TC>,
        typeId: Int,
        goodCount: Int = 8,
        withSub: Boolean? = null,
        delivery: Int? = 1
    ): Pair<Int, List<TC>?>? {
        return suspendCancellableCoroutine {
            if (!NetUtils.canNetworking() || list.isEmpty()) {
                it.resumeWith(Result.success(null))
            } else {
                val netResult = duffleApi.requestMaterialsInCategories(
                    list.map { it.categoryId }, typeId,
                    list.elementAt(0)::class.java, goodCount,
                    withSub, delivery = delivery
                )
                it.resumeWith(Result.success(Pair(netResult.first ?: 0, netResult.second)))
            }
        }
    }


    /**
     * 把对象缓存管理器和多个LifecycleOwner 绑定到一起。
     * 当这个仓库所有的LifecycleOwner为0 时，自动清除内存缓存。
     */
    fun bindEntityPoolOwner(key: String, owner: LifecycleOwner) {
        materialSynchronizer?.addOneLifecycle(key, owner)
    }

    /**
     * 获取顶层的分类
     */
    open suspend fun fetchTopCategories(
        typeId: Int,
        clz: Class<TC>,
        limit: Int = 40,
        isLoadMore: Boolean = true,
        cacheFirstWhenFirstLoad: Boolean = true,
        recorder: LoadMoreOffsetRecorder = LoadMoreOffsetRecorder(),
        loadStartCallback: (() -> Unit)? = null,
        isFirLevelCate: Boolean = true, // 是否第一级的分类
        isNeedRequestOnline: Boolean = true,
        delivery: Int? = 1,
        processTopCateInfo: ((data: MutableList<TC>, isCache: Boolean) -> Unit)? = null
    ) = flow {
        // 这边就返回，开始网络加载了，因为回调有两次，不然会先线上无网再loading
        if (isNeedRequestOnline) {
            loadStartCallback?.invoke()
        }

        // 先加载本地数据
        val localResult = mutableListOf<TC>()
        if (!isLoadMore) {
//            DuffleV4RepoConfig.getTopCateInfo(tag)?.forEach {
//                tcDao?.loadEntity(it)?.let { localResult.add(it) }
//            }
            // 先加载本地内置数据。
            tcDao?.loadAllOnlineTopCate()?.sortedBy { it.categorySort }?.let {
                localResult.addAll(it)
            }
            if (localResult.isNotEmpty()) {
                processTopCateInfo?.invoke(localResult, true)
            }
            if (cacheFirstWhenFirstLoad) {
                emit(DataWrapper(true, hasMore = false, data = localResult))
            }
        }
        // 加载线上数据
        if (isNeedRequestOnline) {
            val onLineResult = mutableListOf<TC>()
            val key = "TopCate$tag"
            val netResult = requestCategoriesInternal(
                if (isFirLevelCate) ReqType.TOP_CATEGORY else ReqType.LAST_LEVEL,
                typeId,
                clz,
                key,
                limit = limit,
                isLoadMore = isLoadMore,
                recorder = recorder
            )
            netResult?.second?.takeIf { it.isNotEmpty() }?.let { onLineResult.addAll(it) }
            when {
                onLineResult.isNotEmpty() -> {
                    onLineResult.onEachIndexed { index, tc ->
                        tc.categorySort = index
                        tc.cateLevel = 1
                    }.map { it.categoryId }.let { loadEntityWithKeys(tcDao, it) }?.let {
                        val temp = mutableListOf<TC>().apply { addAll(onLineResult) }
                        OnlineLocalIntegrationUtils.compareEntities(temp, it,
                            object : OnlineLocalIntegrationUtils.CallBack<List<TC>?> {
                                override fun onCallback(
                                    insert: List<TC>?,
                                    update: List<TC>?,
                                    remove: List<TC>?
                                ) {
                                    update?.forEach { tcDao?.update(it) }
                                    insert?.forEach { tcDao?.insert(it) }
                                }
                            })
                    }
                    if (!isLoadMore) {
                        processTopCateInfo?.invoke(onLineResult, false)
                    }
                    val hasMore = (netResult?.first ?: 0) != 0
                    emit(DataWrapper(false, hasMore = hasMore, data = onLineResult))
                }

                !cacheFirstWhenFirstLoad && localResult.isNotEmpty() -> {
                    emit(DataWrapper(true, hasMore = false, data = localResult))
                }

                localResult.isEmpty() -> {
                    emit(null)
                }
            }
        }
    }

    /**
     * 二级分类数据拉取
     */
    suspend fun fetchSubCategories(
        typeId: Int,
        clz: Class<TC>,
        topCate: TC,
        @ReqType reqType: Int? = null,
        limit: Int = 40,
        isLoadMore: Boolean = true,
        cacheFirstWhenFirstLoad: Boolean = true,
        recorder: LoadMoreOffsetRecorder = LoadMoreOffsetRecorder(),
        delivery: Int? = 1
    ) = flow {
        // 加载本地数据
        val localResult = mutableListOf<TC>()
        if (!isLoadMore) {
            topCate.subCateIds?.forEach {
                tcDao?.loadEntity(it)?.let { localResult.add(it) }
            }
            if (localResult.isNotEmpty() && cacheFirstWhenFirstLoad) {
                emit(DataWrapper(true, hasMore = false, data = localResult))
            }
        }
        // 加载线上数据
        val key = topCate.categoryId
        val onlineResult = mutableListOf<TC>()
        val netResult = if (reqType != null) {
            requestCategoriesInternal(
                reqType, typeId, clz, key, limit = limit,
                isLoadMore = isLoadMore, recorder = recorder, delivery = delivery
            )
        } else {
            requestCategoriesInternal(
                ReqType.SEARCH, typeId,
                clz, key, topCate.categoryId,
                limit = limit,
                isLoadMore = isLoadMore,
                recorder = recorder,
                delivery = delivery
            )
        }
        netResult?.second?.takeIf { it.isNotEmpty() }?.let { onlineResult.addAll(it) }

        when {
            onlineResult.isNotEmpty() -> {
                // 记录一下子分类信息。
                topCate.apply {
                    subCateIds = if (isLoadMore) {
                        val temp = (topCate.subCateIds ?: mutableListOf())
                        temp.apply { addAll(onlineResult.map { it.categoryId }) }
                    } else {
                        onlineResult.map { it.categoryId }.toMutableList()
                    }
                    tcDao?.update(this)
                }
                // 比对数据
                onlineResult.onEach { it.cateLevel = 2 }.map { it.categoryId }.let { loadEntityWithKeys(tcDao, it) }?.let {
                    val temp = mutableListOf<TC>().apply { addAll(onlineResult) }
                    OnlineLocalIntegrationUtils.compareEntities(
                        temp, it,
                        object : OnlineLocalIntegrationUtils.CallBack<List<TC>?> {
                            override fun onCallback(
                                insert: List<TC>?,
                                update: List<TC>?,
                                remove: List<TC>?
                            ) {
                                insert?.forEach { tcDao?.insert(it) }
                                update?.forEach { tcDao?.update(it) }
                            }
                        })
                }
                val hasMore = (netResult?.first ?: 0) != 0
                emit(DataWrapper(false, hasMore = hasMore, data = onlineResult))
            }

            !cacheFirstWhenFirstLoad && localResult.isNotEmpty() -> emit(
                DataWrapper(
                    true,
                    hasMore = false,
                    data = localResult
                )
            )

            localResult.isEmpty() -> emit(null)
        }
    }

    /**
     * 根据传入的一级 / 二级分类信息来获取素材。
     * pCate 分类对应的类型
     */
    suspend fun fetchMaterials(
        pCate: TC, typeId: Int, clz: Class<M>? = null,
        @MaterialReqType tagType: Int? = null,
        limit: Int = 40, isLoadMore: Boolean = true,
        cacheFirstWhenFirstLoad: Boolean = true,
        isRequestAll: Boolean = true,
        withSub: Boolean? = null,
        isNeedRequestOnline: Boolean = true,
        recorder: LoadMoreOffsetRecorder = LoadMoreOffsetRecorder(),
        delivery: Int? = 1
    ) = flow {
        // 加载本地数据
        val localResult = mutableListOf<M>()
        if (!isLoadMore) {
            pCate.subMaterials?.forEach {
                materialDao?.loadEntity(it)?.let { localResult.add(it) }
            }
            if (localResult.isNotEmpty()) {
                materialSynchronizer?.rebuildMaterials(localResult, false)
                if (cacheFirstWhenFirstLoad) {
                    emit(DataWrapper(true, hasMore = false, data = localResult))
                }
            }
        }

        // 加载线上数据
        if (isNeedRequestOnline) {
            val key = pCate.categoryId
            val onlineResult = mutableListOf<M>()
            val netResult = if (tagType != null) {
                requestMaterialInternal(
                    tagType, typeId, key, clz, limit = limit,
                    isLoadMore = isLoadMore, upLevel = 1,
                    recorder = recorder, withSub = withSub, delivery = delivery
                ).also {
                    it?.second?.onEach {
                        it.categoryId = it.upCategory?.elementAtOrNull(0)?.categoryId
                            ?: DecorateConstant.CATEGORY_NONE
                    }
                }
            } else {
                requestMaterialInternal(
                    MaterialReqType.SEARCH, typeId, key,
                    parentId = pCate.categoryId,
                    clz2 = pCate::class.java, limit = limit,
                    isLoadMore = isLoadMore, isRequestAll = isRequestAll,
                    withSub = withSub, recorder = recorder, delivery = delivery
                ).also { it?.second?.onEach { it.categoryId = pCate.categoryId } }
            }
            netResult?.second?.takeIf { it.isNotEmpty() }?.let {
                onlineResult.addAll(it)
                pCate.subMaterials = if (isLoadMore) {
                    val temp = (pCate.subMaterials ?: mutableListOf())
                    temp.apply { addAll(onlineResult.map { it.materialId }) }
                } else {
                    onlineResult.map { it.materialId }.toMutableList()
                }
                //优先查询一次本地分类 = insertOrUpdate
                val dbInstance = tcDao?.loadEntity(pCate.categoryId)
                if (dbInstance == null) {
                    tcDao?.insert(pCate)
                } else {
                    tcDao?.update(pCate)
                }
            }

            when {
                onlineResult.isNotEmpty() -> {
                    // 比对一下数据。
                    onlineResult.map { it.materialId }.let { loadEntityWithKeys(materialDao, it) }?.let {
                        val temp = mutableListOf<M>().apply { addAll(onlineResult) }
                        OnlineLocalIntegrationUtils.compareEntities(
                            temp, it,
                            object : OnlineLocalIntegrationUtils.CallBack<List<M>?> {
                                override fun onCallback(
                                    insert: List<M>?,
                                    update: List<M>?,
                                    remove: List<M>?
                                ) {
                                    insert?.forEach { materialDao?.insert(it) }
                                    update?.forEach { materialDao?.update(it) }
                                }
                            })
                    }
                    val hasMore = (netResult?.first ?: 0) != 0
                    materialSynchronizer?.rebuildMaterials(onlineResult, true)
                    emit(DataWrapper(false, hasMore = hasMore, data = onlineResult))
                }

                !cacheFirstWhenFirstLoad && localResult.isNotEmpty() -> emit(
                    DataWrapper(
                        true,
                        hasMore = false,
                        data = localResult
                    )
                )

                localResult.isEmpty() -> emit(null)
            }
        }
    }


    /**
     *  获取多个分类下的素材们。
     *  线上接口那边分类数量有限制，不能超过30个。
     */
    fun fetchMaterialsInCategories(
        list: List<TC>,
        @DuffleType duffleType: Int,
        goodCount: Int = 8,
        cacheFirstWhenFirstLoad: Boolean = true,
        withSub: Boolean? = null,
        delivery: Int? = 1
    ) = flow {
        val tempMap = HashMap<String, TC>()
        // 先拉取缓存的
        var hasCacheData = false
        list.forEach { cate ->
            tempMap[cate.categoryId] = cate
            cate.subMaterials?.takeIf { it.isNotEmpty() }?.take(goodCount)?.let {
                loadEntityWithKeys(materialDao, it)?.let {
                    materialSynchronizer?.rebuildMaterials(it, false)
                    cate.lowerMaterial = it
                    hasCacheData = true
                }
            }
        }
        if (cacheFirstWhenFirstLoad && hasCacheData) {
            emit(DataWrapper(isCacheData = true, hasMore = false, data = list))
        }

        // 拉取在线素材
        val netResult =
            requestMaterialInCategoriesInternal(
                list,
                duffleType,
                goodCount,
                withSub,
                delivery = delivery
            )

        when {
            netResult?.second?.isNotEmpty() == true -> {
                netResult.second?.forEach { onlineCate ->
                    tempMap[onlineCate.categoryId]?.let { srcCate ->
                        val temp = mutableListOf<M>()
                        onlineCate.lowerMaterial?.let { temp.addAll(it) }
                        temp.onEach { it.categoryId = onlineCate.categoryId }.map { it.materialId }
                            .let { loadEntityWithKeys(materialDao, it) }?.let {
                                OnlineLocalIntegrationUtils.compareEntities(
                                    temp,
                                    it,
                                    object : OnlineLocalIntegrationUtils.CallBack<List<M>?> {
                                        override fun onCallback(
                                            insert: List<M>?,
                                            update: List<M>?,
                                            remove: List<M>?
                                        ) {
                                            insert?.forEach { materialDao?.insert(it) }
                                            update?.forEach { materialDao?.update(it) }
                                        }
                                    })
                            }
                        srcCate.lowerMaterial = onlineCate.lowerMaterial
                        srcCate.subMaterials =
                            onlineCate.lowerMaterial?.map { it.materialId }?.toMutableList()
                        insertOrUpdateCateToDb(srcCate)
                    }
                }
                emit(DataWrapper(isCacheData = false, hasMore = false, data = list))
            }

            !cacheFirstWhenFirstLoad && hasCacheData -> {
                emit(DataWrapper(isCacheData = true, hasMore = false, data = list))
            }

            !hasCacheData -> emit(null)
        }

    }


    /**
     * 搜索指定的一级分类 / 二级分类。
     */
    fun searchSpecifyCategory(
        cIds: List<String>,
        typeId: Int,
        clz: Class<TC>,
        isCacheFirst: Boolean = false,
        delivery: Int? = 1
    ): List<TC> {
        val cacheEntities = loadEntityWithKeys(tcDao, cIds)
        if (isCacheFirst && cacheEntities.isNotEmpty()) {
            return cacheEntities
        }
        return duffleApi.searchCategories(cIds, typeId, clz, delivery = delivery)?.also { online ->
            // 对线上数据为空的。不做处理。
            // 不删除本地的数据。
            if (online.isNotEmpty()) {
                OnlineLocalIntegrationUtils.compareEntities(online, cacheEntities,
                    object : OnlineLocalIntegrationUtils.CallBack<List<TC>?> {
                        override fun onCallback(
                            insert: List<TC>?,
                            update: List<TC>?,
                            remove: List<TC>?
                        ) {
                            insert?.forEach { tcDao?.insert(it) }
                            update?.forEach { tcDao?.update(it) }
                            remove?.forEach { tcDao?.delete(it) }
                        }
                    })
            }
        } ?: cacheEntities
    }


    /**
     * 搜索指定的素材。
     */
    fun searchSpecifyMaterial(
        typeId: Int,
        mIds: List<String>,
        clz: Class<M>,
        isCacheFirst: Boolean = false,
        withUpCategory: Boolean = false,
        withSub: Boolean? = null,
        delivery: Int? = 1
    ): List<M> {
        val cacheEntities = loadEntityWithKeys(materialDao, mIds)
        var cacheParents: List<TC>? = null
        // 关联父层级的分类
        if (withUpCategory && cacheEntities.isNotEmpty()) {
            cacheParents = loadEntityWithKeys(tcDao, cacheEntities.map { it.categoryId })
        }
        if (isCacheFirst && cacheEntities.isNotEmpty()) {
            materialSynchronizer?.rebuildMaterials(cacheEntities, false)
            return cacheEntities
        }
        val searchResult = (duffleApi.searchMaterials(
            typeId, mIds, clz,
            upCategoryLevel = if (withUpCategory) 1 else 0,
            withSub = withSub, delivery = delivery
        )?.also { onlines ->
            if (onlines.isNotEmpty()) {
                onlines.onEach {
                    it.categoryId = (it.upCategory?.elementAtOrNull(0)?.categoryId
                        ?: DecorateConstant.CATEGORY_NONE)
                }
                OnlineLocalIntegrationUtils.compareEntities(onlines, cacheEntities,
                    object : OnlineLocalIntegrationUtils.CallBack<List<M>?> {
                        override fun onCallback(
                            insert: List<M>?,
                            update: List<M>?,
                            remove: List<M>?
                        ) {
                            insert?.forEach { materialDao?.insert(it) }
                            update?.forEach { materialDao?.update(it) }
                            remove?.forEach { materialDao?.delete(it) }
                        }
                    })
                // 比对分类。
                if (withUpCategory) {
                    onlines.mapNotNull { it.upCategory?.elementAtOrNull(0) }.let {
                        cacheParents?.let { caches ->
                            OnlineLocalIntegrationUtils.compareEntities(it, caches,
                                object : OnlineLocalIntegrationUtils.CallBack<List<TC>?> {
                                    override fun onCallback(
                                        insert: List<TC>?,
                                        update: List<TC>?,
                                        remove: List<TC>?
                                    ) {
                                        insert?.forEach { tcDao?.insert(it) }
                                        update?.forEach { tcDao?.update(it) }
                                        remove?.forEach { tcDao?.delete(it) }
                                    }
                                })
                        } ?: kotlin.run {
                            it.forEach { tc -> tcDao?.insert(tc) }
                        }
                    }
                }
            }
        }?.toMutableList()?.apply {
            materialSynchronizer?.rebuildMaterials(this, true)
        } ?: cacheEntities.apply {
            materialSynchronizer?.rebuildMaterials(this, false)
        })

        return searchResult
    }


    fun updateInternalTopCateName(list: List<TC>) {
        list.forEach {
            when (it.categoryId) {
                DecorateConstant.NEW_CATEGORY_NEW -> it.categoryName =
                    ResourcesUtils.getString(R.string.ar_new)

                DecorateConstant.NEW_CATEGORY_HOT -> it.categoryName =
                    ResourcesUtils.getString(R.string.v77100_C_2)

                DecorateConstant.NEW_CATEGORY_COLLECT -> it.categoryName =
                    ResourcesUtils.getString(R.string.favorites)

                DecorateConstant.NEW_CATEGORY_RECENT -> it.categoryName = R.string.t_recently.text()

                DecorateConstant.NEW_CATEGORY_RECOMMEND -> it.categoryName =
                    ResourcesUtils.getString(R.string.filter_recommend)

                DecorateConstant.NEW_CATEGORY_VIP -> it.categoryName =
                    ResourcesUtils.getString(R.string.filter_shop_premium)

                DecorateConstant.NEW_CATEGORY_ALL -> it.categoryName = R.string.t_all.text()
            }
        }
    }


    /**
     * 更新素材信息
     */
    fun updateMaterial2Db(material: M) {
        materialDao?.update(material)
    }

    fun updateCategory2Db(category: TC) {
        tcDao?.update(category)
    }

    fun insertMaterial2Db(material: M) {
        materialSynchronizer?.putMaterial2Pool(material)
        materialDao?.insert(material)
    }

    fun insertCate2Db(cate: TC) {
        tcDao?.insert(cate)
    }

    fun loadMaterial(id: String): M? {
        return materialDao?.loadEntity(id)
    }


    fun insertOrUpdateMaterialToDb(material: M) {
        materialDao?.loadEntity(material.materialId)?.let {
            updateMaterial2Db(material)
        } ?: kotlin.run {
            insertMaterial2Db(material)
        }
    }

    fun insertOrUpdateCateToDb(cate: TC) {
        tcDao?.loadEntity(cate.categoryId)?.let {
            updateCategory2Db(cate)
        } ?: kotlin.run {
            insertCate2Db(cate)
        }
    }

    private fun <T> loadEntityWithKeys(duffleDao: DuffleDao<T>?, keys: List<String>?): MutableList<T> {
        val allResult = mutableListOf<T>()
        keys?.takeIf { it.isNotEmpty() }?.let {
            if (it.size > 40) {
                // 拆分多个进行查询
                val count = keys.size / 40
                for (index in 0..count) {
                    val startIndex = index * 40
                    if (startIndex < keys.size) {
                        val endIndex = (startIndex + 40).coerceAtMost(keys.size)
                        val result = duffleDao?.loadEntityInKeys(keys.subList(startIndex, endIndex))
                        if (result?.isNotEmpty() == true) {
                            allResult.addAll(result)
                        }
                    }
                }
                return allResult
            } else {
                val result = duffleDao?.loadEntityInKeys(keys)
                if (result?.isNotEmpty() == true) {
                    allResult.addAll(result)
                }
            }
        }
        return allResult
    }


    /**
     *  请求返回的数据实体
     */
    data class DataWrapper<T>(val isCacheData: Boolean, val hasMore: Boolean, val data: List<T>) :
        Serializable


}