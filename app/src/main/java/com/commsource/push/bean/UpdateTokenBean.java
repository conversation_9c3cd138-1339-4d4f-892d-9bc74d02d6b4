package com.commsource.push.bean;

import java.io.Serializable;

/**
 * 更新Token接口返回结果json实体类
 * Created by lhy on 2017/6/2.
 * <AUTHOR>
 */

public class UpdateTokenBean implements Serializable {
    /**返回编号，仅为0时表示成功返回，其余非0值均为错误返回。*/
    private int code;
    /**提示信息*/
    private String msg;
    /** 请求的唯一标识*/
    private String request_id;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getRequestId() {
        return request_id;
    }

    public void setRequestId(String request_id) {
        this.request_id = request_id;
    }
}
