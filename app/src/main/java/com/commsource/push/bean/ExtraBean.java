package com.commsource.push.bean;

import java.io.Serializable;

/**
 * Created by lhy on 2017/5/23.
 * <AUTHOR>
 */

public class ExtraBean implements Serializable {
    /**缩略图*/
    private String thumbnail;
    /**模式，如拍照有ar模式*/
    private String mode;
    /**主题*/
    private String theme;
    /** 素材key，如ar、二次元、相机等。*/
    private String item;
    /**协议链接*/
    private String url;

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
