/**
 * 
 */
package com.commsource.push;

import com.commsource.beautyplus.R;
import com.commsource.beautyplus.web.WebConstant;
import com.commsource.config.ApplicationConfig;
import com.meitu.library.util.Debug.Debug;

import android.annotation.SuppressLint;
import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

/**
 * 接收通知栏的广播
 * 
 * <AUTHOR>
 * 
 */
@SuppressLint("SimpleDateFormat")
public class NotificationBroadcastReceiver extends BroadcastReceiver {

    /**
     * 十天推送通知栏
     */
    public static final String ACTION_TEN_DAYS_PUSH_NOTIFICATION = "com.commsource.beautyplus.TenDaysPushNotification";
    public static final String DATE_PUSH = "DATE_PUSH";
    public static final int TEN_DAY = 0x01;
    public static final int SEVENTEEN_DAY = 0x02;

    /**
     * 推送通知参数
     */
    public static final String PUSH_NOTIFICATION = "PUSH_NOTIFICATION";

    /*
     * (non-Javadoc)
     * 
     * @see android.content.BroadcastReceiver#onReceive(android.content.Context,
     * android.content.Intent)
     */
    @Override
    public void onReceive(Context context, Intent intent) {

        try {
            if (ACTION_TEN_DAYS_PUSH_NOTIFICATION.equals(intent.getAction())) {
                tenDaysPushNotification(context, intent);
            }
        } catch (Exception e) {
            // TODO: handle exception
            Debug.w(e);
        }
    }

    private void tenDaysPushNotification(Context context, Intent intent) {
        NotificationBarPush notificationBarPush = new NotificationBarPush();

        int date = intent.getIntExtra(DATE_PUSH, 0x00);
        if (date == TEN_DAY) {
            ApplicationConfig.setPushState(context, ApplicationConfig.PUSH_TEN_DAYS);
        } else if (date == SEVENTEEN_DAY) {
            ApplicationConfig.setPushState(context, ApplicationConfig.PUSH_SEVENTEEN_DAYS);
        }

        int contentId;
        int resId = ApplicationConfig.getNextPushResID(context);
        if (resId == 0) {
            contentId = R.string.notification_content1;
            ApplicationConfig.setNextPushResID(context, 1);
        } else if (resId == 1) {
            contentId = R.string.notification_content2;
            ApplicationConfig.setNextPushResID(context, 2);
        } else {
            contentId = R.string.notification_content3;
            ApplicationConfig.setNextPushResID(context, 0);
        }

        notificationBarPush.setTaskId(resId + "");
        notificationBarPush.setTitle(context.getString(R.string.app_name));
        notificationBarPush.setUri(WebConstant.URL_PROTOCOL_BEAUTYPLUS);
        notificationBarPush.setContent(context.getString(contentId));
        notificationBarPush.setPushType(NotificationBarPush.CALLUP_PUSH);

        NotificationSender.notificationBar((NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE),
            context, notificationBarPush);
    }

}
