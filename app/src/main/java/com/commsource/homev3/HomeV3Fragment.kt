package com.commsource.homev3

import android.graphics.Paint
import android.graphics.Rect
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Html
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.commsource.ad.DailyMembershipUnlocker
import com.commsource.ad.adtaskcenter.AdTaskCenterActivity
import com.commsource.ad.adtaskcenter.AdTaskCenterViewModel
import com.commsource.ad.adtaskcenter.rewardcenter.RewardCenter
import com.commsource.advertisiting.FireBaseConfigHelper
import com.commsource.airepair.imagequality.engine.ImageProcessStatus
import com.commsource.airepair.imagequality.repo.ImageRepairRecord
import com.commsource.airepair.imagequality.repo.ImageRepairRecordRepo
import com.commsource.applanuch.HWBusinessLaunchTask
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.BeautyPlusApplication
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentHomeV3Binding
import com.commsource.beautyplus.setting.abtest.ABTestDataEnum
import com.commsource.billing.ProStatusManager
import com.commsource.billing.SubSource
import com.commsource.billing.activity.SubscribeActivity
import com.commsource.billing.activity.SubscribeViewModel
import com.commsource.billing.bean.PriceUtil
import com.commsource.billing.cache.ProCache
import com.commsource.billing.cache.TEST_DOWN_END
import com.commsource.billing.cache.TEST_IN
import com.commsource.billing.cache.TEST_INIT
import com.commsource.billing.cache.TEST_SUBSCRIBE
import com.commsource.billing.cache.getStatus
import com.commsource.billing.pro.GmsManager
import com.commsource.billing.pro.SubsConfigManager
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.SubscribeInfo
import com.commsource.config.ApplicationConfig
import com.commsource.config.SubscribeConfig
import com.commsource.home.NewHomeViewModel
import com.commsource.home.create.FunctionViewModel
import com.commsource.home.work.AiRecordManager
import com.commsource.homev2.HomeContentViewModel
import com.commsource.search_common.view.SearchEditTextView
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.Meepo
import com.commsource.statistics.SpmParamConstant
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.statistics.constant.MTAnalyticsConstant.home_clk_edit
import com.commsource.statistics.constant.MTAnalyticsConstant.topbar_clk
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.BPLocationUtils
import com.commsource.util.BitmapUtils
import com.commsource.util.DateUtils
import com.commsource.util.ErrorNotifier
import com.commsource.util.LOGE
import com.commsource.util.LOGV
import com.commsource.util.LOGV_Camera
import com.commsource.util.LOGV_Pro
import com.commsource.util.LOGV_Pro_Aways
import com.commsource.util.XDrawableFactory
import com.commsource.util.applyGradientToTextView
import com.commsource.util.common.BaseCallback2
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.isVisible
import com.commsource.util.logV
import com.commsource.util.resColor
import com.commsource.util.setHeight
import com.commsource.util.setMarginStart
import com.commsource.util.setSize
import com.commsource.util.sp2dp
import com.commsource.util.string
import com.commsource.util.text
import com.commsource.util.traverseShowViewHolder
import com.commsource.util.visible
import com.commsource.widget.bind.setWidth
import com.commsource.widget.mask.MaskType
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.common.utils.GradientDrawableFactory
import com.meitu.hwbusinesskit.core.HWBusinessSDK
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.SPMShare
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.net.NetUtils
import com.meitu.media.tools.editor.MVEditorTool
import com.pixocial.androidx.core.extension.paramsWidth
import com.pixocial.androidx.core.extension.setMargins
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

class HomeV3Fragment : BaseBottomSubFragment() {

    private val binding by lazy {
        FragmentHomeV3Binding.inflate(layoutInflater)
    }

    private val baseAdapter by lazy {
        BaseRecyclerViewAdapter(ownerActivity)
    }


    private val viewModel by lazy {
        ViewModelProvider(ownerActivity)[HomeV3ViewModel::class.java]
    }

    private val homeViewModel by lazy {
        ViewModelProvider(ownerActivity)[NewHomeViewModel::class.java]
    }

    private val functionViewModel by lazy {
        ViewModelProvider(ownerActivity)[FunctionViewModel::class.java]
    }

    private val contentViewModel by lazy {
        ViewModelProvider(ownerActivity)[HomeContentViewModel::class.java]
    }

    /**
     * 订阅状态
     */
    private val subscribeViewModel by lazy {
        ViewModelProvider(ownerActivity)[SubscribeViewModel::class.java]
    }

    private val adTaskCenterViewModel by lazy {
        ViewModelProvider(
            ownerActivity.application as BeautyPlusApplication,
            ViewModelProvider.AndroidViewModelFactory.getInstance(ownerActivity.application)
        )[AdTaskCenterViewModel::class.java]
    }

    private var transitionHelper: HomeUITransitionHelper? = null
    private val topBannerHolder by lazy {
        TopBannerUIHolder(ownerActivity as BaseActivity, binding)
    }

    private val dailyRewardUIHolder by lazy {
        DailyRewardUIHolder(ownerActivity as BaseActivity, binding)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        dailyRewardUIHolder.init()
        initialContentRV()
        initialSearchBar()
        initialObserver()
        initialData()
        initNewUserView()
        initAdTaskCenterEntrance()

        tryEnterAdMobTest()

        if (!SubscribeConfig.isSubValid()) {
            Meepo.enterTest(ABTestDataEnum.HOME_PRO_VIEW_REF, ABTestDataEnum.HOME_PRO_VIEW_TEST)
        }

        if (Meepo.isInHomeProViewTest()) {
            binding.proView.setIconGravity(Gravity.START)
            binding.proView.setMarginStart(16.dp)
        }

        AiRecordManager
    }

    private fun initialData() {
        lifecycle.addObserver(homeViewModel)
        if (needShowDefaultHomeData()) {
            // 新用户流程优化实验，用户未同意协议前显示本地兜底数据
            val uiState = HomeV3ViewModel.UIState(
                topBanner = HomeLocal.topBanner,
                kingKong = HomeLocal.kingKong,
                aiCreative = HomeLocal.aiCreative
            )
            viewModel.dataEvent.value = uiState
        } else {
            loadHomeData()
        }
        viewModel.requestHotSearchWord(this@HomeV3Fragment)
    }

    /**
     * hasDefaultData 是否存在默认兜底数据（新用户流程实验）
     */
    private fun loadHomeData(hasDefaultData: Boolean = false) {
        viewModel.startLoadHomeData(hasDefaultData = hasDefaultData)
    }

    private fun needShowDefaultHomeData(): Boolean {
        return Meepo.isNewUserOnboardingTest() && ApplicationConfig.shouldShowProtocol(AppContext.context)
    }

    private fun initialObserver() {
        binding.personalStudio.setOnClickListener {
            MTAnalyticsAgent.logEvent(topbar_clk, "type", "me")
            HomeRouter.toPersonalWork(requireActivity())
        }

        binding.photoContent.setOnClickListener {
            functionViewModel.enterStudioEditEvent.value = true
            MTAnalyticsAgent.logEvent(home_clk_edit, "type", "photo")
        }

        binding.portraitContent.setOnClickListener {
            functionViewModel.enterStudioEditEvent.value = false
            MTAnalyticsAgent.logEvent(home_clk_edit, "type", "portrait")
        }

        lifecycleScope.launch {
            subscribeViewModel.innerViewModel.homeUiStateFlow.collect {
                binding.proView.homeSubscribeUiState = it
            }
        }
        subscribeViewModel.innerViewModel.countDownTextEvent.observe(viewLifecycleOwner) {
            binding.proView.setDiscount(it)
        }

        binding.proView.setOnClickListener {
            MTAnalyticsAgent.logEvent(topbar_clk, "type", "vip")
            SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "默认入口")
            SubscribeActivity.startActivity(ownerActivity, SubSource.FROM_HOME)
        }
        // 订阅用户类型
        SubscribeInfo.instance.notifyUseTypeChanged.observe(viewLifecycleOwner,
            object : NoStickLiveData.CustomObserver<Boolean?>() {
                override fun onAccept(result: Boolean?) {
                    if (result == true) {
                        subscribeViewModel.initUserType()
                    }
                }
            })

        // 订阅通知
        GmsManager.instance.subscribeResultEvent.observe(viewLifecycleOwner) {
            baseAdapter.notifyAllItemChange()
        }

        viewModel.recordStateChangeListener.observe(viewLifecycleOwner) {
            viewModel.dataEvent.value?.kingKong?.elementAtOrNull(0)?.let {
                baseAdapter.notifyItemChanged(it)
            }
        }

        lifecycleScope.launchWhenCreated {
            combine(viewModel.personalWorkHasNew, viewModel.isShowSearch) { hasNew, showSearch ->
                hasNew && !showSearch
            }.collect {
                binding.vPersonalStudioRed.visibility = if (it) View.VISIBLE else View.GONE

            }

        }

        //定位到顶部
        contentViewModel.focusHomeTopEvent.observe(viewLifecycleOwner) {
            binding.rvContent.scrollToPosition(0)
            binding.appBarLayout.setExpanded(true, true)
        }


        subscribeViewModel.subSuccessEvent.observe(requireActivity()) {
            //刷新功能item
            subscribeViewModel.innerViewModel.checkSubscribed()
            "通知功能区刷新 ${SubscribeConfig.isSubValid()}".LOGV_Pro()
            GmsManager.instance.postSubscribeResult(SubscribeConfig.isSubValid())
            subscribeViewModel.setNeedLogSubSuccess(false)
        }

        homeViewModel.analyzeEvent.observe(viewLifecycleOwner) {
            viewModel.clearContentShowSet()
            dailyRewardUIHolder.resetLogStatus()
            if (it) {
                notifyRvTraverseState()
            }
        }

        homeViewModel.applyAllSearchEvent.observe(viewLifecycleOwner) {
            viewModel.isShowSearch.value = it
            if (!it) {
                transitionHelper?.resumeSearch2LastState()
                // 取消焦点
                binding.searchBg.gone()
                binding.searchEdit.setText("")
                binding.searchEdit.restartScroll()
                binding.searchEdit.requestEditFocus(false, "")
                topBannerHolder.setAutoScrollState(false)
            }
        }

        homeViewModel.setSearchWord.observe(viewLifecycleOwner) {
            it?.run {
                binding.searchEdit.setText(this)
                binding.searchEdit.requestEditFocus(false, "")
            }
        }

        functionViewModel.showMainFunctionEvent.observe(viewLifecycleOwner) {
            if (it?.fgClazz == HomeV3Fragment::class.java) {
                topBannerHolder.setAutoScrollState(false)
            } else {
                topBannerHolder.setAutoScrollState(true)
            }
        }

        homeViewModel.closeInputEvent.observe(viewLifecycleOwner) {
            binding.searchEdit.requestEditFocus(false)
        }

        homeViewModel.searchApplyEvent.observe(viewLifecycleOwner) {
            it?.run {
                SPMManager.instance.getTopModel()?.let {
                    first?.run {
                        it.addExtraInfo(SpmParamConstant.KEY_MIDS_MATERIAL, this)
                    }

                    second?.run {
                        it.addExtraInfo(SpmParamConstant.KEY_MIDS_MATERIAL_TAG, this)
                    }
                    it.content = third
                }
            }
        }

        viewModel.requestStateEvent.observe(viewLifecycleOwner) {
            when (it) {
                null -> {
                    binding.netMask.gone()
                    binding.netMask.hideAll()
                }

                true -> {
                    binding.netMask.visible()
                    binding.netMask.showMask(MaskType.Loading)
                    binding.netMask.getMask(MaskType.Loading)?.view
                        ?.setBackgroundColor(R.color.Gray_O.resColor())
                }

                else -> {
                    binding.netMask.visible()
                    binding.netMask.showMask(MaskType.NetError)
                    binding.netMask.getMask(MaskType.NetError)?.view
                        ?.setBackgroundColor(R.color.Gray_O.resColor())
                }
            }
        }

        homeViewModel.clickBlankEvent.observe(viewLifecycleOwner) {
            binding.searchEdit.requestEditFocus(false, "")
            binding.searchEdit.restartScroll()
        }

        homeViewModel.userApproveProtocolEvent.observe(viewLifecycleOwner) {
            // 用户同意协议后重新拉取首页数据
            loadHomeData(hasDefaultData = true)
        }

        viewModel.keyWordResultEvent.observe(viewLifecycleOwner) {
            if (!SubscribeConfig.isSubValid() && Meepo.isInHomeProViewTest()) {
                binding.searchEdit.setSearchHintVisible(false)
                binding.searchEdit.hideHotLoopTextView()
                return@observe
            }

            if (it.isNullOrEmpty()) {
                binding.searchEdit.setSearchHintVisible(false)
            } else {
                homeViewModel.loadHotEvent.value = true
                binding.searchEdit.setHotWordList(it)
                if (it.size > 1) {
                    //启动定时轮播热搜词,初次拉取才生效
                    if (binding.searchEdit.checkTextLoop()) {
                        binding.searchEdit.initShowText(isRandom = false)
                    } else {
                        binding.searchEdit.setHotLoopTextList()
                    }
                    if (binding.searchEdit.hasFocus()) {
                        binding.searchEdit.stopScroll()
                    }
                } else {
                    binding.searchEdit.setSearchHintVisible(true)
                }
            }
        }

        // 加载首页的数据。
        viewModel.dataEvent.observe(viewLifecycleOwner) {
            // Viewpager 数据更新
            //存在没有google支付，导致价格查询失败。因此没显示cvNewUserOff
            topBannerHolder.updateTopVPData(it, binding.cvNewUserOff.isVisible)
            updateRVContent(it)
            // 更数据的时候先屏蔽操作
            binding.optionBarrier.visible()
            binding.rvContent.post {
                binding.appBarLayout.setExpanded(true, false)
                binding.rvContent.scrollToPosition(0)
                transitionHelper?.release()
                transitionHelper = HomeUITransitionHelper(binding, it)
                binding.optionBarrier.gone()
            }
        }

        // 监听显示搜索页事件
        homeViewModel.displayHomeAllSearchEvent.observe(viewLifecycleOwner) {
            onSearchClick()
        }

        // 画质修复相关
        binding.llImageQualityResult.background =
            GradientDrawableFactory.createDrawable(R.color.color_80000000.resColor(), 12.dpf)
        binding.tvGoToTaskManager.background =
            GradientDrawableFactory.createDrawable(R.color.white.resColor(), 12.dpf)
        binding.tvGoToTaskManager.setOnClickListener {
            HomeRouter.toPersonalWork(ownerActivity, true)
        }
        binding.llImageQualityResult.setOnClickListener {
            HomeRouter.toPersonalWork(ownerActivity, true)
        }
        binding.ivCloseImageQualityNotifier.setOnClickListener {
            binding.llImageQualityResult.gone()
        }

        ImageRepairRecordRepo.latestNotifiedImageRepairRecordEvent.observe(viewLifecycleOwner) { record ->
            if (record is ImageRepairRecord) {
                if (
                    !binding.dailyRewardContainer.isVisible
                    || (dailyRewardUIHolder.isCollapseState || dailyRewardUIHolder.isNeedAddToRecyclerView) // 可见，但已经点击关闭（将）会吸附到底部
                ) {
                    if (record.status != ImageProcessStatus.PROCESSED
                        && record.status != ImageProcessStatus.UPLOAD_FAILED
                        && record.status != ImageProcessStatus.PROCESS_FAILED
                    ) {
                        return@observe
                    }

                    if (record.notifiedInApp) {
                        return@observe
                    }

                    lifecycleScope.launch {
                        record.notifiedInApp = true
                        ImageRepairRecordRepo.updateRecordToDb(record)
                    }

                    binding.llImageQualityResult.visible()
                    binding.ivImageQualityPictureContainer.visible()

                    val bitmap = if (record.isVideoType()) {
                        if (record.coverPath == null) {
                            MVEditorTool.getFirstFrame(ownerActivity, record.localFilePath)
                        } else {
                            record.coverPath?.let {
                                BitmapUtils.loadBitmapFromSDcard(it)
                            }
                        }
                    } else {
                        record.coverPath?.let {
                            BitmapUtils.loadBitmapFromSDcard(it)
                        }
                    }
                    when (record.status) {
                        ImageProcessStatus.PROCESSED -> {
                            if (record.isVideoType()) {
                                binding.tvImageQualityResult.text = R.string.v7120_B_27.string()
                            } else {
                                binding.tvImageQualityResult.text = R.string.v7120_B_26.string()
                            }

                            if (bitmap != null) {
                                binding.ivImageQualityResultPic.setImageBitmap(bitmap)
                                binding.ivImageQualityResultPicMask.gone()
                            } else {
                                if (record.localFilePath == null) {
                                    // 卸载重装的情况
                                    if (record.uploadedUrl != null) {
                                        Glide.with(this)
                                            .load(record.uploadedUrl)
                                            .frame(0)
                                            .into(binding.ivImageQualityResultPic)
                                    } else {
                                        binding.ivImageQualityPictureContainer.gone()
                                    }
                                } else {
                                    binding.ivImageQualityResultPic.setImageBitmap(null)
                                    binding.ivImageQualityResultPicMask.visible()
                                }
                            }
                            binding.ivImageQualityResultWrongIcon.gone()
                        }

                        ImageProcessStatus.UPLOAD_FAILED,
                        ImageProcessStatus.PROCESS_FAILED -> {
                            if (record.isVideoType()) {
                                binding.tvImageQualityResult.text = R.string.v7120_B_29.string()
                            } else {
                                binding.tvImageQualityResult.text = R.string.v7120_B_28.string()
                            }

                            binding.ivImageQualityResultWrongIcon.visible()
                            if (bitmap != null) {
                                binding.ivImageQualityResultPic.setImageBitmap(bitmap)
                                binding.ivImageQualityResultPicMask.visible()
                            } else {
                                if (record.localFilePath == null) {
                                    // 卸载重装的情况
                                    binding.ivImageQualityPictureContainer.gone()
                                } else {
                                    binding.ivImageQualityResultPic.setImageBitmap(null)
                                    binding.ivImageQualityResultPicMask.gone()
                                }
                            }
                        }
                    }
                }
            }
        }

        ImageRepairRecordRepo.linkToRepairWorkPageEvent.observe(viewLifecycleOwner) {
            binding.llImageQualityResult.gone()
        }
    }

    override fun onStop() {
        super.onStop()
        binding.llImageQualityResult.gone()
    }

    /**
     * Admob聚合实验
     */
    private fun tryEnterAdMobTest() {
        if (!SubscribeConfig.isSubValid()) {
            Meepo.enterTest(
                ABTestDataEnum.ADMOB_MEDIATIONS_REF,
                ABTestDataEnum.ADMOB_MEDIATIONS_TEST
            )
        }
        HWBusinessSDK.filterBusinessExtra(HWBusinessLaunchTask.adSlotPlatformFilterForVersion712)
    }

    private fun initialSearchBar() {
        if (!SubscribeConfig.isSubValid() && Meepo.isInHomeProViewTest()) {
            binding.searchEdit.setWidth(32.dp)
            binding.searchEdit.setSearchIconStartMargin(1.dp)
            binding.searchEdit.viewBinding.searchIcon.setTextColor(R.color.Gray_A.resColor())
        } else {
            binding.searchEdit.setSearchIconStartMargin(6.dp)
        }

        // 取消搜索
        binding.searchEdit.switchWhiteBg()
        binding.tvCancelSearch.setOnClickListener {
            homeViewModel.applyAllSearchEvent.value = false
            homeViewModel.cancelSearchEvent.value = true
        }

        // 点击搜索。
        binding.searchEdit.setOnClickSearchLayoutListener(object :
            SearchEditTextView.OnClickSearchLayoutListener {

            override fun onClearClick() {
                //如果当前在搜索loading状态，取消loading
                homeViewModel.delWord.value = true
            }

            override fun onCancelClick() {

            }


            override fun onSearchClick(hotWord: String?) {
                onSearchClick()
            }

            override fun onInvokeSearch(searchWord: String?) {
                if (searchWord?.isBlank() == false) {
                    "search $searchWord".LOGV()
                    homeViewModel.searchWord.value = searchWord
                }
            }

            override fun onInputting(searchWord: String) {
                homeViewModel.inputEvent.value = searchWord
            }
        })
    }

    private fun onSearchClick() {
        // 还在动画之中
        if (transitionHelper?.isInUITransition() == true) {
            return
        }
        // 已经进入搜索界面
        if (homeViewModel.applyAllSearchEvent.value == true) {
            binding.searchEdit.stopScroll()
            binding.searchEdit.requestEditFocus(true)
            homeViewModel.inputEvent.value = binding.searchEdit.getText()
            return
        }
        MTAnalyticsAgent.logEvent(
            MTAnalyticsConstant.material_search_button_clk,
            HashMap<String, String>(4).apply {
                put(MTAnalyticsConstant.source, MTAnalyticsConstant.home_page_search)
                put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.all)
            })
        viewModel.requestHotSearchWord(this@HomeV3Fragment)
        viewModel.clearContentShowSet()
        dailyRewardUIHolder.resetLogStatus()
        //停止轮播热搜词
        binding.searchBg.visible()
        binding.searchEdit.stopScroll()
        transitionHelper?.expandSearchBar()
        homeViewModel.applyAllSearchEvent.value = true
        binding.searchEdit.requestEditFocus(true, "")
        topBannerHolder.setAutoScrollState(true)
    }

    private fun initialContentRV() {
        updatePhotoPortraitUi()
        topBannerHolder.initialTopBanner()
        binding.rvContent.apply {
            layoutManager = LinearLayoutManager(ownerActivity)
            adapter = baseAdapter
        }
        // 计算屏幕高度是否足够
        binding.root.post {
            val tempHeight = DeviceUtils.getScreenWidth() / 390f * 270f + 477.dp
            if (binding.root.height - tempHeight < 60.dp) {
                baseAdapter.tag = true
            }
        }

        binding.appBarLayout.addOnOffsetChangedListener { _, _ ->
            notifyRvTraverseState()
        }

        binding.rvContent.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            var scrolledDy = 0
            var hasReported = false

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                scrolledDy += dy
                if (scrolledDy >= 150.dp && !hasReported) {
                    hasReported = true
                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.home_second_page_appr,
                        "type",
                        "home"
                    )
                }
                notifyRvTraverseState()
            }
        })

        //添加数据mask的基础监听回调
        binding.netMask.maskContainerHelper.newBuilder()
            .bindView(MaskType.NetError, R.id.tv_action) {
                if (NetUtils.canNetworking(AppContext.context)) {
                    viewModel.startLoadHomeData(false)
                    subscribeViewModel.initUserType()
                } else {
                    ErrorNotifier.showNetworkErrorToast()
                }
            }.build()
    }

    private fun updatePhotoPortraitUi() {
        val maxWidth = (DeviceUtils.getScreenWidth() - 100.dp) / 2 - 56.dp
        binding.photo.maxWidth = maxWidth
        binding.portrait.maxWidth = maxWidth

        val width = (DeviceUtils.getScreenWidth() - 48.dp) / 2
        binding.portraitContent.paramsWidth = width
        binding.photoContent.paramsWidth = width
    }

    override fun onResume() {
        super.onResume()

        tryEnterAdTaskTest("onResume")
    }

    private val callback = BaseCallback2<Int, RecyclerView.ViewHolder> { state, holder ->
        (holder as? BaseHomeStateHolder<*>)?.holderState = state
    }

    private fun notifyRvTraverseState() {
        topBannerHolder.analyzeIfShowComplete()
        binding.rvContent.traverseShowViewHolder(
            false,
            excludeBottom = 59.dp,
            callback2 = callback
        )
        binding.rvContent.adapter?.let {
            for (index in 0 until it.itemCount) {
                (binding.rvContent.findViewHolderForAdapterPosition(index)
                        as? BaseHomeStateHolder<*>)?.onNotifyScrollY()
            }
        }

        // 展示统计
        dailyRewardUIHolder.analyzeHomeContentShow()
    }

    override fun onSupportVisible() {
        super.onSupportVisible()
        binding.rvContent.post {
            if (functionViewModel.isShowMain()) {
                if (ProStatusManager.inNewUserSubscribe() && binding.cvNewUserOff.isVisible) {
                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.HOME_BANNER_CONTENT_SHOW_F,
                        hashMapOf<String, String>().apply {
                            put("模块位置", "-3")
                            put("模块ID", "0")
                            put("模块类型", "横幅")
                            put("内容类型", "首页订阅横幅")
                        })
                } else if (binding.llAdTaskCenterEntrance.isVisible) {
                    MTAnalyticsAgent.logEvent(MTAnalyticsConstant.homepage_ad_task_imp)
                }
            }
        }
    }

    private fun updateRVContent(uiState: HomeV3ViewModel.UIState) {
        if (binding.rvContent.itemDecorationCount > 0) {
            binding.rvContent.removeItemDecorationAt(0)
        }
        val hasRVData = !uiState.aiCreative.isNullOrEmpty() || !uiState.kingKong.isNullOrEmpty()
        if (hasRVData) {
            binding.rvContent.addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect, view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    (parent.adapter as? BaseRecyclerViewAdapter)?.let { adapter ->
                        val position = parent.getChildAdapterPosition(view)
                        if (position == adapter.itemCount - 1) {
                            val holder = parent.getChildViewHolder(view)
                            if (holder is DailyRewardViewHolder) {
                                outRect.bottom = 58.dp
                            } else {
                                outRect.bottom = 70.dp
                            }
                        } else {
                            outRect.bottom = 24.dp
                        }
                    }
                }
            })
        }

        //更新数据。
        baseAdapter.updateItemEntities(AdapterDataBuilder.create().apply {
            uiState.kingKong?.let { features ->
                addEntities(features, KingKongAreaHolder::class.java)
            }
            uiState.aiCreative?.let { aiCreative ->
                addEntities(aiCreative, AiCreativeHolder::class.java)
            }
            if (DailyMembershipUnlocker.shouldShowAd()) {
                addEntities(
                    listOf(DailyRewardUIHolder.REWARD_HOLDER_ID),
                    DailyRewardViewHolder::class.java
                )
            }
        }.build(), true)
    }

    private fun initNewUserView() {

        subscribeViewModel.loadPriceEvent.observe(viewLifecycleOwner) {
            if (canShowNewUserPro()) {
                "显示新用户订阅横幅".LOGV_Pro()

                val offPercent = PriceUtil.getYearlyOff(it, null)?.replace("%", "")
                ProCache.initNewUserSubscribeOffPercent(offPercent)

                val discount = if (ProStatusManager.supportWeekSubscribe()) {
                    PriceUtil.getYearVsWeekSave(it)
                } else {
                    var discountTmp = PriceUtil.getAnnualDiscount(it)
                    val introductoryDiscount = PriceUtil.getIntroductoryAnnualDiscount(it)
                    if (!TextUtils.isEmpty(introductoryDiscount) && it.isYearPeriodicity) {
                        discountTmp = introductoryDiscount
                    }
                    discountTmp
                }
                ProCache.initNewUserSubscribeDiscountPercent(discount)

                if (!binding.cvNewUserOff.isVisible) {
                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.HOME_BANNER_CONTENT_SHOW_F,
                        hashMapOf<String, String>().apply {
                            put("模块位置", "-3")
                            put("模块ID", "0")
                            put("模块类型", "横幅")
                            put("内容类型", "首页订阅横幅")
                        })
                }
                binding.cvNewUserOff.visible()
                binding.ivNewUserDiamond.setMargins(bottom = (-34).dp)
                binding.tvNewUserOff.text = R.string.v77100_A_1.text("${offPercent}%")

                binding.tvNewUserDetail.text = Html.fromHtml(
                    context?.getString(
                        R.string.t_pro_price,
                        String.format(
                            R.string.v77100_A_3.text(), PriceUtil.getPrice(
                                it?.yearlyPrice,
                                12
                            ), PriceUtil.getPrice(
                                it?.yearlyPrice,
                                1
                            )
                        ),
                        PriceUtil.getPrice(it?.yearlyFullPrice, 1)
                    )
                )

                binding.tvNewUserOff.post {
                    fixSize(
                        binding.tvNewUserOff.text.toString(),
                        binding.tvNewUserDetail.text.toString()
                    )
                }
                binding.cvNewUserOff.setOnClickListener {

                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.home_content_clk,
                        hashMapOf<String, String>().apply {
                            put("模块位置", "-3")
                            put("模块ID", "0")
                            put("模块类型", "横幅")
                            put("内容类型", "首页订阅横幅")
                        })

                    SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "首页订阅横幅")

                    subscribe()
                }
                startCountDownTimer(
                    ProCache.getNewUserSubscribeEndTime() - System.currentTimeMillis(),
                    onTick = {
                        binding.tvNewUserTime.text = it
                    },
                    onFinish = {
                        "不显示横幅".LOGV_Pro()
                        ProCache.setNewUserSubscribeStatus(TEST_DOWN_END)
                        viewModel.dataEvent.value?.run {
                            topBannerHolder.updateTopVPData(this, canShowNewUserPro())
                        }

                        binding.cvNewUserOff.gone()
                    })

            }

            viewModel.dataEvent.value?.run {
                topBannerHolder.updateTopVPData(this, canShowNewUserPro())
            }

        }

        subscribeViewModel.subSuccessEvent.observe(requireActivity()) {
            "订阅成功".LOGV_Pro()
            ProCache.setNewUserSubscribeStatus(TEST_SUBSCRIBE)
            subscribeViewModel.innerViewModel.checkSubscribed()
            viewModel.dataEvent.value?.run {
                topBannerHolder.updateTopVPData(this, canShowNewUserPro())
            }
            binding.cvNewUserOff.gone()

            //刷新功能item
            "通知功能区刷新 ${SubscribeConfig.isSubValid()}".LOGV_Pro()
            GmsManager.instance.postSubscribeResult(SubscribeConfig.isSubValid())
            subscribeViewModel.setNeedLogSubSuccess(false)
        }

        subscribeViewModel.userTypeEvent.observe(viewLifecycleOwner) {
            ("是否新用户 ： ${subscribeViewModel.subUserState.isNewUser} 订阅 ${SubscribeConfig.isSubValid()}  非美日英 ${!BPLocationUtils.isAJE()}  " +
                    "首次安装 ${ProCache.isNewUserSubscribeFirstInstall()}  " +
                    "当前实验状态 ${
                        ProCache.getNewUserSubscribeStatus().getStatus()
                    }").LOGV_Pro_Aways()
            //新用户订阅优化相关
            if (subscribeViewModel.subUserState.isNewUser && !SubscribeConfig.isSubValid()
                && ProCache.isNewUserSubscribeFirstInstall()
                && ProCache.getNewUserSubscribeStatus() == TEST_INIT
                && !BPLocationUtils.isAJE()
            ) {
                ProCache.setNewUserSubscribeStatus(TEST_IN)
            }
            ProCache.setNewUserSubscribeFirstInstall()
        }

    }

    private fun initAdTaskCenterEntrance() {
        RewardCenter.init()

//        subscribeViewModel.userTypeEvent.observe(viewLifecycleOwner) {
//            // TODO，跟测试确认，这个条件是否可以移除了，因为只有在测试环境下，才会同时是新用户又拿到Code，否则这个Code是会过滤掉新用户的
//            if (subscribeViewModel.subUserState.isNewUser) {
//                "new user".LOGV()
//                dailyRewardUIHolder.enableThisFeature = true
//                return@observe
//            }
//
//            if (binding.dailyRewardContainer.isVisible) {
//                return@observe
//            }
//
//            adTaskCenterViewModel.tryEnterTest("onGetUserType")
//        }

        Meepo.meepoCodeEvent.observe(viewLifecycleOwner) { meepoList ->
            "${Meepo.TAG_BIZ_MEEPO}-gotCodes-meepoList=$meepoList".logV()
            if (meepoList.isEmpty()) {
                "${Meepo.TAG_BIZ_MEEPO}-Code is empty".logV()
                return@observe
            }
            tryEnterAdTaskTest("onGetMeepoData")
        }

        subscribeViewModel.subSuccessEvent.observe(requireActivity()) {
            if (SubscribeConfig.isSubValid()) {
                binding.llAdTaskCenterEntrance.gone()

                viewModel.dataEvent.value?.run {
                    topBannerHolder.updateLLTopHeight(this, canShowNewUserPro())
                }
            }
        }

        adTaskCenterViewModel.enteredTestEvent.observe(viewLifecycleOwner) { enteredTest ->
            if (enteredTest) {
                adTaskCenterViewModel.todayAdTaskEvent.observeForever { todayAdTasks ->
                    todayAdTasks.toString().LOGV()

                    DailyMembershipUnlocker.featureBarrier.apply {
                        banFeature = true
                        banReason = "展示广告任务中心入口时，不开启此功能"
                    }
                    ImageStudioViewModel.featureBarrier.apply {
                        banFeature = true
                        banReason = "展示广告任务中心入口时，不开启此功能"
                    }

                    if (todayAdTasks.adTasks.isEmpty()) {
                        "todayAdTasks num = 0".LOGE()
                        binding.llAdTaskCenterEntrance.gone()
                        return@observeForever
                    }

                    if (!binding.llAdTaskCenterEntrance.isVisible) {
                        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.homepage_ad_task_imp)
                    }

                    binding.llAdTaskCenterEntrance.setOnClickListener {
                        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.homepage_ad_task_clk)
                        AdTaskCenterActivity.start(ownerActivity)
                    }

                    // 是目标用户 & 今日任务数>0
                    binding.llAdTaskCenterEntrance.visible()
                    viewModel.dataEvent.value?.run {
                        topBannerHolder.updateLLTopHeight(this, canShowNewUserPro())
                    }
                    if (todayAdTasks.claimed) {
                        binding.llClaimedTodayVip.visible()
                        binding.llUnclaimedTodayVip.gone()
                    } else {
                        binding.llClaimedTodayVip.gone()
                        binding.llUnclaimedTodayVip.visible()
                        binding.llGoAdTaskCenter.setBackgroundDrawable(
                            XDrawableFactory.getDrawable(R.color.color_1C1C1C.resColor(), 16.dpf())
                        )
                        binding.tvGo.apply {
                            applyGradientToTextView(
                                text.toString(), R.string.v77153_B_4.text(),
                                intArrayOf(
                                    0xffFF99D6.toInt(),
                                    0xffBAACFC.toInt(),
                                    0xff6ADAF8.toInt(),
                                    0xffAFF2C0.toInt()
                                ),
                                floatArrayOf(0f, 0.33f, 0.66f, 1f)
                            )
                        }
                    }

                }

                // Firebase拉到任务配置后，再加载数据
                FireBaseConfigHelper.onConfigLoadedEvent.observe(viewLifecycleOwner) { isFetched ->
                    if (isFetched) {
                        "FireBaseConfigHelper isFetched".logV()
                        adTaskCenterViewModel.loadToadyAdTask()
                    }
                }
                adTaskCenterViewModel.scheduleRefreshDailyAdTasks()
            } else {
                dailyRewardUIHolder.enableThisFeature = true
                binding.llAdTaskCenterEntrance.gone()
            }
        }
    }

    private fun tryEnterAdTaskTest(reason: String) {
        if (binding.dailyRewardContainer.isVisible) {
            // for case
            // 1、是新用户 & 已经展示[看x次解锁会员]UI
            // 2、新用户时间到期 & [首页常驻广告入口]可展示
            // 3、此时先不展示[首页常驻广告入口]，下次启动再展示
            return
        }
        adTaskCenterViewModel.tryEnterTest(reason)
    }


    private fun canShowNewUserPro(): Boolean {
        val config = SubsConfigManager.getValidConfig(
            null,
            2,
            subscribeViewModel.subscriptionRefinement
        )
        return config == null && ProStatusManager.inNewUserSubscribe() && NetUtils.canNetworking()
    }

    private fun fixSize(topText: String, bottomText: String) {
        var topTextSize = 16f
        val topPaint = Paint()
        topPaint.textSize = topTextSize.sp2dp()


        var bottomTextSize = 12f
        val bottomPaint = Paint()
        bottomPaint.textSize = bottomTextSize.sp2dp()

        var bottomFontMetrics = bottomPaint.fontMetrics
        var actualBottomTextHeight =
            bottomFontMetrics.descent - bottomFontMetrics.ascent

        var topFontMetrics = topPaint.fontMetrics
        var actualTopTextHeight =
            topFontMetrics.descent - topFontMetrics.ascent

        val maxWidth = DeviceUtils.getScreenWidth() - 108.dp

        while (topPaint.measureText(topText) > maxWidth * binding.tvNewUserOff.lineCount && topTextSize > 6f) {
            topTextSize -= 0.5f
            topPaint.textSize = topTextSize.sp2dp()
        }

        while (bottomPaint.measureText(bottomText) > maxWidth && bottomTextSize > 6f) {
            bottomTextSize -= 0.5f
            bottomPaint.textSize = bottomTextSize.sp2dp()
        }

        "topTextSize $topTextSize  bottomTextSize $bottomTextSize line ${binding.tvNewUserOff.lineCount}".LOGV_Camera()
        while (actualTopTextHeight * binding.tvNewUserOff.lineCount + actualBottomTextHeight > 45.dpf && topTextSize > 6f) {
            topTextSize -= 0.5f
            topPaint.textSize = (topTextSize).sp2dp()
            topFontMetrics = topPaint.fontMetrics
            actualTopTextHeight = topFontMetrics.descent - topFontMetrics.ascent
        }

        "topTextSize $topTextSize  bottomTextSize $bottomTextSize".LOGV_Camera()
        binding.tvNewUserOff.textSize = topTextSize
        binding.tvNewUserDetail.textSize = bottomTextSize
    }

    private var countDownTimer: CountDownTimer? = null


    private fun startCountDownTimer(endTime: Long, onTick: (String) -> Unit, onFinish: () -> Unit) {
        //倒计时触发
        countDownTimer?.cancel()
        countDownTimer = null
        countDownTimer = object : CountDownTimer(endTime, 1000) {

            override fun onTick(millisUntilFinished: Long) {
                DateUtils.formatTimeList(millisUntilFinished).run {
                    onTick("${this[0]}:${this[1]}:${this[2]}")
                }
            }

            override fun onFinish() {
                onFinish()
            }
        }
        countDownTimer?.start()

    }

    private fun subscribe() {
        subscribeViewModel.setNeedLogSubSuccess(true)
        subscribeViewModel.subscribe(requireActivity())
    }

    override fun onScreenSizeConfigurationChanged() {
        super.onScreenSizeConfigurationChanged()
        updatePhotoPortraitUi()

        viewModel.dataEvent.value?.run {
            // 更新Banner显示，如高度
            topBannerHolder.updateTopVPData(this, canShowNewUserPro())
        }
        transitionHelper?.onScreenSizeConfigurationChanged()
    }
}