package com.commsource.homev3

import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.util.Log
import android.view.View
import android.view.ViewGroup
import androidx.core.view.get
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentHomeV3Binding
import com.commsource.beautyplus.databinding.ItemHomev3PagerBannerBinding
import com.commsource.home.HomeLayoutUtils
import com.commsource.home.create.FunctionViewModel
import com.commsource.homev3.entity.BannerEntity
import com.commsource.statistics.BPAnalyticsSource
import com.commsource.util.FileDownloader
import com.commsource.util.GlideProxy
import com.commsource.util.UIHelper
import com.commsource.util.ViewShowState
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.isVisible
import com.commsource.util.print
import com.commsource.util.resColor
import com.commsource.util.safeGet
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.androidx.core.extension.paramsHeight
import com.pixocial.videokit.PlaySource
import com.pixocial.videokit.decoder.OnVideoEventListener
import com.pixocial.videokit.decoder.PlayerEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Locale

class TopBannerUIHolder(
    val context: BaseActivity,
    val binding: FragmentHomeV3Binding,
) : LifecycleEventObserver {

    // 记录Vp 向上滚动的距离
    private var currentScrollY = 0

    // Vp 的可见区域
    private val visibleRect = Rect()

    // Vp 是否resume 状态
    private var isHoldResume = true

    // Vp是否完全展示
    private var holderState = ViewShowState.SHOW_COMPLETE

    // 自动滚动的Runnable
    private var autoScrollRunnable: Runnable? = null

    private val viewModel by lazy {
        ViewModelProvider(context)[HomeV3ViewModel::class.java]
    }

    private val funcViewModel by lazy {
        ViewModelProvider(context)[FunctionViewModel::class.java]
    }

    private val pageAdapter by lazy {
        BaseRecyclerViewAdapter(context)
    }

    private fun pauseAllVideo() {
        (binding.rvPager.getChildAt(0) as? RecyclerView)?.let { rv ->
            for (i in 0 until rv.childCount) {
                val holder = rv.getChildViewHolder(rv[i])
                (holder as? PagerBannerViewHolder)?.let {
                    it.binding.run {
                        videoContainer.extraVideoListener = null
                        if (videoContainer.isAttachVideo()) {
                            videoContainer.seekTo(0)
                            videoContainer.requestPause()
                        }
                    }
                }
            }
        }
    }

    private fun startVideo(position: Int) {
        if (holderState != ViewShowState.NO_SHOW && isHoldResume) {
            (binding.rvPager.getChildAt(0) as? RecyclerView)?.let { rv ->
                (rv.findViewHolderForAdapterPosition(position) as? PagerBannerViewHolder)?.let {
                    it.binding.run {
                        videoContainer.extraVideoListener = videoListener
                        if (videoContainer.isAttachVideo()) {
                            videoContainer.requestStart()
                        }
                    }
                }
            }
        }
    }

    /**
     * 当前播放视频的监听
     */
    private val videoListener = object : OnVideoEventListener {
        override fun onPlayerEventChange(state: Int) {
            when (state) {
                PlayerEvent.playerReady -> {
                    //开始渲染就刷新当前倒计时
                    startAutoScroll()
                }


            }
        }

    }

    fun initialTopBanner() {
        context.lifecycle.addObserver(this)
        binding.rvPager.adapter = pageAdapter
        pageAdapter.setOnEntityClickListener(BannerEntity::class.java) { position, entity ->
            viewModel.onEntityClick(
                context,
                entity.deeplink,
                "首页内容",
                entity.miniAppData,
                entity.rid,
                enterSource = BPAnalyticsSource.CameraEnterSource.TopBanner.sourceString
            )
            val contentPos = if (pageAdapter.itemCount > 1) position else 1
            viewModel.logOnEntityClick(
                1, "0", "Topbanner",
                entity.rid, contentPos
            )
            true
        }

        autoScrollRunnable = Runnable {
            val nextIndex = (binding.rvPager.currentItem + 1) % pageAdapter.itemCount
            binding.rvPager.setCurrentItem(nextIndex, true)
            if (pageAdapter.itemCount > 1
                && holderState != ViewShowState.NO_SHOW
                && isHoldResume
            ) {
                UIHelper.runOnUiThreadDelay(autoScrollRunnable, 4000)
            }
        }

        binding.rvPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                val listSize = pageAdapter.itemCount
                if (state == ViewPager2.SCROLL_STATE_IDLE) {
                    when (binding.rvPager.currentItem) {
                        listSize - 1 -> binding.rvPager.setCurrentItem(1, false)
                        0 -> binding.rvPager.setCurrentItem(listSize - 2, false)
                    }
                    startAutoScroll()
                } else if (state == RecyclerView.SCROLL_STATE_DRAGGING) {
                    stopAutoScroll()
                }
            }

            override fun onPageSelected(position: Int) {
                if (position in 1 until pageAdapter.itemCount - 1) {
                    binding.idvPager.selectIndex(position - 1)
                }
                checkVideoPlayerState(position)
                pauseAllVideo()
                startVideo(position)
                analyzeIfShowComplete()
            }
        })




        binding.appBarLayout.addOnOffsetChangedListener { _, verticalOffset ->
            if (pageAdapter.itemCount > 0) {
                if (currentScrollY != verticalOffset) {
                    currentScrollY = verticalOffset
                    // 检查Viewpager的可见状态
                    val lastHolderState = holderState
                    holderState = if (verticalOffset == 0) {
                        ViewShowState.SHOW_COMPLETE
                    } else {
                        binding.rvPager.getGlobalVisibleRect(visibleRect)
                        if (visibleRect.bottom <= 104.dp) {
                            ViewShowState.NO_SHOW
                        } else {
                            ViewShowState.SHOW_PART
                        }
                    }
                    if (holderState != lastHolderState) {
                        setAutoScrollState(holderState != ViewShowState.SHOW_COMPLETE)
                    }
                }
            }
        }
    }


    fun updateTopVPData(uiState: HomeV3ViewModel.UIState, showNewUserBanner: Boolean = false) {
        UIHelper.removeCallbacks(autoScrollRunnable)
        if (uiState.topBanner.isNullOrEmpty()) {
            // 改小VP 的高度
            pageAdapter.clearItems()
            updateLLTopHeight(uiState, showNewUserBanner)
            binding.idvPager.visibility = View.GONE
        } else {
            updateLLTopHeight(uiState, showNewUserBanner)
            prepareBannerVideo(uiState.topBanner)
            binding.rvPager.offscreenPageLimit = uiState.topBanner.size
            // 滚动到当前记忆位置。
            //比较新老数据是否一致，如果一致则不刷新
            val oldDatas = pageAdapter.items?.map {
                it.entity as BannerEntity
            }
            if (oldDatas == null || !(oldDatas.containsAll(uiState.topBanner) && uiState.topBanner.containsAll(oldDatas))) {
                pageAdapter.setSingleItemEntities(uiState.topBanner, PagerBannerViewHolder::class.java)

            }
            val targetPosition = 1.coerceAtMost((uiState.topBanner.size - 1))
            binding.rvPager.setCurrentItem(targetPosition, false)
            (binding.rvPager.getChildAt(0) as? RecyclerView)?.let { rv ->
                rv.post {
                    checkVideoPlayerState(targetPosition)
                    pauseAllVideo()
                    startVideo(targetPosition)
                }
            }
            // 修改指示器坐标。
            if (pageAdapter.itemCount > 1) {
                binding.idvPager.count = pageAdapter.itemCount - 2
                binding.idvPager.visibility = View.VISIBLE
                startAutoScroll()
            } else {
                binding.idvPager.visibility = View.GONE
            }
        }
    }

    private val videoTasks = mutableListOf<String>()

    /**
     * 异步准备topbanner需要的视频
     */
    private fun prepareBannerVideo(list: List<BannerEntity>) {
        list.forEach {
            if (it.isVideoType()) {
                it.banner?.videoUrl?.let { url ->
                    val md5 = it.banner.videoMd5
                    val useMd5: Boolean
                    val key: String
                    if (md5.isNullOrEmpty()) {
                        key = url
                        useMd5 = false
                    } else {
                        key = md5
                        useMd5 = true
                    }
                    if (!videoTasks.contains(key)) {
                        videoTasks.add(key)
                        (context as? FragmentActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
                            val videoPath = FileDownloader.downloadVideoFile(url, "HomeBannerMp4", md5)
                            withContext(Dispatchers.Main) {
                                videoTasks.remove(key)
                                notifyVideoDownloaded(key, useMd5)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 视频下载完成通知
     */
    private fun notifyVideoDownloaded(key: String, isMd5: Boolean = true) {
        (binding.rvPager.getChildAt(0) as? RecyclerView)?.let { rv ->
            for (i in 0 until rv.childCount) {
                val holder = rv.getChildViewHolder(rv[i])
                (holder as? PagerBannerViewHolder)?.let {
                    if (isMd5) {
                        if (it.item.entity.banner?.videoMd5 == key) {
                            it.attachVideo(true)
                        }
                    } else {
                        if (it.item.entity.banner?.videoUrl == key) {
                            it.attachVideo(true)
                        }
                    }

                }
            }
        }
    }

    /**
     * 检查视频播放状态
     */
    private fun checkVideoPlayerState(currentPosition: Int) {
        (binding.rvPager.getChildAt(0) as? RecyclerView)?.let { rv ->
            for (i in 0 until pageAdapter.itemCount) {
                val holder = rv.findViewHolderForAdapterPosition(i)
                if (holder is PagerBannerViewHolder) {
                    //在currentPosition -2 到 currentPosition +2 之间的视频播放，如果小于0 则用itemCount -1，如果大于itemCount -1 则用0
                    if (i != currentPosition) {
                        holder.detachVideo()
                    }
                }
            }
            (rv.findViewHolderForAdapterPosition(currentPosition) as? PagerBannerViewHolder)?.let {
                //看不见的首位不用播放视频
                if (currentPosition != 0 && currentPosition != pageAdapter.itemCount - 1) {
                    it.attachVideo(true)
                }
            }

        }
    }

    private fun getValidPosition(position: Int): Int {
        return when {
            position < 0 -> pageAdapter.itemCount + position
            position >= pageAdapter.itemCount -> position - pageAdapter.itemCount
            else -> position
        }
    }

    fun updateLLTopHeight(uiState: HomeV3ViewModel.UIState, showNewUserBanner: Boolean = false) {
        if (uiState.topBanner.isNullOrEmpty()) {
            binding.tbContainer.paramsHeight = 84.dp
            binding.llTop.paramsHeight = binding.tbContainer.paramsHeight +
                    if (showNewUserBanner) 68.dp else 0 +
                            if (binding.llAdTaskCenterEntrance.isVisible) 68.dp else 0  // 68dp=height+topMargin
        } else {
            val vpHeight = (DeviceUtils.getScreenWidth() / 390f * 270).toInt()
            binding.tbContainer.paramsHeight = vpHeight
            binding.llTop.paramsHeight = vpHeight +
                    if (showNewUserBanner) 68.dp else 0 +
                            if (binding.llAdTaskCenterEntrance.isVisible) 68.dp else 0
        }
    }


    fun setAutoScrollState(isStop: Boolean) {
        if (isStop) {
            pauseAllVideo()
            stopAutoScroll()
        } else {
            startVideo(binding.rvPager.currentItem)
            startAutoScroll()
        }
    }

    private fun startAutoScroll() {
        if (autoScrollRunnable != null && pageAdapter.itemCount > 1
            && holderState != ViewShowState.NO_SHOW
            && isHoldResume
        ) {
            UIHelper.removeCallbacks(autoScrollRunnable)
            UIHelper.runOnUiThreadDelay(autoScrollRunnable, 4000)
        }
    }

    private fun stopAutoScroll() {
        if (autoScrollRunnable != null) {
            UIHelper.removeCallbacks(autoScrollRunnable)
        }
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        isHoldResume = event == Lifecycle.Event.ON_RESUME
        if (!isHoldResume) {
            pauseAllVideo()
            setAutoScrollState(true)
        } else {
            if (funcViewModel.isShowMain()) {
                startVideo(binding.rvPager.currentItem)
                setAutoScrollState(false)
            }
        }
    }


    fun analyzeIfShowComplete() {
        if (holderState == ViewShowState.SHOW_COMPLETE) {
            val currentPos = binding.rvPager.currentItem
            if (pageAdapter.itemCount == 1) {
                (pageAdapter.getEntityByPosition(currentPos) as? BannerEntity)?.let {
                    viewModel.logOnContentShow(
                        1, "0", "Topbanner",
                        it.rid, 1
                    )
                }
            } else if (currentPos in 1 until pageAdapter.itemCount - 1) {
                (pageAdapter.getEntityByPosition(currentPos) as? BannerEntity)?.let {
                    viewModel.logOnContentShow(
                        1, "0", "Topbanner",
                        it.rid, currentPos
                    )
                }
            }
        }
    }


    class PagerBannerViewHolder(context: Context, parent: ViewGroup) :
        BaseHomeStateHolder<BannerEntity>(context, parent, R.layout.item_homev3_pager_banner) {

        val binding = ItemHomev3PagerBannerBinding.bind(itemView)

        override fun onBindViewHolder(
            position: Int,
            item: BaseItem<BannerEntity>,
            payloads: MutableList<Any>?
        ) {
            super.onBindViewHolder(position, item, payloads)

            //如果是mp4
            if (item.entity.isVideoType()) {
                binding.videoContainer.visible()
                Log.d("lyddd", ">>>mp4 url>>${item.entity.banner?.url} videoUrl>>${item.entity.banner?.videoUrl}  $position")
                attachVideo(true)
            } else {
                if (item.entity.banner?.fileType == "pag") {
                    Log.d("lyddd", ">>>PAG 文件>>${item.entity.banner?.url}")
                    binding.placeHolder.visible()
                    item.entity.banner?.url?.let { url ->
                        (mContext as? FragmentActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
                            val pagPath = FileDownloader.downloadFile(url, "HomePAG", url)
                            withContext(Dispatchers.Main) {
                                binding.pagView.visible()
                                binding.pagView.setPAGPath(pagPath)
                                binding.pagView.play()
                            }
                        }
                    }
                } else {
                    Log.d("lyddd", ">>>image url>>${item.entity.banner?.url}   $position")
                    binding.ivImage.visible()
                    GlideProxy.with(mContext)
                        .load(item.entity.banner?.url)
                        .placeHolder(ColorDrawable(R.color.Gray_E.resColor()))
                        .requestOptions(HomeLayoutUtils.OPTIONS_TOP_BANNER)
                        .fadeTransition(400)
                        .into(binding.ivImage)
                }

            }
        }

        /**
         * 激活视频播放
         * @param onlyPrepareCover 是否只准备封面
         */
        fun attachVideo(isLazy: Boolean, onlyPrepareCover: Boolean = false) {
            if (item.entity.isVideoType()) {
                if (!binding.videoContainer.isAttachVideo()) {
                    val imageUrl = item.entity.banner?.staticUrl ?: item.entity.banner?.url
                    binding.videoContainer.prepareVideoCover(imageUrl, isAssets = false)
                }
                if (onlyPrepareCover) {
                    return
                }
                item.entity.banner?.videoUrl?.let { url ->
                    val videoPath = FileDownloader.getLocalVideoPathOrNull(url, "HomeBannerMp4", item.entity.banner?.videoMd5)
                    if (videoPath?.lowercase()?.endsWith(".mp4") == true) {
                        // imageUrl 优先取staticUrl 如果为空使用 url
                        videoPath.let {
                            binding.videoContainer.play(
                                playSource = PlaySource.LocalPlaySource(videoPath),
                                isLazy = isLazy,
                                isLoop = adapter.items.size == 1, //如果只有一个视频就循环播放，否则由外界操控
                                singleMode = false

                            )
                        }
                    } else {
                        Log.d("lyddd", "filePath invalid $adapterPosition")
                    }

                }
            }

        }

        fun detachVideo() {
            binding.videoContainer.detach()
        }

        override fun onViewDetached() {
            super.onViewDetached()
            if (binding.videoContainer.isAttachVideo()) {
                binding.videoContainer.seekTo(0)
                binding.videoContainer.requestPause()
            }
        }

        override fun onViewRecycler() {
            super.onViewRecycler()
            binding.placeHolder.gone()
            binding.ivImage.gone()
            binding.videoContainer.detach()
            binding.videoContainer.gone()

            binding.pagView.gone()
        }

    }


}


private fun BannerEntity.isVideoType(): Boolean {
    return banner?.videoUrl?.lowercase()?.endsWith(".mp4") == true
}