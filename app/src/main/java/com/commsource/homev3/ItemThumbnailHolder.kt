package com.commsource.homev3

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHomev3ThumbnailBinding
import com.commsource.home.HomeLayoutUtils
import com.commsource.homev3.entity.BannerEntity
import com.commsource.util.FileDownloader
import com.commsource.util.GlideProxy
import com.commsource.util.gone
import com.commsource.util.resColor
import com.commsource.util.setSize
import com.commsource.util.toAspectRatio
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.pixocial.androidx.core.extension.dp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ItemThumbnailHolder(context: Context, parent: ViewGroup) :
    BaseHomeStateHolder<BannerEntity>(
        context, parent,
        R.layout.item_homev3_thumbnail
    ) {

    private val binding by lazy {
        ItemHomev3ThumbnailBinding.bind(itemView)
    }

    override fun onBindViewHolder(position: Int, item: BaseItem<BannerEntity>, payloads: MutableList<Any>?) {
        super.onBindViewHolder(position, item, payloads)
        // 设置Item 的宽高
        val ratio = item.entity?.banner?.ratio?.toAspectRatio() ?: 1f
        val width = (152.dp * ratio).toInt()
        val height = 152.dp
        binding.cvThumbnail.setSize(width = width, height = height)
        if (item.entity?.banner?.fileType == "pag") {
            binding.pagThumbnail.setSize(width = width, height = height)
            binding.cvThumbnail.setCardBackgroundColor(Color.WHITE)
            binding.thumbnail.gone()
            item.entity?.banner?.url?.let { url ->
                (mContext as? FragmentActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
                    val pagPath = FileDownloader.downloadFile(url, "HomePAG", url)
                    withContext(Dispatchers.Main) {
                        binding.pagThumbnail.visible()
                        binding.pagThumbnail.setPAGPath(pagPath)
                        binding.pagThumbnail.play()
                    }
                }
            }
        } else {
            binding.thumbnail.setSize(width = width + 2.dp, height = height + 2.dp)
            binding.cvThumbnail.setCardBackgroundColor(Color.TRANSPARENT)
            binding.pagThumbnail.gone()
            binding.thumbnail.visible()
            GlideProxy.with(mContext)
                .load(item.entity?.banner?.url)
                .override(width, height)
                .placeHolder(ColorDrawable(R.color.White.resColor()))
                .requestOptions(HomeLayoutUtils.OPTIONS_TOP_BANNER)
                .into(binding.thumbnail)
        }

        // New/Hot显示
        binding.hotIcon.gone()
        binding.newIcon.gone()
        item.entity?.tag?.let {
            if (it == "New") {
                binding.newIcon.visible()
            } else if (it == "Hot") {
                binding.hotIcon.visible()
            }
        }
    }
}