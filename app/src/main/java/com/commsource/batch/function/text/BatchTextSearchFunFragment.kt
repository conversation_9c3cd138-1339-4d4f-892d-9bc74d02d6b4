package com.commsource.batch.function.text

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.commsource.batch.BatchEditViewModel
import com.commsource.batch.renderinfo.TextRenderInfo
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.BR
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentBatchTextSearchFunBinding
import com.commsource.beautyplus.databinding.FragmentTextSearchFunBinding
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.shop.FragmentBuilder
import com.commsource.home.SearchDataTasks
import com.commsource.library_mvvm.AndroidViewModelFactory
import com.commsource.repository.child.TextTemplateRepository
import com.commsource.search_common.entity.KeyWordInfo
import com.commsource.search_common.entity.NET_KEY_TEXT_TEMPLATE
import com.commsource.search_common.entity.SearchInfo
import com.commsource.search_common.entity.TEXT_TEMPLATE_WAY
import com.commsource.search_common.repo.KeyWordRepo
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.store.filter.search.PageConfig
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.StudioLayoutConstants.SEARCH_FUNCTION_INIT_HEIGHT
import com.commsource.studio.bean.TextLayerInfo
import com.commsource.studio.text.TextTemplateMaterial
import com.commsource.studio.text.search.TextSearchFunViewModel
import com.commsource.studio.text.search.TextTemplateSearchItemHolder
import com.commsource.studio.text.search.entity.TextTemplateData
import com.commsource.util.ErrorNotifier
import com.commsource.util.LOGV
import com.commsource.util.MaterialVisibleTracker
import com.commsource.util.ViewShowState
import com.commsource.util.ViewUtils
import com.commsource.util.coroutine.launch
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.setCornerRadius
import com.commsource.widget.mask.MaskType
import com.meitu.common.AppContext.application
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.util.net.NetUtils


class BatchTextSearchFunFragment : BaseBottomSubFragment() {
    
    val viewBinding by lazy { FragmentBatchTextSearchFunBinding.inflate(layoutInflater) }

    var config: PageConfig? = null

    private val textViewModel: BatchTextViewModel by lazy {
        ViewModelProvider(ownerActivity)[BatchTextViewModel::class.java]
    }

    private val batchEditViewModel: BatchEditViewModel by lazy {
        ViewModelProvider(ownerActivity)[BatchEditViewModel::class.java]
    }

    private val textSearchViewModel by lazy {
        ViewModelProvider(
            this,
            AndroidViewModelFactory(application) {
                BatchTextSearchFunViewModel(
                    application,
                    textViewModel,
                )
            }
        )[BatchTextSearchFunViewModel::class.java]
    }

    val tracker: MaterialVisibleTracker<SearchInfo> by lazy {
        object : MaterialVisibleTracker<SearchInfo>() {

            override fun isScrollCheck(): Boolean {
                return isSupportVisible
            }

            override fun onCallback(int: Int, viewHolder: RecyclerView.ViewHolder?) {

                if (viewHolder is TextTemplateSearchItemHolder && (int >= ViewShowState.SHOW_PART)
                ) {
                    viewHolder.item?.entity?.keyId?.let {
                        if (textSearchViewModel.isFirstVisible(it)) {
                            MTAnalyticsAgent.logEvent(
                                MTAnalyticsConstant.BEAUTY_TEXT_IMP,
                                HashMap<String, String>(8).apply {
                                    this["tex_material_tag"] = "BP_cat_TEX_SCH"
                                    this["text_id"] = it
                                    this["source"] = MTAnalyticsConstant.func_page
                                    this["word_content"] =
                                        textSearchViewModel.searchWord.get() ?: ""
                                })
                        }
                    }
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        //限定面板高度
        ViewUtils.setHeight(viewBinding.searchLoadMore, SEARCH_FUNCTION_INIT_HEIGHT)
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.setVariable(BR.viewModel, textSearchViewModel)
        viewBinding.lifecycleOwner = this
        //关联ViewModel
        //让ViewModel拥有View的生命周期感应
        lifecycle.addObserver(textSearchViewModel)

        tracker.bindRecyclerView(viewBinding.rv)
        textSearchViewModel.searchEditViewModel.showInput()

        //被选中的id,在搜索界面，如果有搜索到，需要被默认选中,条件需要添加是否在编辑状态
        var chosenId = textViewModel.applyTextTemplateEvent.value?.textTemplate?.id?:""

        textSearchViewModel.selectedIdEvent.value = chosenId

        viewBinding.mask.maskContainerHelper.newBuilder()
            .bindView(MaskType.NetError, R.id.tv_action) {
                textSearchViewModel.netInitData<TextTemplateData>(
                    TextTemplateSearchItemHolder::class.java,
                    false
                )
            }
        initViewModel()

        launch({
            SearchDataTasks.initSearchTags(TEXT_TEMPLATE_WAY, arrayOf(NET_KEY_TEXT_TEMPLATE))
        })

        SearchDataTasks.initHotWord(this,KeyWordInfo.TYPE_TEXT_TEMPLATES){
            textSearchViewModel.searchHotViewModel.loadView(true)
        }
        //无网情况先使用缓存数据
        if (!NetUtils.canNetworking() && KeyWordRepo.hasData(KeyWordInfo.TYPE_TEXT_TEMPLATES)) {
            "无网，先使用缓存热词".LOGV()
            textSearchViewModel.searchHotViewModel.loadView(true)
        }
    }

    private fun initViewModel() {
        TextTemplateRepository.downloadObserver.apply {
            successEvent.observe(viewLifecycleOwner) {
                updateAdapter(it)
            }
        }

        //更新素材下载进度
        textViewModel.templateDownloadState.observe(viewLifecycleOwner) { updateTextTemplateMaterial ->
            //更新下载状态
            updateAdapter(updateTextTemplateMaterial)
        }

        textSearchViewModel.editClickEvent.observe(viewLifecycleOwner) {
            viewBinding.searchLoadMore.setLayoutState(true)
        }

        textSearchViewModel.cancelSearchEvent.observe(viewLifecycleOwner) {
            if (it == true) {
                textSearchViewModel.searchEditViewModel.closeInput()
                FragmentBuilder.instance.remove(this.javaClass)
            }
        }

        //收藏状态更新事件
        textViewModel.collectStateChangeEvent.observe(viewLifecycleOwner) {
            it?.let { collectTextTemplateMaterial ->
                try {
                    val searchAdapterItems = textSearchViewModel.searchAdapter.items
                    searchAdapterItems?.let { searchList ->
                        searchList.forEach { adapterItem ->
                            var textTemplateMaterial =
                                (adapterItem.entity as SearchInfo).linkObj as? TextTemplateMaterial
                            if (textTemplateMaterial?.id == collectTextTemplateMaterial.id) {
                                textTemplateMaterial = collectTextTemplateMaterial
                                textSearchViewModel.searchAdapter.notifyItemChanged(adapterItem.entity)
                                return@observe
                            }
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        textSearchViewModel.loadFinishEvent.observe(viewLifecycleOwner) {
            //当前没有选中，并且viewModel里面有选中的【文字模版页面选中了】，需要筛选一下选中状态
            it?.let {
                textSearchViewModel.selectedIdEvent.value?.let { textTemplateId ->
                    setCurrentSelectEntity(textTemplateId)
                }
            }
        }

        // 应用模版，更新选中框
        textViewModel.applyTextTemplateEvent.observe(viewLifecycleOwner) {
            if (it == null) {
                textSearchViewModel.searchAdapter.currentSelectEntity = null
                textSearchViewModel.selectedIdEvent.value = ""
                return@observe
            }
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.BEAU_CLK_TEXT_USE, HashMap<String, String>().apply {
                    put("tex_material_tag", "BP_cat_TEX_SCH")
                    put("text_id", it.textTemplate.id)
                    put("source", MTAnalyticsConstant.func_page)
                    put("word_content", textSearchViewModel.searchWord.get() ?: "")
                    putAll(SPMManager.instance.getCurrentSpmInfo())
                }
            )
            // 处理选中
            textSearchViewModel.selectedIdEvent.value = it.textTemplate.id
            setCurrentSelectEntity(it.textTemplate.id)
        }

        //网络错误。
        textViewModel.netWorkErrorEvent.observe(
            viewLifecycleOwner,
            object : NoStickLiveData.CustomObserver<Boolean?>() {
                override fun onAccept(isChange: Boolean?) {
                    isChange?.let {
                        ErrorNotifier.showNetworkErrorToast()
                    }
                }
            })

        // 图层焦点变更，更新选中框
        textViewModel.focusTextLayerEvent.observe(viewLifecycleOwner) { focusLayer ->
            if (focusLayer is TextRenderInfo) {
                textSearchViewModel.selectedIdEvent.value = focusLayer.textGroupParam?.template?.templateId
                setCurrentSelectEntity(focusLayer.textGroupParam?.template?.templateId)
            } else {
                textSearchViewModel.selectedIdEvent.value = ""
                textSearchViewModel.searchAdapter.currentSelectEntity = null
            }
        }

        batchEditViewModel.fullCoverClickEvent.observe(viewLifecycleOwner) { it ->
            if (it) {
                hide()
            }
        }
    }

    private fun hide() {
        var searchTagVisibility = textSearchViewModel.searchTagsViewModel.visibility.get()
        var maskStatus = textSearchViewModel.maskStatus.get()
        if (maskStatus == null) {//搜索的界面的初始状态[有推荐，搜索历史界面],销毁搜索界面
            FragmentBuilder.instance.remove(this@BatchTextSearchFunFragment::class.java)
        } else {//进入了编辑界面，隐藏当前没有数据的界面包括关联搜索不为空，数据为空，正在请求等状态；如果有数据就折叠界面
            if (searchTagVisibility == View.VISIBLE) {//出现关联搜索的内容隐藏
                FragmentBuilder.instance.hide(this@BatchTextSearchFunFragment::class.java)
            } else if (textSearchViewModel.allDataList.isEmpty()) {//数据为空隐藏
                FragmentBuilder.instance.hide(this@BatchTextSearchFunFragment::class.java)
            } else {
                viewBinding.searchLoadMore.setLayoutState(false)
            }
        }
        hideKeyBoard()
    }

    // 设置选中框
    private fun setCurrentSelectEntity(templateId: String?) {
        templateId?.let {
            // 非收藏分类如果无模版，选中第一个位置
            try {
                val searchAdapterItems = textSearchViewModel.searchAdapter.items
                searchAdapterItems?.let { searchList ->
                    searchList.forEach { adapterItem ->
                        val textTemplateMaterial =
                            (adapterItem.entity as SearchInfo).linkObj as? TextTemplateMaterial
                        if (textTemplateMaterial?.id == it) {
                            textSearchViewModel.searchAdapter.currentSelectEntity =
                                adapterItem.entity
                            return
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        textSearchViewModel.searchAdapter.currentSelectEntity = null
    }

    private fun updateAdapter(textTemplateMaterial: TextTemplateMaterial?) {
        textTemplateMaterial?.let {
            try {
                val searchAdapterItems = textSearchViewModel.searchAdapter.items
                searchAdapterItems?.let { searchList ->
                    searchList.forEach { adapterItem ->
                        val textTemplateMaterial =
                            (adapterItem.entity as SearchInfo).linkObj as? TextTemplateMaterial
                        if (textTemplateMaterial?.id == it.id) {
                            textTemplateMaterial.downloadProgress = it.downloadProgress
                            textTemplateMaterial.downloadState = it.downloadState
                            textSearchViewModel.searchAdapter.notifyItemChanged(adapterItem.entity)
                            return
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (textViewModel.isDataChangeOnStop()) {
            textSearchViewModel.searchAdapter.notifyAllItemChange()
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            textSearchViewModel.searchHotViewModel.trendWord()
//            if (imageStudioViewModel.isShowFoldedSearchEvent.value == true) {
//                imageStudioViewModel.isShowFoldedSearchEvent.value = false
//            } else {
//                imageStudioViewModel.fullOutCoverEvent.value = true
//            }
//            imageStudioViewModel.collapsedPanelEvent.value = false
        }
    }

    private fun hideKeyBoard() {
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        view?.windowToken?.let {
            imm.hideSoftInputFromWindow(it, 0)
        }
    }
}