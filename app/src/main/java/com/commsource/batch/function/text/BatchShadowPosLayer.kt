package com.commsource.batch.function.text

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.view.MotionEvent
import android.view.View
import com.commsource.batch.layer.BatchBaseScrollLayer
import com.commsource.beautyplus.R
import com.commsource.studio.MatrixBox
import com.commsource.studio.layer.BaseScrollLayer
import com.commsource.util.ResourcesUtils
import com.commsource.util.common.MathUtil
import com.commsource.util.hapticVirtualKey
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.pow
import kotlin.math.sin

class BatchShadowPosLayer(private val ctx: Context) : BatchBaseScrollLayer(ctx),
    BatchBaseScrollLayer.ScrollListener {

    private var isUserOpt = false
    private val optMatrix = Matrix()
    val limitCircleCenter = PointF()
    var limitCircleRadius = 0f
    private val userTouchPoint = floatArrayOf(0f, 0f)
    val curLightPos = floatArrayOf(0f, 0f)
    private var currentLightAngle = 0
    var shadowCenterChangeCallBack: ((cx: Float, cy: Float, isStopMove: Boolean) -> Unit)? = null

    // 点击回调
    var onTapCallback: (() -> Unit)? = null

    init {
        addScrollListener(this, 0)
    }

    /**
     * 绘制光源的画笔。
     */
    private val lightPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    /**
     * 画笔展示提示。
     */
    private var paintTipsAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
        duration = 2000
        repeatCount = 2
        addUpdateListener {
            lightPaint.alpha = (255 * it.animatedValue as Float).toInt()
            layerView.invalidate()
        }
    }

    /**
     * 展示画笔提示。
     * @param animate 是否需要动画
     */
    fun showPaintTips(animate: Boolean) {
        if (animate) {
            paintTipsAnimator.start()
        } else {
            lightPaint.alpha = 255
            layerView.invalidate()
        }
    }

    override fun onCreateView(): View {
        return IndicatorView(context)
    }

    fun resetPos() {
        optMatrix.setTranslate(limitCircleCenter.x, limitCircleCenter.y)
        curLightPos[0] = limitCircleCenter.x
        curLightPos[1] = limitCircleCenter.y
    }

    fun prepareLimitRect(array: FloatArray, initPos: FloatArray? = null) {
        // 中心点就是圆心
        limitCircleCenter.x = array[0]
        limitCircleCenter.y = array[1]
        limitCircleRadius =
            MathUtil.getDistance(array[0], array[1], array[2], array[3])

        if (initPos == null) {
            optMatrix.setTranslate(limitCircleCenter.x, limitCircleCenter.y)
            curLightPos[0] = limitCircleCenter.x
            curLightPos[1] = limitCircleCenter.y
            //shadowCenterChangeCallBack?.invoke(limitCircleCenter.x, limitCircleCenter.y)
        } else {
            currentLightAngle = (atan2(
                (initPos[1] - limitCircleCenter.y).toDouble(),
                (initPos[0] - limitCircleCenter.x).toDouble()
            ) * 180 / Math.PI).toInt()
            optMatrix.setTranslate(initPos[0], initPos[1])
            curLightPos[0] = initPos[0]
            curLightPos[1] = initPos[1]
            // shadowCenterChangeCallBack?.invoke(initPos[0], initPos[1])
        }
        layerView.postInvalidate()
    }

    override fun onCanvasGestureMatrixChange(matrixBox: MatrixBox) {
        layerView.postInvalidate()
    }

    inner class IndicatorView(context: Context) : View(context) {
        private var lightBitmap: Bitmap =
            ResourcesUtils.getBitmapFromDrawable(R.drawable.edit_icon_lightsource)
        private val posMatrix = Matrix()
        override fun onDraw(canvas: Canvas) {
            //手势触摸点已经变换成了ViewPort 中的点了
            // 这里也需要做一个对应
            canvas.concat(containerMatrixBox.matrix)
            canvas.concat(canvasInitMatrixBox.matrix)
            // 根据触摸点绘制路径 ,偏移中心保持大小不变。
            posMatrix.reset()
            posMatrix.postTranslate(
                -lightBitmap.width / 2.0f,
                -lightBitmap.height / 2.0f
            )
            posMatrix.postScale(
                1.0f / (containerScale) / canvasInitMatrixBox.getScale(),
                1.0f / (containerScale) / canvasInitMatrixBox.getScale()
            )

            //posMatrix.postRotate((currentLightAngle + 90).toFloat())
            posMatrix.postConcat(optMatrix)
            canvas.drawBitmap(lightBitmap, posMatrix, lightPaint)
        }
    }

    override fun onTap(viewPortX: Float, viewPortY: Float, motionEvent: MotionEvent) {
        super.onTap(viewPortX, viewPortY, motionEvent)

        // 回调点击事件
        onTapCallback?.invoke()
    }

    override fun onSingleFingerScroll(
        viewPortX: Float,
        viewPortY: Float,
        motionEvent: MotionEvent
    ) {
        isUserOpt = true
        userTouchPoint[0] = motionEvent.x
        userTouchPoint[1] = motionEvent.y
        mapPointContainerToCanvas(userTouchPoint)
        calOptMatrix(userTouchPoint[0], userTouchPoint[1], limitCircleCenter.x, limitCircleCenter.y)
    }


    override fun onStopSingleFinger(
        viewPortX: Float,
        viewPortY: Float,
        isStartScroll: Boolean,
        isMajorFingerUp: Boolean,
        motionEvent: MotionEvent?
    ) {
        if (isUserOpt) {
            isUserOpt = false
            calOptMatrix(
                userTouchPoint[0],
                userTouchPoint[1],
                limitCircleCenter.x,
                limitCircleCenter.y,
                true
            )
        }
    }

    private fun isInLimitCircle(x: Float, y: Float): Boolean {
        return ((x - limitCircleCenter.x).pow(2) + (y - limitCircleCenter.y).pow(2)) <= limitCircleRadius.pow(
            2
        )
    }

    private fun calOptMatrix(
        x: Float,
        y: Float,
        c1: Float,
        c2: Float,
        isStopMove: Boolean = false
    ) {
        val radius = if (isInLimitCircle(x, y)) {
            MathUtil.getDistance(x, y, limitCircleCenter.x, limitCircleCenter.y)
        } else {
            limitCircleRadius
        }
        val angle =
            fixAngle((atan2((y - c2).toDouble(), (x - c1).toDouble()) * 180 / Math.PI).toInt())

        val finalX = c1 + radius * cos(angle * Math.PI / 180)
        val finalY = c2 + radius * sin(angle * Math.PI / 180)
        optMatrix.setTranslate(finalX.toFloat(), finalY.toFloat())
        curLightPos[0] = finalX.toFloat()
        curLightPos[1] = finalY.toFloat()
        shadowCenterChangeCallBack?.invoke(finalX.toFloat(), finalY.toFloat(), isStopMove)
    }

    private fun fixAngle(curAngle: Int, offsetAngle: Int = 4): Int {
        var finalAngle = curAngle
        var isWillAlign = false
        val factor = if (curAngle == 0) 1 else curAngle / Math.abs(curAngle)
        if ((Math.abs(curAngle) % 45 <= offsetAngle)) {
            finalAngle = Math.abs(curAngle) / 45 * 45
            finalAngle *= factor
            isWillAlign = true
        } else if ((Math.abs(curAngle) + offsetAngle) % 45 <= offsetAngle) {
            finalAngle = (Math.abs(curAngle) + offsetAngle) / 45 * 45
            finalAngle *= factor
            isWillAlign = true
        }
        if (isWillAlign && currentLightAngle != finalAngle) {
            layerView.hapticVirtualKey()
        }
        currentLightAngle = finalAngle
        return finalAngle
    }

    private fun Float.pow(x: Int): Float {
        return this.toDouble().pow(x.toDouble()).toFloat()
    }

}