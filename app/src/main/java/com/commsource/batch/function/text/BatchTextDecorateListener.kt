package com.commsource.batch.function.text

import com.commsource.batch.renderinfo.FocusableRenderInfo
import com.commsource.batch.renderinfo.TextRenderInfo

interface BatchTextDecorateListener {
    fun onTextCopy(textRenderInfo: TextRenderInfo)
    fun onTextTap(textRenderInfo: TextRenderInfo, paragraphIndex: Int)
    fun onTextDelete(textRenderInfo: TextRenderInfo)
    // 订阅
    fun onSubscribe(renderInfo: FocusableRenderInfo)
}