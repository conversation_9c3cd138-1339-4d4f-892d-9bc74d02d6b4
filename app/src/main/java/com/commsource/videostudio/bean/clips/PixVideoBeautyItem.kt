package com.commsource.videostudio.bean.clips

import java.io.Serializable

/**
 * 美颜效果类型
 */
class PixVideoBeautyItem : Cloneable, Serializable {

    /**
     * 一键美颜
     */
    var autoBeautyEffect: PixVideoAutoBeautyEffect = PixVideoAutoBeautyEffect()

    /**
     * 美妆
     */
    var makeupEffect: PixVideoMakeupEffect = PixVideoMakeupEffect()

    /**
     * looks
     */
    var looksEffect: PixVideoLooksEffect = PixVideoLooksEffect()

    /**
     * 面部重塑
     */
    var remodelEffect: PixVideoRemodelEffect = PixVideoRemodelEffect()

    /**
     * 塑形
     */
    var bodyShapeEffect: PixVideoReshapeEffect = PixVideoReshapeEffect()

    /**
     * 基础美颜
     */
    var baseBeautyEffect: PixVideoBaseBeautyEffect = PixVideoBaseBeautyEffect()

    /**
     * 是否多人脸效果
     */
    var isMultiFaceEffect: Boolean = false

    /**
     * 主轨道效果数据
     */
    @Transient
    var globalEffectData: BeautyEffectData = BeautyEffectData()

    /**
     * 画中画效果数据
     */
    @Transient
    var pipEffectDatas: List<BeautyEffectData> = emptyList()

    public override fun clone(): PixVideoBeautyItem {
        return (super.clone() as PixVideoBeautyItem).also {
            it.autoBeautyEffect = this.autoBeautyEffect.copy()
            it.makeupEffect = this.makeupEffect.copy()
            it.remodelEffect = this.remodelEffect.copy()
            it.baseBeautyEffect = this.baseBeautyEffect.copy()
            it.looksEffect = this.looksEffect.copy()
            it.bodyShapeEffect = this.bodyShapeEffect.copy()
        }
    }

    init {
        //添加默认的美颜配置
        autoBeautyEffect.items = arrayListOf()
        baseBeautyEffect.items = arrayListOf(PixVideoBaseBeautyItem().apply {
            faceID = PixVideoBeautyEffectItem.InvalidFaceID
        })
        remodelEffect.items = arrayListOf(PixVideoRemodelItem().apply {
            faceID = PixVideoBeautyEffectItem.InvalidFaceID
        })
        makeupEffect.items = arrayListOf(PixVideoMakeupItem().apply {
            faceID = PixVideoBeautyEffectItem.InvalidFaceID
        })
        looksEffect.items = arrayListOf(PixVideoLooksItem().apply {
            faceID = PixVideoBeautyEffectItem.InvalidFaceID
        })
        bodyShapeEffect.items = arrayListOf(PixVideoReshapeItem().apply {
            faceID = PixVideoBeautyEffectItem.InvalidFaceID
        })
    }

    /**
     * 判断效果中是否有美颜效果
     * @param faceId 目标人脸ID
     */
    fun hasBaseBeautyEffect(faceId: Long? = null): Boolean {
        if (faceId == null) {
            baseBeautyEffect.items.forEach {
                it.params.values.forEach {
                    if (it.degree != 0f) {
                        return true
                    }
                }
            }
        } else {
            baseBeautyEffect.items.find { faceId == it.faceID }?.let {
                it.params.values.forEach {
                    if (it.degree != 0f) {
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * 是否含有塑形效果
     */
    fun hasBodyShapeEffect(faceId: Long? = null): Boolean {
        if (faceId == null) {
            bodyShapeEffect.items.forEach {
                it.params.values.forEach {
                    if (it.degree != 0f) {
                        return true
                    }
                }
            }
        } else {
            bodyShapeEffect.items.find { faceId == it.faceID }?.let {
                it.params.values.forEach {
                    if (it.degree != 0f) {
                        return true
                    }
                }
            }
        }
        return false
    }


    /**
     * 是否存在美型效果
     */
    fun hasFaceEffect(faceId: Long? = null): Boolean {
        if (faceId == null) {
            remodelEffect.items.forEach {
                it.params.values.forEach {
                    if (it.degree != 0f) {
                        return true
                    }
                }
            }
        } else {
            remodelEffect.items.find { faceId == it.faceID }?.let {
                it.params.values.forEach {
                    if (it.degree != 0f) {
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * 当前美颜整体是否需要付费
     */
    fun needPay(): Boolean {
        return isMakeupNeedPaid() || isFaceNeedPaid() || isBeautyNeedPaid() || isLooksNeedPaid() || isBodyShapeNeedPaid()
    }

    /**
     * 当前状态美妆是否付费
     */
    fun isMakeupNeedPaid(): Boolean {
        if (isMultiFaceEffect) {
            makeupEffect.items.forEach {
                if (it.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    return it.materials.find { it.needPaid() } != null
                }
            }
        } else {
            makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }?.let {
                return it.materials.find { it.needPaid() } != null
            }
        }
        return false
    }

    /**
     * 当前状态美妆是否付费
     */
    fun isLooksNeedPaid(): Boolean {
        if (isMultiFaceEffect) {
            var needPaid: Boolean = false
            looksEffect.items.forEach {
                if (it.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    if (it?.needPaid() == true) {
                        needPaid = true
                    }
                    return needPaid
                }
            }
        } else {
            looksEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }?.let {
                return it?.needPaid() == true
            }
        }
        return false
    }

    /**
     * 判断脸型是否付费
     */
    fun isFaceNeedPaid(): Boolean {
        if (isMultiFaceEffect) {
            remodelEffect.items.forEach {
                if (it.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    if (hasFaceEffect(it.faceID)) {
                        return true
                    }
                }
            }
            return false
        }
        return hasFaceEffect()
    }

    /**
     * 判断塑形是否需要付费
     */
    fun isBodyShapeNeedPaid(): Boolean {
        bodyShapeEffect.items.forEach {
            if (hasBodyShapeEffect(it.faceID)) {
                return true
            }
        }
        return false
    }


    /**
     * 判断使用的美颜是否付费
     */
    fun isBeautyNeedPaid(): Boolean {
        if (isMultiFaceEffect) {
            baseBeautyEffect.items.forEach {
                if (it.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    val needPay = (it.params[PixMediaBeautyType.wrinkle]?.degree ?: 0f) > 0
                            || (it.params[PixMediaBeautyType.darkCircle]?.degree ?: 0f) > 0
                    if (needPay) {
                        return true
                    }
                } else {
                    val needPay = (it.params[PixMediaBeautyType.ance]?.degree ?: 0f) > 0
                    if (needPay) {
                        return true
                    }
                }
            }
        } else {
            baseBeautyEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let {
                        val needPay = (it.params[PixMediaBeautyType.wrinkle]?.degree ?: 0f) > 0
                                || (it.params[PixMediaBeautyType.ance]?.degree ?: 0f) > 0
                                || (it.params[PixMediaBeautyType.darkCircle]?.degree ?: 0f) > 0
                        if (needPay) {
                            return true
                        }
                    }
        }
        return false
    }

    /**
     * 判断使用了某个美颜效果付费
     */
    fun isBaseBeautyNeedPaid(@PixMediaBeautyType beautyType: String): Boolean {
        if (beautyType != PixMediaBeautyType.wrinkle && beautyType != PixMediaBeautyType.ance && beautyType != PixMediaBeautyType.darkCircle) {
            return false
        }
        if (isMultiFaceEffect) {
            baseBeautyEffect.items.forEach {
                if (it.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    val needPay = (it.params[beautyType]?.degree ?: 0f) > 0
                    if (needPay) {
                        return true
                    }
                } else if (beautyType == PixMediaBeautyType.ance) {
                    val needPay = (it.params[beautyType]?.degree ?: 0f) > 0
                    if (needPay) {
                        return true
                    }
                }
            }
        } else {
            baseBeautyEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let {
                        val needPay = (it.params[beautyType]?.degree ?: 0f) > 0
                        if (needPay) {
                            return true
                        }
                    }
        }
        return false
    }


    /**
     * 是否存在自动美颜效果
     */
    fun hasAutoEffect(faceId: Long? = null): Boolean {
        if (faceId == null) {
            autoBeautyEffect.items.forEach {
                it.params.values.forEach {
                    if (it.degree != 0f) {
                        return true
                    }
                }
            }
        } else {
            autoBeautyEffect.items.find { faceId == it.faceID }?.let {
                it.params.values.forEach {
                    if (it.degree != 0f) {
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * 判断是否含有美妆效果
     */
    fun hasMakeupEffect(faceId: Long? = null): Boolean {
        if (faceId == null) {
            makeupEffect.items.forEach {
                it.materials.forEach {
                    if (it.material.productID != null && it.degree != 0f) {
                        return true
                    }
                }
            }
        } else {
            makeupEffect.items.find { faceId == it.faceID }?.let {
                it.materials.forEach {
                    if (it.material.productID != null && it.degree != 0f) {
                        return true
                    }
                }
            }
        }
        return false
    }


    /**
     * 是否含有视频套装效果
     */
    fun hasLooksEffect(faceId: Long? = null): Boolean {
        if (faceId == null) {
            looksEffect.items.forEach {
                it.lookMaterial?.let {
                    if (it?.materialId != null && it?.alphaX != 0) {
                        return true
                    }
                }
            }
        } else {
            looksEffect.items.find { faceId == it.faceID }?.let {
                it.lookMaterial?.let {
                    if (it?.materialId != null && it?.alphaX != 0) {
                        return true
                    }
                }
            }
        }
        return false
    }


    /**
     * 是否含有多人脸效果
     */
    fun hasMultiFaceEffect(): Boolean {
        makeupEffect.items.apply {
            val globalItem = firstOrNull { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
            filter { it.faceID != PixVideoBeautyEffectItem.InvalidFaceID }.forEach { item ->
                item.materials.forEach { makeupMaterial ->
                    if (makeupMaterial.degree > 0) {
                        val globalMaterial =
                            globalItem?.materials?.find { it.material.productID == makeupMaterial.material.productID }
                        if (globalMaterial == null || globalMaterial.degree != makeupMaterial.degree) {
                            return true
                        }
                    }
                }
            }
        }

        remodelEffect.items.apply {
            val globalItem = firstOrNull { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
            filter { it.faceID != PixVideoBeautyEffectItem.InvalidFaceID }.forEach { item ->
                item.params.forEach {
                    val globalValue = globalItem?.params?.get(it.key)?.degree
                    if (it.value.degree > 0f && it.value.degree != globalValue) {
                        return true
                    }
                }
            }
        }

        looksEffect.items.apply {
            val globalItem = firstOrNull { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
            filter { it.faceID != PixVideoBeautyEffectItem.InvalidFaceID }.forEach { item ->
                if (item.lookMaterial != null && (item.lookMaterial?.materialId != globalItem?.lookMaterial?.materialId
                            || item.lookMaterial?.alphaX != globalItem?.lookMaterial?.alphaX)
                ) {
                    return true
                }
            }
        }

        autoBeautyEffect.items.apply {
            val globalItem = firstOrNull { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
            filter { it.faceID != PixVideoBeautyEffectItem.InvalidFaceID }.forEach { item ->
                item.params.forEach {
                    val globalValue = globalItem?.params?.get(it.key)?.degree
                    if (it.value.degree > 0f && it.value.degree != globalValue) {
                        return true
                    }
                }
            }
        }

        baseBeautyEffect.items.apply {
            val globalItem = firstOrNull { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
            filter { it.faceID != PixVideoBeautyEffectItem.InvalidFaceID }.forEach { item ->
                item.params.forEach {
                    val globalValue = globalItem?.params?.get(it.key)?.degree
                    if (it.value.degree > 0f && it.value.degree != globalValue) {
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * 美颜美型美妆效果数据集合
     * 因为画中画有区分 所以包装把effectId包装起来
     */
    data class BeautyEffectData(
            /**
             * 画中画效果ID
             */
            var pipEffectId: Long? = null,
            /**
             * 基础美颜效果ID
             */
            @Transient
            var baseBeautyEffectId: Long? = null,

            /**
             * looks效果ID 多人脸和单人脸使用同一个 美妆区分多人脸和单人脸是因为有「染发」这个全局效果
             */
            @Transient
            var looksEffectId: Long? = null,

            /**
             * 一键美颜效果ID
             */
            @Transient
            var autoBeautyEffectId: Long? = null,

            /**
             * 美型效果ID
             */
            @Transient
            var faceEffectId: Long? = null,

            /**
             * 塑形效果ID
             */
            @Transient
            var reShapeEffectId: Long? = null,

            /**
             * 整妆美妆效果ID
             * 染发效果只能公用整体妆容
             */
            @Transient
            var globalMakeupEffectId: Long? = null,

            /**
             * 多人脸美妆效果ID
             * 多人脸美妆有单独的数据
             */
            @Transient
            var multifaceMakeupEffectId: Long? = null
    ) : Serializable

    /**
     * 判断是否需要入栈
     */
    fun needPushStack(
            oldBeautyItem: PixVideoBeautyItem?
    ): Boolean {
        oldBeautyItem ?: return false
        //跳过个数匹配
        val changeMultiFaceEffect = isMultiFaceEffect != oldBeautyItem.isMultiFaceEffect
        autoBeautyEffect.items.forEach { currentItem ->
            val oldItem = if (changeMultiFaceEffect) {//如果修改过多人脸效果开关 直接拿出全局的比较 有差都入栈
                oldBeautyItem.autoBeautyEffect.items.find { PixVideoBeautyEffectItem.InvalidFaceID == it.faceID }
                        ?: return true
            } else {
                oldBeautyItem.autoBeautyEffect.items.find { currentItem.faceID == it.faceID }
                        ?: return true
            }
            currentItem.params.keys.forEach { key ->
                val currentParam = currentItem.params[key] ?: return true
                val oldParam = oldItem.params[key] ?: return true
                if (currentParam.degree != oldParam.degree) {
                    return true
                }
            }
        }
        baseBeautyEffect.items.forEach { currentItem ->
            val oldItem = if (changeMultiFaceEffect) {//如果修改过多人脸效果开关 直接拿出全局的比较 有差都入栈
                oldBeautyItem.baseBeautyEffect.items.find { PixVideoBeautyEffectItem.InvalidFaceID == it.faceID }
                        ?: return true
            } else {
                oldBeautyItem.baseBeautyEffect.items.find { currentItem.faceID == it.faceID }
                        ?: return true
            }
            currentItem.params.keys.forEach { key ->
                val currentParam = currentItem.params[key] ?: return true
                val oldParam = oldItem.params[key] ?: return true
                if (currentParam.degree != oldParam.degree) {
                    return true
                }
            }
        }
        remodelEffect.items.forEach { currentItem ->
            val oldItem = if (changeMultiFaceEffect) {//如果修改过多人脸效果开关 直接拿出全局的比较 有差都入栈
                oldBeautyItem.remodelEffect.items.find { PixVideoBeautyEffectItem.InvalidFaceID == it.faceID }
                        ?: return true
            } else {
                oldBeautyItem.remodelEffect.items.find { currentItem.faceID == it.faceID }
                        ?: return true
            }
            currentItem.params.keys.forEach { key ->
                val currentParam = currentItem.params[key] ?: return true
                val oldParam = oldItem.params[key] ?: return true
                if (currentParam.degree != oldParam.degree) {
                    return true
                }
            }
        }

        bodyShapeEffect.items.forEach { currentItem ->
            val oldItem = if (changeMultiFaceEffect) {//如果修改过多人脸效果开关 直接拿出全局的比较 有差都入栈
                oldBeautyItem.bodyShapeEffect.items.find { PixVideoBeautyEffectItem.InvalidFaceID == it.faceID }
                        ?: return true
            } else {
                oldBeautyItem.bodyShapeEffect.items.find { currentItem.faceID == it.faceID }
                        ?: return true
            }
            currentItem.params.keys.forEach { key ->
                val currentParam = currentItem.params[key] ?: return true
                val oldParam = oldItem.params[key] ?: return true
                if (currentParam.degree != oldParam.degree) {
                    return true
                }
            }
        }

        looksEffect.items.forEach { currentItem ->
            val oldItem = if (changeMultiFaceEffect) {//如果修改过多人脸效果开关 直接拿出全局的比较 有差都入栈
                oldBeautyItem.looksEffect.items.find { PixVideoBeautyEffectItem.InvalidFaceID == it.faceID }
                        ?: return true
            } else {
                oldBeautyItem.looksEffect.items.find { currentItem.faceID == it.faceID }
                        ?: return true
            }
            currentItem.lookMaterial.let { material ->
                val oldMaterial = oldItem.lookMaterial
                if (material?.alphaX == 0 && oldMaterial == null) {
                    //如果现在效果值是0 和null没区别
                    return@forEach
                }
                oldMaterial ?: return true
                if (material?.alphaX != oldMaterial.alphaX || (material.alphaX != 0 && oldMaterial.alphaX != 0 && (material.materialId != oldMaterial.materialId))) {
                    return true
                }
            }
        }
        makeupEffect.items.forEach { currentItem ->
            val oldItem = if (changeMultiFaceEffect) {//如果修改过多人脸效果开关 直接拿出全局的比较 有差都入栈
                oldBeautyItem.makeupEffect.items.find { PixVideoBeautyEffectItem.InvalidFaceID == it.faceID }
                        ?: return true
            } else {
                oldBeautyItem.makeupEffect.items.find { currentItem.faceID == it.faceID }
                        ?: return true
            }
            currentItem.materials.forEach { material ->
                val oldMaterial = oldItem.materials.find { material.type == it.type }
                if (material.degree == 0f && oldMaterial == null) {
                    //如果现在效果值是0 和null没区别
                    return@forEach
                }
                oldMaterial ?: return true
                if (material.degree != oldMaterial.degree || (material.degree != 0f && oldMaterial.degree != 0f && (material.material.productID != oldMaterial.material.productID
                                || material.otherMaterial.productID != oldMaterial.otherMaterial.productID))
                ) {
                    return true
                }
            }
        }
        return false
    }
}