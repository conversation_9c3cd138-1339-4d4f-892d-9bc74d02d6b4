package com.commsource.videostudio.bean.clips

import com.commsource.repository.child.SpecialEffectRepository
import com.meitu.template.bean.SpecialEffectMaterial

/**
 * 视频特效片段
 */
class PixVideoSpecialEffectItem : PixVideoEffectItem(), PixVideoMaterialInterface {

    override val effectType: Int = EffectType.SPECIAL_EFFECT

    override var material: PixVideoMaterialInterface.PixVideoMaterial =
        PixVideoMaterialInterface.PixVideoMaterial()

    @Transient
    var specialEffectMaterial: SpecialEffectMaterial? = null

    override fun copyItem(withSameKey: Boolean): PixVideoSpecialEffectItem {
        return (super.copyItem(withSameKey) as PixVideoSpecialEffectItem).also {
            // specialEffectMaterial 保持浅拷贝即可
            it.material = material.clone()
        }
    }

    override suspend fun checkItemValid(): Boolean {
        // 检查贴纸是否存在
        if (specialEffectMaterial == null) {
            specialEffectMaterial = SpecialEffectRepository.getSpecialEffect(material.productID)
            // 检查是否已经下载
            if (specialEffectMaterial == null || specialEffectMaterial?.isDownloaded() == false) {
                return false
            }
        }
        return true
    }

    override fun needPaid(): Boolean {
        return specialEffectMaterial?.needPaid()?:false
    }

}