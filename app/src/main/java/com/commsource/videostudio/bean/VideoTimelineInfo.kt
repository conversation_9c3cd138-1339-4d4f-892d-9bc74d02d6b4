package com.commsource.videostudio.bean

import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.util.dpf
import com.meitu.library.util.device.DeviceUtils

/**
 * 时间轴信息
 *
 * 这是一个视频编辑中可编辑的最小单位数据结构
 * 设想是只要一个TimelineInfo给到VideoStudioActivity 就可以构建器视频编辑的数据
 */
class VideoTimelineInfo {

    companion object {
        /**
         * 标准时间轴比例
         */
        val DEFAULT_TIME_RATIO = 3000f / 100.dpf

        /**
         * 最小时间轴比例
         */
        val MIN_TIME_RATIO = 1000 / 100.dpf
    }

    /**
     * 计算显示的时间比例
     */
    fun calculateDisplayTimeRatio() {
        val time = timeRatio * 100.dpf
        displayTimeSpace = when {
            time <= 1500 -> 1000
            time <= 2500 -> 2000
            time <= 4000 -> 3000
            time <= 7500 -> 5000
            time <= 15000 -> 10000
            else -> ((time / 10000).toInt() + 1) * 10000L
        }
    }

    /**
     * 展示的时间比例
     */
    var displayTimeSpace: Long = 3000

    /**
     * 当前时间轴展示比例 是连续的时间比例 不是一个固定的时间比例
     */
    var timeRatio = DEFAULT_TIME_RATIO
        set(value) {
            field = value
        }
        get() {
            if (switchTimeRatioEnd) {
                return timeRatioValuer.endValue //拦截内部计算用的终止位置的timeRatio 保证过渡联动是正确值
            } else {
                return timeRatioValuer.value
            }
        }

    /**
     * 这个字段是动态修改时间轴比例返回值的问题 ClipGroup中有很多计算位置使用到timeRatio，但是timeRatio本身有动画因子在最小可缩小尺寸下，所有有联动动画的时候有位置错误的问题 现在用一个返回值规避 这个字段不能乱用
     */
    var switchTimeRatioEnd: Boolean = false

    val timeRatioValuer = XAnimatorCalculateValuer(DEFAULT_TIME_RATIO)

    /**
     * 屏幕宽度
     */
    val screenWidth = DeviceUtils.getScreenWidth()

    /**
     * 总时长
     */
    var duration: Long = 0L

    fun calculateMaxTimeRatio(v1Duration: Long) {
        maxTimeRatio = (v1Duration / (screenWidth * .8f)).coerceAtLeast(MIN_TIME_RATIO)
    }

    var maxTimeRatio = duration / 100.dpf

    /**
     * 缩放timeline
     */
    fun scaleTimeline(scale: Float) {
        if (scale < 1) {
            val currentRatio =
                (timeRatio / scale).coerceAtLeast(MIN_TIME_RATIO).coerceAtMost(maxTimeRatio)
            if (timeRatio < currentRatio) {
                timeRatioValuer.value = currentRatio
                calculateDisplayTimeRatio()
            }
        } else {
            timeRatioValuer.value = (timeRatio / scale).coerceAtLeast(MIN_TIME_RATIO)
            calculateDisplayTimeRatio()
        }
    }

    /**
     * 主轨道(产品侧V1轨道)
     */
    var mediaClips: MutableList<MediaClipInfo> = arrayListOf()

    /**
     * 装饰轨道(产品侧的V2轨道)
     */
    var multiClips: MutableList<TimelineClipInfo> = arrayListOf()

    /**
     * 音轨 (音乐 + 音效)
     */
    var audioClips: MutableList<TimelineClipInfo> = arrayListOf()

    fun getMainTrackEndTime(): Long {
        return mediaClips.lastOrNull()?.endTime() ?: 0
    }

}