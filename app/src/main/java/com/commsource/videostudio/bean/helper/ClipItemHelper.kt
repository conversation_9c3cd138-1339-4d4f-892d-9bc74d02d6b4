package com.commsource.videostudio.bean.helper

import android.graphics.Matrix
import android.graphics.RectF
import com.commsource.studio.MatrixBox
import com.commsource.videostudio.bean.clips.PixVideoMediaItem
import com.meitu.library.mtmediakit.core.MTMediaEditor


/**
 *  主要是一些片段的计算之类的
 */
object ClipItemHelper {
    /**
     * 计算片段内切于画布的缩放比
     */
    fun getClipItemInitialScale(outputW: Int, outputH: Int, clipItem: PixVideoMediaItem): Float {
        val canvasBound = RectF(0f, 0f, outputW.toFloat(), outputH.toFloat())
        val clipItemBound = RectF(0f, 0f, clipItem.position.width.toFloat(), clipItem.position.height.toFloat())
        val calMatrix = MatrixBox().apply {
            postRotate(clipItem.position.rotate, clipItemBound.centerX(), clipItemBound.centerY())
        }
        calMatrix.matrix.mapRect(clipItemBound)
        calMatrix.matrix.setRectToRect(clipItemBound, canvasBound, Matrix.ScaleToFit.CENTER) // 计算内切矩阵
        return calMatrix.apply { set(matrix) }.getScale()
    }

    /**
     * 获取片段的ClipId
     */
    fun getClipId(editor: MTMediaEditor, clipItem: PixVideoMediaItem): Long? {
        return editor.getSingleClipBySpecialId(clipItem.specialId)?.clipId
    }



    fun getBindSnapshotClipsId(editor: MTMediaEditor, originMediaClipId: Long): LongArray {
        val wrapper = editor.getAllBindSnapshotClipByClipId(originMediaClipId)
        val ids = mutableListOf<Long>()
        ids.add(originMediaClipId)
        wrapper?.beforeSnapshotMediaClip?.defClip?.clipId?.let { clipId ->
            ids.add(clipId)
        }
        wrapper?.afterSnapshotMediaClip?.defClip?.clipId?.let { clipId ->
            ids.add(clipId)
        }
        return ids.toLongArray()
    }

}