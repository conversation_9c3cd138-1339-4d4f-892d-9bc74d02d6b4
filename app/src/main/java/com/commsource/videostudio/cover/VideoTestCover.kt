package com.commsource.videostudio.cover

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverVideoTestBinding
import com.commsource.videostudio.test.TestFunctionViewHolder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter

/**
 * author: admin
 * Date: 2022/11/22
 * Des:
 */
class VideoTestCover : BaseVideoCover<CoverVideoTestBinding>() {

    override fun getLayoutId(): Int {
        return R.layout.cover_video_test
    }

    override fun initView() {
        val adapter = BaseRecyclerViewAdapter(coverGroup.mActivity)
        mViewBinding.rv.layoutManager =
            LinearLayoutManager(coverGroup.mActivity, RecyclerView.HORIZONTAL, false)
        mViewBinding.rv.adapter = adapter
        val data = listOf(
            TestFunction("调色"),
            TestFunction("文字"),
            TestFunction("贴纸"),
            TestFunction("选中"),
            TestFunction("取消选中")
        )
        adapter.setSingleItemEntities(data, TestFunctionViewHolder::class.java)
        adapter.setOnEntityClickListener(TestFunction::class.java) { position, entity ->
            when (entity.name) {
                "调色" -> {
//            effectViewModel.applyAdjustEffect(mutableListOf(PixVideoAdjustItem().apply {
//                adjustType = MTARToneType.ARComparison
//                curValue = 1.0f
//            }))
                }
                "文字" -> {
                    // effectViewModel.addTextClip("artext/text_template/BP_TEX_00000000/ar/configuration.plist")
                }
                "贴纸" -> {
                }

                "选中" -> {
                    // effectViewModel.tempEffect?.setSelected(true)
                }
                "取消选中" -> {
                    //  effectViewModel.tempEffect?.setSelected(false)
                }

            }
            true
        }

    }
}


data class TestFunction(val name: String)