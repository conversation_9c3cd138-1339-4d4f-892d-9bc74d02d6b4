package com.commsource.videostudio.cover

import android.content.Intent
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.ViewModel
import com.commsource.beautyplus.router.RouterEntity
import com.commsource.util.ipermission.PermissionResult
import com.commsource.videostudio.VideoStudioViewModel
import com.commsource.videostudio.func.StudioFunctionViewModel
import com.commsource.videostudio.viewmodel.EffectViewModel

/**
 * author: admin
 * Date: 2022/11/15
 * Des:
 */
abstract class BaseVideoCover<T : ViewDataBinding> : IVideoTransaction<VideoCoverGroup>, LifecycleObserver {
    lateinit var coverGroup: VideoCoverGroup

    /**
     * 布局文件
     */
    lateinit var mViewBinding: T

    lateinit var studioViewModel:VideoStudioViewModel

    lateinit var functionViewModel:StudioFunctionViewModel

    lateinit var effectViewModel: EffectViewModel

    /**
     * 创建layoutView
     */
    private fun createView(parent: ViewGroup): T {
        return DataBindingUtil.inflate<T>(
            LayoutInflater.from(coverGroup.mActivity),
            getLayoutId(),
            parent,
            false
        ) as T
    }

    override fun onAttach(group: VideoCoverGroup) {
        this.coverGroup = group
        studioViewModel = group.studioViewModel
        functionViewModel = group.functionViewModel
        effectViewModel = group.effectViewModel
        mViewBinding = createView(group)
        if (mViewBinding.root.parent == null) {
            this.coverGroup.addView(mViewBinding.root, -1, -1)
        }
        initView()
    }

    override fun onDetach(group: VideoCoverGroup) {
        if (mViewBinding.root.parent == group) {
            group.removeView(mViewBinding.root)
        }
    }

    abstract fun getLayoutId(): Int

    abstract fun initView()

    fun <K : ViewModel> getViewModel(clazz: Class<K>): K {
        return coverGroup.viewModelProvider[clazz]
    }


    override fun onHandleProtocol(webEntity: RouterEntity) {

    }

    override fun onHandleUIProtocol(webEntity: RouterEntity) {

    }

    override fun onDispatchPhysicKeyEvent(event: KeyEvent?): Boolean {
        return false
    }

    override fun onDispatchScreenGestureEvent(event: MotionEvent?): Boolean {
        return false
    }

    override fun onDispatchDeviceOrientationEvent(orientation: Int) {

    }

    override fun onDispatchActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {

    }

    override fun onDispatchPermissionResult(
        results: List<PermissionResult>?,
        isRequestResult: Boolean
    ) {

    }

    override fun onDispatchBackPressed(): Boolean {
        return false
    }

    override fun onLazyCreate() {

    }

}