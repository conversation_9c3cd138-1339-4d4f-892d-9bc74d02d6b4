package com.commsource.videostudio.cover

import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.CoverVideoTabBinding
import com.commsource.camera.util.animationTransition
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.text
import com.commsource.videostudio.func.EditStyle
import com.commsource.videostudio.func.SubFunction
import com.commsource.videostudio.func.VideoEditTabType
import com.commsource.videostudio.func.VideoStudioDisplayMode
import com.commsource.videostudio.func.beauty.VideoBeautyConfig
import com.commsource.videostudio.widget.VideoFuncViewHolder
import com.commsource.widget.SpaceHorizontalItemDecoration
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.utils.ToastUtils
import kotlinx.coroutines.launch

/**
 * 视频底部tab层级
 */
class VideoTabCover : BaseVideoCover<CoverVideoTabBinding>() {

    private val functionAdapter: BaseRecyclerViewAdapter by lazy {
        BaseRecyclerViewAdapter(coverGroup.mActivity)
    }

    override fun getLayoutId(): Int {
        return R.layout.cover_video_tab
    }

    override fun initView() {
        initRv()
    }

    private fun initRv() {
        mViewBinding.rv.addItemDecoration(
            SpaceHorizontalItemDecoration(
                firstItemSpace = 8.dp,
                space = 8.dp,
                lastItemSpace = 8.dp
            )
        )
        mViewBinding.rv.layoutManager =
            LinearLayoutManager(coverGroup.mActivity, RecyclerView.HORIZONTAL, false)

        mViewBinding.rv.adapter = functionAdapter
        functionAdapter.setSingleItemEntities(
            VideoEditTabType.values().toMutableList(),
            VideoFuncViewHolder::class.java
        )
        functionAdapter.setOnEntityClickListener(VideoEditTabType::class.java) { position, entity ->
            MTAnalyticsAgent.logEvent(
                MTAnalyticsConstant.video_edit_first_tab_clk,
                "function",
                entity.analyticsTitle
            )
            //尾帧不让进入音乐音效
            if (effectViewModel.videoPlayFractionEvent.value == 1f || studioViewModel.infoManager.timelineInfo.duration == 0L) {
                if (entity == VideoEditTabType.Music || entity == VideoEditTabType.SoundEffect) {
                    ToastUtils.showShortToast(R.string.v_audio_canont_added.text())
                    return@setOnEntityClickListener false
                }
            }
            if (entity == VideoEditTabType.Import) {
                coverGroup.mActivity.lifecycleScope.launch {
                    effectViewModel.pause()
                    studioViewModel.importV2MediaEvent.emit(true)
                }
            } else {
                if (entity == VideoEditTabType.Beauty && !entity.isEnable) {
                    ToastUtils.showShortToast(R.string.v_disable_beauty_tips)
                } else {
                    functionViewModel.selectVideoEditTab(entity)
                }
            }
            true
        }

        studioViewModel.infoManager.dragClipEvent.observe(coverGroup.mActivity) {
            it?.let {
                mViewBinding.rv.animationTransition(duration = EditStyle.ANIMATION_DURATION) {
                    if (it) {
                        mViewBinding.rv.alpha = 0f
                        mViewBinding.rv.translationY = 64.dpf
                    } else {
                        mViewBinding.rv.alpha = 1f
                        mViewBinding.rv.translationY = 0f
                    }
                }
            }
        }

        functionViewModel.displayModeChangeEvent.observe(coverGroup.mActivity) {
            mViewBinding.root.animationTransition(duration = EditStyle.ANIMATION_DURATION) {
                if (it == VideoStudioDisplayMode.Preview) {
                    mViewBinding.rv.translationY = 64.dpf
                    mViewBinding.rv.alpha = 0f
                } else {
                    mViewBinding.rv.alpha = 1f
                    mViewBinding.rv.translationY = 0f
                }
            }
        }

        /**
         * 清理小红点状态
         */
        functionViewModel.subFunctionEvent.observe(coverGroup.mActivity) {
            it?.let { moduleEnum ->
                if (SubFunction.beautyFunctions.contains(moduleEnum)) {
                    VideoBeautyConfig.setShowSubModuleNewTag(moduleEnum, false)
                }
                functionAdapter.notifyAllItemChange(moduleEnum)
            }
        }

        studioViewModel.infoManager.invalidateDurationEvent.observe(coverGroup.mActivity) {
            if (effectViewModel.holder.curVideoEditorItem?.isMediaEmpty() == true) {
                VideoEditTabType.Beauty.isEnable = false
                functionAdapter.notifyItemChanged(VideoEditTabType.Beauty)
            } else {
                VideoEditTabType.Beauty.isEnable = true
                functionAdapter.notifyItemChanged(VideoEditTabType.Beauty)
            }
        }
    }
}