package com.commsource.videostudio.func.sticker

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.commsource.beautyplus.R
import com.commsource.util.dpf

class SimpleProgressBar : View {

    // 进度条颜色
    private val progressBarColor: Int

    //进度颜色
    private val progressColor: Int

    // 进度宽度
    private val progressStrokeWidth: Float

    //当前进度值
    var curProgress: Int = 0
        set(value) {
            field = value.coerceAtLeast(0)
            invalidate()
        }

    private val barPaint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.STROKE
        strokeCap = Paint.Cap.ROUND
    }


    constructor(ctx: Context, attr: AttributeSet) : super(ctx, attr) {
        val a = context.obtainStyledAttributes(attr, R.styleable.SimpleProgressBar)
        progressBarColor = a.getColor(R.styleable.SimpleProgressBar_simple_progress_bar_color, 0x80ffffff.toInt())
        progressColor = a.getColor(R.styleable.SimpleProgressBar_simple_progress_bar_color, 0xffffffff.toInt())
        progressStrokeWidth = a.getDimension(R.styleable.SimpleProgressBar_simple_progress_stroke_width, 4.dpf())
        a.recycle()
    }


    override fun onDraw(canvas: Canvas) {
        barPaint.strokeWidth = progressStrokeWidth
        barPaint.color = progressBarColor
        // 绘制进度条
        canvas.drawArc(
            progressStrokeWidth / 2.0f,
            progressStrokeWidth / 2.0f,
            width - progressStrokeWidth / 2.0f,
            height - progressStrokeWidth / 2.0f,
            0f,
            360f,
            false,
            barPaint
        )
        barPaint.color = progressColor
        val angle = (curProgress / 100f) * 360f
        // 绘制进度
        canvas.drawArc(
            progressStrokeWidth / 2.0f,
            progressStrokeWidth / 2.0f,
            width - progressStrokeWidth / 2.0f,
            height - progressStrokeWidth / 2.0f, -90f, angle, false, barPaint
        )
    }
}