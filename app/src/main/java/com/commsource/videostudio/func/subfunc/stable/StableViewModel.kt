package com.commsource.videostudio.func.subfunc.stable

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.commsource.beautyplus.R
import com.meitu.media.mtmvcore.MTMVTrack
import kotlin.math.abs
import kotlin.math.min

class StableViewModel(app: Application) : AndroidViewModel(app) {

    //档位刻度列表
    val levelRuleMap = mapOf(
        0 to Pair(
            MTMVTrack.kMTVideoStabilizationNone,
            R.string.v_none
        ),
        33 to Pair(
            MTMVTrack.kMTVideoStabilizationLow,
            R.string.v_low
        ),
        66 to Pair(
            MTMVTrack.kMTVideoStabilizationMedium,
            R.string.v_medium
        ),
        100 to Pair(
            MTMVTrack.kMTVideoStabilizationHigh,
            R.string.v_high
        )
    )

    /**
     * 找到最接近的值
     * [value] 当前值
     * [snapToRight] 是否开启向上对齐
     */
    fun findCloseValue(value: Int, snapToRight: Boolean = false): Pair<Int, Int> {
        val compareList = levelRuleMap.keys.toList()
        var diffTmp = Int.MAX_VALUE
        var indexTmp = 0
        compareList.forEachIndexed { index, i ->
            val diff = abs(i - value)
            if (diff < diffTmp) {
                diffTmp = diff
                indexTmp = index
            }
        }
        if (snapToRight && value - compareList[indexTmp] > 0) {
            val index = min(indexTmp + 1, compareList.lastIndex)
            return Pair(index, compareList[index])
        }
        return Pair(indexTmp, compareList[indexTmp])
    }


    fun getProgress(stableMode: Int): Int {
        levelRuleMap.forEach {
            if (it.value.first == stableMode) {
                return it.key
            }
        }
        return 0
    }
}