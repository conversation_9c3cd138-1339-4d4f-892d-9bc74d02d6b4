package com.commsource.videostudio.func.subfunc.opacity

import android.os.Bundle
import android.view.View
import com.commsource.beautyplus.databinding.FragmentVideoOpacityBinding
import com.commsource.videostudio.bean.clips.PixVideoEffectItem
import com.commsource.videostudio.bean.clips.PixVideoMediaItem
import com.commsource.videostudio.bean.clips.PixVideoStickerItem
import com.commsource.videostudio.func.SubFunction
import com.commsource.videostudio.func.subfunc.BaseBottomSheetFragment
import com.commsource.widget.XSeekBar

class VideoOpacityFragment : BaseBottomSheetFragment() {

    override val panelInfo = Triple(SubFunction.Alpha.bottomHeight?.toInt() ?: 0, SubFunction.Alpha.titleRes, false)

    val binding by lazy {
        FragmentVideoOpacityBinding.inflate(layoutInflater)
    }

    override fun getContentView(): View {
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.seekBar.addOnProgressChangeListener(object : XSeekBar.OnProgressChangeListener {
            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                if (fromUser) {
                    updateBubblePosition(binding.seekBar, progress, leftDx)
                }
                studioModel.selectClipEvent.value?.effectItem?.let {
                    when (it) {
                        is PixVideoStickerItem -> {
                            it.alpha = progress / 100f
                        }
                        is PixVideoMediaItem -> {
                            it.alpha = progress / 100f
                        }
                    }
                    effectModel.applySubEffect(SubFunction.Alpha, it)
                }
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                super.onStopTracking(progress, leftDx, fromUser)
                hideProgressBubble()
                effectModel.pushCurStateToStack()
            }
        })

        studioModel.selectClipEvent.observe(viewLifecycleOwner) {
            val effectItem = it?.effectItem
            if (effectItem is PixVideoStickerItem || effectItem is PixVideoMediaItem) {
                refreshSeekBarProgress(effectItem)
            } else {
                // 选中为空的。并且页面可见。就退出。
                if (isSupportVisible) {
                    functionViewModel.selectSubFunction(null)
                }
            }
        }
        studioModel.selectClipEvent.value?.effectItem?.let { refreshSeekBarProgress(it) }
    }


    private fun refreshSeekBarProgress(item: PixVideoEffectItem) {
        val curProgress = when (item) {
            is PixVideoStickerItem -> {
                item.alpha * 100
            }
            is PixVideoMediaItem -> {
                item.alpha * 100
            }
            else -> 100
        }
        binding.seekBar.setProgress(curProgress.toInt())
    }

}

