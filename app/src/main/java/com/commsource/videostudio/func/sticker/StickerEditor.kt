package com.commsource.videostudio.func.sticker

import com.commsource.videostudio.BaseEditor
import com.commsource.videostudio.bean.clips.EffectType
import com.commsource.videostudio.bean.clips.PixVideoStickerItem
import com.commsource.videostudio.func.SubFunction
import com.commsource.videostudio.func.subfunc.anim.MaterialAnimEditor
import com.meitu.library.mtmediakit.ar.effect.MTAREffectEditor
import com.meitu.library.mtmediakit.ar.effect.model.MTARBubbleEffect
import com.meitu.library.mtmediakit.ar.effect.model.MTARStickerEffect
import com.meitu.library.mtmediakit.constants.MTBlendAttrib
import com.meitu.library.mtmediakit.constants.MTMediaEffectConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class StickerEditor : BaseEditor<PixVideoStickerItem>() {

    /**
     * 套用贴纸效果。
     */
    override suspend fun addEffect(bean: PixVideoStickerItem) {
        super.addEffect(bean)
        //需要lock操作保护贴纸切换线程加载
        lock {
            // 目前贴纸主要是通过底层AR 包进行上效果的。
            // 如果后续有自定义贴纸。则需要通过画中画进行实现。
            val stickerEffect: MTARStickerEffect? = withContext(Dispatchers.IO) {
                MTARStickerEffect.create(
                    VSticker.getConfigPath(bean.material.productID),
                    bean.startTimeInterval,
                    bean.processedDuration
                ).also { it.tag = "STICKER" }
            }
            effectEditor?.initEnvForEffectAfterCreated(stickerEffect)
            //初始化贴纸宽高处理
            stickerEffect?.let {
                //直接读取宽高
                if (bean.position.width <= 0 || bean.position.height <= 0) {
                    fitScreenIfNeed(
                        bean,
                        stateHolder?.curVideoEditorItem?.canvasInfo,
                        it.width.toInt(),
                        it.height.toInt()
                    )?.let {
                        bean.position.scale = it
                    }
                    bean.position.width = it.width.toInt()
                    bean.position.height = it.height.toInt()
                }
                //读取贴纸效果时长
                bean.stickerDuration = stickerEffect.stickDuration.toLong()
                if (bean.originalDuration == 0L) {
                    bean.originalDuration = bean.stickerDuration
                    bean.processedDuration = bean.stickerDuration
                }
            }
            // 设置给底层
            stickerEffect?.let {
                setAttrToNative(bean, it)
                // 添加到底层编辑器
                val effectId = effectEditor?.addAREffect(it)
                if (effectId != MTAREffectEditor.ERROR_EFFECT_ID) {
                    bean.effectID = effectId
                }
                processSubEffect(bean)
            }
        }
    }

    private fun processSubEffect(bean: PixVideoStickerItem) {
        applyAnim(bean)
        applyAlpha(bean)
        applyMix(bean)
        applyFlip(bean)
    }

    override fun visibleEffect(bean: PixVideoStickerItem, visible: Boolean) {
        super.visibleEffect(bean, visible)
        bean.effectID?.let {
            mediaEditor?.getEffect<MTARStickerEffect>(it)?.let {
                it.setVisible(visible)
            }
        }
    }

    /**
     * 更新贴纸
     */
    override fun updateEffect(bean: PixVideoStickerItem) {
        bean.effectID?.let { effectEditor?.getAREffect(it) as? MTARStickerEffect }?.let {
            it.startTime = bean.startTimeInterval
            it.duration = bean.processedDuration
            // 其他底层属性变更。
            setAttrToNative(bean, it)
        }
    }

    /**
     * 移除贴纸。
     */
    override suspend fun removeEffect(bean: PixVideoStickerItem) {
        super.removeEffect(bean)
        bean.effectID?.let {
            effectEditor?.removeAREffect(it)
        }
    }

    /**
     * 属性设置给底层。
     */
    private fun setAttrToNative(
        sticker: PixVideoStickerItem,
        stickerEffect: MTARBubbleEffect<*, *>
    ) {
        stateHolder?.curVideoEditorItem?.canvasInfo?.let {
            stickerEffect.apply {
                touchEventFlag = "STICKER"
                duration = sticker.duration
                setVertexMarkRotateAndScale(MTMediaEffectConstants.VERTEX_RIGHT_BOTTOM)
                // 位置信息设置给底层
                setCenter(
                    it.outputWidth * sticker.position.centerX,
                    it.outputHeight * sticker.position.centerY
                )
                rotateAngle = sticker.position.rotate
                scale = sticker.position.scale
                flip = sticker.position.flipMode
                zLevel = sticker.level
                enableRenderThumbnail = false // True 代表仅仅渲染贴纸的缩略图。(就会只展示静态图)
            }
        }
    }

    /**
     * 变更贴纸透明度。
     */
    override fun applyAlpha(bean: PixVideoStickerItem) {
        bean.effectID?.let { effectEditor?.getAREffect(it) as? MTARBubbleEffect }?.run {
            alpha = bean.alpha
        }
    }

    override fun applyFlip(bean: PixVideoStickerItem) {
        bean.effectID?.let { effectEditor?.getAREffect(it) as? MTARBubbleEffect }?.run {
            flip = bean.position.flipMode
        }
    }


    override fun applyMix(bean: PixVideoStickerItem) {
        bean.effectID?.let { effectEditor?.getAREffect(it) as? MTARBubbleEffect }?.run {
            if (bean.mixType != MTBlendAttrib.kilterTypeNone) {
                materialAnimation.setBlendAttrib(bean.mixType)
            }
        }
    }

    override fun applyAnim(bean: PixVideoStickerItem) {
        val subAnimEditor =
            subEditorFactory?.fetchSubEditor<MaterialAnimEditor<PixVideoStickerItem>>(
                SubFunction.Anim,
                EffectType.STICKER
            )
        subAnimEditor?.applyEffect(bean)
    }


    override suspend fun applyCut(
        bean: PixVideoStickerItem,
        isFreeze: Boolean
    ): PixVideoStickerItem? {
        val cutStickerItem = super.applyCut(bean, isFreeze) ?: return null

        // 处理素材动画问题
        val subAnimEditor =
            subEditorFactory?.fetchSubEditor<MaterialAnimEditor<PixVideoStickerItem>>(
                SubFunction.Anim,
                EffectType.STICKER
            )
        // 处理出入场动画
        bean.endingAnimation = null
        bean.openingAnimation?.let {
            subAnimEditor?.fitMaxLimitDuration(bean)
        }
        cutStickerItem.openingAnimation = null
        cutStickerItem.endingAnimation?.let {
            subAnimEditor?.fitMaxLimitDuration(cutStickerItem)
        }
        // 循环动画全部继承
        cutStickerItem.loopAnimation?.let {
            subAnimEditor?.fitMaxLimitDuration(bean)
        }
        bean.loopAnimation?.let {
            subAnimEditor?.fitMaxLimitDuration(bean)
        }
        updateEffect(bean)
        applyAnim(bean)
        applyAnim(cutStickerItem)
        return cutStickerItem
    }

    override fun updateAnimationDuration(bean: PixVideoStickerItem) {
        bean.effectID?.let { effectEditor?.getAREffect(it) as? MTARBubbleEffect }?.let {
            it.startTime = bean.startTimeInterval
            it.duration = bean.processedDuration
        }
        // 更新动画时长
        val subAnimEditor =
            subEditorFactory?.fetchSubEditor<MaterialAnimEditor<PixVideoStickerItem>>(
                SubFunction.Anim,
                EffectType.STICKER
            )
        subAnimEditor?.fitMaxLimitDuration(bean)
        subAnimEditor?.applyEffect(bean)
    }

}