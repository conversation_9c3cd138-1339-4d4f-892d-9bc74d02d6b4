package com.commsource.videostudio.func.text.style

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.videostudio.bean.clips.PixVideoTextParams

/**
 * author: admin
 * Date: 2022/12/14
 * Des:
 */
class VideoTextStyleViewModel(application: Application):AndroidViewModel(application) {

    // 滑杆气泡相关参数
    val bubbleParam: FloatArray = floatArrayOf(0f, 0f, 0f)
    val bubbleEvent = MutableLiveData<FloatArray?>()

    // 进入调色板存储当前颜色值，调色板有取消颜色设置方法
    var videoTextParams:PixVideoTextParams?=null

    // 拾色器
    val showColorPickerLayerEvent = MutableLiveData<Boolean>()

    // 选色器
    val showColorSelectEvent = NoStickLiveData<Boolean>()


    // 取色器屏障展示事件
    val hideColorPickerEvent = MutableLiveData<Boolean>()

    private val updateShadowLayerState = MutableLiveData<Boolean>()

    fun updateShadowLayerState() {
        updateShadowLayerState.postValue(true)
    }

    fun getShadowLayerState():LiveData<Boolean> {
        return updateShadowLayerState
    }


    val applyEmptyTemplateEvent = NoStickLiveData<Boolean>()

}