package com.commsource.videostudio.func.sticker

import androidx.room.*
import com.meitu.room.dao.IDataResource

@Dao
interface VStickerCategoryDao : IDataResource<VStickerCategory, String> {

    @Query("select * from VStickerCategory where id = :key")
    override fun loadEntity(key: String?): VStickerCategory?

    @Query("select id from VStickerCategory")
    override fun loadKeys(): MutableList<String>?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    override fun insert(entity: VStickerCategory)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    override fun insertAll(list: Array<VStickerCategory>)

    @Update
    override fun updateAll(list: Array<VStickerCategory>)

    @Update
    override fun update(entity: VStickerCategory)

    @Delete
    override fun delete(entity: VStickerCategory)

    @Delete
    override fun deleteAll(array: Array<VStickerCategory>)

    @Query("select * from VStickerCategory")
    fun loadAllEnableEntity(): List<VStickerCategory>
}