package com.commsource.videostudio.func.subfunc.anim

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.util.RTLTool
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.resColor
import com.commsource.util.setRTL

class DivideItemDecoration : RecyclerView.ItemDecoration() {

    var firstItemLeftMargin = 16.dp
    var lastItemRightMargin = 16.dp
    var commonItemMargin = 8.dp
    var divideItemsMargin = 16.dp
    var divideItemStartPosList: List<Int>? = null

    var divideIndicatorW: Float = 2.dpf
    var divideIndicatorH: Float = 16.dpf
    var divideIndicatorColor = R.color.white.resColor() //R.color.Gray_Dashline.resColor()


    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        var start = 0
        var end = 0
        if (position == 0) {
            start = firstItemLeftMargin
        } else {
            start = if (divideItemStartPosList?.contains(position - 1) == true) {
                divideItemsMargin
            } else {
                commonItemMargin
            }

            if (position == (parent.adapter?.itemCount ?: 0) - 1) {
                end = lastItemRightMargin
            }
        }
        outRect.setRTL(start, 0, end, 0)
    }

    val paint = Paint().apply {
        style = Paint.Style.STROKE
        strokeWidth = divideIndicatorW
        strokeCap = Paint.Cap.ROUND
        isAntiAlias = true
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)
        val childCount = parent.childCount
        paint.color = divideIndicatorColor
        for (pos in 0 until childCount) {
            val child = parent.getChildAt(pos)
            val adapterPos = parent.getChildAdapterPosition(child)
            if (divideItemStartPosList?.contains(adapterPos) == true) {
                val start = if (RTLTool.isLayoutRtl()) {
                    child.left - divideItemsMargin / 2.0f
                } else {
                    child.right + divideItemsMargin / 2.0f
                }
                val top = child.top + (child.height - divideIndicatorH) / 2.0f
                c.drawLine(start, top, start, top + divideIndicatorH, paint)
            }
        }
    }


}