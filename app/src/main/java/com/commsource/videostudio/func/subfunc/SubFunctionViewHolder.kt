package com.commsource.videostudio.func.subfunc

import android.content.Context
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemVideoFuncBinding
import com.commsource.util.SpanBuilder
import com.commsource.util.gone
import com.commsource.util.resColor
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.videostudio.func.SubFunction
import com.commsource.videostudio.func.beauty.VideoBeautyConfig
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

/**
 * author: admin
 * Date: 2022/12/3
 * Des:
 */
class SubFunctionViewHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<SubFunction>(context, parent, R.layout.item_video_func) {
    private val binding = ItemVideoFuncBinding.bind(itemView)
    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<SubFunction>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        item.entity.run {
            item.entity.iconFontRes?.let {
                binding.ivIcon.visible()
                binding.imageIcon.gone()
                binding.ivIcon.text = it.text()
            } ?: kotlin.run {
                item.entity.imageIconRes?.let {
                    binding.ivIcon.gone()
                    binding.imageIcon.visible()
                    binding.imageIcon.setImageResource(it)
                }
            }
            binding.tvName.text = item.entity.titleRes.text()
            if (item.entity.isEnable) {
                binding.tvName.setTextColor(R.color.Gray_label_1.resColor())
                binding.ivIcon.setTextColor(R.color.Gray_label_1.resColor())
            } else {
                binding.tvName.setTextColor(R.color.color_4dEBEBEB.resColor())
                binding.ivIcon.setTextColor(R.color.color_4dEBEBEB.resColor())
            }

        }
    }


}