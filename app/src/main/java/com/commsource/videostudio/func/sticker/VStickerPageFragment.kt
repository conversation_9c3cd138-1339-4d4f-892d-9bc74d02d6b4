package com.commsource.videostudio.func.sticker

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.databinding.FragmentVstickerPanelBinding
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.sticker.StickerAuthor
import com.commsource.studio.sticker.StickerAuthorDecoration
import com.commsource.studio.sticker.StickerAuthorVH
import com.commsource.studio.sticker.StickerItemDecoration
import com.commsource.util.MaterialVisibleTracker
import com.commsource.util.ViewShowState
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.visible
import com.commsource.widget.list.XRecyclerViewAdapter
import kotlin.collections.set

class VStickerPageFragment : BaseFragment() {

    private val mViewBinding by lazy { FragmentVstickerPanelBinding.inflate(layoutInflater) }
    private var rvAdapter: XRecyclerViewAdapter? = null
    var curStickerCatId: String? = null
    private val vStickerViewModel by lazy {
        parentFragment?.let {
            ViewModelProvider(it)[VStickerViewModel::class.java]
        }
    }

    val tracker: MaterialVisibleTracker<String> by lazy {
        object : MaterialVisibleTracker<String>(HashMap()) {
            override fun isScrollCheck(): Boolean {
                return isResumed && curStickerCatId == vStickerViewModel?.curSelectCatId
            }

            override fun onCallback(int: Int?, viewHolder: RecyclerView.ViewHolder?) {
                if (viewHolder is VStickerViewHolder && (int == ViewShowState.SHOW_PART || int == ViewShowState.SHOW_COMPLETE)) {
                    viewHolder.item?.entity?.let {
                        if (isFirstVisible(it.id)) {
                            val map = HashMap<String, String>()
                            it.categoryIds?.keys?.elementAtOrNull(0)
                                ?.let { g -> map["贴纸包ID"] = g }
                            map["贴纸素材ID"] = it.id
                            MTAnalyticsAgent.logEvent(
                                MTAnalyticsConstant.video_edit_sticker_imp,
                                map
                            )
                        }
                    }
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return mViewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
        initObserver()
    }

    private fun initUI() {

        mViewBinding.rvVsticker.apply {
            tracker.bindRecyclerView(mViewBinding.rvVsticker)
            val hasAuthorInfo = curStickerCatId != VideoStickerRepo.VSTICKER_HISTORY_ID
                    && curStickerCatId != VideoStickerRepo.VSTICKER_RECOMMEND_ID
            layoutManager = GridLayoutManager(mActivity, 5).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        return when {
                            position >= 0 && position < (adapter?.itemCount ?: 0) - 1 -> 1
                            hasAuthorInfo -> 5
                            else -> 1
                        }
                    }
                }
            }
            adapter = XRecyclerViewAdapter(requireContext(), lifecycleScope).apply {
                setOnEntityClickListener(VSticker::class.java) { pos, entity ->
                    if (vStickerViewModel?.curSticker?.id != entity.id) {
                        val hashMap = HashMap<String, String>().apply {
                            this["贴纸素材ID"] = entity.id
                            entity.categoryIds?.keys?.elementAtOrNull(0)
                                ?.let { g -> this["贴纸包ID"] = g }
                        }
                        MTAnalyticsAgent.logEvent(
                            MTAnalyticsConstant.video_edit_sticker_clk,
                            hashMap
                        )
                    }
                    if (vStickerViewModel?.curClickSticker != entity) {
                        vStickerViewModel?.downloadVSticker(entity)
                    }
                    vStickerViewModel?.curClickSticker = entity
                    true
                }
                vStickerViewModel?.getVStickerCategory(curStickerCatId)?.let {
                    beginTransaction()
                        .addItems(it.vStickers ?: emptyList(), VStickerViewHolder::class.java)
                        .apply {
                            if (hasAuthorInfo) {
                                addItems(
                                    listOf(StickerAuthor(it.copyrightOwner)),
                                    StickerAuthorVH::class.java
                                )
                            }
                        }
                        .commit()
                    // 无数据状态
                    if (it.vStickers?.isEmpty() == true && curStickerCatId == VideoStickerRepo.VSTICKER_HISTORY_ID) {
                        mViewBinding.flEmpty.visible()
                    } else {
                        mViewBinding.flEmpty.gone()
                    }
                }
            }.also { rvAdapter = it }

            addItemDecoration(
                if (hasAuthorInfo) {
                    StickerAuthorDecoration().apply {
                        authorBottomSpace = 54.dp()
                    }
                } else {
                    StickerItemDecoration(5)
                }
            )
        }
    }


    private fun initObserver() {
        vStickerViewModel?.uiSelectEvent?.observe(viewLifecycleOwner) {
            rvAdapter?.currentSelectEntity = it
            mViewBinding.rvVsticker.post {
                rvAdapter?.currentPosition?.takeIf { it >= 0 }?.let {
                    mViewBinding.rvVsticker.smoothScrollToPosition(it)
                }
            }
        }
        vStickerViewModel?.downloadEvent?.observe(viewLifecycleOwner) { sti ->
            rvAdapter?.getItems()?.find {
                val entity = it.entity
                entity is VSticker && entity.id == sti.id
            }?.let {
                rvAdapter?.notifyItemChanged(it.entity)
            }
        }

        if (curStickerCatId == VideoStickerRepo.VSTICKER_HISTORY_ID) {
            vStickerViewModel?.historyDataChangeEvent?.observe(viewLifecycleOwner) {
                if (it != null) {
                    if (it.isEmpty()) {
                        mViewBinding.flEmpty.visible()
                    } else {
                        mViewBinding.flEmpty.gone()
                    }
                    rvAdapter?.beginTransaction()
                        ?.addItems(it, VStickerViewHolder::class.java)
                        ?.commit()
                    rvAdapter?.currentSelectEntity = vStickerViewModel?.uiSelectEvent?.value
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (curStickerCatId == vStickerViewModel?.curSelectCatId) {
            tracker.checkMaterialVisible(mViewBinding.rvVsticker)
        }
    }
}