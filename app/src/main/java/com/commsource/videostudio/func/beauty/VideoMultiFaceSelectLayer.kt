package com.commsource.videostudio.func.beauty

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import com.commsource.studio.BpGestureDetector
import com.commsource.studio.MatrixBox
import com.commsource.util.print
import com.commsource.videostudio.layer.BaseVideoLayer
import java.util.LinkedList

/**
 * 多人脸选择的Mask。
 */
open class VideoMultiFaceSelectLayer(context: Context) : BaseVideoLayer(context) {

    override var isEnable: Boolean = false
        set(value) {
            field = value
            gestureEnable = value
            viewEnable = value
        }

    var isEnableFocus = false

    /**
     * 绘制、响应点击使用的人脸椭圆。
     */
    var faceOvals: List<VideoFaceOval> = LinkedList<VideoFaceOval>()
        set(value) {
            field = value
            selectFaceId?.let {
                selectFaceByFaceID(it)
            }
            layerView.invalidate()
        }

    /**
     * mask画笔
     */
    private val maskPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.BLACK
        alpha = (0.2f * 255).toInt()
    }

    private var maskAlpha = 0f

    /**
     * Mask的呼吸动画。
     */
    private val breathAnimator = ValueAnimator.ofFloat(0f, 1f, 0f).apply {
        duration = 1000
        repeatCount = 1
        interpolator = AccelerateDecelerateInterpolator()
        addUpdateListener {
            maskAlpha = (it.animatedValue as Float) * 0.5f
            layerView.invalidate()
        }
    }

    override fun onAttachToContainer() {
        layerView.invalidate()
        isEnable = true
    }

    override fun onCanvasGestureMatrixChange(matrixBox: MatrixBox) {
        super.onCanvasGestureMatrixChange(matrixBox)
        layerView.postInvalidate()
    }

    override fun onCanvasMatrixChange(canvasMatrix: MatrixBox, isRefreshNow: Boolean) {
        super.onCanvasMatrixChange(canvasMatrix, isRefreshNow)
        layerView.postInvalidate()
    }

    override fun onCreateGestureListener(): BpGestureDetector.NestOnGestureListener {
        return object : BpGestureDetector.NestOnGestureListener() {
            /**
             * 单机事件响应点击人脸。
             */
            override fun onSingleTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean {
                if (!isEnable) {
                    return false
                }
                floatArrayOf(downEvent!!.x, downEvent.y).apply {
                    mapPointContainerToCanvas(this)
                    faceOvals.forEachIndexed { index, faceOval ->
                        if (faceOval.isInFaceRectF(this) && isFaceCanUse(index)) {
                            studioViewModel.effectViewModel.pause()
                            if (studioViewModel.effectViewModel.beautyManager.multiFaceSelectEvent.value != faceOval.faceData.faceNameId
                                || studioViewModel.effectViewModel.beautyManager.defaultSelectFaceId == faceOval.faceData.faceNameId
                            ) {
                                studioViewModel.effectViewModel.beautyManager.defaultSelectFaceId =
                                    null
                                studioViewModel.effectViewModel.beautyManager.needAutoFocus = true
                                studioViewModel.effectViewModel.beautyManager.multiFaceSelectEvent.isFromUser =
                                    true
                                studioViewModel.effectViewModel.beautyManager.multiFaceSelectEvent.value =
                                    faceOval.faceData.faceNameId
                            }
                            return true
                        }
                    }
                    if (!breathAnimator.isRunning) {
                        breathAnimator.start()
                    }
                }
                return true
            }
        }
    }


    override fun onCreateView(): View {
        return DrawTranslateView(context)
    }


    inner class DrawTranslateView(context: Context) : View(context) {
        override fun onDraw(canvas: Canvas) {
            canvas.let { canvas ->
                canvas.translate(viewPortLeft, viewPortTop)
                canvas.clipRect(0f, 0f, viewPortWidth, viewPortHeight)
                // 绘制灰色遮着
                val count = canvas.saveLayer(
                    0f,
                    0f,
                    viewPortWidth,
                    viewPortHeight,
                    null,
                    Canvas.ALL_SAVE_FLAG
                )
                canvas.drawPaint(maskPaint.apply {
                    alpha = (maskAlpha * 255).toInt()
                })
                canvas.translate(-viewPortLeft, -viewPortTop)

                faceOvals.forEachIndexed { index, faceOval ->
                    if (isFaceCanUse(index)) {
                        faceOval.drawCrop(canvas, canvasInitMatrixBox)
                    }
                }
                canvas.restoreToCount(count)
                canvas.translate(-viewPortLeft, -viewPortTop)
                // 绘制红色
                faceOvals.forEachIndexed { index, faceOval ->
                    if (index == 0) {
                        "draw center = ${faceOval.faceData.faceRect.centerX()}".print("yyj2")
                    }
                    if (isFaceCanUse(index)) {
                        faceOval.drawOval(canvas, containerScale, canvasInitMatrixBox)
                    }
                }
            }
        }
    }

    open fun isFaceCanUse(faceIndex: Int): Boolean {
        return true
    }


    /**
     * 选中人脸
     */
    var selectFaceId: Long? = null
    fun selectFaceByFaceID(faceId: Long) {
        this.selectFaceId = faceId
        faceOvals.forEach {
            it.isSelected = it.faceData.faceNameId == faceId
        }
        layerView.postInvalidate()
    }

}