package com.commsource.videostudio.func.soundeffect

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentMusicPageBinding
import com.commsource.beautyplus.util.PathUtil
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.repository.child.SoundEffectRepository
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.util.ErrorNotifier
import com.commsource.util.MaterialVisibleTracker
import com.commsource.util.ViewShowState
import com.commsource.util.text
import com.commsource.videostudio.VideoStudioViewModel
import com.commsource.videostudio.func.music.MusicItemUiState
import com.commsource.videostudio.func.music.MusicItemViewHolder
import com.commsource.videostudio.func.music.XMusicPlayer
import com.commsource.widget.FastCenterScrollLayoutManager
import com.commsource.widget.mask.DataMask
import com.commsource.widget.mask.MaskType
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.library.util.net.NetUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 音乐子页面
 */
class VideoSoundEffectPageFragment : BaseBottomSubFragment() {

    val mViewBinding by lazy { FragmentMusicPageBinding.inflate(layoutInflater) }

    val adapter by lazy { BaseRecyclerViewAdapter(requireContext()) }

    val soundEffectViewModel by lazy { ViewModelProvider(requireParentFragment())[SoundEffectViewModel::class.java] }

    val studioViewModel by lazy { ViewModelProvider(ownerActivity)[VideoStudioViewModel::class.java] }

    private val layoutManager by lazy {
        FastCenterScrollLayoutManager(
            requireContext(),
            LinearLayoutManager.VERTICAL,
            false
        )
    }

    val tracker: MaterialVisibleTracker<String> by lazy {
        object : MaterialVisibleTracker<String>(HashMap()) {
            override fun isScrollCheck(): Boolean {
                return isSupportVisible
            }

            override fun onCallback(int: Int?, viewHolder: RecyclerView.ViewHolder?) {
                if (viewHolder is SoundEffecItemViewHolder && (int == ViewShowState.SHOW_PART || int == ViewShowState.SHOW_COMPLETE)) {
                    viewHolder.item?.entity?.let {
                        if (isFirstVisible(it.data.onlineId)) {
                            MTAnalyticsAgent.logEvent(
                                MTAnalyticsConstant.video_edit_sound_effect_imp,
                                "material_sde_id",
                                it.data.onlineId
                            )
                        }
                    }
                }
            }
        }
    }


    var tagId: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return mViewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mViewBinding.dataMask.maskContainerHelper
            .newBuilder()
            .addMask(
                MaskType.Empty,
                DataMask.EmptyMask(
                    title = R.string.v_soundEffect_favorites_tips.text(),
                    theme = DataMask.DataMaskTheme.Dark()
                )
            )
            .build()

        mViewBinding.rv.adapter = adapter
        mViewBinding.rv.layoutManager = layoutManager
        tracker.bindRecyclerView(mViewBinding.rv)

        tagId = arguments?.getString("tag") ?: ""

        adapter.setOnEntityClickListener(SoundEffectItemUiState::class.java) { position, entity ->
            if (adapter.currentSelectEntity != entity) {
                if (!entity.data.isDownloading()) {
                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.video_edit_sound_effect_clk,
                        "material_sde_id",
                        entity.data.onlineId
                    )
                }
                soundEffectViewModel.applyItem(entity as SoundEffectItemUiState)
            } else {
                attachMusicPlayer(entity as SoundEffectItemUiState)
            }
            false
        }

        adapter.setOnItemChildClickListener { position, item, view ->
            when (view.id) {
                R.id.ifPlay -> {
                    soundEffectViewModel.resetAutoApply()
                    if (XMusicPlayer.isPlaying()) {
                        MTAnalyticsAgent.logEvent(
                            MTAnalyticsConstant.video_edit_sound_effect_clk,
                            "material_sde_id",
                            (item.entity as MusicItemUiState).data.onlineId
                        )
                    }
                    if (adapter.currentSelectEntity != item.entity) {
                        soundEffectViewModel.applyItem(item.entity as SoundEffectItemUiState)
                    } else {
                        attachMusicPlayer(item.entity as SoundEffectItemUiState)
                    }
                }

                R.id.ccUse -> {
                    (item.entity as SoundEffectItemUiState).let {
                        MTAnalyticsAgent.logEvent(
                            MTAnalyticsConstant.video_edit_sound_effect_use,
                            "material_sde_id",
                            (item.entity as SoundEffectItemUiState).data.onlineId
                        )
                        if (it.data.isDownloaded()) {
                            soundEffectViewModel.applyItemTransaction(item.entity as SoundEffectItemUiState)
                        } else {
                            if (!NetUtils.canNetworking()) {
                                ErrorNotifier.showNetworkErrorToast()
                                return@let
                            }
                            soundEffectViewModel.autoWithTransaction = it
                            SoundEffectRepository.requestDownload(
                                it.data,
                                needDownloadDialog = false
                            )
                        }
                    }
                }

                R.id.ifvCollect -> {
                    lifecycleScope.launch {
                        soundEffectViewModel.clickAndUpdateCollect(item.entity as SoundEffectItemUiState)
                        adapter.notifyItemChanged(item.entity, MusicItemViewHolder.PAYLOAD_COLLECT)
                    }
                }
            }
        }

        //数据监听
        lifecycleScope.launch {
            soundEffectViewModel.dataEvent.collect {
                it.pages.find { it.tag.onlineId == tagId }?.let {
                    if (it.maskType == null) {
                        mViewBinding.dataMask.hideAll()
                    } else {
                        mViewBinding.dataMask.showMask(it.maskType)
                    }
                    adapter.updateItemEntities(
                        AdapterDataBuilder.create()
                            .addEntities(it.items, SoundEffecItemViewHolder::class.java)
                            .build()
                    )
                    soundEffectViewModel.visiblePair?.let {
                        if (it.first == tagId) {
                            delay(300)
                            mViewBinding.rv.smoothScrollToPosition(it.second)
                            soundEffectViewModel.visiblePair = null
                        }
                    }
                }
            }
        }

        //套用
        soundEffectViewModel.applyEvent.observe(viewLifecycleOwner) { uiState ->
            adapter.items?.find { it.entity is SoundEffectItemUiState && (it.entity as SoundEffectItemUiState).data == uiState?.data }
                ?.let {
                    attachMusicPlayer(
                        it.entity as SoundEffectItemUiState?,
                        attachControll = uiState?.fromUser == true
                    )
                    uiState?.fromUser = false
                    return@observe
                }
            adapter.currentSelectEntity = null
        }

        //收藏状态更新
        if (tagId == SoundEffectViewModel.CollectTagId) {
            soundEffectViewModel.collectUpdateEvent.observe(viewLifecycleOwner) {
                if (it.maskType == null) {
                    mViewBinding.dataMask.hideAll()
                } else {
                    mViewBinding.dataMask.showMask(it.maskType)
                }
                adapter.updateItemEntities(
                    AdapterDataBuilder.create()
                        .addEntities(it.items, SoundEffecItemViewHolder::class.java)
                        .build()
                        .apply {
                            soundEffectViewModel.applyEvent.value?.let { apply ->
                                this.find { it.entity is SoundEffectItemUiState && (it.entity as SoundEffectItemUiState).data == apply.data }
                                    ?.let {
                                        it.isSelect = true
                                    }
                            }
                        }
                )
            }
        }

        soundEffectViewModel.materialUpdateEvent.observe(viewLifecycleOwner) { item ->
            adapter.notifyAllItemChange(MusicItemViewHolder.PAYLOAD_COLLECT)
        }

        SoundEffectRepository.downloadObserver.successEvent.observe(viewLifecycleOwner) { success ->
            adapter.items?.find { it.entity is SoundEffectItemUiState && (it.entity as SoundEffectItemUiState).data.onlineId == success?.onlineId }
                ?.let {
                    (it.entity as SoundEffectItemUiState).data.readDownloadState(success)
                    adapter.notifyItemChanged(it.entity, MusicItemViewHolder.PAYLOAD_PROGRESS)
                }
        }

        SoundEffectRepository.downloadObserver.progressChangeEvent.observe(viewLifecycleOwner) { update ->
            adapter.items?.find { it.entity is SoundEffectItemUiState && (it.entity as SoundEffectItemUiState).data.onlineId == update?.onlineId }
                ?.let {
                    (it.entity as SoundEffectItemUiState).data.readDownloadState(update)
                    adapter.notifyItemChanged(it.entity, MusicItemViewHolder.PAYLOAD_PROGRESS)
                }
        }

        SoundEffectRepository.downloadObserver.startEvent.observe(viewLifecycleOwner) { start ->
            adapter.items?.find { it.entity is SoundEffectItemUiState && (it.entity as SoundEffectItemUiState).data.onlineId == start?.onlineId }
                ?.let {
                    (it.entity as SoundEffectItemUiState).data.readDownloadState(start)
                    adapter.notifyItemChanged(it.entity, MusicItemViewHolder.PAYLOAD_PROGRESS)
                }
        }

        SoundEffectRepository.downloadObserver.failedEvent.observe(viewLifecycleOwner) { fail ->
            adapter.items?.find { it.entity is SoundEffectItemUiState && (it.entity as SoundEffectItemUiState).data.onlineId == fail?.onlineId }
                ?.let {
                    (it.entity as SoundEffectItemUiState).data.readDownloadState(fail)
                    adapter.notifyItemChanged(it.entity, MusicItemViewHolder.PAYLOAD_PROGRESS)
                }
        }

        SoundEffectRepository.downloadObserver.networkErrorEvent.observe(viewLifecycleOwner) {
            it?.takeIf { it }?.let {
                ErrorNotifier.showNetworkErrorToast()
            }
        }

    }

    override fun onSupportVisible() {
        super.onSupportVisible()
        tracker.checkMaterialVisible(mViewBinding.rv)
    }

    /**
     * 关联音乐播放器
     */
    private fun attachMusicPlayer(
        musicItemUiState: SoundEffectItemUiState?,
        attachControll: Boolean = true
    ) {
        soundEffectViewModel.resetAutoApply()
        if (musicItemUiState?.tag?.onlineId == tagId) {
            adapter.currentSelectEntity = musicItemUiState
            if (XMusicPlayer.isAttach(PathUtil.getSoundEffectPath(musicItemUiState?.data))) {
                if (attachControll) {
                    if (XMusicPlayer.isPlaying()) {
                        XMusicPlayer.pause()
                    } else {
                        //BugFix:不同时播放的处理
                        if (studioViewModel.effectViewModel.isPlaying()) {
                            studioViewModel.effectViewModel.pause()
                        }
                        XMusicPlayer.start(if ((XMusicPlayer.player.getDuration() - XMusicPlayer.player.getCurrentPosition()) <= 10) 0 else -1)
                    }
                }
            } else if (attachControll) {
                //BugFix:不同时播放的处理
                if (studioViewModel.effectViewModel.isPlaying()) {
                    studioViewModel.effectViewModel.pause()
                }
                XMusicPlayer.attach(
                    PathUtil.getSoundEffectPath(musicItemUiState?.data),
                    isLoop = false
                )
            }
        } else {
            adapter.currentSelectEntity = null
        }
    }
}