package com.commsource.videostudio.func.beauty.remold

import android.os.Bundle
import android.util.SparseArray
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentVideoRemoldBinding
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.effect.remold.VideoRemoldEntity
import com.commsource.studio.effect.remold.VideoRemoldEnum
import com.commsource.studio.effect.remold.VideoRemoldPartEnum
import com.commsource.util.ResourcesUtils
import com.commsource.util.dp
import com.commsource.util.getItemViewVisibleRect
import com.commsource.util.gone
import com.commsource.util.visible
import com.commsource.videostudio.VideoStudioConfig
import com.commsource.videostudio.VideoStudioViewModel
import com.commsource.videostudio.bean.clips.PixMediaRemodelSubType
import com.commsource.videostudio.cover.VideoPromptCover
import com.commsource.videostudio.func.StudioFunctionViewModel
import com.commsource.videostudio.func.SubFunction
import com.commsource.videostudio.viewmodel.EffectViewModel
import com.commsource.widget.TableLayoutManager
import com.commsource.widget.XSeekBar
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.utils.ToastUtils
import com.meitu.library.hwanalytics.spm.SPMManager

/**
 * @Description:
 *
 * @Author: vinvince, @Time: 2022/12/5 19:05
 */
class VideoRemoldFragment : BaseBottomSubFragment() {

    val viewBinding by lazy { FragmentVideoRemoldBinding.inflate(layoutInflater) }

    /**
     * 面部重塑不同部位的rv
     */
    private val remoldPartRv = SparseArray<Pair<RecyclerView, BaseRecyclerViewAdapter>>()

    /**
     * 当前选中面部重塑功能。
     */
    private var currentSelectRemoldEntity: VideoRemoldEntity? = null

    /**
     * 当前选中的Tab。
     */
    private var currentSelectPartEnum: VideoRemoldPartEnum? = null

    /**
     * 当前展示的rv。
     */
    private var currentShowView: RecyclerView? = null

    private val functionViewModel by lazy {
        ViewModelProvider(ownerActivity)[StudioFunctionViewModel::class.java]
    }

    val studioViewModel by lazy { ViewModelProvider(ownerActivity)[VideoStudioViewModel::class.java] }

    private val effectViewModel by lazy { ViewModelProvider(ownerActivity)[EffectViewModel::class.java] }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewBinding.ivClose.setOnClickListener {
            functionViewModel.selectSubFunction(null)
        }

        viewBinding.lineSelect.indicatorColor = ResourcesUtils.getColor(R.color.white)

        // 需要折叠收起事件
        viewBinding.flBottom.run {
            onCollapsedEvent = {
                functionViewModel.selectSubFunction(null)
            }
            onCollapsingFractionEvent = {
                if (viewBinding.flAdjust.isVisible) {
                    viewBinding.flAdjust.translationY =
                        (SubFunction.Remold.bottomHeight ?: 0f) * it - 60.dp()
                }
            }
            onCollapsingFractionChangeByGesture = {
                functionViewModel.gestureUpdateBottomHeightChange(228.dp * (1 - it))
            }
        }

        remoldPartRv.apply {
            VideoRemoldPartEnum.values().forEach {
                val pair = Pair(RecyclerView(mActivity), BaseRecyclerViewAdapter(requireContext()))
                pair.first.layoutManager = TableLayoutManager()
                pair.first.adapter = pair.second
                pair.second.setOnEntityClickListener(VideoRemoldEntity::class.java) { position, entity ->
                    if (currentSelectRemoldEntity?.remoldTypeEnum != entity.remoldTypeEnum) {
                        MTAnalyticsAgent.logEvent(
                            MTAnalyticsConstant.BEAUTY_APPR_RESHAPE_CLK,
                            hashMapOf(
                                "子功能" to entity.remoldTypeEnum.statisticName
                            ).apply {
                                putAll(SPMManager.instance.getCurrentSpmInfo())
                            }
                        )
                    }
                    onRemoldEntitySelect(position, entity)
                    false
                }
                pair.second.setOnItemPressChangeListener { isPress, position, item ->
                    if (isPress) {
                        item?.entity?.takeIf { it is VideoRemoldEntity }?.let {
                            it as VideoRemoldEntity
                            ToastUtils.showShortToast(R.string.v_reset_to_default)
                            //长按如果不是当前选中的 直接选中
                            if (currentSelectRemoldEntity != it) {
                                currentSelectRemoldEntity = it
                                pair.second.currentSelectEntity = it
                            }
                            //重置效果值
                            it.alpha = 0f
                            updateRemoldEffect(it)
                            //刷新更新
                            pair.second.notifyAllItemChange()
                            //滑杆更新
                            checkRemoldEntitySelectValueState()
                            //订阅显示处理
                            functionViewModel.showProBanner(effectViewModel.beautyManager.isFaceEffectNeedPay())
                        }
                    }
                }

                put(it.id, pair)
                viewBinding.llContainer.addView(
                    pair.first,
                    -1,
                    -1
                )
            }
        }

        viewBinding.rvTab.layoutManager = TableLayoutManager()
        viewBinding.rvTab.adapter = BaseRecyclerViewAdapter(mActivity).apply {
            setSingleItemEntities(
                VideoRemoldPartEnum.values().asList(),
                VideoRemoldTabViewHolder::class.java,
                true
            )
            setOnEntityClickListener(VideoRemoldPartEnum::class.java) { position, entity ->
                onTabSelect(entity)
                false
            }
        }

        viewBinding.xsbAuto.addOnProgressChangeListener(object : XSeekBar.OnProgressChangeListener {

            override fun onStartTracking(progress: Int, leftDx: Float) {
                super.onStartTracking(progress, leftDx)
                effectViewModel.pause()
            }

            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                super.onProgressChange(progress, leftDx, fromUser)
                if (fromUser) {
                    currentSelectRemoldEntity?.alpha = progress / 100f
                    // 更新小红点
                    currentSelectPartEnum?.let {
                        remoldPartRv[it.id].second.notifyItemChanged(currentSelectRemoldEntity)
                    }
                    updateRemoldEffect(currentSelectRemoldEntity)
                    functionViewModel.showProBanner(effectViewModel.beautyManager.isFaceEffectNeedPay())
                }
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                super.onStopTracking(progress, leftDx, fromUser)
                currentSelectPartEnum?.let {
                    remoldPartRv[it.id].second.currentPosition?.takeIf { it >= 0 }?.let { index ->
                        (remoldPartRv[it.id].second.currentSelectEntity?.takeIf { it is VideoRemoldEntity } as? VideoRemoldEntity)?.run {
                            if (progress == 0 && !VideoStudioConfig.isShowPromptTip(
                                    VideoPromptCover.TIP_TYPE_7
                                )
                            ) {
                                studioViewModel.showTipsEvent.value =
                                    Pair(remoldPartRv[it.id].first.getItemViewVisibleRect(index), VideoPromptCover.TIP_TYPE_7)
                            }
                        }
                    }
                }
            }
        })

        if (currentSelectRemoldEntity == null) {
            // 初始选中第一个Tab
            currentSelectPartEnum = VideoRemoldPartEnum.FaceLift
        }
        onTabSelect(currentSelectPartEnum!!)

        //多人脸选中切换
        effectViewModel.beautyManager.multiFaceSelectEvent.observe(viewLifecycleOwner) {
            onTabSelect(currentSelectPartEnum!!)
            functionViewModel.showProBanner(effectViewModel.beautyManager.isFaceEffectNeedPay())
        }
        //多人脸转台切换
        effectViewModel.beautyManager.multiFaceEffectEvent.observe(viewLifecycleOwner) {
            onTabSelect(currentSelectPartEnum!!)
            functionViewModel.showProBanner(effectViewModel.beautyManager.isFaceEffectNeedPay())
        }
    }

    /**
     * 部位选中
     */
    private fun onTabSelect(remoldPartEnum: VideoRemoldPartEnum) {
        (viewBinding.rvTab.adapter as BaseRecyclerViewAdapter).apply {
            currentSelectEntity = remoldPartEnum
            viewBinding.lineSelect.setSelectIndex(remoldPartEnum.id)
        }
        currentSelectPartEnum = remoldPartEnum
        for (i in 0 until viewBinding.llContainer.childCount) {
            viewBinding.llContainer.getChildAt(i).gone()
        }
        currentShowView = remoldPartRv[remoldPartEnum.id].first
        remoldPartRv[remoldPartEnum.id].first.visible()
        remoldPartRv[remoldPartEnum.id].second.let {
            val result = getRemoldSubEntites(remoldPartEnum)
            result.let { partRemoldEntities ->
                it.updateItemEntities(
                    AdapterDataBuilder.create()
                        .addSelectableEntities(
                            partRemoldEntities,
                            VideoRemoldEffectViewHolder::class.java
                        )
                        .flush(), false
                )
                val index = partRemoldEntities.indexOf(currentSelectRemoldEntity)
                if (index >= 0) {
                    // 协议跳转通过这种方式控制选中。
                    it.setCurrentSelectPosition(index)
                    onRemoldEntitySelect(index, currentSelectRemoldEntity!!)
                } else {
                    it.setCurrentSelectPosition(it.currentPosition)
                    onRemoldEntitySelect(it.currentPosition, partRemoldEntities[it.currentPosition])
                }
            }
        }
    }

    /**
     * 获取当前tab选中下的数据
     */
    private fun getRemoldSubEntites(remoldPartEnum: VideoRemoldPartEnum): List<VideoRemoldEntity> {
        return ArrayList(remoldPartEnum.remoldEnums).map {
            VideoRemoldEntity(it).apply {
                alpha = getRemoldEffectAlpha(this.remoldTypeEnum)
            }
        }
    }

    /**
     * 获取
     */
    private fun getRemoldEffectAlpha(remoldEnum: VideoRemoldEnum): Float {
        return when (remoldEnum) {
            VideoRemoldEnum.SlimFace -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.faceTrans
            )

            VideoRemoldEnum.FaceWidth -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.narrowFace
            )

            VideoRemoldEnum.JawReduction -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.mandible
            )

            VideoRemoldEnum.Chin -> effectViewModel.beautyManager.getFaceDegree(PixMediaRemodelSubType.jawTrans)
            VideoRemoldEnum.BottomHalfOfFace -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.bottomHalfOfFace
            )

            VideoRemoldEnum.HairLine -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.forehead
            )

            VideoRemoldEnum.EyeEnlarge -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.eyeTrans
            )

            VideoRemoldEnum.EyeHeight -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.eyeHeight
            )

            VideoRemoldEnum.EyeWidth -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.eyeDistance
            )

            VideoRemoldEnum.EyeAngle -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.eyeTilt
            )

            VideoRemoldEnum.EyeBrowHeight -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.eyebrowHeight
            )

            VideoRemoldEnum.EyeBrowAngle -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.eyebrowBias
            )

            VideoRemoldEnum.EyeBrowSize -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.eyebrowThick
            )

            VideoRemoldEnum.NoseSize -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.scaleAlaNasi
            )

            VideoRemoldEnum.NoseHeight -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.noseLonger
            )

            VideoRemoldEnum.NoseSwing -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.shrinkNose
            )

            VideoRemoldEnum.NoseBridge -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.bridgeNose
            )

            VideoRemoldEnum.NoseTip -> effectViewModel.beautyManager.getFaceDegree(PixMediaRemodelSubType.nasaltip)
            VideoRemoldEnum.MouthSize -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.mouthTrans
            )

            VideoRemoldEnum.Smile -> effectViewModel.beautyManager.getFaceDegree(PixMediaRemodelSubType.smile)
            VideoRemoldEnum.MouthHeight -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.highMouth
            )

            VideoRemoldEnum.MouthThick -> effectViewModel.beautyManager.getFaceDegree(
                PixMediaRemodelSubType.lip
            )

            else -> 0f
        }
    }

    /**
     * 更新面部重塑效果
     */
    private fun updateRemoldEffect(entity: VideoRemoldEntity?) {
        entity?.let {
            val subType = when (it.remoldTypeEnum) {
                VideoRemoldEnum.SlimFace -> PixMediaRemodelSubType.faceTrans
                VideoRemoldEnum.FaceWidth -> PixMediaRemodelSubType.narrowFace
                VideoRemoldEnum.JawReduction -> PixMediaRemodelSubType.mandible
                VideoRemoldEnum.Chin -> PixMediaRemodelSubType.jawTrans
                VideoRemoldEnum.BottomHalfOfFace -> PixMediaRemodelSubType.bottomHalfOfFace
                VideoRemoldEnum.HairLine -> PixMediaRemodelSubType.forehead
                VideoRemoldEnum.EyeEnlarge -> PixMediaRemodelSubType.eyeTrans
                VideoRemoldEnum.EyeHeight -> PixMediaRemodelSubType.eyeHeight
                VideoRemoldEnum.EyeWidth -> PixMediaRemodelSubType.eyeDistance
                VideoRemoldEnum.EyeAngle -> PixMediaRemodelSubType.eyeTilt
                VideoRemoldEnum.EyeBrowHeight -> PixMediaRemodelSubType.eyebrowHeight
                VideoRemoldEnum.EyeBrowAngle -> PixMediaRemodelSubType.eyebrowBias
                VideoRemoldEnum.EyeBrowSize -> PixMediaRemodelSubType.eyebrowThick
                VideoRemoldEnum.NoseSize -> PixMediaRemodelSubType.scaleAlaNasi
                VideoRemoldEnum.NoseHeight -> PixMediaRemodelSubType.noseLonger
                VideoRemoldEnum.NoseSwing -> PixMediaRemodelSubType.shrinkNose
                VideoRemoldEnum.NoseBridge -> PixMediaRemodelSubType.bridgeNose
                VideoRemoldEnum.NoseTip -> PixMediaRemodelSubType.nasaltip
                VideoRemoldEnum.MouthSize -> PixMediaRemodelSubType.mouthTrans
                VideoRemoldEnum.Smile -> PixMediaRemodelSubType.smile
                VideoRemoldEnum.MouthHeight -> PixMediaRemodelSubType.highMouth
                VideoRemoldEnum.MouthThick -> PixMediaRemodelSubType.lip
                else -> null
            }
            subType?.let {
                effectViewModel.beautyManager.updateFaceDegree(it, entity.alpha)
            }
        }
    }

    private fun onRemoldEntitySelect(position: Int, remoldEntity: VideoRemoldEntity) {
        currentSelectRemoldEntity = remoldEntity
        checkRemoldEntitySelectValueState()
        currentShowView?.smoothScrollToPosition(position)
    }

    /**
     * 检查当前选中的美型
     */
    private fun checkRemoldEntitySelectValueState() {
        currentSelectRemoldEntity?.let {
            viewBinding.xsbAuto.apply {
                if (it.remoldTypeEnum.bothWay) {
                    minProgress = -100
                    maxProgress = 100
                    isEnableCenterPoint = true
                    centerPointPercent = 0.5f
                } else {
                    minProgress = 0
                    maxProgress = 100
                    isEnableCenterPoint = false
                    centerPointPercent = 0f
                }
                setProgress((it.alpha * 100).toInt())
            }
        }
    }

    override fun animateIn(action: () -> Unit) {
        super.animateIn(action)
        functionViewModel.showProBanner(effectViewModel.beautyManager.isFaceEffectNeedPay())
        viewBinding.flBottom.translationY = SubFunction.Remold.bottomHeight!!
        viewBinding.flBottom.collapsing(false)
    }

    override fun animateOut(action: () -> Unit) {
        viewBinding.flBottom.collapsing(true, endAction = { action.invoke() })
    }

}