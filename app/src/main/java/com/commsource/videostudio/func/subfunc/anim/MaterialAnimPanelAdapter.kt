package com.commsource.videostudio.func.subfunc.anim

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter

class MaterialAnimPanelAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {

    private var dataList: List<MaterialAnimCategory>? = null

    fun setData(dataList: List<MaterialAnimCategory>?) {
        this.dataList = dataList
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return dataList?.size ?: 0
    }

    override fun createFragment(position: Int): Fragment {
        return MaterialAnimPageFragment().apply {
            curAnimCatId = dataList?.get(position)?.id
        }
    }
}