package com.commsource.videostudio

import android.graphics.Matrix
import androidx.annotation.CallSuper
import com.commsource.util.dpf
import com.commsource.videostudio.bean.clips.*
import com.commsource.videostudio.func.EditorBuilder
import com.commsource.videostudio.func.MediaEditor
import com.commsource.videostudio.func.subfunc.SubEditorFactory
import com.commsource.videostudio.func.subfunc.SubFuncEditInterface
import com.meitu.library.mtmediakit.ar.effect.MTAREffectEditor
import com.meitu.library.mtmediakit.ar.effect.model.MTARBubbleEffect
import com.meitu.library.mtmediakit.core.MTMediaEditor
import com.meitu.library.mtmediakit.effect.MTBaseEffect
import com.meitu.library.mtmediakit.player.MTMediaPlayer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

abstract class BaseEditor<T : PixVideoEffectItem> : EditorInterface<T>, SubFuncEditInterface<T> {

    var effectEditor: MTAREffectEditor? = null
    var mediaEditor: MTMediaEditor? = null
    var stateHolder: EditorStateHolder? = null // 当前编辑器全局状态，全局通用
    var playerManager: PlayerManager? = null
    var subEditorFactory: SubEditorFactory? = null
    var editorBuilder: EditorBuilder? = null
    var beautyManager: BeautyManager? = null

    @CallSuper
    override suspend fun addEffect(bean: T) {
    }

    @CallSuper
    override suspend fun removeEffect(bean: T) {

    }

    @CallSuper
    override fun visibleEffect(bean: T, visible: Boolean) {

    }

    override suspend fun add(bean: T, index: Int?) {
        stateHolder?.curVideoEditorItem?.addEffectItem(bean, index)
        addEffect(bean)
    }

    override suspend fun remove(bean: T) {
        removeEffect(bean)
        stateHolder?.curVideoEditorItem?.removeEffectItem(bean)
    }


    override fun updatePosition(bean: T) {
        if (bean is PixVideoMovableInterface) {
            bean.effectID?.let { effectEditor?.getAREffect(it) as? MTARBubbleEffect }?.run {
                val outputWidth = stateHolder?.curVideoEditorItem?.canvasInfo?.outputWidth ?: 0
                val outputHeight = stateHolder?.curVideoEditorItem?.canvasInfo?.outputHeight ?: 0
                setCenter(outputWidth * bean.position.centerX, outputHeight * bean.position.centerY)
                rotateAngle = bean.position.rotate
                scale = bean.position.scale
                flip = bean.position.flipMode
            }
        }
    }

    override fun updateTimeDuration(bean: T) {
        bean.effectID?.let {
            val effect: MTBaseEffect<*, *>? = when (bean.effectType) {
                EffectType.MUSIC -> mediaEditor?.getEffect(it)
                EffectType.MEDIA -> mediaEditor?.getEffect(it)
                else -> effectEditor?.getEffect(it)
            }
            effect?.let {
                it.startTime = bean.startTimeInterval
                it.duration = bean.leftTimeInterval + bean.duration
            }
        }
    }

    fun fitScreenIfNeed(
        item: PixVideoMovableInterface,
        canvasInfo: PixVideoCanvasInfo?,
        itemW: Int,
        itemH: Int
    ): Float? {
        canvasInfo ?: return null
        val positionInfo = item.position
        if (positionInfo.width == 0 || positionInfo.height == 0) {
            val canvasW = canvasInfo.outputWidth
            if (item is PixVideoStickerItem || item is PixVideoTextItem) {
                // 三分之一的宽度匹配。
                if (itemW == 0 || canvasW == 0) {
                    return 1f
                }
                return (1 / 3f) / (itemW / canvasW.toFloat())
            }
        } else if (positionInfo.width == -1 || positionInfo.height == -1) {
            return positionInfo.scale // 这边不改变效果的缩放大小
        }
        return null
    }

    /**
     * 更新 Z 轴位置。
     */
    fun updateZLevel(bean: PixVideoEffectItem) {
        bean.effectID?.let {
            val effect: MTBaseEffect<*, *>? = when (bean.effectType) {
                EffectType.MUSIC -> mediaEditor?.getEffect(it)
                EffectType.MEDIA -> mediaEditor?.getEffect(it)
                else -> effectEditor?.getEffect(it)
            }
            effect?.setZLevel(bean.level)
        }
    }


    /**
     *  层级调整功能。
     *  目前只有V2轨道可以进行层级调整。
     */
    override fun applyUpDown(bean: T, isUp: Boolean) {
        val curPlatTime = stateHolder?.curVideoEditorItem?.curPlayTime ?: 0
        val targetItem = if (isUp) {
            stateHolder?.curVideoEditorItem?.v2ClipInfos?.find {
                val range = getTimeOverlappingRange(bean, it)
                range != null && curPlatTime >= range.first && curPlatTime <= range.second && it.level > bean.level
            }
        } else {
            stateHolder?.curVideoEditorItem?.v2ClipInfos?.findLast {
                val range = getTimeOverlappingRange(bean, it)
                range != null && curPlatTime >= range.first && curPlatTime <= range.second && it.level < bean.level
            }
        }
        // 交换Index
        targetItem?.let {
            val newIndex = stateHolder?.curVideoEditorItem?.v2ClipInfos?.indexOf(it) ?: 0
            stateHolder?.curVideoEditorItem?.v2ClipInfos?.remove(bean)
            stateHolder?.curVideoEditorItem?.v2ClipInfos?.add(newIndex, bean)
            stateHolder?.curVideoEditorItem?.fixV2OrA2ZIndex(true)
            stateHolder?.curVideoEditorItem?.v2ClipInfos?.forEach {
                updateZLevel(it)
            }
        }
    }

    private fun getTimeOverlappingRange(
        src: PixVideoEffectItem,
        dst: PixVideoEffectItem
    ): Pair<Long, Long>? {
        if (!(src.startTimeInterval + src.processedDuration <= dst.startTimeInterval
                    || src.startTimeInterval >= dst.startTimeInterval + dst.processedDuration)
        ) {
            // 如果重叠。
            return Pair(
                src.startTimeInterval.coerceAtLeast(dst.startTimeInterval),
                (src.startTimeInterval + src.processedDuration)
                    .coerceAtMost(dst.startTimeInterval + dst.processedDuration)
            )
        }
        return null
    }

    /**
     * 时间区域是否重叠
     */
    private fun isTimeRangeOverlapping(src: PixVideoEffectItem, dst: PixVideoEffectItem): Boolean {
        return !(src.startTimeInterval + src.processedDuration <= dst.startTimeInterval
                || src.startTimeInterval >= dst.startTimeInterval + dst.processedDuration)
    }


    /**
     *  分割Item。不直接套用效果。
     */
    fun cutItem(bean: T): T? {
        val curPlayTime = stateHolder?.curVideoEditorItem?.curPlayTime ?: 0L
        val cutItem = bean.copyItem()
        when {
            // 在开始帧的位置和结束帧的位置，不支持分割。
            // 底层左闭又开。总时长 -1ms 就是尾帧。
            curPlayTime <= bean.startTimeInterval || curPlayTime >= (bean.startTimeInterval + bean.processedDuration - 1) -> {
                return null
            }
            else -> {
                cutItem.processedDuration =
                    bean.startTimeInterval + bean.processedDuration - curPlayTime
            }
        }
        cutItem.startTimeInterval = curPlayTime
        bean.processedDuration = bean.processedDuration - cutItem.processedDuration
        cutItem.leftTimeInterval = bean.leftTimeInterval + bean.duration
        return cutItem as T
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun applyCut(bean: T, isFreeze: Boolean): T? {
        val cutItem = cutItem(bean) ?: return null
        // 插入指定位置
        val curIndex = when (bean.timelineType) {
            TimeLineType.MAIN_TIME_LINE -> stateHolder?.curVideoEditorItem?.v1ClipInfos?.indexOf(
                bean
            )
            TimeLineType.EFFECT_TIME_LINE -> stateHolder?.curVideoEditorItem?.v2ClipInfos?.indexOf(
                bean
            )
            else -> stateHolder?.curVideoEditorItem?.a2ClipInfos?.indexOf(bean)
        }

        curIndex?.takeIf { it >= 0 }?.let { index ->
            val list = stateHolder?.curVideoEditorItem?.insertEffectItem(cutItem, index + 1)
            if (bean.timelineType != TimeLineType.MAIN_TIME_LINE) {
                list?.forEach { updateZLevel(it) }
                (editorBuilder?.fetchBaseEditor(cutItem.effectType) as? BaseEditor<PixVideoEffectItem>)?.let {
                    it.updateEffect(bean) // 更新效果
                    it.addEffect(cutItem)
                }
            } else {
                editorBuilder?.fetchEditor<MediaEditor>()
                    ?.insertMediaEffect2V1(bean as PixVideoMediaItem, index)
            }
        }
        return cutItem
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun applyCopy(
        bean: PixVideoEffectItem,
        currentV1SelectIndex: Int
    ): PixVideoEffectItem {
        val cloneBean = bean.copyItem(false)
        val targetIndex = when (bean.timelineType) {
            TimeLineType.EFFECT_TIME_LINE -> {
                if (cloneBean is PixVideoMovableInterface) {
                    offsetCopyItemPos(cloneBean.position)
                }
                stateHolder?.curVideoEditorItem?.v2ClipInfos?.size
            }
            TimeLineType.MUSIC_TIME_LINE -> {
                cloneBean.level = cloneBean.level + 1
                stateHolder?.curVideoEditorItem?.a2ClipInfos?.size
            }
            else -> {
                stateHolder?.curVideoEditorItem?.v1ClipInfos?.indexOf(bean)
            }
        }

        targetIndex?.takeIf { it >= 0 }?.also { index ->
            if (bean.timelineType != TimeLineType.MAIN_TIME_LINE) {
                val needUpdateList =
                    stateHolder?.curVideoEditorItem?.insertEffectItem(
                        cloneBean,
                        index
                    ) // 插入指定位置。
                (editorBuilder?.fetchBaseEditor(cloneBean.effectType) as? BaseEditor<PixVideoEffectItem>)?.addEffect(
                    cloneBean
                )
                needUpdateList?.forEach { updateZLevel(it) }
            } else {
                if (cloneBean is PixVideoMediaItem) {
                    cloneBean.transition.transitionMaterial = null//被复制的对象 转场清空
                }
                stateHolder?.curVideoEditorItem?.insertEffectItem(
                    cloneBean,
                    currentV1SelectIndex + 1
                )
                //BugFix:V1轨道插入在当前时间指示器的片段
                editorBuilder?.fetchEditor<MediaEditor>()
                    ?.insertMediaEffect2V1(cloneBean as PixVideoMediaItem, currentV1SelectIndex)
            }
        }
        return cloneBean
    }

    /**
     * 偏移图片的位置。
     */
    private fun offsetCopyItemPos(anchor: PixVideoMovableInterface.PositionInfo) {
        // 偏移量
        val offset = 10.dpf
        val containerScale = stateHolder?.curVideoEditorItem?.canvasInfo?.canvasContainerScale ?: 1f
        val canvasW = stateHolder?.curVideoEditorItem?.canvasInfo?.outputWidth?.toFloat() ?: 1f
        val canvasH = stateHolder?.curVideoEditorItem?.canvasInfo?.outputHeight?.toFloat() ?: 1f
        val canvasMatrix = stateHolder?.curVideoEditorItem?.canvasInfo?.canvasMatrix ?: return
        val points = floatArrayOf(anchor.centerX * canvasW, anchor.centerY * canvasH)
        canvasMatrix.mapPoints(points)
        val offsetMatrixBox = Matrix()
        val targetOffset = offset / containerScale //目标偏移距离。
        offsetMatrixBox.postTranslate(targetOffset, -targetOffset)
        offsetMatrixBox.mapPoints(points)
        canvasMatrix.invert(offsetMatrixBox)
        offsetMatrixBox.mapPoints(points)
        anchor.centerX = points[0] / canvasW
        anchor.centerY = points[1] / canvasH
    }

    suspend fun lock(task: suspend (() -> Unit)) = withContext(Dispatchers.Main) {
        val isLock = playerManager?.player?.isLocked == MTMediaPlayer.LOCK_STATUS_YES
        if (!isLock) {
            playerManager?.player?.lock()
        }
        task.invoke()
        if (!isLock) {
            playerManager?.player?.unlock()
        }
    }

}