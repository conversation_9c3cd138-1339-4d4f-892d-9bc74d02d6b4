package com.commsource.videostudio

import android.util.SparseArray
import androidx.core.util.forEach
import androidx.lifecycle.MutableLiveData
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.camera.param.MakeupType
import com.commsource.camera.xcamera.cover.GestureLiveData
import com.commsource.camera.xcamera.cover.bottomFunction.effect.makeup.MakeupConfig
import com.commsource.camera.xcamera.cover.bottomFunction.effect.makeup.MakeupUtils
import com.commsource.camera.xcamera.cover.bottomFunction.effect.makeup.MakeupViewModel
import com.commsource.repository.child.makeup.MakeupMaterial
import com.commsource.studio.looks.LooksDuffleMaterial
import com.commsource.util.ArMaterialHelper
import com.commsource.util.print
import com.commsource.videostudio.bean.clips.DetectorType
import com.commsource.videostudio.bean.clips.PixMediaBeautyType
import com.commsource.videostudio.bean.clips.PixMediaMakeupType
import com.commsource.videostudio.bean.clips.PixMediaRemodelSubType
import com.commsource.videostudio.bean.clips.PixVideoAutoBeautyItem
import com.commsource.videostudio.bean.clips.PixVideoBaseBeautyItem
import com.commsource.videostudio.bean.clips.PixVideoBeautyEffectItem
import com.commsource.videostudio.bean.clips.PixVideoBeautyEffectParam
import com.commsource.videostudio.bean.clips.PixVideoBeautyItem
import com.commsource.videostudio.bean.clips.PixVideoEditorItem
import com.commsource.videostudio.bean.clips.PixVideoEffectItem
import com.commsource.videostudio.bean.clips.PixVideoLooksItem
import com.commsource.videostudio.bean.clips.PixVideoMakeupItem
import com.commsource.videostudio.bean.clips.PixVideoMakeupMaterial
import com.commsource.videostudio.bean.clips.PixVideoMediaItem
import com.commsource.videostudio.bean.clips.PixVideoRemodelItem
import com.commsource.videostudio.bean.clips.PixVideoReshapeItem
import com.commsource.videostudio.bean.clips.PixVideoReshapeType
import com.commsource.videostudio.bean.clips.TimeLineType
import com.commsource.videostudio.detector.BodyShapeDetector
import com.commsource.videostudio.detector.FaceDetector
import com.commsource.videostudio.func.VideoEditTabType
import com.commsource.videostudio.func.beauty.FaceUiState
import com.commsource.videostudio.func.beauty.VideoBeautyConfig
import com.commsource.videostudio.func.bodyshape.VideoBodyShapeEntity
import com.commsource.videostudio.func.bodyshape.VideoBodyShapeEnum
import com.commsource.videostudio.func.bodyshape.switchEnumId
import com.commsource.videostudio.viewmodel.EffectViewModel
import com.meitu.library.mtmediakit.ar.effect.MTAREffectEditor
import com.meitu.library.mtmediakit.ar.effect.model.MTARBeautyFaceEffect
import com.meitu.library.mtmediakit.ar.effect.model.MTARBeautyMakeupEffect
import com.meitu.library.mtmediakit.ar.effect.model.MTARBeautySkinEffect
import com.meitu.library.mtmediakit.constants.MTARBeautyParm
import com.meitu.library.mtmediakit.constants.MTARConstants
import com.meitu.library.mtmediakit.constants.MTAREffectActionRange
import com.meitu.library.mtmediakit.core.MTMediaEditor
import com.meitu.library.mtmediakit.detection.MTAsyncDetector
import com.meitu.library.mtmediakit.effect.MTPipEffect
import com.meitu.library.mtmediakit.listener.OnAREventListener

/**
 * 美颜管理
 * 暂时只是为了把美颜相关方法剥离出来而已
 *
 * 管理多人脸的数据和检测处理
 * 一级美颜的处理
 *
 * 基础美颜、一键美颜、美型、美妆
 */
class BeautyManager(
        val effectEditor: MTAREffectEditor?,
        val mediaEditor: MTMediaEditor?,
        val effectViewModel: EffectViewModel
) {

    var needLog = false

    /**
     * 当前塑形是否支持检测进度
     */
    var isSupportDetectLoading = false

    var detectNum = 0

    companion object {

        /**
         * 不同类型 区分先后顺序上
         */
        const val BaseBeautyZLevel = 0
        const val MakeupZLevel = 1
        const val GlobalMakeupZLevel = 2
        const val FaceZLevel = 3

        /**
         * 美颜判定是否付费
         *
         * 奇怪的订阅逻辑 美颜也要整体媒体付费
         * 因为结构太远了 传递不现实，直接静态判定
         */
        var beautyNeedPaid: Boolean = false
    }

    /**
     * 多人脸状态
     */
    val multiFaceEffectEvent: MutableLiveData<Boolean> = MutableLiveData(false)

    /**
     * 设置多人脸设置 保证多人脸数据从全局人脸状态拷贝
     * 也就是区分多人脸效果
     */
    fun setMultiFaceState(isMultiFace: Boolean) {
        if (effectViewModel.holder.curVideoEditorItem?.beautyItem?.isMultiFaceEffect == isMultiFace) {
            return
        }
        effectViewModel.holder.curVideoEditorItem?.beautyItem?.isMultiFaceEffect = isMultiFace
        multiFaceUiStateEvent.value?.let { faceUiState ->
            effectViewModel.holder.curVideoEditorItem?.let { item ->
                val baseItem =
                        item.beautyItem.baseBeautyEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val faceItem =
                        item.beautyItem.remodelEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val makeupItem =
                        item.beautyItem.makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val autoItem =
                        item.beautyItem.autoBeautyEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val lookItem =
                        item.beautyItem.looksEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val bodyShapeItem =
                        item.beautyItem.bodyShapeEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                if (isMultiFace) {
                    //多人脸效果数据处理
                    val autoItems = ArrayList<PixVideoAutoBeautyItem>()
                    val baseItems = ArrayList<PixVideoBaseBeautyItem>()
                    val faceItems = ArrayList<PixVideoRemodelItem>()
                    val makeupItems = ArrayList<PixVideoMakeupItem>()
                    val lookItems = ArrayList<PixVideoLooksItem>()
                    val reShapeItems = ArrayList<PixVideoReshapeItem>()
                    faceUiState.forEach { faceState ->
                        baseItem?.let {
                            baseItems.add(it.copy().apply { it.faceID = faceState.faceId })
                        }
                        faceItem?.let {
                            faceItems.add(it.copy().apply { it.faceID = faceState.faceId })
                        }
                        makeupItem?.let {
                            makeupItems.add(it.copy().apply { it.faceID = faceState.faceId })
                        }
                        autoItem?.let {
                            autoItems.add(it.copy().apply { it.faceID = faceState.faceId })
                        }
                        lookItem?.let {
                            lookItems.add(it.copy().apply { it.faceID = faceState.faceId })
                        }
                        bodyShapeItem?.let {
                            reShapeItems.add(it.copy().apply { it.faceID = faceState.faceId })
                        }
                    }
                    item.beautyItem.baseBeautyEffect.items =
                            ArrayList<PixVideoBaseBeautyItem>().apply {
                                baseItem?.let {
                                    add(baseItem)
                                }
                                addAll(baseItems)
                            }
                    item.beautyItem.autoBeautyEffect.items =
                            ArrayList<PixVideoAutoBeautyItem>().apply {
                                autoItem?.let {
                                    add(autoItem)
                                }
                                addAll(autoItems)
                            }
                    item.beautyItem.remodelEffect.items =
                            ArrayList<PixVideoRemodelItem>().apply {
                                faceItem?.let {
                                    add(faceItem)
                                }
                                addAll(faceItems)
                            }
                    item.beautyItem.makeupEffect.items =
                            ArrayList<PixVideoMakeupItem>().apply {
                                makeupItem?.let {
                                    add(makeupItem)
                                }
                                addAll(makeupItems)
                            }
                    item.beautyItem.looksEffect.items = ArrayList<PixVideoLooksItem>().apply {
                        lookItem?.let {
                            add(lookItem)
                        }
                        addAll(lookItems)
                    }
                    item.beautyItem.bodyShapeEffect.items =
                            ArrayList<PixVideoReshapeItem>().apply {
                                bodyShapeItem?.let {
                                    add(bodyShapeItem)
                                }
                                addAll(reShapeItems)
                            }
                } else {
                    //去除多人脸效果
                    baseItem?.let {
                        item.beautyItem.baseBeautyEffect.items = arrayListOf(it)
                    }
                    autoItem?.let {
                        item.beautyItem.autoBeautyEffect.items = arrayListOf(it)
                    }
                    faceItem?.let {
                        item.beautyItem.remodelEffect.items = arrayListOf(it)
                    }
                    makeupItem?.let {
                        item.beautyItem.makeupEffect.items = arrayListOf(it)
                    }
                    lookItem?.let {
                        item.beautyItem.looksEffect.items = arrayListOf(it)
                    }
                }
            }
        }
        effectViewModel.holder.curVideoEditorItem?.let { item ->
            removeAutoBeauty(item.beautyItem.globalEffectData)
            removeFaceBeauty(item.beautyItem.globalEffectData)
            removeBaseBeauty(item.beautyItem.globalEffectData)
            removeMakeup(item.beautyItem.globalEffectData)
            removeLookEffect(item.beautyItem.globalEffectData)
            removeReshapeBeauty(item.beautyItem.globalEffectData)
            addBaseBeauty(item.beautyItem.globalEffectData)
            addMakeup(item.beautyItem.globalEffectData, needUpdate = false)
            addLooks(item.beautyItem.globalEffectData, needUpdate = false)
            addFaceBeauty(item.beautyItem.globalEffectData)
            addShapeBeauty(item.beautyItem.globalEffectData)
            addAutoBeauty(item.beautyItem.globalEffectData)
            updateBaseBeautyEffect(item.beautyItem.globalEffectData)
            updateFaceBeautyEffect(item.beautyItem.globalEffectData)
            updateMakeupEffect(item.beautyItem.globalEffectData)
            updateLooksEffect(item.beautyItem.globalEffectData)
            item.beautyItem.pipEffectDatas.forEach {
                removeAutoBeauty(it)
                removeFaceBeauty(it)
                removeBaseBeauty(it)
                removeMakeup(it)
                removeLookEffect(it)
                removeReshapeBeauty(it)
                addBaseBeauty(it)
                addMakeup(it, needUpdate = false)
                addLooks(it, needUpdate = false)
                addFaceBeauty(it)
                addShapeBeauty(it)
                addAutoBeauty(it)
                updateBaseBeautyEffect(it)
                updateFaceBeautyEffect(it)
                updateMakeupEffect(it)
                updateLooksEffect(it)
            }
        }
        multiFaceEffectEvent.value = isMultiFace
    }

    /**
     * 检查多人脸中人脸数量更新以及数据更新
     * 在人脸新增、删除、以及片段替换 删除、检测人脸后处理
     */
    fun adjustMutliFaceData(
            _allFaceData: List<FaceUiState>? = multiFaceUiStateEvent.value,
            removeClipId: Long? = null
    ) {
        val item = effectViewModel.holder.curVideoEditorItem
        item ?: return
        var allFaceData = _allFaceData
        removeClipId?.let {//人脸移除处理
            allFaceData = _allFaceData?.run {
                ArrayList(this).apply {
                    val iterator = iterator()
                    while (iterator.hasNext()) {
                        val current = iterator.next()
                        current.facePositions =
                                ArrayList(current.facePositions).apply {
                                    removeAll { it.clipId == removeClipId }
                                }
                        if (current.facePositions.isEmpty()) {
                            iterator.remove()
                        }
                    }
                }
            }
        }
        //优先移除 需要被移除的人脸
        if (isMultiFaceEffect()) { //内部效果调整
            allFaceData?.let {
                val baseItem =
                        item.beautyItem.baseBeautyEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val faceItem =
                        item.beautyItem.remodelEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val makeupItem =
                        item.beautyItem.makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val autoItem =
                        item.beautyItem.autoBeautyEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val looksItem =
                        item.beautyItem.looksEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                val autoItems = ArrayList<PixVideoAutoBeautyItem>()
                val baseItems = ArrayList<PixVideoBaseBeautyItem>()
                val faceItems = ArrayList<PixVideoRemodelItem>()
                val makeupItems = ArrayList<PixVideoMakeupItem>()
                val looksItems = ArrayList<PixVideoLooksItem>()
                allFaceData?.forEach { uiState ->
                    (item.beautyItem.baseBeautyEffect.items.find { it.faceID == uiState.faceId }
                            ?: baseItem?.run { copy().apply { faceID = uiState.faceId } })?.let {
                        baseItems.add(it)
                    }
                    (item.beautyItem.autoBeautyEffect.items.find { it.faceID == uiState.faceId }
                            ?: autoItem?.run { copy().apply { faceID = uiState.faceId } })?.let {
                        autoItems.add(it)
                    }
                    (item.beautyItem.remodelEffect.items.find { it.faceID == uiState.faceId }
                            ?: faceItem?.run { copy().apply { faceID = uiState.faceId } })?.let {
                        faceItems.add(it)
                    }
                    (item.beautyItem.makeupEffect.items.find { it.faceID == uiState.faceId }
                            ?: makeupItem?.run { copy().apply { faceID = uiState.faceId } })?.let {
                        makeupItems.add(it)
                    }
                    (item.beautyItem.looksEffect.items.find { it.faceID == uiState.faceId }
                            ?: looksItem?.run { copy().apply { faceID = uiState.faceId } })?.let {
                        looksItems.add(it)
                    }
                }
                baseItem?.let {
                    baseItems.add(it)
                }
                autoItem?.let {
                    autoItems.add(it)
                }
                faceItem?.let {
                    faceItems.add(it)
                }
                makeupItem?.let {
                    makeupItems.add(it)
                }
                looksItem?.let {
                    looksItems.add(it)
                }
                item.beautyItem.baseBeautyEffect.items = baseItems
                item.beautyItem.autoBeautyEffect.items = autoItems
                item.beautyItem.remodelEffect.items = faceItems
                item.beautyItem.makeupEffect.items = makeupItems
                item.beautyItem.looksEffect.items = looksItems
            }
            effectViewModel.updateProState()
        }
        //切换后 效果直接重新处理 上效果
        if (!item.beautyItem.isMultiFaceEffect) {
            removeMultiMakeup(item.beautyItem.globalEffectData)
        }
        addMakeup(item.beautyItem.globalEffectData)
        addLooks(item.beautyItem.globalEffectData)
        addBaseBeauty(item.beautyItem.globalEffectData)
        addFaceBeauty(item.beautyItem.globalEffectData)
        addShapeBeauty(item.beautyItem.globalEffectData)
        updateBaseBeautyEffect(item.beautyItem.globalEffectData)
        updateFaceBeautyEffect(item.beautyItem.globalEffectData)
        updateMakeupEffect(item.beautyItem.globalEffectData)
        updateLooksEffect(item.beautyItem.globalEffectData)

        item.beautyItem.pipEffectDatas.forEach {
            if (!item.beautyItem.isMultiFaceEffect) {
                removeMultiMakeup(it)
            }
            addMakeup(it)
            addLooks(it)
            addBaseBeauty(item.beautyItem.globalEffectData)
            addFaceBeauty(item.beautyItem.globalEffectData)
            addShapeBeauty(item.beautyItem.globalEffectData)
            updateBaseBeautyEffect(it)
            updateFaceBeautyEffect(it)
            updateMakeupEffect(it)
            updateLooksEffect(item.beautyItem.globalEffectData)
        }
        multiFaceDetectionState.faceCount = allFaceData?.size ?: 0
        multiFaceUiStateEvent.value = allFaceData
    }

    /**
     * 设置美妆是否可见
     */
    fun setMakeupEnable(isEnable: Boolean) {
        effectEditor?.setBeautyMakeupEffectVisible(isEnable)
    }

    /**
     * 设置美型是否可见
     */
    fun setFaceEnable(isEnable: Boolean) {
        effectEditor?.setBeautyFaceEffectVisible(isEnable)
    }

    /**
     * 设置美颜是否可见
     */
    fun setBeautyEnable(isEnable: Boolean) {
        effectEditor?.setBeautySkinEffectVisible(isEnable)
    }

    /**
     * 移除所有美颜
     */
    suspend fun removeGlobalBeauty(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        removeBaseBeauty(effectItem?.beautyItem?.globalEffectData)
        removeAutoBeauty(effectItem?.beautyItem?.globalEffectData)
        removeFaceBeauty(effectItem?.beautyItem?.globalEffectData)
        removeMakeup(effectItem?.beautyItem?.globalEffectData)
        removeLookEffect(effectItem?.beautyItem?.globalEffectData)
        removeReshapeBeauty(effectItem?.beautyItem?.globalEffectData)
    }

    /**
     * 添加所有美颜
     */
    suspend fun addGlobalBeauty(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        addAllBaseBeauty(effectItem)
        addAllAutoBeauty(effectItem)
        addAllFaceBeauty(effectItem)
        addAllShapeBeauty(effectItem)
        addAllLooks(effectItem)
        addAllMakeup(effectItem)
    }

    private fun addAllBaseBeauty(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        addBaseBeauty(effectItem?.beautyItem?.globalEffectData, effectItem)
        //处理画中画
        effectItem?.beautyItem?.pipEffectDatas?.forEach {
            addBaseBeauty(it)
            updateBaseBeautyEffect(it)
        }
    }

    private fun addAllAutoBeauty(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        addAutoBeauty(effectItem?.beautyItem?.globalEffectData, effectItem)
        //处理画中画
        effectItem?.beautyItem?.pipEffectDatas?.forEach {
            addAutoBeauty(it)
            updateAutoBeautyEffect(it)
        }
    }

    private fun addAllShapeBeauty(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        addShapeBeauty(effectItem?.beautyItem?.globalEffectData, effectItem)
        //处理画中画
        effectItem?.beautyItem?.pipEffectDatas?.forEach {
            addShapeBeauty(it)
            updateBodyShapeBeautyEffect(it)
        }
    }

    private fun addAllLooks(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        addLooks(effectItem?.beautyItem?.globalEffectData, effectItem)
        //处理画中画
        effectItem?.beautyItem?.pipEffectDatas?.forEach {
            addLooks(it)
            updateLooksEffect(it)
        }
    }

    private fun addAllMakeup(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        addMakeup(effectItem?.beautyItem?.globalEffectData, effectItem)
        //处理画中画
        effectItem?.beautyItem?.pipEffectDatas?.forEach {
            addMakeup(it)
            updateMakeupEffect(it)
        }
    }

    private fun addAllFaceBeauty(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        addFaceBeauty(effectItem?.beautyItem?.globalEffectData, effectItem)
        //处理画中画
        effectItem?.beautyItem?.pipEffectDatas?.forEach {
            addFaceBeauty(it)
            updateFaceBeautyEffect(it)
        }
    }

    /**
     * 更新基础美颜效果
     */
    private fun updateBaseBeautyEffect(
            effectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        effectData ?: return
        effectData.baseBeautyEffectId?.let {
            effectEditor?.getEffect<MTARBeautySkinEffect>(it)?.let {
                updateBaseBeautyEffect(effectItem, effectData, it)
            }
        }
    }

    /**
     * 更新美颜效果
     * 内部根据数据区分多人脸配置
     */
    private fun updateBaseBeautyEffect(
            effectItem: PixVideoEditorItem,
            effectData: PixVideoBeautyItem.BeautyEffectData,
            effect: MTARBeautySkinEffect
    ) {
        effect.setDeactivate()
        //全局美颜效果更新
        effectItem.beautyItem.baseBeautyEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                ?.let { item ->
                    setSkinEffectDegree(
                            effect,
                            item,
                            onlyGlobalEffect = effectItem.beautyItem.isMultiFaceEffect,
                            isMultiFace = effectItem.beautyItem.isMultiFaceEffect
                    )
                }
        //基础
        if (effectItem.beautyItem.isMultiFaceEffect) {
            effectItem.beautyItem.baseBeautyEffect.items.forEach { item ->
                if (item.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    effect.activateFace(item.faceID)
                    setSkinEffectDegree(
                            effect,
                            item,
                            excludeGlobalEffect = true,
                            isMultiFace = effectItem.beautyItem.isMultiFaceEffect
                    )
                }
            }
        }
    }

    /**
     * 所有美颜部位
     */
    private val allBeautyParams = arrayListOf<Int>(
            MTARBeautyParm.kParamFlag_AnattaBlur,
            MTARBeautyParm.kParamFlag_Anatta_SHINYCLEAN_SKIN,
            MTARBeautyParm.kParamFlag_AnattaSharpen,
            MTARBeautyParm.kParamFlag_AnattaLaughLineNew,
            MTARBeautyParm.kParamFlag_AnattaAcneClean,
            MTARBeautyParm.kParamFlag_AnattaRemovePouch,
            MTARBeautyParm.kParamFlag_AnattaBrightEye,
            MTARBeautyParm.kParamFlag_AnattaWhiteTeeth
    )

    /**
     * 设置美颜单个效果
     * @param excludeGlobalEffect 移除全局的效果保存
     * @param onlyGlobalEffect 只上全局效果
     * @param isMultiFace 是否多人脸
     */
    private fun setSkinEffectDegree(
            effect: MTARBeautySkinEffect,
            item: PixVideoBaseBeautyItem,
            excludeGlobalEffect: Boolean = false,
            onlyGlobalEffect: Boolean = false,
            isMultiFace: Boolean = false
    ) {
        item.params.keys.forEach { key ->
            val param = when (key) {
                PixMediaBeautyType.smooth -> MTARBeautyParm.kParamFlag_AnattaBlur
                PixMediaBeautyType.oiliness -> MTARBeautyParm.kParamFlag_Anatta_SHINYCLEAN_SKIN
                PixMediaBeautyType.wrinkle -> MTARBeautyParm.kParamFlag_AnattaLaughLineNew
                PixMediaBeautyType.ance -> MTARBeautyParm.kParamFlag_AnattaAcneClean
                PixMediaBeautyType.darkCircle -> MTARBeautyParm.kParamFlag_AnattaRemovePouch
                PixMediaBeautyType.mascra -> MTARBeautyParm.kParamFlag_AnattaBrightEye
                PixMediaBeautyType.teeth -> MTARBeautyParm.kParamFlag_AnattaWhiteTeeth
                else -> null
            }
            param?.let {
                if (excludeGlobalEffect && key == PixMediaBeautyType.ance) {
                    return@forEach
                }
                if (onlyGlobalEffect) {
                    if (key == PixMediaBeautyType.ance) {
                        effect.setBeautyAnattaForFaceControl(it, false)
                        effect.setParmDegree(
                            it,
                            calculateBeautyFaceConvertValue(key, item.params[key]?.degree ?: 0f)
                        )
                    }
                    return@forEach
                }
                effect.setBeautyAnattaForFaceControl(it, isMultiFace)
                effect.setParmDegree(
                        it,
                        calculateBeautyFaceConvertValue(key, item.params[key]?.degree ?: 0f)
                )
            }
        }
    }

    /**
     * 更新具体美颜效果
     */
    fun updateBeautyDegree(
            @PixMediaBeautyType type: String,
            degree: Float,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        needLog = true
        //保存值更新
        val faceId = when {
            type == PixMediaBeautyType.ance -> PixVideoBeautyEffectItem.InvalidFaceID

            effectItem.beautyItem.isMultiFaceEffect -> multiFaceSelectEvent.value

            else -> PixVideoBeautyEffectItem.InvalidFaceID
        }
        //细节、祛斑祛痘是整体效果
        effectItem.beautyItem.baseBeautyEffect.items.find { it.faceID == faceId }
                ?.let {
                    if (it.params[type] == null) {
                        it.params[type] =
                                PixVideoBeautyEffectParam(degree)
                    } else {
                        it.params[type]?.degree = degree
                    }
                }
        //效果更新
        addBaseBeauty(effectItem.beautyItem.globalEffectData)
        updateBaseBeautyEffect(effectItem.beautyItem.globalEffectData)
        effectItem.beautyItem.pipEffectDatas.forEach {
            addBaseBeauty(it)
            updateBaseBeautyEffect(it)
        }
    }


    /**
     * 更新塑形效果
     */
    fun updateBodyShapeDegree(@PixVideoReshapeType type: String, degree: Float,
                              effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        needLog = true
        effectItem?.beautyItem?.let { item ->
            item.bodyShapeEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let {
                        if (it.params[type] == null) {
                            it.params[type] = PixVideoBeautyEffectParam(degree)
                        } else {
                            it.params[type]?.degree = degree
                        }
                    }
            //效果更新
            addShapeBeauty(item.globalEffectData)
            updateBodyShapeBeautyEffect(item.globalEffectData)
            item.pipEffectDatas.forEach {
                addShapeBeauty(it)
                updateBodyShapeBeautyEffect(it)
            }
        }

    }


    /**
     * 更新人脸美型效果
     */
    fun updateFaceDegree(
            @PixMediaRemodelSubType type: String, degree: Float,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        needLog = true
        effectItem?.beautyItem?.let { item ->
            //保存值更新
            if (item.isMultiFaceEffect) {
                item.remodelEffect.items.find { it.faceID == multiFaceSelectEvent.value }?.let {
                    if (it.params[type] == null) {
                        it.params[type] = PixVideoBeautyEffectParam(degree)
                    } else {
                        it.params[type]?.degree = degree
                    }
                }
            } else {
                item.remodelEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                        ?.let {
                            if (it.params[type] == null) {
                                it.params[type] = PixVideoBeautyEffectParam(degree)
                            } else {
                                it.params[type]?.degree = degree
                            }
                        }
            }
            //效果更新
            addFaceBeauty(item.globalEffectData)
            updateFaceBeautyEffect(item.globalEffectData)
            item.pipEffectDatas.forEach {
                addFaceBeauty(it)
                updateFaceBeautyEffect(it)
            }
        }
    }

    /**
     * 添加画中画效果
     */
    fun addPipEffect(
            effectId: Long,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        effectItem.beautyItem.pipEffectDatas =
                ArrayList<PixVideoBeautyItem.BeautyEffectData>(effectItem.beautyItem.pipEffectDatas).apply {
                    add(PixVideoBeautyItem.BeautyEffectData(pipEffectId = effectId).apply {
                        addBaseBeauty(this, effectItem)
                        addAutoBeauty(this, effectItem)
                        addFaceBeauty(this, effectItem)
                        addShapeBeauty(this, effectItem)
                        addMakeup(this, effectItem)
                        addLooks(this, effectItem)
                    })
                }
        "现在画中画效果数量:${effectItem.beautyItem.pipEffectDatas.size}".print("csx")
    }

    /**
     * 移除画中画效果
     */
    fun removePipEffect(
            effectId: Long,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        effectItem.beautyItem.pipEffectDatas =
                ArrayList<PixVideoBeautyItem.BeautyEffectData>(effectItem.beautyItem.pipEffectDatas).apply {
                    find { it.pipEffectId == effectId }?.let {
                        remove(it)
                        it.apply {
                            removeBaseBeauty(this)
                            removeAutoBeauty(this)
                            removeFaceBeauty(this)
                            removeMakeup(this)
                            removeLookEffect(this)
                            removeReshapeBeauty(this)
                        }
                    }
                }
        "现在画中画效果数量:${effectItem.beautyItem.pipEffectDatas.size}".print("csx")
    }

    /**
     * 添加基础美颜
     */
    private fun addBaseBeauty(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem,
    ) {
        beautyEffectData ?: return
        effectItem ?: return
        if (effectItem.beautyItem.hasBaseBeautyEffect() && beautyEffectData.baseBeautyEffectId == null) {
            "添加美颜效果".print("csx")
            val duration = beautyEffectData.pipEffectId?.let {
                mediaEditor?.getEffect<MTPipEffect>(it)?.duration ?: effectViewModel.getDuration()
            } ?: effectViewModel.getV1Duration() ?: effectViewModel.getDuration()
            val effect =
                    MTARBeautySkinEffect.create(
                            VideoBeautyConfig.getNormalBeautyConfigPath(),
                            0,
                            duration
                    ).apply {
                        effectEditor?.initEnvForEffectAfterCreated(this)
                        zLevel = BaseBeautyZLevel
                        if (beautyEffectData.pipEffectId != null) {
                            val pipEffect =
                                mediaEditor?.getEffect<MTPipEffect>(beautyEffectData.pipEffectId!!)
                            attrsConfig?.configBindDetection(true)
                                ?.configBindPipEffect(pipEffect)
                                ?.configActionRange(
                                    MTAREffectActionRange.RANGE_PIP
                                )
                        }
                    }
            effectEditor?.addAREffect(effect)
            updateBaseBeautyEffect(effectItem, beautyEffectData, effect)
            beautyEffectData.baseBeautyEffectId = effect.effectId
        }
    }

    /**
     * 移除基础美颜
     */
    private fun removeBaseBeauty(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?
    ) {
        beautyEffectData?.baseBeautyEffectId?.let {
            effectEditor?.removeAREffect(it)
        }
        beautyEffectData?.baseBeautyEffectId = null
    }

    /**
     * 添加一键美颜
     */
    private fun addAutoBeauty(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData? = effectViewModel.holder.curVideoEditorItem?.beautyItem?.globalEffectData,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        beautyEffectData ?: return
        effectItem ?: return
        if (effectItem.beautyItem.hasAutoEffect() && beautyEffectData.autoBeautyEffectId == null) {
            "添加一键美颜".print("csx")
            val duration = beautyEffectData.pipEffectId?.let {
                mediaEditor?.getEffect<MTPipEffect>(it)?.duration ?: effectViewModel.getDuration()
            } ?: effectViewModel.getV1Duration() ?: effectViewModel.getDuration()
            val autoEffect = MTARBeautySkinEffect.create(
                    VideoBeautyConfig.getNormalBeautyConfigPath(),
                    0, duration
            ).apply {
                effectEditor?.initEnvForEffectAfterCreated(this)
                zLevel = BaseBeautyZLevel
                if (beautyEffectData.pipEffectId != null) {
                    val pipEffect = mediaEditor?.getEffect<MTPipEffect>(beautyEffectData.pipEffectId!!)
                    attrsConfig.configBindDetection(true)
                            .configBindPipEffect(pipEffect)
                            .configActionRange(
                                    MTAREffectActionRange.RANGE_PIP
                            )
                }
            }
            effectEditor?.addAREffect(autoEffect)
            updateAutoBeautyEffect(effectItem, beautyEffectData, autoEffect)
            beautyEffectData.autoBeautyEffectId = autoEffect.effectId
        }
    }

    /**
     * 移除一键美颜
     */
    private fun removeAutoBeauty(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData? = effectViewModel.holder.curVideoEditorItem?.beautyItem?.globalEffectData
    ) {
        beautyEffectData?.autoBeautyEffectId?.let {
            effectEditor?.removeAREffect(it)
        }
        beautyEffectData?.autoBeautyEffectId = null
    }


    /**
     * 添加视频套装
     */
    private fun addLooks(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem,
            needUpdate: Boolean = true
    ) {
        beautyEffectData ?: return
        effectItem ?: return
        var isAdd = false
        if (beautyEffectData.looksEffectId == null) {
            if (!isMultiFaceEffect()) {
                //全局look
                effectItem.takeIf { it.beautyItem.hasLooksEffect(faceId = PixVideoBeautyEffectItem.InvalidFaceID) && beautyEffectData.looksEffectId == null }
                        ?.let { item ->
                            val duration = beautyEffectData.pipEffectId?.let {
                                mediaEditor?.getEffect<MTPipEffect>(it)?.duration
                                        ?: effectViewModel.getDuration()
                            } ?: effectViewModel.getV1Duration() ?: effectViewModel.getDuration()
                            val globalLookItem =
                                    effectItem.beautyItem.looksEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                            val lookEffect = MTARBeautyMakeupEffect.createWithSuitConfig(
                                    globalLookItem?.lookMaterial?.getConfigPath(),
                                    0,
                                    duration,
                                    MTARConstants.CONFIGURATION_FILE_JSON
                            ).apply {
                                effectEditor?.initEnvForEffectAfterCreated(this)
                                loadPublicParamConfig(ArMaterialHelper.LIPSTICK_EDGE_BLUR_CONFIG_VideoEdit)
                                zLevel = GlobalMakeupZLevel
                                if (beautyEffectData.pipEffectId != null) {
                                    val pipEffect = mediaEditor?.getEffect<MTPipEffect>(beautyEffectData.pipEffectId!!)
                                    attrsConfig.configBindDetection(true)
                                            .configBindPipEffect(pipEffect)
                                            .configActionRange(
                                                    MTAREffectActionRange.RANGE_PIP
                                            )
                                }
                            }
                            effectEditor?.addAREffect(lookEffect)
                            beautyEffectData.looksEffectId = lookEffect.effectId
                            isAdd = true
                        }
            } else {
                //多人脸look
                val duration = beautyEffectData.pipEffectId?.let {
                    mediaEditor?.getEffect<MTPipEffect>(it)?.duration
                            ?: effectViewModel.getDuration()
                } ?: effectViewModel.getV1Duration() ?: effectViewModel.getDuration()
                val lookEffect = MTARBeautyMakeupEffect.createMultiFace(
                        0,
                        duration,
                        MTARConstants.CONFIGURATION_FILE_JSON
                ).apply {
                    effectEditor?.initEnvForEffectAfterCreated(this)
                    loadPublicParamConfig(ArMaterialHelper.LIPSTICK_EDGE_BLUR_CONFIG_VideoEdit)
                    zLevel = MakeupZLevel
                    if (beautyEffectData.pipEffectId != null) {
                        val pipEffect = mediaEditor?.getEffect<MTPipEffect>(beautyEffectData.pipEffectId!!)
                        attrsConfig.configBindDetection(true)
                                .configBindPipEffect(pipEffect)
                                .configActionRange(
                                        MTAREffectActionRange.RANGE_PIP
                                )
                    }
                }
                effectEditor?.addAREffect(lookEffect)
                beautyEffectData.looksEffectId = lookEffect.effectId
                isAdd = true
            }
        }
        if (needUpdate) {
            updateLooksEffect(beautyEffectData)
        }
    }

    /**
     * 添加美妆
     */
    private fun addMakeup(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem,
            needUpdate: Boolean = true
    ) {
        beautyEffectData ?: return
        effectItem ?: return
        var isAdd = false
        effectItem.takeIf { it.beautyItem.hasMakeupEffect(faceId = PixVideoBeautyEffectItem.InvalidFaceID) && beautyEffectData.globalMakeupEffectId == null }
                ?.let { item ->
                    val duration = beautyEffectData.pipEffectId?.let {
                        mediaEditor?.getEffect<MTPipEffect>(it)?.duration
                                ?: effectViewModel.getDuration()
                    } ?: effectViewModel.getV1Duration() ?: effectViewModel.getDuration()
                    val effect = MTARBeautyMakeupEffect.create(
                            0,
                            duration
                    ).apply {
                        effectEditor?.initEnvForEffectAfterCreated(this)
                        loadPublicParamConfig(ArMaterialHelper.LIPSTICK_EDGE_BLUR_CONFIG_VideoEdit)
                        zLevel = GlobalMakeupZLevel
                        if (beautyEffectData.pipEffectId != null) {
                            val pipEffect = mediaEditor?.getEffect<MTPipEffect>(beautyEffectData.pipEffectId!!)
                            attrsConfig.configBindDetection(true)
                                    .configBindPipEffect(pipEffect)
                                    .configActionRange(
                                            MTAREffectActionRange.RANGE_PIP
                                    )
                        }
                    }
                    effectEditor?.addAREffect(effect)
                    beautyEffectData.globalMakeupEffectId = effect.effectId
                    isAdd = true
                }

        //多人脸的添加
        effectItem.takeIf { it.beautyItem.hasMakeupEffect() && isMultiFaceEffect() && beautyEffectData.multifaceMakeupEffectId == null }
                ?.let {
                    addMultiMakeup(beautyEffectData)
                    isAdd = true
                }
        if (needUpdate) {
            if (isAdd) {
                updateMakeupEffect(beautyEffectData)
            }
        }
    }

    /**
     * 添加多人脸美妆
     */
    private fun addMultiMakeup(beautyEffectData: PixVideoBeautyItem.BeautyEffectData?) {
        //多人脸
        val duration = beautyEffectData?.pipEffectId?.let {
            mediaEditor?.getEffect<MTPipEffect>(it)?.duration ?: effectViewModel.getDuration()
        } ?: effectViewModel.getV1Duration() ?: effectViewModel.getDuration()
        val multifaceEffect = MTARBeautyMakeupEffect.createMultiFace(
                0,
                duration,
                MTARConstants.CONFIGURATION_FILE_JSON
        ).apply {
            effectEditor?.initEnvForEffectAfterCreated(this)
            loadPublicParamConfig(ArMaterialHelper.LIPSTICK_EDGE_BLUR_CONFIG_VideoEdit)
            zLevel = MakeupZLevel
            if (beautyEffectData?.pipEffectId != null) {
                val pipEffect = mediaEditor?.getEffect<MTPipEffect>(beautyEffectData.pipEffectId!!)
                attrsConfig.configBindDetection(true)
                        .configBindPipEffect(pipEffect)
                        .configActionRange(
                                MTAREffectActionRange.RANGE_PIP
                        )
            }
        }
        effectEditor?.addAREffect(multifaceEffect)
        beautyEffectData?.multifaceMakeupEffectId = multifaceEffect.effectId
    }

    /**
     * 移除多人脸
     */
    private fun removeMultiMakeup(beautyEffectData: PixVideoBeautyItem.BeautyEffectData?) {
        beautyEffectData?.multifaceMakeupEffectId?.let {
            effectEditor?.removeAREffect(it)
            beautyEffectData.multifaceMakeupEffectId = null
        }
    }

    /**
     * 移除美妆
     */
    private fun removeMakeup(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?
    ) {
        beautyEffectData?.globalMakeupEffectId?.let {
            effectEditor?.removeAREffect(it)
            beautyEffectData.globalMakeupEffectId = null
        }
        beautyEffectData?.multifaceMakeupEffectId?.let {
            effectEditor?.removeAREffect(it)
            beautyEffectData.multifaceMakeupEffectId = null
        }
    }

    /**
     *移除套装
     */
    private fun removeLookEffect(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?
    ) {
        beautyEffectData?.looksEffectId?.let {
            effectEditor?.removeAREffect(it)
            beautyEffectData.looksEffectId = null
        }
    }

    /**
     * 移除塑形
     */
    private fun removeReshapeEffect(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?
    ) {
        beautyEffectData?.reShapeEffectId?.let {
            effectEditor?.removeAREffect(it)
            beautyEffectData.reShapeEffectId = null
        }
    }

    /**
     * 更新looks套装效果
     */
    fun updateLooksEffect(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        beautyEffectData ?: return
        val globalEffect = beautyEffectData.looksEffectId?.run {
            effectEditor?.getEffect<MTARBeautyMakeupEffect>(this)
        }
        updateFinalLooksEffect(
                effectItem,
                beautyEffectData,
                globalEffect
        )
    }

    private fun updateLookEffectAlpha(
            effectItem: PixVideoEditorItem,
            effectData: PixVideoBeautyItem.BeautyEffectData,
            lookEffect: MTARBeautyMakeupEffect?
    ) {
        if (isMultiFaceEffect()) {
            lookEffect?.effectModel?.setIsMultiFaceType(true)
            //多人脸设置
            effectItem.beautyItem.looksEffect.items.forEach { item ->
                if (item.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    lookEffect?.activateFace(item.faceID)
                    //更改透明度使用
                    lookEffect?.suitAlpha = item.degree
                }
            }
        } else {
            effectItem.beautyItem.looksEffect.items.forEach { item ->
                if (item.faceID == PixVideoBeautyEffectItem.InvalidFaceID) {
                    //更改透明度使用
                    lookEffect?.suitAlpha = item.degree
                }
            }
        }

    }


    /**
     * 更新套装效果
     */
    private fun updateFinalLooksEffect(
            effectItem: PixVideoEditorItem,
            effectData: PixVideoBeautyItem.BeautyEffectData,
            lookEffect: MTARBeautyMakeupEffect?
    ) {
        if (isMultiFaceEffect()) {
            lookEffect?.effectModel?.setIsMultiFaceType(true)
            //多人脸设置
            effectItem.beautyItem.looksEffect.items.forEach { item ->
                if (item.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    lookEffect?.activateFace(item.faceID)
                    lookEffect?.addARFaceSuitPlist(
                            item.faceID,
                            item.lookMaterial?.getConfigPath()
                    )
                    lookEffect?.suitAlpha = item.degree
                }
            }
        } else {
            //全局
            effectItem.beautyItem.looksEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let { item ->
                        item.lookMaterial?.let {
                            lookEffect?.suitAlpha = item.degree
                        }
                    }
        }
    }

    /**
     * 更新美妆效果
     */
    private fun updateMakeupEffect(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem,
            onlyUpdateColor: Boolean = false
    ) {
        effectItem ?: return
        beautyEffectData ?: return
        val globalEffect = beautyEffectData.globalMakeupEffectId?.run {
            effectEditor?.getEffect<MTARBeautyMakeupEffect>(this)
        }
        val multiFaceEffect = beautyEffectData.multifaceMakeupEffectId?.run {
            effectEditor?.getEffect<MTARBeautyMakeupEffect>(this)
        }
        updateMakeupEffect(
                effectItem,
                beautyEffectData,
                globalEffect,
                multiFaceEffect,
                onlyUpdateColor = onlyUpdateColor
        )
    }

    /**
     * 更新美妆效果
     */
    private fun updateMakeupEffect(
            effectItem: PixVideoEditorItem,
            effectData: PixVideoBeautyItem.BeautyEffectData,
            globalMakeupEffect: MTARBeautyMakeupEffect?,
            multiFaceMakeupEffect: MTARBeautyMakeupEffect?,
            onlyUpdateColor: Boolean = false
    ) {


        if (isMultiFaceEffect()) {
            //区分人脸情况下 全局美妆还是要控制染发效果
            val globalHairMaterial =
                    effectItem.beautyItem.makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }?.materials?.find { it.type == PixMediaMakeupType.hairColor }
            val hairPartName = VideoBeautyConfig.getPartName(PixMediaMakeupType.hairColor)
            clearHairMakeupPart(globalMakeupEffect)//移除全局美妆
            multiFaceMakeupEffect?.effectModel?.setIsMultiFaceType(false)
            if (globalHairMaterial != null) {
                val part = if (onlyUpdateColor) {
                    multiFaceMakeupEffect?.getMakeupPart(hairPartName)
                } else {
                    multiFaceMakeupEffect?.updateMakeupPart(
                            hairPartName,
                            globalHairMaterial.getConfigPath()
                    )
                }
                part?.let {
                    part.partAlpha = globalHairMaterial.degree
                    globalHairMaterial.getRGBA()?.let {
                        part.setPartRgba(it)
                    }
                }
            } else {
                multiFaceMakeupEffect?.removeMakeupPart(hairPartName)
            }
            multiFaceMakeupEffect?.effectModel?.setIsMultiFaceType(true)
            //多人脸设置
            effectItem.beautyItem.makeupEffect.items.forEach { item ->
                if (item.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    multiFaceMakeupEffect?.activateFace(item.faceID)
                    VideoBeautyConfig.getAllMakeupPart().forEach { partName ->
                        var material =
                                item.materials.find { VideoBeautyConfig.getPartName(it.type) == partName }
                        if (partName != VideoBeautyConfig.getPartName(PixMediaMakeupType.hairColor)) {
                            if (material != null) {
                                val part = if (onlyUpdateColor) {
                                    if (multiFaceMakeupEffect?.getAllARFaceMakeupPart(item.faceID) != null) {
                                        multiFaceMakeupEffect.getMakeupPart(partName)
                                    } else {
                                        null
                                    }
                                } else {
                                    multiFaceMakeupEffect?.updateMakeupPart(
                                            partName,
                                            material.getConfigPath()
                                    )
                                }
                                part?.let {
                                    part.partAlpha = material.degree
                                    material.getRGBA()?.let {
                                        part.setPartRgba(it)
                                    }
                                }
                            } else {
                                multiFaceMakeupEffect?.removeMakeupPart(partName)
                            }
                        } else {
                            multiFaceMakeupEffect?.removeMakeupPart(partName)
                        }
                    }
                }
            }
        } else {
            //全局美妆 在多人脸下 去掉染发效果 染发在多人脸的效果中处理
            effectItem.beautyItem.makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let { item ->

                        VideoBeautyConfig.getAllMakeupPart().forEach { partName ->
                            val material =
                                    item.materials.find { VideoBeautyConfig.getPartName(it.type) == partName }
                            //如果在多人脸逻辑下
                            if (material == null) {
                                if (!onlyUpdateColor) {
                                    globalMakeupEffect?.removeMakeupPart(partName)
                                }
                            } else {
                                val effectPart = if (onlyUpdateColor) {
                                    globalMakeupEffect?.getMakeupPart(partName)
                                } else {
                                    globalMakeupEffect?.updateMakeupPart(
                                            partName,
                                            material.getConfigPath()
                                    )
                                }
                                effectPart?.let {
                                    effectPart.partAlpha = material.degree
                                    material.getRGBA()?.let {
                                        effectPart.setPartRgba(it)
                                    }
                                }
                            }
                        }
                    }
        }
    }

    /**
     * 清理所有部位
     */
    private fun clearHairMakeupPart(effect: MTARBeautyMakeupEffect?) {
        effect?.removeMakeupPart(VideoBeautyConfig.getPartName(PixMediaMakeupType.hairColor))
    }


    /**
     * 添加塑形
     */
    private fun addShapeBeauty(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        beautyEffectData ?: return
        effectItem ?: return
        if (effectItem.beautyItem.hasBodyShapeEffect() && beautyEffectData.reShapeEffectId == null) {
            "添加塑形效果".print("shsh")
            val duration = beautyEffectData.pipEffectId?.let {
                mediaEditor?.getEffect<MTPipEffect>(it)?.duration ?: effectViewModel.getDuration()
            } ?: effectViewModel.getV1Duration() ?: effectViewModel.getDuration()
            val effect = MTARBeautyFaceEffect.create(
                    VideoBeautyConfig.getBodyShapeBeautyConfigPath(),
                    0,
                    duration
            ).apply {
                effectEditor?.initEnvForEffectAfterCreated(this)
                zLevel = FaceZLevel
                if (beautyEffectData.pipEffectId != null) {
                    val pipEffect = mediaEditor?.getEffect<MTPipEffect>(beautyEffectData.pipEffectId!!)
                    attrsConfig.configBindDetection(true)
                            .configBindPipEffect(pipEffect)
                            .configActionRange(
                                    MTAREffectActionRange.RANGE_PIP
                            )
                }
            }
            effectEditor?.addAREffect(effect)
            updateFinalBodyShapeEffect(effectItem, beautyEffectData, effect)
            beautyEffectData.reShapeEffectId = effect.effectId
        }
    }


    /**
     * 添加美型
     */
    private fun addFaceBeauty(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        beautyEffectData ?: return
        effectItem ?: return
        if (effectItem.beautyItem.hasFaceEffect() && beautyEffectData.faceEffectId == null) {
            "添加美型效果".print("csx")
            val duration = beautyEffectData.pipEffectId?.let {
                mediaEditor?.getEffect<MTPipEffect>(it)?.duration ?: effectViewModel.getDuration()
            } ?: effectViewModel.getV1Duration() ?: effectViewModel.getDuration()
            val effect = MTARBeautyFaceEffect.create(
                    VideoBeautyConfig.getFaceRemoldBeautyConfigPath(),
                    0,
                    duration
            ).apply {
                effectEditor?.initEnvForEffectAfterCreated(this)
                zLevel = FaceZLevel
                if (beautyEffectData.pipEffectId != null) {
                    val pipEffect = mediaEditor?.getEffect<MTPipEffect>(beautyEffectData.pipEffectId!!)
                    attrsConfig.configBindDetection(true)
                            .configBindPipEffect(pipEffect)
                            .configActionRange(
                                    MTAREffectActionRange.RANGE_PIP
                            )
                }
            }
            effectEditor?.addAREffect(effect)
            updateFaceBeautyEffect(effectItem, beautyEffectData, effect)
            beautyEffectData.faceEffectId = effect.effectId
        }
    }

    /**
     * 移除美型效果
     */
    private fun removeFaceBeauty(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?
    ) {
        beautyEffectData?.faceEffectId?.let {
            effectEditor?.removeAREffect(it)
        }
        beautyEffectData?.faceEffectId = null
    }

    /**
     * 移除塑形效果
     */
    private fun removeReshapeBeauty(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData?
    ) {
        beautyEffectData?.reShapeEffectId?.let {
            effectEditor?.removeAREffect(it)
        }
        beautyEffectData?.reShapeEffectId = null
    }

    /**
     * 更新美型效果参数
     */
    private fun updateFaceBeautyEffect(
            effectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem?.let {
            effectData?.faceEffectId?.let {
                effectEditor?.getEffect<MTARBeautyFaceEffect>(it)?.let {
                    updateFaceBeautyEffect(effectItem, effectData, it)
                }
            }
        }
    }

    /**
     * 更新塑形效果参数
     */
    private fun updateBodyShapeBeautyEffect(
            effectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem?.let {
            effectData?.reShapeEffectId?.let {
                effectEditor?.getEffect<MTARBeautyFaceEffect>(it)?.let {
                    updateFinalBodyShapeEffect(effectItem, effectData, it)
                }
            }
        }
    }


    /**
     * 更新塑形效果
     */
    private fun updateFinalBodyShapeEffect(
            effectItem: PixVideoEditorItem,
            effectData: PixVideoBeautyItem.BeautyEffectData,
            effect: MTARBeautyFaceEffect
    ) {
        effect.cleanParamForAllFaces()
        effect.setDeactivate()
        effectItem.beautyItem.bodyShapeEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                ?.let { item ->
                    setReshapeEffectDegree(effect, item)
                }
    }

    /**
     * 更新美型效果参数
     */
    private fun updateFaceBeautyEffect(
            effectItem: PixVideoEditorItem,
            effectData: PixVideoBeautyItem.BeautyEffectData,
            effect: MTARBeautyFaceEffect
    ) {
//        effect.cleanParamForAllFaces()
        if (effectItem.beautyItem.isMultiFaceEffect) {
            effectItem.beautyItem.remodelEffect.items.forEach {
                if (it.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    effect.activateFace(it.faceID)
                    setFaceEffectDegree(effect, it)
                }
            }
        } else {
            effect.setDeactivate()
            effectItem.beautyItem.remodelEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let { item ->
                        setFaceEffectDegree(effect, item)
                    }
        }
    }


    /**
     * 设置形体效果程度值
     */
    private fun setReshapeEffectDegree(effect: MTARBeautyFaceEffect, item: PixVideoReshapeItem) {
        item.params.keys.forEach { key ->
            val param = when (key) {
                PixVideoReshapeType.slim -> MTARBeautyParm.kParamFlag_Realtime_Slim
                PixVideoReshapeType.slimLeg -> MTARBeautyParm.kParamFlag_Realtime_SlimLeg
                PixVideoReshapeType.longLeg -> MTARBeautyParm.kParamFlag_Realtime_Lengthen
                PixVideoReshapeType.slimHand -> MTARBeautyParm.kParamFlag_Realtime_SlimHand
                PixVideoReshapeType.waist -> MTARBeautyParm.kParamFlag_Realtime_SlimWaist
                PixVideoReshapeType.chest -> MTARBeautyParm.kParamFlag_Realtime_ChestEnlarge
                PixVideoReshapeType.shoulder -> MTARBeautyParm.kParamFlag_Plaotosllop_TensileShoulder
                PixVideoReshapeType.shoulderMLS -> MTARBeautyParm.kParamFlag_Realtime_ShoulderMLS
                PixVideoReshapeType.shrinkHead -> MTARBeautyParm.kParamFlag_Realtime_Shrink_Head
                PixVideoReshapeType.slimHip -> MTARBeautyParm.kParamFlag_Realtime_SlimHip
                PixVideoReshapeType.winkShoulder -> MTARBeautyParm.kParamFlag_Realtime_WinkShoulder

                else -> null
            }
            param?.let {
                //如果是肩宽则与旧版保持一致
                effect.setParmDegree(it, if (it == MTARBeautyParm.kParamFlag_Plaotosllop_TensileShoulder)
                    (item.params[key]?.degree ?: 0f).unaryMinus() else item.params[key]?.degree
                        ?: 0f)
            }
        }
    }

    /**
     * 设置人脸效果程度值
     */
    private fun setFaceEffectDegree(effect: MTARBeautyFaceEffect, item: PixVideoRemodelItem) {
        item.params.keys.forEach { key ->
            val param = when (key) {
                PixMediaRemodelSubType.faceTrans -> MTARBeautyParm.kParamFlag_FaceTrans
                PixMediaRemodelSubType.narrowFace -> MTARBeautyParm.kParamFlag_Narrow_Face
                PixMediaRemodelSubType.mandible -> MTARBeautyParm.kParamFlag_Mandible
                PixMediaRemodelSubType.jawTrans -> MTARBeautyParm.kParamFlag_JawTrans
                PixMediaRemodelSubType.bottomHalfOfFace -> MTARBeautyParm.kParamFlag_MiddleHalfOfFace
                PixMediaRemodelSubType.forehead -> MTARBeautyParm.kParamFlag_Face_Forehead
                PixMediaRemodelSubType.eyeTrans -> MTARBeautyParm.kParamFlag_EyeTrans
                PixMediaRemodelSubType.eyeHeight -> MTARBeautyParm.kParamFlag_EyeHeight
                PixMediaRemodelSubType.eyeDistance -> MTARBeautyParm.kParamFlag_Eye_Distance
                PixMediaRemodelSubType.eyeTilt -> MTARBeautyParm.kParamFlag_EyeTilt
                PixMediaRemodelSubType.eyebrowHeight -> MTARBeautyParm.kParamFlag_EyeBrowsHeight
                PixMediaRemodelSubType.eyebrowBias -> MTARBeautyParm.kParamFlag_EyeBrowsTilt
                PixMediaRemodelSubType.eyebrowThick -> MTARBeautyParm.kParamFlag_EyeBrowSize
                PixMediaRemodelSubType.scaleAlaNasi -> MTARBeautyParm.kParamFlag_ScaleAlaNasi
                PixMediaRemodelSubType.noseLonger -> MTARBeautyParm.kParamFlag_Nose_Longer
                PixMediaRemodelSubType.shrinkNose -> MTARBeautyParm.kParamFlag_ShrinkNose
                PixMediaRemodelSubType.bridgeNose -> MTARBeautyParm.kParamFlag_BtidgeNose
                PixMediaRemodelSubType.nasaltip -> MTARBeautyParm.kParamFlag_Nasaltip
                PixMediaRemodelSubType.mouthTrans -> MTARBeautyParm.kParamFlag_MouthTrans
                PixMediaRemodelSubType.smile -> MTARBeautyParm.kParamFlag_Smile
                PixMediaRemodelSubType.highMouth -> MTARBeautyParm.kParamFlag_HighMouth
                PixMediaRemodelSubType.lip -> MTARBeautyParm.kParamFlag_Lip
                else -> null
            }
            param?.let {
                effect.setParmDegree(it, item.params[key]?.degree ?: 0f)
            }
        }
    }

    /**
     * 更新基础美颜效果
     */
    private fun updateAutoBeautyEffect(
            effectData: PixVideoBeautyItem.BeautyEffectData?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        effectData ?: return
        effectData.autoBeautyEffectId?.let {
            effectEditor?.getEffect<MTARBeautySkinEffect>(it)?.let {
                updateAutoBeautyEffect(effectItem, effectData, it)
            }
        }
    }

    /**
     * 更新自动美颜
     */
    private fun updateAutoBeautyEffect(
            effectItem: PixVideoEditorItem,
            effectData: PixVideoBeautyItem.BeautyEffectData,
            effect: MTARBeautySkinEffect
    ) {
        if (effectItem.beautyItem.isMultiFaceEffect) {
            //一键美颜多人脸先重置所有人脸的效果
            multiFaceUiStateEvent.value?.forEach {
                effect.activateFace(it.faceId)
                allBeautyParams.forEach {
                    effect.setParmDegree(it, 0f)
                }
            }
            effectItem.beautyItem.autoBeautyEffect.items.forEach { item ->
                if (item.faceID != PixVideoBeautyEffectItem.InvalidFaceID) {
                    effect.activateFace(item.faceID)
                    item.params.keys.forEach { key ->
                        val param = when (key) {
                            PixMediaBeautyType.smooth -> MTARBeautyParm.kParamFlag_AnattaBlur
                            PixMediaBeautyType.oiliness -> MTARBeautyParm.kParamFlag_Anatta_SHINYCLEAN_SKIN
                            PixMediaBeautyType.wrinkle -> MTARBeautyParm.kParamFlag_AnattaLaughLineNew
                            PixMediaBeautyType.ance -> MTARBeautyParm.kParamFlag_AnattaAcneClean
                            PixMediaBeautyType.darkCircle -> MTARBeautyParm.kParamFlag_AnattaRemovePouch
                            PixMediaBeautyType.mascra -> MTARBeautyParm.kParamFlag_AnattaBrightEye
                            PixMediaBeautyType.teeth -> MTARBeautyParm.kParamFlag_AnattaWhiteTeeth
                            else -> null
                        }
                        param?.let {
                            effect.setParmDegree(it, item.params[key]?.degree ?: 0f)
                        }
                    }
                }
            }
        } else {
            effect.setDeactivate()
            //基础美颜效果更新
            effectItem.beautyItem.autoBeautyEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let { item ->
                        item.params.keys.forEach { key ->
                            val param = when (key) {
                                PixMediaBeautyType.smooth -> MTARBeautyParm.kParamFlag_AnattaBlur
                                PixMediaBeautyType.oiliness -> MTARBeautyParm.kParamFlag_Anatta_SHINYCLEAN_SKIN
                                PixMediaBeautyType.wrinkle -> MTARBeautyParm.kParamFlag_AnattaLaughLineNew
                                PixMediaBeautyType.ance -> MTARBeautyParm.kParamFlag_AnattaAcneClean
                                PixMediaBeautyType.darkCircle -> MTARBeautyParm.kParamFlag_AnattaRemovePouch
                                PixMediaBeautyType.mascra -> MTARBeautyParm.kParamFlag_AnattaBrightEye
                                PixMediaBeautyType.teeth -> MTARBeautyParm.kParamFlag_AnattaWhiteTeeth
                                else -> null
                            }
                            param?.let {
                                effect.setParmDegree(it, item.params[key]?.degree ?: 0f)
                            }
                        }
                    }
        }
    }

    /**
     * 开启关闭一键美颜
     */
    fun toggleAutoBeauty(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        effectItem ?: return
        needLog = true
        if (isAutoBeauty()) {
            //关闭一键美颜
            effectViewModel.holder.curVideoEditorItem?.beautyItem?.autoBeautyEffect?.let {
                if (isMultiFaceEffect()) {
                    it.items = ArrayList(it.items).apply {
                        find { it.faceID == multiFaceSelectEvent.value }?.let {
                            remove(it)
                        }
                    }
                } else {
                    it.items = ArrayList(it.items).apply {
                        find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }?.let {
                            remove(it)
                        }
                    }
                }
            }
        } else {
            //添加一键美颜
            effectViewModel.holder.curVideoEditorItem?.beautyItem?.autoBeautyEffect?.let {
                if (isMultiFaceEffect()) {
                    it.items = ArrayList(it.items).apply {
                        add(PixVideoAutoBeautyItem().apply {
                            faceID =
                                    multiFaceSelectEvent.value
                                            ?: PixVideoBeautyEffectItem.InvalidFaceID
                        })
                    }
                } else {
                    it.items = ArrayList(it.items).apply {
                        add(PixVideoAutoBeautyItem().apply {
                            faceID = PixVideoBeautyEffectItem.InvalidFaceID
                        })
                    }
                }
            }
        }
        if (needAutoBeauty()) {
            addAutoBeauty()
            updateAutoBeautyEffect(effectItem.beautyItem.globalEffectData)
            effectItem.beautyItem.pipEffectDatas.forEach {
                addAutoBeauty(it)
                updateAutoBeautyEffect(it)
            }
        } else {
            removeAutoBeauty()
            effectItem.beautyItem.pipEffectDatas.forEach {
                removeAutoBeauty(it)
            }
        }
    }

    /**
     * 是否开启一键美颜
     */
    fun isAutoBeauty(): Boolean {
        effectViewModel.holder.curVideoEditorItem?.beautyItem?.autoBeautyEffect?.let {
            if (isMultiFaceEffect()) {
                return it.items.find { multiFaceSelectEvent.value != null && it.faceID == multiFaceSelectEvent.value } != null
            } else {
                return it.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID } != null
            }
        }
        return false
    }

    /**
     * 是否需要一键美颜效果
     */
    fun needAutoBeauty(): Boolean {
        effectViewModel.holder.curVideoEditorItem?.beautyItem?.autoBeautyEffect?.let {
            if (isMultiFaceEffect()) {
                return it.items.find { it.faceID != PixVideoBeautyEffectItem.InvalidFaceID } != null
            } else {
                return it.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID } != null
            }
        }
        return false
    }

    /**
     * 是否多人脸编辑
     */
    fun isMultiFaceEffect(): Boolean {
        return effectViewModel.holder.curVideoEditorItem?.beautyItem?.isMultiFaceEffect ?: false
    }

    /**
     * 多人脸选中目标
     */
    val multiFaceSelectEvent: GestureLiveData<Long?> = GestureLiveData()

    var needAutoFocus: Boolean = false

    var defaultSelectFaceId: Long? = null

    /**
     * 多人脸检测loading状态
     */
    data class MultiFaceDetectionState(
            var faceCount: Int = 0,//已经检测到的人脸
            var progress: Float,
            var isFinish: Boolean
    )

    val multiFaceDetectionState: MultiFaceDetectionState = MultiFaceDetectionState(0, 0f, false)
    val multiFaceDetectLoading: MutableLiveData<MultiFaceDetectionState> =
            MutableLiveData(multiFaceDetectionState)


    data class BodyDetectionState(var progress: Float, var isFinish: Boolean)

    val bodyDetectionState: BodyDetectionState = BodyDetectionState(0f, false)
    val bodyDetectLoading: NoStickLiveData<BodyDetectionState> =
            NoStickLiveData()

    var isBodyDetectStopEvent: NoStickLiveData<Boolean> = NoStickLiveData()

    /**
     * 当前多人脸数据代表当前检测所有多人脸数据 也用于判断是否存在多人脸
     */
    val multiFaceUiStateEvent: NoStickLiveData<List<FaceUiState>?> =
            NoStickLiveData()

    /**
     * 多人脸检测数据
     */
    val multiFaceDetectDataEvent: MutableLiveData<MomentFaceData> = MutableLiveData()

    /**
     * 身体检测数据
     */
    val bodyDetectDataEvent: NoStickLiveData<MomentBodyData> = NoStickLiveData()

    /**
     * 强制发送数据
     */
    private var needSendFaceData = false

    private var needSendBodyData = false

    /**
     * 检查人脸数量
     */
    fun recheckFaceDetectByUndoRedo() {
        needSendFaceData = true
        adjustMutliFaceData(
                effectViewModel.subEditorFactory.fetchSubDetector<FaceDetector>(
                        DetectorType.FACE_DETECTOR
                )?.getAllFaceData()
        )
    }

    private val onArEventList = object : OnAREventListener {

        override fun onAREvent(effectId: Long, arEvent: Int) {
            "onAREvent".print("OnAREventListener")
            effectViewModel?.arEventCallBack(effectId, arEvent)
        }

        override fun onParseDetectionEnd() {
            //解析肢体检测数据完成回调
            "onParseDetectionEnd".print("OnAREventListener")
            //检测结束以后 发送一次所有人体数据
            //解析完所有身体美型数据，获取美型数据
            bodyShapeDetector?.apply {
                detectAllBodyData()
                //用完后调用end
                effectEditor?.endParseDetection()
                bodyShapeDetector = null
            }
        }

        override fun onEffectInitializeEvent(effectId: Long) {

        }
    }

    fun setAREventListener() {
        effectEditor?.setOnAREventListener(onArEventList)
    }


    private var bodyShapeDetector: BodyShapeDetector? = null


    /**
     * 新增媒体片段后，检查是否自动进行肢体检测
     * 如果有调整过塑形参数，添加片段或者替换片段后自动进行肢体检测
     * 如果没有调整过塑形参数，添加片段或者替换片段就不需要自动进行肢体检测
     * 如果进行了自动检测，检测完的画面可自动应用效果
     */
    fun checkValidToBodyDetect(): Boolean {
        var curHasEffect = false
        getCurrentBodyShapeDetectData().takeIf { it.isNotEmpty() }?.let { shapeEffectData ->
            for (index in 0 until shapeEffectData.size) {
                if (shapeEffectData[index].alpha != 0f) {
                    curHasEffect = true
                    break
                }
            }
        }
        return curHasEffect
    }

    /**
     * 自动检测完之后肢体上效果
     */
    fun updateNewClipMediaEffect() {
        getCurrentBodyShapeDetectData().takeIf { it.isNotEmpty() }?.let { shapeEffectData ->
            for (index in 0 until shapeEffectData.size) {
                if (shapeEffectData[index].alpha != 0f) {
                    //上效果
                    updateBodyShapeEffect(shapeEffectData[index])
                }
            }
        }
    }


    /**
     * 更新塑形子功能效果
     */
    fun updateBodyShapeEffect(entity: VideoBodyShapeEntity?) {
        entity?.let {
            val subType = when (it.bodyShapeEnum) {
                VideoBodyShapeEnum.SlimBody -> PixVideoReshapeType.slim
                VideoBodyShapeEnum.Waist -> PixVideoReshapeType.waist
                VideoBodyShapeEnum.ThinLeg -> PixVideoReshapeType.slimLeg
                VideoBodyShapeEnum.LongLeg -> PixVideoReshapeType.longLeg
                VideoBodyShapeEnum.Breast -> PixVideoReshapeType.chest
                VideoBodyShapeEnum.Shoulder -> PixVideoReshapeType.shoulder
                VideoBodyShapeEnum.ThinArms -> PixVideoReshapeType.slimHand
                VideoBodyShapeEnum.ReshapeHip -> PixVideoReshapeType.slimHip
                VideoBodyShapeEnum.ReshapeHead -> PixVideoReshapeType.shrinkHead
                VideoBodyShapeEnum.StraightShoulders -> PixVideoReshapeType.winkShoulder
                VideoBodyShapeEnum.ReshapeElegant -> PixVideoReshapeType.shoulderMLS
                else -> null
            }
            subType?.let {
                effectViewModel.beautyManager.updateBodyShapeDegree(it, entity.alpha)
            }
        }
    }

    /**
     * 获取当前生效的塑形效果值
     */
    fun getCurrentBodyShapeDetectData(): List<VideoBodyShapeEntity> {
        return ArrayList(VideoBodyShapeEnum.values().toList()).map {
            VideoBodyShapeEntity(it).apply {
                alpha = getBodyShapeDegree(switchEnumId(it))
            }
        }
    }

    /**
     * 发起肢体检测
     */
    fun startBodyDetect(onStateCallBack: ((isFinish: Boolean, progress: Float) -> Unit)? = null) {
        effectViewModel.subEditorFactory.fetchSubDetector<BodyShapeDetector>(DetectorType.BODY_DETECTOR)
                ?.let {
                    bodyShapeDetector = it
                    it.clear() //移除所有人体检测
                    bodyDetectionState.isFinish = false
                    val detectItems = ArrayList<PixVideoEffectItem>().apply {
                        effectViewModel.holder.curVideoEditorItem?.v1ClipInfos?.forEach {
                            add(it)
                        }
                        effectViewModel.holder.curVideoEditorItem?.v2ClipInfos?.forEach {
                            if (it is PixVideoMediaItem) {
                                add(it)
                            }
                        }
                    }
                    it.onStateCallback = { isFinish, progress ->
                        //检测进度
                        bodyDetectionState.isFinish = isFinish
                        bodyDetectionState.progress = progress
                        //这个目前只能通过判断该视频是否有返回检测进度来看有没有检测缓存
                        if (progress.div(100) < 1.0) {
                            if(isSupportDetectLoading){
                                detectNum++
                            }else{
                                isSupportDetectLoading = true
                            }
                        }
                        //检测进度实时更新到ui/loading
                        if (isFinish) {
                            needSendBodyData = true
                            //检测完成之后调用这个传入身体美型的configPath,传入想要检测的主轨和画中画的id,是一个list
                            effectEditor?.beginParseDetection(VideoBeautyConfig.getBodyShapeBeautyConfigPath(), LongArray(detectItems.size).apply {
                                detectItems.forEachIndexed { index, pixVideoEffectItem ->
                                    this[index] = (if (pixVideoEffectItem.timelineType == TimeLineType.MAIN_TIME_LINE) {
                                        pixVideoEffectItem.clipID ?: 0
                                    } else {
                                        pixVideoEffectItem.effectID ?: 0
                                    }).toLong()
                                }
                            })
                            //每一个解析肢体检测的信息都会到onParseDetectionEnd回调进行处理
                        }
                        bodyDetectLoading.postValue(bodyDetectionState)
                        onStateCallBack?.invoke(isFinish, progress)

                    }
                    it.bodyDetect(detectItems) { fullBodyData ->
                        if (!effectViewModel.seeking && (
                                        needSendBodyData
                                                || !bodyDetectionState.isFinish)
                        ) {
                            effectViewModel.needUpdateBodyData = false
                            if (bodyDetectionState.isFinish) {
                                needSendBodyData = false
                            }
                            bodyDetectDataEvent.value = MomentBodyData(fullBodyData)
                        }
                    }
                }
    }


    /**
     * 发起人脸检测
     */
    fun startFaceDetect(onStateCallBack: ((isFinish: Boolean, progress: Float) -> Unit)? = null) {
        effectViewModel.subEditorFactory.fetchSubDetector<FaceDetector>(DetectorType.FACE_DETECTOR)
                ?.let {
                    it.clear() //移除所有人脸检测
                    multiFaceDetectionState.isFinish = false
                    val detectItems = ArrayList<PixVideoEffectItem>().apply {
                        effectViewModel.holder.curVideoEditorItem?.v1ClipInfos?.forEach {
                            add(it)
                        }
                        effectViewModel.holder.curVideoEditorItem?.v2ClipInfos?.forEach {
                            if (it is PixVideoMediaItem) {
                                add(it)
                            }
                        }
                    }
                    it.onStateCallback = { isFinish, progress ->
                        //检测进度
                        multiFaceDetectionState.isFinish = isFinish
                        multiFaceDetectionState.progress = progress
                        if (isFinish) {
                            needSendFaceData = true
                            adjustMutliFaceData(
                                    effectViewModel.subEditorFactory.fetchSubDetector<FaceDetector>(
                                            DetectorType.FACE_DETECTOR
                                    )?.getAllFaceData()
                            )//检测结束以后 发送一次所有人脸数据
                            //如果不在美颜面板中
                            if (!effectViewModel.studioViewModel.functionViewModel.isSelectVideoTab(
                                            VideoEditTabType.Beauty
                                    )
                            ) {
                                stopFaceDetect()
                            }
                        }
                        multiFaceDetectLoading.postValue(multiFaceDetectionState)
                        onStateCallBack?.invoke(isFinish, progress)
                    }
                    it.faceDetect(detectItems) { time, fullFaceData ->
                        if (!effectViewModel.seeking && ((multiFaceDetectDataEvent.value?.time != time
                                        && time >= 0
                                        || needSendFaceData
                                        || !multiFaceDetectionState.isFinish)
                                        || effectViewModel.needUpdateFaceData)
                        ) {
                            effectViewModel.needUpdateFaceData = false
                            if (multiFaceDetectionState.isFinish) {
                                needSendFaceData = false
                            }
                            multiFaceDetectDataEvent.value = MomentFaceData(time, fullFaceData)
                        }
                    }
                }
    }

    /**
     * 获取当前美颜的效果值
     */
    fun getBeautyDegree(@PixMediaBeautyType type: String): Float {
        effectViewModel.holder.curVideoEditorItem?.beautyItem?.let { item ->
            val faceId = when {
                type == PixMediaBeautyType.ance || type == PixMediaBeautyType.shape  -> PixVideoBeautyEffectItem.InvalidFaceID

                item.isMultiFaceEffect -> multiFaceSelectEvent.value
                        ?: PixVideoBeautyEffectItem.InvalidFaceID

                else -> PixVideoBeautyEffectItem.InvalidFaceID
            }
            when (type) {
                PixMediaBeautyType.autoBeauty -> return if (item.hasAutoEffect(faceId)) 1f else 0f
                PixMediaBeautyType.remodel -> return if (item.hasFaceEffect(faceId)) 1f else 0f
                PixMediaBeautyType.makeup -> return if (item.hasMakeupEffect(faceId)) 1f else 0f
                PixMediaBeautyType.looks -> return if (item.hasLooksEffect(faceId)) 1f else 0f
                PixMediaBeautyType.shape -> return if (item.hasBodyShapeEffect(faceId)) 1f else 0f
                else -> {
                    item.baseBeautyEffect.items.find { it.faceID == faceId }
                            ?.let {
                                return it.params[type]?.degree ?: 0f
                            }
                }
            }
        }
        return 0f
    }

    /**
     * 判断是否有美颜存在 如果没有此效果值
     * 如果没有说明从没上过此美颜效果
     */
    fun hasApplyBeautyDegree(@PixMediaBeautyType type: String): Boolean {
        effectViewModel.holder.curVideoEditorItem?.beautyItem?.let { item ->
            val faceId = when {
                type == PixMediaBeautyType.ance -> PixVideoBeautyEffectItem.InvalidFaceID
                item.isMultiFaceEffect -> multiFaceSelectEvent.value
                else -> PixVideoBeautyEffectItem.InvalidFaceID
            }
            item.baseBeautyEffect.items.find { it.faceID == faceId }
                    ?.let {
                        return it.params[type] != null
                    }
        }
        return false
    }

    /**
     * 获取当前美型的效果值
     */
    fun getFaceDegree(@PixMediaRemodelSubType type: String): Float {
        effectViewModel.holder.curVideoEditorItem?.beautyItem?.let { item ->
            val faceId = if (item.isMultiFaceEffect) {
                multiFaceSelectEvent.value
            } else {
                PixVideoBeautyEffectItem.InvalidFaceID
            }
            item.remodelEffect.items.find { it.faceID == faceId }
                    ?.let {
                        return it.params[type]?.degree ?: 0f
                    }
        }
        return 0f
    }

    /**
     * 获取当前塑形的效果值
     */
    fun getBodyShapeDegree(@PixVideoReshapeType type: String): Float {
        effectViewModel.holder.curVideoEditorItem?.beautyItem?.let { item ->
            item.bodyShapeEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let {
                        return it.params[type]?.degree ?: 0f
                    }
        }
        return 0f
    }


    /**
     * 停止人脸检测
     */
    fun stopFaceDetect() {
        effectViewModel.subEditorFactory.fetchSubDetector<FaceDetector>(DetectorType.FACE_DETECTOR)
                ?.let {
                    it.clear()
                }
    }

    /**
     * 停止人体检测
     */
    fun stopBodyDetect() {
        effectViewModel.subEditorFactory.fetchSubDetector<BodyShapeDetector>(DetectorType.BODY_DETECTOR)
                ?.let {
                    it.clear()
                }
    }


    /**
     * 保存套装素材
     */
    fun updateLooksMaterialEffect(
            looksDuffleMaterial: LooksDuffleMaterial?,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        val faceId =
                if (isMultiFaceEffect()) multiFaceSelectEvent.value else PixVideoBeautyEffectItem.InvalidFaceID
        //素材填充
        effectItem.beautyItem.looksEffect.items.find { it.faceID == faceId }?.let {
            it.lookMaterial = looksDuffleMaterial
            it.degree = looksDuffleMaterial?.alphaX?.toFloat()?.div(100) ?: 0.0f
        }
        //主轨道效果处理
        removeLookEffect(effectItem.beautyItem.globalEffectData)
        addLooks(effectItem.beautyItem.globalEffectData)
        updateLooksEffect(effectItem.beautyItem.globalEffectData)
        //画中画效果处理
        effectItem.beautyItem.pipEffectDatas.forEach {
            removeLookEffect(it)
            addLooks(it)
            updateLooksEffect(it)
        }
    }

    /**
     * 保存美妆素材
     */
    suspend fun updateMakeupMaterial(
            sparseArray: SparseArray<MakeupMaterial>,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        val faceId =
                if (isMultiFaceEffect()) multiFaceSelectEvent.value else PixVideoBeautyEffectItem.InvalidFaceID
        val globalMakeupEffect =
                effectItem.beautyItem.makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
        //染发效果全局放置
        globalMakeupEffect?.let {
            it.materials = ArrayList<PixVideoMakeupMaterial>(it.materials).apply {
                if (sparseArray[MakeupType.TYPE_HAIR] == null) {
                    indexOfFirst { it.type == getPixVideoMakeupType(MakeupType.TYPE_HAIR) }.takeIf { it >= 0 }
                            ?.let {
                                removeAt(it)
                            }
                } else {
                    fillMaterial(this, sparseArray[MakeupType.TYPE_HAIR], isOtherMaterial = false)
                }
            }
        }
        effectItem.beautyItem.makeupEffect.items.find { it.faceID == faceId }
                ?.let {
                    it.materials = ArrayList<PixVideoMakeupMaterial>(it.materials).apply {
                        MakeupConfig.makeupTypes.forEach { makeupType ->
                            val material = sparseArray[makeupType]
                            //空素材清空
                            if (material == null) {
                                indexOfFirst {
                                    it.type == getPixVideoMakeupType(makeupType)
                                }.takeIf { it >= 0 }
                                        ?.let {
                                            removeAt(it)
                                        }
                            }
                        }
                        sparseArray.forEach { key, value ->
                            if (key == MakeupType.TYPE_HAIR) {
                                return@forEach
                            }
                            fillMaterial(
                                    this,
                                    value,
                                    isOtherMaterial = false
                            )
                        }
                    }
                }
        //效果更新

        addMakeup(effectItem.beautyItem.globalEffectData)
        updateMakeupEffect(effectItem.beautyItem.globalEffectData)
        effectItem.beautyItem.pipEffectDatas.forEach {
            addMakeup(it)
            updateMakeupEffect(it)
        }
    }

    /**
     * 更新内部颜色素材
     */
    fun updateInnerMakeupColor(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem) {
        effectItem ?: return
        //颜色更新
        updateMakeupEffect(effectItem.beautyItem.globalEffectData, onlyUpdateColor = true)
        effectItem.beautyItem.pipEffectDatas.forEach {
            updateMakeupEffect(it, onlyUpdateColor = true)
        }
    }

    /**
     * 更新套装程度值
     */
    fun updateLooksAlpha(
            looksDuffleMaterial: LooksDuffleMaterial?,
            alpha: Int,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        val faceId =
                if (isMultiFaceEffect()) multiFaceSelectEvent.value else PixVideoBeautyEffectItem.InvalidFaceID
        effectItem.beautyItem.looksEffect.items.find { it.faceID == faceId }?.let { item ->
            item.lookMaterial.takeIf { it?.materialId == looksDuffleMaterial?.materialId }?.let {
                it.alphaX = alpha
                item.degree = alpha / 100f
            }
        }
        val globalEffect = effectItem.beautyItem.globalEffectData.looksEffectId?.run {
            effectEditor?.getEffect<MTARBeautyMakeupEffect>(this)
        }
        updateLookEffectAlpha(
                effectItem,
                effectItem.beautyItem.globalEffectData,
                globalEffect
        )
        effectItem.beautyItem.pipEffectDatas.forEach {
            val pipLookEffect = it.looksEffectId?.run {
                effectEditor?.getEffect<MTARBeautyMakeupEffect>(this)
            }
            updateLookEffectAlpha(
                    effectItem,
                    effectItem.beautyItem.globalEffectData,
                    pipLookEffect
            )
        }
    }

    /**
     * 更新美妆程度值
     */
    fun updateMakeupDegree(
            @MakeupType makeupType: Int,
            degree: Float,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        val pixMakeupType = getPixVideoMakeupType(makeupType)
        if (isMultiFaceEffect()) {
            if (makeupType == MakeupType.TYPE_HAIR) {
                effectItem.beautyItem.makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                        ?.let {
                            it.materials.find { it.type == pixMakeupType }?.let {
                                it.degree = degree
                            }
                        }
            } else {
                effectItem.beautyItem.makeupEffect.items.find { it.faceID == multiFaceSelectEvent.value }
                        ?.let {
                            it.materials.find { it.type == pixMakeupType }?.let {
                                it.degree = degree
                            }
                        }
            }
        } else {
            effectItem.beautyItem.makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let {
                        it.materials.find { it.type == pixMakeupType }?.let {
                            it.degree = degree
                        }
                    }
        }
        updateInnerMakeupColor()
    }

    /**
     * 保存美妆素材颜色素材
     */
    fun updateMakeupColorMaterial(
            sparseArray: SparseArray<MakeupMaterial>,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        if (isMultiFaceEffect()) {
            effectItem.beautyItem.makeupEffect.items.find { it.faceID == multiFaceSelectEvent.value }
                    ?.let {
                        it.materials = ArrayList<PixVideoMakeupMaterial>(it.materials).apply {
                            sparseArray.forEach { key, value ->
                                fillMaterial(
                                        this,
                                        value,
                                        isOtherMaterial = true
                                )
                            }
                        }
                    }
        } else {
            effectItem.beautyItem.makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                    ?.let {
                        it.materials = ArrayList<PixVideoMakeupMaterial>(it.materials).apply {
                            sparseArray.forEach { key, value ->
                                fillMaterial(
                                        this,
                                        value,
                                        isOtherMaterial = true
                                )
                            }
                        }
                    }
        }
        //效果更新
        updateMakeupEffect(effectItem.beautyItem.globalEffectData, onlyUpdateColor = true)
        effectItem.beautyItem.pipEffectDatas.forEach {
            updateMakeupEffect(it, onlyUpdateColor = true)
        }
    }

    /**
     * 填充素材 保存到beautyItem中的美妆块
     */
    private fun fillMaterial(
            array: ArrayList<PixVideoMakeupMaterial>,
            material: MakeupMaterial,
            isOtherMaterial: Boolean = false
    ) {
        val type = getPixVideoMakeupType(material.makeupType)
        var videoMaterial = array.find { it.type == type }
        if (videoMaterial == null) {
            videoMaterial = PixVideoMakeupMaterial().apply { this.type = type }
            array.add(videoMaterial)
        }
        if (isOtherMaterial) {
            videoMaterial.otherMaterial.productID = material.onlineId
            videoMaterial.colorMaterial = material
        } else {
            videoMaterial.material.productID = material.onlineId
            videoMaterial.makeupMaterial = material
        }
        val alpha = if (isMultiFaceEffect()) {
            multiFaceSelectEvent.value?.run {
                material.getFaceAlpha(this.toInt())
            } ?: material.defaultAlpha
        } else {
            material.getFaceAlpha(-1)
        }
        if (MakeupUtils.isMainColorTab(material.makeupType)) {
            if (isOtherMaterial) {
                videoMaterial.degree = alpha / 100f
                videoMaterial.defaultDegree = material.defaultAlpha / 100f
            }
        } else {
            if (!isOtherMaterial) {
                videoMaterial.degree = alpha / 100f
                videoMaterial.defaultDegree = material.defaultAlpha / 100f
            }
        }
    }

    /**
     * 获取视频编辑中的配方美妆类型
     */
    private fun getPixVideoMakeupType(@MakeupType type: Int): String {
        return when (type) {
            MakeupType.TYPE_LIP_STICK -> PixMediaMakeupType.lipstick
            MakeupType.TYPE_BLUSH -> PixMediaMakeupType.blusher
            MakeupType.TYPE_TRIMMING -> PixMediaMakeupType.highlight
            MakeupType.TYPE_EYE_BROW -> PixMediaMakeupType.eyebrows
            MakeupType.TYPE_EYE_LASH -> PixMediaMakeupType.eyeLash
            MakeupType.TYPE_EYE_SHADOW -> PixMediaMakeupType.eyeMakeup
            MakeupType.TYPE_BEAUTY_PUPIL -> PixMediaMakeupType.eyeColored
            MakeupType.TYPE_EYE_SMILES -> PixMediaMakeupType.eyeSmiles
            MakeupType.TYPE_HAIR -> PixMediaMakeupType.hairColor
            MakeupType.TYPE_BEAUTY_FRECKLE -> PixMediaMakeupType.freckle
            MakeupType.TYPE_BEAUTY_DECORATION -> PixMediaMakeupType.decoration
            MakeupType.TYPE_BEAUTY_EYELINER -> PixMediaMakeupType.eyeliner
            else -> PixMediaMakeupType.none
        }
    }

    /**
     * 获取美妆类型
     */
    private fun getMakeType(@PixMediaMakeupType type: String): Int {
        return when (type) {
            PixMediaMakeupType.lipstick -> MakeupType.TYPE_LIP_STICK
            PixMediaMakeupType.blusher -> MakeupType.TYPE_BLUSH
            PixMediaMakeupType.highlight -> MakeupType.TYPE_TRIMMING
            PixMediaMakeupType.eyebrows -> MakeupType.TYPE_EYE_BROW
            PixMediaMakeupType.eyeLash -> MakeupType.TYPE_EYE_LASH
            PixMediaMakeupType.eyeMakeup -> MakeupType.TYPE_EYE_SHADOW
            PixMediaMakeupType.eyeColored -> MakeupType.TYPE_BEAUTY_PUPIL
            PixMediaMakeupType.eyeSmiles -> MakeupType.TYPE_EYE_SMILES
            PixMediaMakeupType.hairColor -> MakeupType.TYPE_HAIR
            else -> MakeupType.TYPE_LIP_STICK
        }
    }

    /**
     * 针对视频播放多时刻的多人脸数据包装 提供多人脸组件绘制使用
     */
    data class MomentFaceData(
            val time: Long,
            val faceArray: Array<out MTAsyncDetector.FaceRectData>?
    )

    /**
     * 当前视频的身体数据包装
     */
    data class MomentBodyData(val fullFaceData: List<BodyShapeDetector.DetectBodyData>?)

    /**
     * 刷新美颜效果全局时长
     */
    fun refreshAllBeautyDuration(
            duration: Long,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        refreshDuration(duration, effectItem.beautyItem.globalEffectData.baseBeautyEffectId)
        refreshDuration(duration, effectItem.beautyItem.globalEffectData.faceEffectId)
        refreshDuration(duration, effectItem.beautyItem.globalEffectData.autoBeautyEffectId)
        refreshDuration(duration, effectItem.beautyItem.globalEffectData.multifaceMakeupEffectId)
        refreshDuration(duration, effectItem.beautyItem.globalEffectData.globalMakeupEffectId)
        refreshDuration(duration, effectItem.beautyItem.globalEffectData.looksEffectId)
        refreshDuration(duration, effectItem.beautyItem.globalEffectData.reShapeEffectId)
        effectItem.v2ClipInfos?.forEach { media ->
            if (media is PixVideoMediaItem) {
                effectItem.beautyItem.pipEffectDatas.find { it.pipEffectId == media.effectID }
                        ?.let {
                            refreshPipEffecData(it, targetDuration = media.processedDuration)
                        }
            }
        }
    }

    /**
     * 刷新画中画的总时长
     */
    fun refreshPipTargetDuration(
            pipID: Long,
            item: PixVideoMediaItem,
            effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem
    ) {
        effectItem ?: return
        effectItem.beautyItem.pipEffectDatas.find { it.pipEffectId == pipID }?.let {
            refreshPipEffecData(it, targetDuration = item.processedDuration)
        }
    }

    /**
     * 美妆素材ID
     */
    fun getMakeupMaterialIds(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem): List<MakeupViewModel.DefaultMaterial>? {
        val faceId =
                if (isMultiFaceEffect()) multiFaceSelectEvent.value else PixVideoBeautyEffectItem.InvalidFaceID
        effectItem?.let {
            return ArrayList<MakeupViewModel.DefaultMaterial>().apply {
                //染发从全局位置读取
                it.beautyItem.makeupEffect.items.find { it.faceID == PixVideoBeautyEffectItem.InvalidFaceID }
                        ?.let {
                            it.materials.find { it.type == getPixVideoMakeupType(MakeupType.TYPE_HAIR) }
                                    ?.let { material ->
                                        material.material.productID?.let {
                                            add(
                                                    MakeupViewModel.DefaultMaterial(
                                                            it, (material.degree * 100f).toInt()
                                                    )
                                            )
                                        }
                                    }
                        }
                it.beautyItem.makeupEffect.items.forEach {
                    if (faceId == it.faceID) {
                        it.materials.forEach { material ->
                            if (!isMultiFaceEffect() || material.type != PixMediaMakeupType.hairColor) {
                                material.material.productID?.let {
                                    add(
                                            MakeupViewModel.DefaultMaterial(
                                                    it,
                                                    (material.degree * 100f).toInt()
                                            )
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
        return null
    }

    /**
     * 美妆颜色ID
     */
    fun getMakeupColorMaterialIds(effectItem: PixVideoEditorItem? = effectViewModel.holder.curVideoEditorItem): List<MakeupViewModel.DefaultMaterial>? {
        val faceId =
                if (isMultiFaceEffect()) multiFaceSelectEvent.value else PixVideoBeautyEffectItem.InvalidFaceID
        effectItem?.let {
            return ArrayList<MakeupViewModel.DefaultMaterial>().apply {
                it.beautyItem.makeupEffect.items.forEach {
                    if (faceId == it.faceID) {
                        it.materials.forEach { material ->
                            material.otherMaterial.productID?.let {
                                add(
                                        MakeupViewModel.DefaultMaterial(
                                                it,
                                                (material.degree * 100f).toInt()
                                        )
                                )
                            }
                        }
                    }
                }
            }
        }
        return null
    }

    private fun refreshDuration(duration: Long, effectId: Long?) {
        effectId?.let {
            effectEditor?.getAREffect(it)?.let {
                it.duration = duration
            }
        }
    }

    private fun refreshPipEffecData(
            beautyEffectData: PixVideoBeautyItem.BeautyEffectData,
            targetDuration: Long
    ) {
        beautyEffectData.pipEffectId?.let {
            mediaEditor?.getEffect<MTPipEffect>(it)?.let { pipEffect ->
                beautyEffectData.autoBeautyEffectId?.let {
                    effectEditor?.getAREffect(it)?.let {
                        it.startTime = 0
                        it.duration = targetDuration
                    }
                }
                beautyEffectData.baseBeautyEffectId?.let {
                    effectEditor?.getAREffect(it)?.let {
                        it.startTime = 0
                        it.duration = targetDuration
                    }
                }
                beautyEffectData.globalMakeupEffectId?.let {
                    effectEditor?.getAREffect(it)?.let {
                        it.startTime = 0
                        it.duration = targetDuration
                    }
                }
                beautyEffectData.multifaceMakeupEffectId?.let {
                    effectEditor?.getAREffect(it)?.let {
                        it.startTime = 0
                        it.duration = targetDuration
                    }
                }
                beautyEffectData.faceEffectId?.let {
                    effectEditor?.getAREffect(it)?.let {
                        it.startTime = 0
                        it.duration = targetDuration
                    }
                }
                beautyEffectData.reShapeEffectId?.let {
                    effectEditor?.getAREffect(it)?.let {
                        it.startTime = 0
                        it.duration = targetDuration
                    }
                }
                beautyEffectData.looksEffectId?.let {
                    effectEditor?.getAREffect(it)?.let {
                        it.startTime = 0
                        it.duration = targetDuration
                    }
                }
            }
        }
    }

    /**
     * 获取美颜默认值
     */
    fun getBeautyDefaultValue(@PixMediaBeautyType type: String): Float {
        return when (type) {
            PixMediaBeautyType.autoBeauty -> 1f
            PixMediaBeautyType.smooth -> .3f
            PixMediaBeautyType.oiliness -> .5f
            PixMediaBeautyType.darkCircle -> .7f
            PixMediaBeautyType.mascra -> .5f
            PixMediaBeautyType.ance -> 1f
            PixMediaBeautyType.teeth -> .4f
            else -> .5f
        }
    }

    /**
     * 计算美颜美型转换值
     */
    private fun calculateBeautyFaceConvertValue(
            @PixMediaBeautyType type: String,
            degree: Float
    ): Float {
        return when (type) {
            PixMediaBeautyType.mascra -> .3f * degree
            else -> degree
        }
    }

    /**
     * 面部重塑 脸型类型效果是否需要付费
     */
    fun isFaceEffectNeedPay(beautyItem: PixVideoBeautyItem? = effectViewModel.holder.curVideoEditorItem?.beautyItem): Boolean {
        beautyItem?.let {
            val faceId = if (isMultiFaceEffect()) {
                multiFaceSelectEvent.value
            } else {
                PixVideoBeautyEffectItem.InvalidFaceID
            }
            return it.hasFaceEffect(faceId)
        }
        return false
    }

    /**
     * 判断当前是否在身体检测
     */
    fun isBodyShapeDetectLoading(): Boolean {
        return bodyDetectLoading.value != null && (!bodyDetectLoading.value!!.isFinish && bodyDetectLoading.value!!.progress >= 0f)
    }

    /**
     * 塑形效果是否需要付费
     */
    fun isBodyShapeEffectNeedPay(beautyItem: PixVideoBeautyItem? = effectViewModel.holder.curVideoEditorItem?.beautyItem): Boolean {
        beautyItem?.let {
            val faceId = if (isMultiFaceEffect()) {
                multiFaceSelectEvent.value
            } else {
                PixVideoBeautyEffectItem.InvalidFaceID
            }
            return it.hasBodyShapeEffect(faceId)
        }
        return false
    }


    /**
     * 是否可开启多人脸 判断是否可开启区分人像
     */
    fun hasMultiFace(): Boolean {
        return multiFaceDetectionState.faceCount > 0
    }

    /**
     * 多人脸是否有效果
     */
    fun hasMultiFaceEffect(): Boolean {
        return isMultiFaceEffect() && effectViewModel.holder.curVideoEditorItem?.beautyItem?.hasMultiFaceEffect() == true
    }


}