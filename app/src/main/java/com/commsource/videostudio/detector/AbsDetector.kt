package com.commsource.videostudio.detector

import com.commsource.util.DeviceLevelAdapter
import com.commsource.videostudio.bean.clips.PixVideoEffectItem
import com.commsource.videostudio.bean.clips.TimeLineType
import com.meitu.aidetectionplugin.MTAIDetectionPluginConfig
import com.meitu.common.AppContext
import com.meitu.library.mtmediakit.ar.effect.MTAREffectEditor
import com.meitu.library.mtmediakit.constants.MTARAsyncConstants
import com.meitu.library.mtmediakit.core.MTMediaEditor
import com.meitu.library.mtmediakit.detection.MTARBindType
import com.meitu.library.mtmediakit.detection.MTAsyncDetector
import com.meitu.library.mtmediakit.detection.MTBaseDetectionRange
import com.meitu.library.mtmediakit.detection.MTBaseDetector
import com.meitu.library.mtmediakit.detection.MTDetectionRange
import com.meitu.library.mtmediakit.listener.OnAsyncDetectionListener
import com.meitu.library.mtmediakit.model.MTMVInfo
import com.meitu.media.mtmvcore.MTDetectionService
import com.meitu.mtlab.MTAiInterface.common.MTAiEngineType

abstract class AbsDetector : OnAsyncDetectionListener {

    companion object {
        fun clearAllCachedDetectData() {
            val cacheDir = MTMVInfo.getAndMkdirsDefaultMTMVCacheDir(AppContext.context)
            MTBaseDetector.cleanAllCacheWithIgnoreExtendId(cacheDir, null)
        }
    }

    var mtDetector: MTBaseDetector? = null

    // 媒体编辑器。
    var mediaEditor: MTMediaEditor? = null

    // 视频底层效果编辑对象
    var effectEditor: MTAREffectEditor? = null

    // 检测状态回调
    var onStateCallback: ((isFinish: Boolean, progress: Float) -> Unit)? = null

    /**
     * 初始化检测器
     */
    fun initDetector(editor: MTMediaEditor, effectEdit: MTAREffectEditor?) {
        setModelPath()
        mediaEditor = editor
        effectEditor = effectEdit
        mtDetector = buildDetector()
        // 低端机不用全脸分割(未开启VIP功能，说明去油光为不接入状态，则全脸分割不开启)
        (mtDetector as? MTAsyncDetector)?.setFaceMaskOption(!DeviceLevelAdapter.isLowDevice())
        (mtDetector as? MTAsyncDetector)?.setPostJobOption(// 人脸检测
            // 人脸检测
            MTDetectionService.kMTDetectionFace // 对人脸检测数据完成头像抠图
                    or MTDetectionService.kMTDetectionCutoutFace // 人脸识别
                    or MTDetectionService.kMTDetectionFRFace // 全脸分割
                    or MTDetectionService.kMTDetectionFace3DFA // 3DFA检测
        )
        (mtDetector as? MTAsyncDetector)?.setEnableCallbackRealTimeFace(true)
        //(mtDetector as? MTAsyncDetector)?.setLogProgress(true)
        mtDetector?.setDetectionListener(this)
    }

    /**
     * 设置模型路径
     */
    private fun setModelPath() {
        //设置人体检测模型路径，由于mediakit无法设置modelType，经确认，可以将large模型按middle处理
        val models = mapOf(
            MTAiEngineType.MTAIENGINE_MODEL_BODYINONE_NECK_MIDDLE to "MTAiModel/BodyInOneModel/neck.manis",
            MTAiEngineType.MTAIENGINE_MODEL_BODYINONE_BREAST_MIDDLE to "MTAiModel/BodyInOneModel/BreastMiddle/breast.manis",
            MTAiEngineType.MTAIENGINE_MODEL_BODYINONE_POSE_MIDDLE to "MTAiModel/BodyInOneModel/pose.manis",
        )
        models.forEach {
            MTAIDetectionPluginConfig.nativeSetSingleModelPath(it.key, it.value)
        }

    }


    abstract fun buildDetector(): MTBaseDetector?

    /**
     * 目前允许检测功能包含主轨(clip)和画中画(effect)
     */
    fun addDetectionJob(item: PixVideoEffectItem) {
        // 如果是主轴的话。用clipID ，否则用effectID
        val detectionRange = MTDetectionRange().apply {
            if (item.timelineType == TimeLineType.MAIN_TIME_LINE) {
                clipId = item.clipID ?: 0
            } else {
                effectId = item.effectID ?: 0
            }
            bindType =
                if (item.timelineType == TimeLineType.MAIN_TIME_LINE) MTARBindType.BIND_CLIP else MTARBindType.BIND_PIP
        }
        // post 到检测队列。
        mtDetector?.postDetectionJob(detectionRange)
    }


    /**
     * 移除检测任务。
     */
    fun removeDetectionJob(item: PixVideoEffectItem) {
        if (item.timelineType != TimeLineType.MAIN_TIME_LINE) {
            getDetectionRange(item)?.let { mtDetector?.removeDetectionJob(it) }
        } else {
            val mediaClip = mediaEditor?.getSingleClipBySpecialId(item.specialId)
            getDetectionRange(item)?.let { range ->
                // 移除检测的时候加一层判断。如果其他任务里还有这个同样的job 不做移除
                val find = mtDetector?.detectionRangeList?.find {
                    it != range && item.clipID?.let { mediaEditor?.getSingleClip(it)?.detectJobExtendId == mediaClip?.detectJobExtendId } ?: false
                }
                if (find == null) {
                    mtDetector?.removeDetectionJob(range)
                }
            }
        }
    }

    /**
     * 移除所有的检测任务
     */
    fun removeAllDetectionJob() {
        mtDetector?.removeAllDetectionJobs()
    }

    /**
     *  从队列中获取MTDetectionRange
     */
    private fun getDetectionRange(item: PixVideoEffectItem): MTDetectionRange? {
        return (if (item.timelineType == TimeLineType.MAIN_TIME_LINE) {
            mtDetector?.detectionRangeList?.find { range ->
                range is MTDetectionRange && range.clipId == item.clipID
            }
        } else {
            mtDetector?.detectionRangeList?.find { range ->
                range is MTDetectionRange && range.effectId == item.clipID
            }
        }) as? MTDetectionRange
    }


    // 返回一个所有任务的检测进度。
    // 需要做一次二次计算，进行拼接。
    override fun onDetectionJobProgress(ranges: MutableMap<out MTBaseDetectionRange, Float>?) {
        ranges?.size?.takeIf { it > 0 }?.let { size ->
            // 计算总的进度。
            val value = ranges.values.fold(0f) { a, b ->
                a + b
            } / size

            // 回调。
            onStateCallback?.invoke(false, value * 100f)
        }
    }

    // 只关心全部检测完成的回调
    override fun onDetectionJobComplete(
        event: Int,
        ranges: MutableList<out MTBaseDetectionRange>?
    ) {
        if (event == MTARAsyncConstants.kDetectFinishAll) {
            onStateCallback?.invoke(true, 100f)
        }
    }

    override fun onDetectionFaceEvent(eventId: Int) {

    }

    override fun onDetectionFaceDataInRealTime(
        reqTime: Long,
        faceData: Array<out MTAsyncDetector.FaceRectData>?
    ) {

    }

}