package com.commsource.videostudio.timeline

import com.commsource.util.dpf
import com.commsource.util.hapticVirtualKey
import com.commsource.videostudio.bean.TimelineClipInfo

/**
 * 吸附逃逸指示器
 * 特殊的逃逸吸附指示器 吸附和逃逸分开计算，且二者有先后判定逻辑
 * 优先吸附
 *
 * 吸附后 全部交给逃逸判定，吸附不参与判定
 * 如果逃逸 动态变成自由调整
 */
class TimelineAdsorbEscapeIndicator(val timelineView: TimelineView) {

    /**
     * 时间轴吸附点
     */
    private var timelineAdsorbPoints: List<AdsorbPoint> = emptyList()

    /**
     * 当前的滑动值
     */
    var currentScrollX: Float = 0f

    /**
     * 目标吸附已经状态
     */
    var adsorbPoint: AdsorbPoint? = null
    var hadEscape: Boolean = false

    /**
     * 偏移
     */
    private fun offset(dx: Float) {
        currentScrollX += dx
    }

    /**
     * 开启吸附
     */
    private fun beginAdsorb(adsorbPoint: AdsorbPoint): Boolean {
        if (this.adsorbPoint?.adsorbScrollX == adsorbPoint.adsorbScrollX) {
            return false
        }
        this.adsorbPoint = adsorbPoint
        this.currentScrollX = adsorbPoint.adsorbScrollX
        hadEscape = false
        return true
    }

    /**
     * 逃逸吸附
     */
    private fun escapeAdsorb() {
        adsorbPoint?.let {
            this.currentScrollX = it.adsorbScrollX
            hadEscape = true
        }
    }

    /**
     * 是否存在吸附
     */
    fun inAdsorb(): Boolean {
        return adsorbPoint != null
    }

    /**
     * 是否处于吸附
     */
    private fun outAdsorbRange(dx: Float): Boolean {
        return inAdsorb() && !adsorbPoint!!.isAdsorbOrientation(currentScrollX, dx)
    }

    /**
     * 是否处于逃逸范围
     */
    private fun outEscapeRange(dx: Float): Boolean {
        return inAdsorb() && adsorbPoint!!.isEscapeOrientation(currentScrollX, dx)
    }

    private fun getAdsorbScrollX(): Float {
        return adsorbPoint?.adsorbScrollX ?: currentScrollX
    }

    /**
     * 结束吸附
     */
    fun endAdsorb() {
        this.adsorbPoint = null
        hadEscape = false
    }

    var isRightAdsorb: Boolean = false

    /**
     * 计算吸附调整后的scrollX
     */
    fun calculateAdsorbScrollX(scrollX: Float, dx: Float): Float {
        //处于吸附态处理
        if (inAdsorb()) {
            offset(dx.toFloat())
            if (hadEscape) {
                if (outAdsorbRange(dx)) {
                    endAdsorb()
                }
                //逃逸了之后什么都不做 在吸附范围自由
            } else if (outEscapeRange(dx)) {
                escapeAdsorb()//逃逸了 发起逃逸
                timelineView.hapticVirtualKey()
            } else {
                return getAdsorbScrollX()
            }
        } else {
            currentScrollX = scrollX
            offset(dx)
            val adsorbPoint: AdsorbPoint? = kotlin.run {
                timelineAdsorbPoints.forEach {
                    if (it.isAdsorbOrientation(currentScrollX, dx)) {
                        return@run it
                    }
                }
                null
            }
            //调整到吸附值
            if (adsorbPoint != null) {
                if (beginAdsorb(adsorbPoint)) {
                    timelineView.hapticVirtualKey()
                    isRightAdsorb = dx > 0
                    timelineView.onTimelineEventListener?.onTimelineAdsorbTime(
                        adsorbPoint.time,
                        adsorbPoint.needDirectAdsorb,
                        isRightAdsorb
                    )
                }
            } else {
                endAdsorb()
            }
        }
        return currentScrollX
    }

    /**
     * 计算吸附值
     */
    fun calculateAdsorbPoints(targetTimelineClipInfo: TimelineClipInfo? = null) {
        timelineAdsorbPoints = timelineView.clipGroup.calculateAllAdsorbTimePosition(
            targetTimelineClipInfo, adsorbValue = 4.dpf,
            isTimelineAdsorb = true,
            escapeValue = 8.dpf
        )
    }
}