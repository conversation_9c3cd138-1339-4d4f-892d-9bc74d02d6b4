package com.commsource.videostudio.timeline.drawer

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.text.TextPaint
import com.commsource.beautyplus.R
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.util.ResourcesUtils
import com.commsource.util.dpf
import com.commsource.util.resColor
import com.commsource.util.text
import com.commsource.videostudio.timeline.ClipGroup
import com.commsource.videostudio.timeline.TimelineView
import com.commsource.videostudio.util.AlphaAnimationBox

/**
 * 删除区域的绘制
 */
class DeleteAreaDrawer(val timeline: TimelineView, val clipGroup: ClipGroup) {

    val alphaBox = AlphaAnimationBox(XAnimatorCalculateValuer(0f))

    val deleteDrawable: Drawable by lazy { ResourcesUtils.getDrawable(R.drawable.common_video_delete) }

    val tempRectF = RectF()

    val deleteText = R.string.album_preview_dialog_delete.text()

    val paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            color = R.color.Gray_Background_4.resColor()
        }
    }

    val textPaint by lazy {
        TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
            textSize = 14.dpf
            textAlign = Paint.Align.CENTER
            color = Color.WHITE
        }
    }

    fun inDeleteArea(y: Float): Boolean {
        return y >= timeline.viewHeight - 80.dpf
    }

    fun onDraw(canvas: Canvas) {
        if (clipGroup.dragClipHelper.hasDragClip() || alphaBox.alphaValuer.value > 0) {
            textPaint.alpha = (alphaBox.alphaValuer.value * 255).toInt()
            deleteDrawable.alpha = (alphaBox.alphaValuer.value * 255).toInt()
            if (clipGroup.dragClipHelper.inDeleteState) {
                paint.color = R.color.Color_Red.resColor()
            } else {
                paint.color = R.color.Gray_Background_4.resColor()
            }
            paint.alpha = (alphaBox.alphaValuer.value * 255).toInt()
            tempRectF.set(
                clipGroup.globalVisibleRect.left.toFloat(), timeline.viewHeight - 80.dpf,
                clipGroup.globalVisibleRect.right.toFloat(), timeline.viewHeight.toFloat()
            )
            canvas.drawRect(tempRectF, paint)

            val width = textPaint.measureText(deleteText)
            //16 + 8 + width
            val visibleWidth = 16.dpf + 8.dpf + width
            //计算一下绘制开始位置
            val startX = clipGroup.globalVisibleRect.centerX() - visibleWidth / 2f
            deleteDrawable.setBounds(
                startX.toInt(),
                (timeline.viewHeight - 80.dpf + 14.dpf).toInt(),
                (startX.toInt() + 16.dpf).toInt(),
                (timeline.viewHeight - 80.dpf + 14.dpf + 16.dpf).toInt()
            )
            deleteDrawable.draw(canvas)
            canvas.drawText(
                deleteText,
                startX + 16.dpf + 8.dpf + width / 2,
                timeline.viewHeight - 80.dpf + 14.dpf + 14.dpf,
                textPaint
            )
        }
    }
}