package com.commsource.videostudio.timeline.drawer

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.text.TextPaint
import androidx.core.content.res.ResourcesCompat
import com.commsource.beautyplus.R
import com.commsource.util.*
import com.commsource.videostudio.timeline.ClipGroup
import com.commsource.videostudio.timeline.TimelineDrawConstants
import com.commsource.videostudio.timeline.TimelineView
import com.commsource.videostudio.util.AlphaAnimationBox
import com.meitu.common.AppContext.context

/**
 * 转场绘制工具
 */
class TransitionDrawer(val timeline: TimelineView, val clipGroup: ClipGroup) {

    val shadowColor = Color.parseColor("#3D000000")

    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
    }

    private val defaultPaint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        color = R.color.Gray_Background_1.resColor()
        strokeCap = Paint.Cap.ROUND
        textAlign = Paint.Align.CENTER
        textSize = 24.dpf
        style = Paint.Style.FILL_AND_STROKE
        strokeWidth = .8f
        typeface = ResourcesCompat.getFont(context, R.font.video_icon_font)

    }

    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = R.color.Gray_Background_1.resColor()
        strokeCap = Paint.Cap.ROUND
        style = Paint.Style.STROKE
        strokeWidth = 2.dpf
    }

    private val transitionOff = R.string.vf_video_icon_clip_transiton_off.text()
    private val transitionOn = R.string.vf_video_icon_clip_transiton_on.text()

    private val backgroundRectF = RectF().apply {
        set((-12).dpf, (-12).dpf, 12.dpf, 12.dpf)
    }

    /**
     * 转场区域alpha控制
     */
    val alphaAnimationBox = AlphaAnimationBox()

    private val visibleRectF = RectF()

    fun onDraw(canvas: Canvas) {
        if (alphaAnimationBox.alphaValuer.value > 0) {
            timeline.timelineInfoManager.timelineInfo.mediaClips.forEachIndexed { index, mediaClipInfo ->
                val drawCenterX =
                    if (clipGroup.isInAdjustModeControll()) mediaClipInfo.rectAnimationBox.rect.right else timeline.calculateScrollX(
                        mediaClipInfo.getTransitionEndTime()
                    )
                //如果在选中状态 当前选中的前后两个转场不绘制
                val drawEnable =
                    !clipGroup.selectClipHelper.isSelectClip(mediaClipInfo) && !clipGroup.selectClipHelper.isSelectClip(
                        timeline.timelineInfoManager.timelineInfo.mediaClips.safeGet(index + 1)
                    )
                if (drawEnable && index != timeline.timelineInfoManager.timelineInfo.mediaClips.size - 1) {
                    visibleRectF.set(backgroundRectF)
                    visibleRectF.offset(
                        timeline.initX + drawCenterX,
                        clipGroup.v1StartYValuer.value
                    )//y不准确判断了 因为没有y滚动
                    if (MathUtils.intersect(clipGroup.globalVisibleRect, visibleRectF)) {
                        canvas.save()
                        canvas.translate(timeline.initX, 0f)
                        canvas.translate(
                            drawCenterX,
                            clipGroup.v1StartYValuer.value + TimelineDrawConstants.PreviewSize / 2f
                        )
                        backgroundPaint.alpha = (255 * alphaAnimationBox.alphaValuer.value).toInt()
                        defaultPaint.alpha = (255 * alphaAnimationBox.alphaValuer.value).toInt()
                        borderPaint.alpha = (255 * alphaAnimationBox.alphaValuer.value).toInt()
                        if (clipGroup.isInAdjustModeControll()) {
                            backgroundPaint.clearShadowLayer()
                        } else {
                            //FIXME 存在native内存上涨过快的性能问题 by bear yyp
                            backgroundPaint.setShadowLayer(2.dpf, 1.dpf, 1.dpf, shadowColor)
                        }
                        canvas.drawRoundRect(
                            backgroundRectF,
                            TimelineDrawConstants.ClipCornerValue,
                            TimelineDrawConstants.ClipCornerValue,
                            backgroundPaint
                        )
                        if (mediaClipInfo.transitionInfo.hasTransition()) {
                            canvas.drawText(
                                transitionOn,
                                0f,
                                defaultPaint.textSize - defaultPaint.descent() - defaultPaint.textSize / 2,
                                defaultPaint
                            )
                        } else {
                            canvas.drawText(
                                transitionOff,
                                0f,
                                defaultPaint.textSize - defaultPaint.descent() - defaultPaint.textSize / 2,
                                defaultPaint
                            )
                        }
                        if (clipGroup.selectTransitionHelper.isSelectTransition(mediaClipInfo)) {
                            canvas.drawRoundRect(
                                backgroundRectF, TimelineDrawConstants.ClipCornerValue,
                                TimelineDrawConstants.ClipCornerValue,
                                borderPaint
                            )
                        }
                        canvas.restore()
                    }
                }
            }
        }
    }
}