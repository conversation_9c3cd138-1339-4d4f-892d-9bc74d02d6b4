package com.commsource.store.doodle

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.view.View
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemShopDoodleBinding
import com.commsource.camera.util.FilterUtil
import com.commsource.config.SubscribeConfig
import com.commsource.statistics.ABTestManager
import com.commsource.store.DownloadState
import com.commsource.store.DownloadUIMode
import com.commsource.studio.DecorateConstant
import com.commsource.studio.doodle.DoodleMaterial
import com.commsource.util.*
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

/**
 * @Desc : 涂鸦笔商店itemviewhodler
 * <AUTHOR> Bear - 6/9/21
 */
class DoodleStoreViewHolder(val context: Context, parent: ViewGroup) :
    BaseViewHolder<DoodleMaterial>(context, parent, R.layout.item_shop_doodle) {
    private var viewBinding: ItemShopDoodleBinding = ItemShopDoodleBinding.bind(itemView)

    init {
        viewBinding.executePendingBindings()
    }

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<DoodleMaterial>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        if (payloads == null || payloads.isEmpty()) {
            viewBinding.groupImage.loadThumbnail {
                load(FilterUtil.getGlideThumbPath(item.entity.icon))
                    .fadeTransition(150)
                    .placeHolder(R.drawable.placeholder_stickers_45)
            }
            viewBinding.groupImage.tag = item.entity.id
            ViewUtils.showOrHideView(
                viewBinding.ivColorRing,
                item.entity.canEditColor == DecorateConstant.EDITABLE
            )
            initUiStyle()
        } else {
            updateGroupState()
        }
    }


    private fun updateGroupState() {
        when {
            item.entity.internalState == DecorateConstant.INTERNAL || item.entity.downloadState == DecorateConstant.DOWNLOADED -> {
                viewBinding.xdb.executer()
                    .state(DownloadState.DOWNLOAD_NEXT)
                    .isEnableAnimation(true)
                    .execute()
            }

            item.entity.isDownloading() -> {
                viewBinding.xdb.executer()
                    .progress(item.entity.downloadProgress)
                    .state(DownloadState.DOWNLOADING)
                    .isEnableAnimation(true)
                    .execute()
            }

            else -> {
                viewBinding.xdb.executer()
                    .state(DownloadState.DOWNLOAD_BEFORE)
                    .isEnableAnimation(true)
                    .execute()
            }
        }
    }

    private fun initUiStyle() {
        val isFree = item.entity.paidType == DecorateConstant.FREE

        if (ABTestManager.isNeedHideVipIcon(ABTestManager.TYPE_PHOTO_EDIT, true)) {
            viewBinding.premiumIcon.gone()
        } else {
            viewBinding.premiumIcon.visibility = if (isFree) View.GONE else View.VISIBLE
        }

        viewBinding.xdb.executer()
            .downloadBeforeIcon(20)
            .downloadNextText(
                if (isFree || SubscribeConfig.isSubValid()) "Use" else "Try",
                36.dpf()
            )
            .isEnableAnimation(false)
            .state(
                when {
                    item.entity.needDownload() -> DownloadState.DOWNLOAD_BEFORE
                    item.entity.isDownloading() -> DownloadState.DOWNLOADING
                    else -> {
                        DownloadState.DOWNLOAD_NEXT
                    }
                }
            )
            .mode(DownloadUIMode.DARK_MODE)
            .execute()
    }

}