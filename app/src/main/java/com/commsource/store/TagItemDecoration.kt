package com.commsource.store

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.commsource.util.dp
import com.commsource.util.setRTL

/**
 * @Desc : tag标签的装饰
 * <AUTHOR> Bear - 6/8/21
 */
class TagItemDecoration : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        super.getItemOffsets(outRect, view, parent, state)
        when (parent.getChildAdapterPosition(view)) {
            0 -> outRect.setRTL(16.dp(), 0, 12.dp(), 0)
            parent.adapter!!.itemCount - 1 -> outRect.setRTL(0, 0, 16.dp(), 0)
            else -> outRect.setRTL(0, 0, 12.dp(), 0)
        }
    }
}