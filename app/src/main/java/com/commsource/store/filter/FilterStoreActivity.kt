package com.commsource.store.filter

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.commsource.beautyfilter.NewFilterConfig
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.R
import com.commsource.beautyplus.constants.FilterConstants
import com.commsource.beautyplus.databinding.ActivityFilterShopBinding
import com.commsource.widget.FastCenterScrollLayoutManager
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.shop.FragmentBuilder
import com.commsource.duffle.sticker.DuffleType
import com.commsource.repository.child.filter.FilterRepository
import com.commsource.repository.child.filter.FilterWrapper
import com.commsource.repository.child.filter.NewFilterCategory
import com.commsource.search_common.SearchConst
import com.commsource.search_common.entity.FILTER_WAY
import com.commsource.search_common.entity.KeyWordInfo
import com.commsource.search_common.entity.NET_KEY_FILTER
import com.commsource.store.TagItemDecoration
import com.commsource.store.TagViewHolder
import com.commsource.store.filter.search.FilterSearchRecordFragment
import com.commsource.store.filter.search.FilterStoreSearchResultLayer
import com.commsource.store.filter.search.FilterV4CatId
import com.commsource.store.filter.search.PageConfig
import com.commsource.studio.DecorateConstant
import com.commsource.util.ErrorNotifier
import com.commsource.util.VerifyMothod
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.delegate.process.FilterShopProcess
import com.commsource.util.delegate.process.PaidFilterProcess
import com.commsource.widget.DisplayExtension
import com.commsource.widget.mask.MaskType
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.bean.PositionModel
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.net.NetUtils

/**
 * @Desc : 滤镜商店页面
 * <AUTHOR> Bear - 6/8/21
 */
class FilterStoreActivity : BaseActivity() {

    private lateinit var viewBinding: ActivityFilterShopBinding

    private val mTagAdapter by lazy { BaseRecyclerViewAdapter(this) }

    private var searchFragment: FilterSearchRecordFragment? = null

    /**外界直接跳转详情页面的包ID */
    private val filterStoreViewModel: FilterStoreViewModel by lazy {
        ViewModelProvider(this).get(FilterStoreViewModel::class.java)
    }

    companion object {
        private const val INVALID_ID = -1
        private const val GROUP_ID = "GROUP_ID"
        const val TO_SHOW_FILTER = "TO_SHOW_FILTER"
        const val TO_SHOW_FILTER_GROUP = "TO_SHOW_FILTER_GROUP"
        const val TO_SHOW_FILTER_CATEGORY = "TO_SHOW_FILTER_CATEGORY"
        const val IS_SCROLL_EVENT = "IS_SCROLL_EVENT"
        const val ENTER_FROM = "ENTER_FROM"


        fun startFilterShopActivityForResult(
            frg: Fragment, requestCode: Int,
            groupId: Int = INVALID_ID,
            enterFrom: Int = 0
        ) {
            val intent = Intent(frg.context, FilterStoreActivity::class.java)
            intent.putExtra(ENTER_FROM, enterFrom)
            intent.putExtra(GROUP_ID, groupId)
            frg.startActivityForResult(intent, requestCode)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        overridePendingTransition(R.anim.slide_right_in, R.anim.slide_left_out)
        // 无网络显示的背景图
        viewBinding = DataBindingUtil.setContentView(this, R.layout.activity_filter_shop)
        if (DisplayExtension.hasCutout()) {
            viewBinding.root.setPadding(0, DeviceUtils.getStatusHeight(), 0, 0)
        }
        //tag适配器
        viewBinding.rvGroup.adapter = mTagAdapter
        viewBinding.rvGroup.layoutManager =
            FastCenterScrollLayoutManager(
                this@FilterStoreActivity,
                RecyclerView.HORIZONTAL,
                false
            )
        viewBinding.rvGroup.addItemDecoration(TagItemDecoration())
        // 一级分类
        mTagAdapter.setOnEntityClickListener(NewFilterCategory::class.java) { position, entity ->
            //选中对应的page
            viewBinding.rvGroup.smoothScrollToPosition(position)
            viewBinding.vp.setCurrentItem(position, false)
            mTagAdapter.currentSelectEntity = entity
            false
        }
        viewBinding.vp.isUserInputEnabled = false
        viewBinding.vp.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                viewBinding.rvGroup.smoothScrollToPosition(position)
            }
        })
        //添加数据mask的基础监听回调
        viewBinding.mask.maskContainerHelper.newBuilder()
            .bindView(MaskType.NetError, R.id.tv_action) {
                if (NetUtils.canNetworking(AppContext.context)) {
                    showBackLoadingDialog()
                    FilterRepository.loadData()
                } else {
                    ErrorNotifier.showNetworkErrorToast()
                }
            }
            .build()

        searchFragment = FragmentBuilder.instance.show(
            viewBinding.detailSearchContainer,
            FilterSearchRecordFragment::class.java
        )?.apply {
            config = PageConfig(
                searchBarExpand = false,
                closeSearchPageOnCancel = false,
                layer = FilterStoreSearchResultLayer(),
                isMaterial = false,
                type = KeyWordInfo.TYPE_FILTERS,
                typeInt = DuffleType.FILTER_CATEGORY,
                enterFrom = SearchConst.ENTRANCE_STORE,
                netKey = NET_KEY_FILTER,
                way = FILTER_WAY,
                acceptModel = FilterV4CatId::class.java,
                hideSearchEnter = !FilterRepository.hasOnlineData(),
                onSearchBack = { onBackPressed() }
            )
        }
        if (!FilterRepository.hasOnlineData()) {
            viewBinding.mask.showMask(MaskType.NetError)
        }

        initObserver()

    }


    private fun initObserver() {

        // 点击立即使用
        // 默认使用第一个
        filterStoreViewModel.uiReplyEvent.observe(this, Observer { filterWrapper ->
            val result = Intent()
            result.putExtra(IS_SCROLL_EVENT, filterStoreViewModel.needScrollToFilterId != null)

            filterWrapper?.let {
                result.putExtra(TO_SHOW_FILTER, it.filter.id)
                result.putExtra(TO_SHOW_FILTER_GROUP, it.groupId)
                result.putExtra(TO_SHOW_FILTER_CATEGORY, it.categoryId)
            }
            setResult(Activity.RESULT_OK, result)
            finish()
            VerifyMothod.doOutAnimationEx(this)
        })

        filterStoreViewModel.tagDataEvent.observe(this, Observer {
            it?.let {
                //默认选中
                //如果有上新
                mTagAdapter.currentSelectEntity = null
                mTagAdapter.updateItemEntities(
                    AdapterDataBuilder
                        .create()
                        .addEntities(it, TagViewHolder::class.java)
                        .build()
                )

                viewBinding.vp.adapter = object : FragmentStateAdapter(this@FilterStoreActivity) {

                    override fun getItemCount(): Int {
                        return it.size
                    }

                    override fun createFragment(position: Int): Fragment {
                        return FilterChildStoreFragment().apply {
                            arguments = Bundle().apply { putInt("position", position) }
                        }
                    }
                }

                val hasNew = NewFilterConfig.isShowFilterStoreNewTag()
                val default: NewFilterCategory? = when {
                    hasNew -> {
                        NewFilterConfig.setShowFilterStoreNewTag(false)
                        it.find { it.categoryId == DecorateConstant.NEW_CATEGORY_ALL }
                    }

                    else -> it.find { it.categoryId == DecorateConstant.NEW_CATEGORY_HOT }
                }

                // 如果快速点击过了，就使用点击的分类
                val currentSelectEntity = mTagAdapter.currentSelectEntity
                if (currentSelectEntity != null) {
                    mTagAdapter.currentSelectEntity = currentSelectEntity
                    viewBinding.vp.setCurrentItem(it.indexOf(currentSelectEntity), false)
                } else {
                    mTagAdapter.currentSelectEntity = default
                    default?.let { defaultEntity ->
                        viewBinding.vp.setCurrentItem(it.indexOf(defaultEntity), false)
                    }
                }
            }
        })

        // 订阅事件
        filterStoreViewModel.toSubscribeEvent.observe(this, Observer {
            IProcessHandler(this)
                .execute(object :
                    PaidFilterProcess(FilterConstants.ORIGINAL_ID, ModuleEnum.CAMERA_TAKE) {
                    override fun onSubscribeResult(isSubscribeSuccess: Boolean) {
                        // 刷新数据。
                        if (isSubscribeSuccess) {
                            filterStoreViewModel.subscribeResultEvent.value = true
                        }
                    }
                })
        })

        //展示详情页面
        filterStoreViewModel.shopDetailEvent.observe(this, Observer { group ->
            val params = Bundle()
            group?.categoryId?.let {
                params.putString(FilterStoreDetailFragment.GROUP_ID, it)
            }
            FragmentBuilder.instance.show(
                viewBinding.detailContainer,
                FilterStoreDetailFragment::class.java,
                params,
                R.anim.slide_right_in,
                R.anim.slide_left_out
            )?.apply {
                val enterFrom = intent.getIntExtra(ENTER_FROM, -1)
                val isInSearch = searchFragment?.hasSearchResult() ?: false
                val searchWord = searchFragment?.getCurrentSearchWord()
                val isSelfieEnter = when (enterFrom) {
                    FilterShopProcess.ENTER_FROM_CAMERA -> true
                    FilterShopProcess.ENTER_FROM_IMAGE -> false
                    else -> null
                }
                logDetailConfig = FilterStoreDetailFragment.LogDetailConfig(
                    isSelfieEnter, isInSearch, searchWord ?: ""
                )
            }
        })

        //进度变化监听
        FilterRepository.downloadObserver.successEvent.observe(this, object : NoStickLiveData.CustomObserver<FilterWrapper?>() {

            override fun onAccept(o: FilterWrapper?) {
                super.onAccept(o)
                o?.let {
                    if (o.filter.downloadState == FilterConstants.DOWNLOADED) {
                        filterStoreViewModel.needScrollToFilterId = o
                    }
                }
            }
        })

        // 拉取数据
        filterStoreViewModel.dataChangeEvent.observe(this, object : NoStickLiveData.CustomObserver<Boolean?>() {
            override fun onAccept(isSuccess: Boolean?) {
                isSuccess?.let {
                    if (isSuccess) {
                        if (FilterRepository.hasOnlineData()) {
                            searchFragment?.setSearchEnterState(true)
                        }
                        viewBinding.mask.hideAll()
                        dismissBackLoadingDialog()
                        filterStoreViewModel.loadData()
                    } else {
                        dismissBackLoadingDialog()
                    }
                }
            }
        })

        // 页面关闭
        filterStoreViewModel.closeEvent.observe(this, object : NoStickLiveData.CustomObserver<Boolean?>() {
            override fun onAccept(o: Boolean?) {
                when {
                    FragmentBuilder.instance.remove(
                        FilterStoreDetailFragment::class.java,
                        R.anim.slide_left_in,
                        R.anim.slide_right_out
                    ) || searchFragment?.existSearch() == true -> {
                    }

                    filterStoreViewModel.needScrollToFilterId != null -> {
                        filterStoreViewModel.needScrollToFilterId?.let {
                            val curFilterWrapper = FilterRepository.getFilterWrapper(
                                it.filter.id,
                                it.groupId,
                                it.categoryId
                            )
                            curFilterWrapper?.let {
                                val group = FilterRepository.getSecondCategoryById(it.groupId)
                                group?.checkGroupDownloadState()
                                if (group?.groupDownloadStatus == FilterConstants.GROUP_DOWNLOADED) {
                                    filterStoreViewModel.uiReplyEvent.value =
                                        group.filterWrapper?.get(0)
                                    return@let
                                }
                                filterStoreViewModel.uiReplyEvent.value =
                                    filterStoreViewModel.needScrollToFilterId
                            }
                        }
                    }

                    else -> {
                        finish()
                        VerifyMothod.doOutAnimationEx(this@FilterStoreActivity)
                    }
                }
            }
        })

        filterStoreViewModel.loadData()
    }

    override fun onDestroy() {
        super.onDestroy()
        filterStoreViewModel.dataChangeEvent.removeObservers(this)
    }

    override fun addSpm() {
        val model = PositionModel()
        model.pageId = FilterStoreActivity::class.java.simpleName
        model.setPageObject(this)
        model.page = "1012_01"
        SPMManager.instance.pushSpm(model)
    }

    override fun updateSpmInfo() {
        val model = PositionModel()
        model.pageId = FilterStoreActivity::class.java.simpleName
        model.setPageObject(this)
        model.page = "1012_01"
        SPMManager.instance.update(model)
    }

    override fun onBackPressed() {
        filterStoreViewModel.closeEvent.value = true
        overridePendingTransition(R.anim.slide_left_in, R.anim.slide_right_out)
    }

}