package com.commsource.store.filter

import android.content.Context
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.constants.FilterConstants
import com.commsource.beautyplus.databinding.ItemShopDetailHeaderBinding
import com.commsource.camera.mvp.helper.XSpanUtils
import com.commsource.camera.xcamera.cover.CameraConfigViewModel
import com.commsource.repository.child.filter.NewFilterCategory
import com.commsource.util.RTLTool
import com.commsource.util.dpf
import com.commsource.util.gone
import com.commsource.util.notnull
import com.commsource.util.resColor
import com.commsource.util.text
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

/**
 * @Desc : 滤镜详情页面头部viewholder
 * <AUTHOR> Bear - 6/9/21
 */
class FilterStoreDetailHeaderViewHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<NewFilterCategory>(context, parent, R.layout.item_shop_detail_header) {
    private val viewBinding = ItemShopDetailHeaderBinding.bind(itemView)

    init {
        viewBinding.executePendingBindings()
    }

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<NewFilterCategory>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        item.entity?.let {
            viewBinding.root.setPadding(0, CameraConfigViewModel.getNotchPaddingTop(), 0, 0)
            viewBinding.group = it

            viewBinding.groupTitleFree.text = it.categoryName ?: ""
            viewBinding.filterCount.text = " . " + R.string.filter_count.text(it.filterWrapper?.size.notnull(0).toString())

            if (it.groupPaidState == FilterConstants.FREE) {
                viewBinding.ivPro.gone()
                viewBinding.freeIndicator.translationX = 0f
            } else {
                viewBinding.ivPro.visible()
                if (RTLTool.isLayoutRtl()) {
                    viewBinding.freeIndicator.translationX = (-20).dpf()
                } else {
                    viewBinding.freeIndicator.translationX = 20.dpf()
                }
            }

            viewBinding.freeIndicator.setText(
                XSpanUtils()
                    .append(if (it.groupPaidState == FilterConstants.FREE) R.string.free.text() else R.string.premium.text())
                    .setForegroundColor(if (it.groupPaidState == FilterConstants.FREE) R.color.Gray_B.resColor() else R.color.black.resColor())
                    .create()
            )
        }

    }

}