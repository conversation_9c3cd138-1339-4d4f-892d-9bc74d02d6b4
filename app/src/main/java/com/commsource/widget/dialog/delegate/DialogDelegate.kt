package com.commsource.widget.dialog.delegate

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.commsource.beautyplus.setting.language.LanguageConfig
import com.commsource.widget.dialog.delegate.config.DialogConfig

/**
 * @Description : XDialog代理
 * <AUTHOR> bear
 * @Date : 2021/12/16
 */
abstract class DialogDelegate<Config : DialogConfig, Binding : ViewDataBinding>(
    val dialog: XDialog,
    val config: Config
) {

    val mViewBindig: Binding by lazy {
        DataBindingUtil.inflate<Binding>(
            LayoutInflater.from(dialog.context),
            config.layoutId,
            null,
            false
        )
    }

    fun getView(): View {
        return mViewBindig.root
    }

    fun getAnimation(): Int {
        return config.animations
    }

    var isInitView = false

    fun onCreateView(rootView: FrameLayout) {
        //清空旧viewGroup 多次调用失败
        mViewBindig.root.parent?.let {
            it as ViewGroup
            it.removeView(mViewBindig.root)
        }
        //添加到rootView
        rootView.addView(mViewBindig.root)
        if (!isInitView) {
            isInitView = true
            initView()
        }
    }

    abstract fun initView()

    open fun onDestory() {}
}