package com.commsource.widget.dialog.delegate

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.text.SpannableString
import android.view.Gravity
import android.view.KeyEvent
import android.view.MotionEvent
import androidx.core.view.doOnPreDraw
import com.commsource.BaseDialog
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.DialogContentBinding
import com.commsource.beautyplus.setting.language.LanguageConfig
import com.commsource.util.dp
import com.commsource.util.text
import com.commsource.widget.dialog.delegate.config.BottomSheetConfig
import com.commsource.widget.dialog.delegate.config.DialogConfig
import com.commsource.widget.dialog.delegate.config.DialogPopAnimation
import com.commsource.widget.dialog.delegate.config.DialogUIMode
import com.commsource.widget.dialog.delegate.config.SimpleDialogConfig
import com.commsource.widget.dialog.delegate.config.VideoPictureTipsDialogConfig
import com.meitu.library.util.device.DeviceUtils

/**
 * @Description : 对话框整体代理结构
 * <AUTHOR> bear
 * @Date : 2021/12/16
 */
open class XDialog : BaseDialog<DialogContentBinding>() {

    var delegate: DialogDelegate<*, *>? = null

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setCanceledOnTouchOutside(delegate?.config?.cancelOutside == true)
        dialog.setOnKeyListener { dialog, keyCode, event ->
            if (event.action == MotionEvent.ACTION_UP && keyCode == KeyEvent.KEYCODE_BACK && delegate?.config?.clickCloseListener != null && delegate?.config?.cancelAble == true) {
                delegate?.config?.clickCloseListener?.invoke(this)
                return@setOnKeyListener true
            }
            return@setOnKeyListener false
        }
        return dialog
    }


    override fun getLayoutId(): Int {
        return R.layout.dialog_content
    }

    override fun getAnimations(): Int {
        return R.style.alpha_in_animation
    }

    override fun getDialogStyle(): Int {
        return R.style.fullScreenDialog
    }

    override fun getDialogGravity(): Int {
        return delegate?.config?.gravity ?: Gravity.CENTER
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        LanguageConfig.changeLanguage(requireContext())
        super.onCreate(savedInstanceState)
    }

    override fun bindView() {
        if (delegate == null) {
            dismissAllowingStateLoss()
            return
        }
        isCancelable = delegate?.config?.cancelAble == true
        delegate?.onCreateView(mBinding.flContent)
        delegate?.apply {
            getView().let {
                it.animate().setListener(null).cancel()
                if (config.animations == DialogPopAnimation.BOTTOM) {
                    it.post {
                        it.translationY = it.height.toFloat()
                        it.animate().translationY(0f).setDuration(250).start()
                    }
                } else {
                    if (config.animations == DialogPopAnimation.CENTER) {
                        it.scaleX = 0.9f
                        it.scaleY = 0.9f
                    }
                    it.animate().apply {
                        if (config.animations == DialogPopAnimation.CENTER) {
                            scaleX(1f)
                            scaleY(1f)
                        }
                    }.setDuration(250)
                        .start()
                }
            }
        }
    }

    private fun dismissIm() {
        super.dismiss()
    }

    private var dismissed = false
    override fun dismiss() {
        if (dismissed) {
            return
        }
        dismissed = true
        delegate?.apply {
            getView().let {
                it.animate().setListener(null).cancel()
                if (config.animations == DialogPopAnimation.BOTTOM) {
                    it.animate().translationY(it.height.toFloat()).setDuration(250).start()
                } else {
                    it.animate()
                        .apply {
                            if (config.animations == DialogPopAnimation.CENTER) {
                                scaleX(0.9f)
                                scaleY(0.9f)
                                alpha(0f)
                            }
                        }.setDuration(200)
                        .setListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: Animator) {
                                super.onAnimationEnd(animation)
                                dismissIm()
                            }
                        })
                        .start()
                }
            }
        }
    }

    /**
     * 特殊的dialog衔接消失动效，需要dialog直接消失
     */
    override fun dismissImmediately() {
        if (dismissed) {
            return
        }
        dismissed = true
        dialog?.window?.setDimAmount(0f)
        delegate?.apply {
            mViewBindig.root.alpha = 0f
            //如果立刻执行dismiss，window类似冻屏幕，所有view的调整无效，我们优先等这次的渲染刷新完成，再执行dismiss
            mViewBindig.root.doOnPreDraw {
                dismissIm()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        delegate?.onDestory()
    }

    override fun onCancel(dialog: DialogInterface?) {
        super.onCancel(dialog)
        delegate?.config?.onCancelListener?.onCancel(dialog)
    }

    override fun onDismiss(dialog: DialogInterface?) {
        super.onDismiss(dialog)
        delegate?.config?.onDismissListener?.onDismiss(dialog)
    }

    companion object {
        const val NEGATIVE_UI = 0
        const val POSITIVE_UI = 1
        const val PRO_UI = 2
    }

    override fun getDialogTag(): String {
        return "XDialog"
    }
}

/**
 * tips
 */
fun buildTips(
    title: String? = null,
    content: String? = null,
    positive: String? = null,
    negative: String? = null,
    closeEnable: Boolean = false,
    cancelOutside: Boolean = true,
    @DialogUIMode mode: Int = DialogUIMode.NORMAL_MODE,
    focusedImageStyle: Boolean = false,
    negativeClick: ((dialog: XDialog) -> Unit)? = null,
    positiveClick: ((dialog: XDialog) -> Unit)? = null
): XDialog {
    return XDialog {
        VideoPictureTips {
            FocusedImageStyle(focusedImageStyle)
            Title(title)
            Content(content)
            PositiveButton(positive ?: R.string.ok.text()) {
                it.dismiss()
                positiveClick?.invoke(it)
            }
            NegativeButton(negative ?: R.string.cancel.text()) {
                it.dismiss()
                negativeClick?.invoke(it)
            }
            this.mode = mode
            this.closeEnable = closeEnable
            this.cancelOutside = cancelOutside
            popupCenter()
        }
    }
}

/**
 * 构建
 */
fun XDialog(content: XDialog.() -> Unit): XDialog {
    return XDialog().apply(content)
}

/**
 * 视频图文弹窗
 */
fun XDialog.VideoPictureTips(content: VideoPictureTipsDialogConfig.() -> Unit) {
    delegate = VideoPictureTipsDialogDelegate(
        this,
        VideoPictureTipsDialogConfig().popupCenter().apply(content)
    )
}

/**
 * BottomSheet 弹窗
 */
fun XDialog.BottomSheet(content: BottomSheetConfig.() -> Unit) {
    delegate = BottomSheetDelegate(
        this,
        BottomSheetConfig().popupBottom().apply(content)
    )
}

fun XDialog.SimpleDialog(content: SimpleDialogConfig.() -> Unit) {
    delegate = SimpleDialogDelegate(
        this,
        SimpleDialogConfig().popupCenter().apply(content)
    )
}

/**
 * 标题
 */
fun DialogConfig.Title(title: String?) {
    this.title = title ?: ""
}

fun DialogConfig.Title(title: SpannableString?) {
    this.titleSpanString = title
}


fun DialogConfig.FocusedImageStyle(focusedImageStyle: Boolean) {
    this.focusedImageStyle = focusedImageStyle
}

/**
 * 内容
 */
fun DialogConfig.Content(content: String?) {
    this.content.add(content ?: "")
}


/**
 * 关闭按钮
 */
fun DialogConfig.CloseButton(
    isEnable: Boolean,
    clickCloseListener: ((dialog: XDialog) -> Unit)? = null
) {
    this.closeEnable = isEnable
    this.clickCloseListener = clickCloseListener
}

/**
 * 推荐按钮
 */
fun DialogConfig.PositiveButton(
    text: String = R.string.confirm.text(),
    onClick: (dialog: XDialog) -> Unit = { it.dismiss() }
) {
    buttons.add(XDialogButton(XDialog.POSITIVE_UI, text, onClick))
}

fun DialogConfig.OnDismiss(onDismiss: DialogInterface.OnDismissListener? = null) {
    onDismissListener = onDismiss
}

/**
 * 订阅按钮
 */
fun DialogConfig.ProButton(text: String, onClick: (dialog: XDialog) -> Unit) {
    buttons.add(XDialogButton(XDialog.PRO_UI, text, onClick))
}

/**
 * 不推荐按钮
 */
fun DialogConfig.NegativeButton(
    text: String = R.string.cancel.text(),
    onClick: (dialog: XDialog) -> Unit = { it.dismiss() }
) {
    buttons.add(XDialogButton(XDialog.NEGATIVE_UI, text, onClick))
}

/**
 * 中心弹出
 */
fun <T : DialogConfig> T.popupCenter(): T {
    animations = DialogPopAnimation.CENTER
    gravity = Gravity.CENTER
    padding = 24.dp()
    startSpace = if (DeviceUtils.getScreenWidth() < 375.dp()) {
        30.dp()
    } else if (DeviceUtils.getScreenWidth() < 600.dp()) {
        40.dp()
    } else {
        100.dp()
    }
    endSpace = if (DeviceUtils.getScreenWidth() < 375.dp()) {
        30.dp()
    } else if (DeviceUtils.getScreenWidth() < 600.dp()) {
        40.dp()
    } else {
        100.dp()
    }
    return this
}

fun <T : DialogConfig> T.popupBottom(): T {
    animations = DialogPopAnimation.BOTTOM
    gravity = Gravity.BOTTOM
    return this
}