package com.commsource.widget.round

import android.content.Context
import android.graphics.Outline
import android.graphics.Rect
import android.os.Build
import android.util.AttributeSet
import android.view.View
import android.view.ViewOutlineProvider
import android.widget.LinearLayout
import androidx.annotation.RequiresApi

class RoundedLinearLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    init {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 确保视图的边角被裁剪成圆角
            clipToOutline = true
        }
    }

    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    override fun setOutlineProvider(outlineProvider: ViewOutlineProvider) {
        super.setOutlineProvider(outlineProvider)
    }

    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    override fun getOutlineProvider(): ViewOutlineProvider {
        return object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                val height = height
                val cornerRadius = (height / 2f) // 圆角半径是视图高度的一半
                outline.setRoundRect(0, 0, width, height, cornerRadius)
            }
        }
    }
}