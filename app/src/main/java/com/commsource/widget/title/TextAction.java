package com.commsource.widget.title;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.meitu.library.util.device.DeviceUtils;

/**
 * @Desc : 图片Action
 * <AUTHOR> csxiong - 2019-08-27
 */
public abstract class TextAction extends Action<TextView> {

    private TextView mTv;

    private String text;

    public TextAction(String text) {
        this.text = text;
    }

    @Override
    public TextView onCreateAction(Context context) {
        if (mTv == null) {
            mTv = new TextView(context);
            TypedValue typedValue = new TypedValue();
            int[] attribute = new int[]{android.R.attr.actionBarItemBackground};
            TypedArray typedArray = context.getTheme().obtainStyledAttributes(typedValue.resourceId, attribute);
            mTv.setBackground(typedArray.getDrawable(0));
            if (!TextUtils.isEmpty(text)) {
                mTv.setText(text);
            }
        }
        return mTv;
    }

    @Override
    public int[] marginLTRB() {
        return new int[]{0, 0, 0, 0};
    }
}
