package com.commsource.widget.infiniteview;

import android.graphics.PointF;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.commsource.util.RTLTool;

/**
 * @Desc : 循环LayoutManager
 * <AUTHOR> csxiong - 2019-09-03
 */
public class LoopLayoutManager extends RecyclerView.LayoutManager
        implements RecyclerView.SmoothScroller.ScrollVectorProvider {

    private boolean isItemChangeCauseLayout;

    private OnLayoutChildrenListener onLayoutChildrenListener;

    public void setOnLayoutChildrenListener(OnLayoutChildrenListener onLayoutChildrenListener) {
        this.onLayoutChildrenListener = onLayoutChildrenListener;
    }

    public LoopLayoutManager() {

    }

    @Override
    public RecyclerView.LayoutParams generateDefaultLayoutParams() {
        return new RecyclerView.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    private int selectPosition = 0;

    public void setSelectPosition(int selectPosition) {
        this.selectPosition = selectPosition;
    }

    public int getSelectPosition() {
        return selectPosition;
    }

    @Nullable
    @Override
    public PointF computeScrollVectorForPosition(int targetPosition) {
        if (getChildCount() == 0) {
            return null;
        }
        int firstChildPos = getPosition(getChildAt(0));
        int direction = targetPosition < firstChildPos ? -1 : 1;
        return new PointF(direction, 0f);
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        super.onLayoutChildren(recycler, state);
        if (!state.didStructureChange()) {
            return;
        }
        if (onLayoutChildrenListener != null) {
            onLayoutChildrenListener.onLayoutChildren();
        }
        //  记录重新排布时候第一位的初始状态
        int startOffsetX = getPaddingStart();
        if (RTLTool.isLayoutRtl()) {
            startOffsetX = getWidth() - getPaddingStart();
        }

        int index = selectPosition;
        if (getChildCount() > 0) {
            View first = getChildAt(0);
            if (first != null) {
                // todo 这里改了计算逻辑有bug，从这边查
                if (RTLTool.isLayoutRtl()) {
                    startOffsetX = first.getRight();
                } else {
                    startOffsetX = first.getLeft();
                }
                index = getPosition(first);
            }
        }
        // 分离并且回收当前附加的所有View
        detachAndScrapAttachedViews(recycler);
        if (getItemCount() == 0) {
            return;
        }

        boolean isOverScreen = false;
        while (true) {
            if (index >= getItemCount() || index < 0) {
                index = 0;
            }
            View view = recycler.getViewForPosition(index);
            index++;
            addView(view);

            measureChildWithMargins(view, 0, 0);
            int viewWidth = getDecoratedMeasuredWidth(view);
            int viewHeight = getDecoratedMeasuredHeight(view);

            if (RTLTool.isLayoutRtl()) {
                layoutDecoratedWithMargins(view, startOffsetX - viewWidth, 0, startOffsetX, viewHeight);
                startOffsetX -= viewWidth;
                isOverScreen = startOffsetX < 0;
            } else {
                layoutDecoratedWithMargins(view, startOffsetX, 0, startOffsetX + viewWidth, viewHeight);
                startOffsetX += viewWidth;
                isOverScreen = startOffsetX > getWidth();
            }

            if (isOverScreen) {
                // 超出屏幕就不绘制了
                return;
            }
        }
    }

    @Override
    public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
        LinearSmoothScroller linearSmoothScroller = new LinearSmoothScroller(recyclerView.getContext()) {
            @Nullable
            @Override
            public PointF computeScrollVectorForPosition(int targetPosition) {
                return LoopLayoutManager.this.computeScrollVectorForPosition(targetPosition);
            }

            @Override
            protected int calculateTimeForScrolling(int dx) {
                return 250;
            }

            @Override
            protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
                // 计算滑动每个像素需要的时间，这里应该与屏幕适配；
                return 300f / displayMetrics.densityDpi;
            }

            @Override
            public int calculateDxToMakeVisible(View view, int snapPreference) {
                if (RTLTool.isLayoutRtl()) {
                    return view.getMeasuredWidth();
                }
                return -view.getMeasuredWidth();
            }
        };
        linearSmoothScroller.setTargetPosition(position);
        startSmoothScroll(linearSmoothScroller);
    }

    /**
     * 目前仅开放水平loop
     *
     * @return
     */
    @Override
    public boolean canScrollHorizontally() {
        return true;
    }

    @Override
    public boolean canScrollVertically() {
        return false;
    }

    @Override
    public int scrollHorizontallyBy(int dx, RecyclerView.Recycler recycler, RecyclerView.State state) {
        if (dx == 0) {
            return dx;
        }
        recyclerViews(dx, recycler);
        fill(dx, recycler);
        offsetChildrenHorizontal(dx * -1);
        return dx;
    }

    /**
     * 填充满目前的滑动距离内
     *
     * @param dx
     * @param recycler
     */
    public void fill(int dx, RecyclerView.Recycler recycler) {
        if (dx > 0) {
            rightScrollToLeft(dx, recycler);
        } else if (dx < 0) {
            leftScrollToRight(dx, recycler);
        }
    }

    private void leftScrollToRight(int dx, RecyclerView.Recycler recycler) {
        while (true) {
            if (RTLTool.isLayoutRtl()) {
                View view = getChildAt(getChildCount() - 1);
                if (view == null) {
                    break;
                }

                if (view.getLeft() - dx < 0) {
                    break;
                }

                int layoutPosition = getPosition(view);

                View fillView;
                if (layoutPosition == getItemCount() - 1) {
                    fillView = recycler.getViewForPosition(0);
                } else {
                    fillView = recycler.getViewForPosition(layoutPosition + 1);
                }

                addView(fillView);
                measureChildWithMargins(fillView, 0, 0);
                int viewWidht = getDecoratedMeasuredWidth(fillView);
                int viewHeight = getDecoratedMeasuredHeight(fillView);
                layoutDecoratedWithMargins(fillView, view.getLeft() - viewWidht, 0, view.getLeft(), viewHeight);
            } else {
                View view = getChildAt(0);
                if (view == null) {
                    break;
                }

                if (view.getLeft() - dx < 0) {
                    break;
                }

                int layoutPosition = getPosition(view);

                View fillView;
                if (layoutPosition <= 0) {
                    fillView = recycler.getViewForPosition(getItemCount() - 1);
                } else {
                    fillView = recycler.getViewForPosition(layoutPosition - 1);
                }

                addView(fillView, 0);
                measureChildWithMargins(fillView, 0, 0);
                int viewWidht = getDecoratedMeasuredWidth(fillView);
                int viewHeight = getDecoratedMeasuredHeight(fillView);
                layoutDecoratedWithMargins(fillView, view.getLeft() - viewWidht, 0, view.getLeft(), viewHeight);
            }
        }
    }

    private void rightScrollToLeft(int dx, RecyclerView.Recycler recycler) {
        while (true) {
            if (RTLTool.isLayoutRtl()) {
                View view = getChildAt(0);
                if (view == null) {
                    break;
                }

                if (view.getRight() - dx > getWidth()) {
                    break;
                }

                int layoutPosition = getPosition(view);

                View fillView;
                if (layoutPosition == 0) {
                    fillView = recycler.getViewForPosition(getItemCount() - 1);
                } else {
                    fillView = recycler.getViewForPosition(layoutPosition - 1);
                }

                addView(fillView, 0);
                measureChildWithMargins(fillView, 0, 0);
                int viewWidth = getDecoratedMeasuredWidth(fillView);
                int viewHeight = getDecoratedMeasuredHeight(fillView);
                layoutDecoratedWithMargins(fillView, view.getRight(), 0, view.getRight() + viewWidth, viewHeight);
            } else {
                // 向左滑动
                View view = getChildAt(getChildCount() - 1);
                if (view == null) {
                    break;
                }

                if (view.getRight() - dx > getWidth()) {
                    break;
                }

                int layoutPosition = getPosition(view);

                View fillView;
                if (layoutPosition == getItemCount() - 1) {
                    fillView = recycler.getViewForPosition(0);
                } else {
                    fillView = recycler.getViewForPosition(layoutPosition + 1);
                }

                addView(fillView);
                measureChildWithMargins(fillView, 0, 0);
                int viewWidth = getDecoratedMeasuredWidth(fillView);
                int viewHeight = getDecoratedMeasuredHeight(fillView);
                layoutDecoratedWithMargins(fillView, view.getRight(), 0, view.getRight() + viewWidth, viewHeight);
            }
        }
    }

    /**
     * 回收不在屏幕内的View
     *
     * @param dx
     * @param recycler
     */
    public void recyclerViews(int dx, RecyclerView.Recycler recycler) {
        if (dx == 0) {
            return;
        }
        for (int i = 0; i < getItemCount(); i++) {
            View view = getChildAt(i);
            if (view == null) {
                continue;
            }
            if (dx > 0) {
                // 左滑动
                if (view.getRight() - dx < 0) {
                    removeAndRecycleViewAt(i, recycler);
                }
            } else {
                // 右滑动
                if (view.getLeft() - dx > getWidth()) {
                    removeAndRecycleViewAt(i, recycler);
                }
            }
        }
    }

    public int findFirstVisiablePosition(RecyclerView recyclerView) {
        if (recyclerView.getChildCount() > 0) {
            View view = recyclerView.getChildAt(0);
            return recyclerView.getChildLayoutPosition(view);
        }
        return 0;
    }

    public int findLastVisiablePosition(RecyclerView recyclerView) {
        if (recyclerView.getChildCount() > 0) {
            View view = recyclerView.getChildAt(recyclerView.getChildCount() - 1);
            return recyclerView.getChildLayoutPosition(view);
        }
        return 0;
    }

    /**
     * 在排版时的回调
     */
    public interface OnLayoutChildrenListener {

        void onLayoutChildren();

    }

    /**
     * @Desc : 需要修改pager的内容
     * <AUTHOR> csxiong - 2019-09-03
     */
    public static class LoopPagerSnapHelper extends PagerSnapHelper {

        private RecyclerView targetRecyclerView;

        private LoopLayoutManager loopLayoutManager;

        private onLoopPageListener onLoopPageListener;

        public LoopPagerSnapHelper(LoopLayoutManager loopLayoutManager) {
            this.loopLayoutManager = loopLayoutManager;
        }

        @Override
        public boolean onFling(int velocityX, int velocityY) {
            // 先帮我们找一遍
            int targetSnapPosition = findTargetSnapPosition(loopLayoutManager, velocityX, velocityY);
            if (targetSnapPosition >= loopLayoutManager.getItemCount()) {
                targetSnapPosition = 0;
            } else if (targetSnapPosition < 0) {
                targetSnapPosition = loopLayoutManager.getItemCount() - 1;
            }
            if (targetSnapPosition == loopLayoutManager.selectPosition) {
                return super.onFling(velocityX, velocityY);
            }
            loopLayoutManager.selectPosition = targetSnapPosition;
            if (onLoopPageListener != null) {
                onLoopPageListener.onNextPage(targetSnapPosition);
            }
            loopLayoutManager.selectPosition = targetSnapPosition;
            if (targetSnapPosition == -1) {
                return false;
            } else {
                RecyclerView.SmoothScroller scroller = createScroller(loopLayoutManager);
                scroller.setTargetPosition(targetSnapPosition);
                loopLayoutManager.startSmoothScroll(scroller);
                return true;
            }
        }

        public void setOnLoopPageListener(LoopPagerSnapHelper.onLoopPageListener onLoopPageListener) {
            this.onLoopPageListener = onLoopPageListener;
        }

        public void scrollToPosition(int position) {
            View view = loopLayoutManager.findViewByPosition(position);
            if (view != null) {
                int[] ints = calculateDistanceToFinalSnap(loopLayoutManager, view);
                if (ints != null) {
                    if (ints[0] != 0 || ints[1] != 0) {
                        targetRecyclerView.smoothScrollBy(ints[0], ints[1]);
                    }
                }
            }
        }

        @Override
        public void attachToRecyclerView(@Nullable RecyclerView recyclerView)
                throws IllegalStateException {
            targetRecyclerView = recyclerView;
            if (targetRecyclerView != null) {
                targetRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {

                    int scrollState = RecyclerView.SCROLL_STATE_IDLE;

                    @Override
                    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                        super.onScrollStateChanged(recyclerView, newState);
                        if (targetRecyclerView != null && scrollState == 0 && newState != 0) {
                            int targetSnapPosition = findTargetSnapPosition(loopLayoutManager, 0, 0);
                            if (targetSnapPosition >= loopLayoutManager.getItemCount()) {
                                targetSnapPosition = 0;
                            } else if (targetSnapPosition < 0) {
                                targetSnapPosition = loopLayoutManager.getItemCount() - 1;
                            }
                            if (targetSnapPosition != loopLayoutManager.selectPosition) {
                                loopLayoutManager.selectPosition = targetSnapPosition;
                                if (onLoopPageListener != null) {
                                    onLoopPageListener.onNextPage(loopLayoutManager.selectPosition);
                                }
                            }
                        }
                        scrollState = newState;
                    }

                    @Override
                    public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                        super.onScrolled(recyclerView, dx, dy);
                    }
                });
            }
            super.attachToRecyclerView(recyclerView);
        }

        public interface onLoopPageListener {

            void onNextPage(int targetPosition);

        }

    }
}
