package com.commsource.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Xfermode;
import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.widget.ImageView;

import com.commsource.beautyplus.R;
import com.meitu.library.util.bitmap.BitmapUtils;

/**
 * <AUTHOR> 2017/6/28.
 */

public class ShadowImageView extends ImageView {

    /**
     * 混合模式
     */
    private Xfermode xfermode;

    /**
     * 阴影半径
     */
    private int mShadowRadius;
    /**
     * 阴影颜色
     */
    private int mShadowColor;

    private Paint mPaint;

    private Bitmap dstBitmap;

    private Bitmap srcBitmap;

    public ShadowImageView(Context context) {
        this(context, null);
    }

    public ShadowImageView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ShadowImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        if (attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.ShadowImageView);
            mShadowRadius = typedArray.getInt(R.styleable.ShadowImageView_shadowRadius, 30);
            mShadowColor = typedArray.getColor(R.styleable.ShadowImageView_shadowColor, getResources().getColor(R.color.color_dedede));
            typedArray.recycle();
        }
    }

    /**
     * 初始化
     */
    private void init() {
        if (mPaint == null) {
            xfermode = new PorterDuffXfermode(PorterDuff.Mode.SRC_OVER);

            mPaint = new Paint();

            mPaint.setFilterBitmap(false);
            mPaint.setXfermode(xfermode);

            dstBitmap = createWhiteBitmap(getWidth(), getHeight(), false);

            srcBitmap = createWhiteBitmap(getWidth(), getHeight(), true);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int layerCount = canvas.saveLayer(0.0F, 0.0F, getWidth(), getHeight(), null, Canvas.ALL_SAVE_FLAG);
        Drawable viewDrawable = getDrawable();
        if (viewDrawable == null) {
            if (BitmapUtils.isAvailableBitmap(dstBitmap)) {
                canvas.drawBitmap(dstBitmap, 0.0F, 0.0F, mPaint);
            }
        } else {
            viewDrawable.setBounds(0, 0, getWidth(), getHeight());
            viewDrawable.draw(canvas);
        }
        if (BitmapUtils.isAvailableBitmap(srcBitmap)) {
            canvas.drawBitmap(srcBitmap, 0.0F, 0.0F, mPaint);
        }
        canvas.restoreToCount(layerCount);
    }

    @Override
    public void layout(int l, int t, int r, int b) {
        super.layout(l, t, r, b);
        init();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        init();
    }

    /**
     * 创建白色的Bitmap
     *
     * @param width
     * @param height
     * @param hasShadow
     * @return
     */
    private Bitmap createWhiteBitmap(int width, int height, boolean hasShadow) {
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        Paint paint = new Paint();
        paint.setColor(Color.WHITE);
        if (hasShadow) {
            paint.setShadowLayer(mShadowRadius, 0, 0, mShadowColor);
            canvas.drawRect(new RectF(new Rect(mShadowRadius, mShadowRadius, width - mShadowRadius, height - mShadowRadius)), paint);
        } else {
            canvas.drawRect(new RectF(new Rect(0, 0, width, height)), paint);
        }
        return bitmap;
    }

}
