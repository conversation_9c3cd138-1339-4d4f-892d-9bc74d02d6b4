package com.commsource.applanuch

import android.app.Application
import android.os.Build
import android.os.StrictMode
import com.asm.GLogUtil
import com.commsource.beautyplus.CrashHandler
import com.commsource.beautyplus.leaktrace.MemoryLeakHooker
import com.commsource.util.AppTools
import com.knightboot.spwaitkiller.SpWaitKiller

class FixBugLaunchTask : AbsLaunchTask() {

    override fun onInitial(app: Application) {
        // 替换CrashHandler
        CrashHandler.getInstance().replaceCrashHandler()
        // Matrix 虚拟内存占用优化
        MemoryLeakHooker.initMatrix()
        //SP QueuedWork修复
        SpWaitKiller.builder(app).build().work()
        // 7.0以上机子分享时传递App外Uri路径导致奔溃，
        // 出现此Bug是在一些H5上面，这里时防止奔溃。
        val builder = StrictMode.VmPolicy.Builder()
        builder.detectFileUriExposure()
        StrictMode.setVmPolicy(builder.build())
        // 关闭看门狗线程
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.O_MR1) {
            AppTools.stopFinalizerWatchdogDaemonThread()
        }
        GLogUtil.init(app.baseContext)
    }
}