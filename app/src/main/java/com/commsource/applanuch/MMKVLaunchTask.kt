package com.commsource.applanuch

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.commsource.beautyplus.BuildConfig
import com.commsource.beautyplus.util.PathUtil
import com.commsource.config.ApplicationConfig
import com.commsource.config.mmkv.MMKVMigrateConfig.fetchMigrateList
import com.commsource.statistics.MTPageDurationManager
import com.commsource.util.common.MMKVMigrateHelper
import com.commsource.util.common.MMKVMigrateHelper.fetchMigratedState
import com.commsource.util.common.MMKVMigrateHelper.isCurVersionCanMigrated
import com.commsource.util.common.MMKVMigrateHelper.setMigrateSpList
import com.commsource.util.common.MMKVMigrateHelper.setNoNeedMigrated
import com.commsource.util.common.SPConfig
import com.commsource.util.common.SpTableName
import com.tencent.mmkv.MMKV

class MMKVLaunchTask : AbsLaunchTask() {

    override fun onInitial(app: Application) {
        val temp = System.currentTimeMillis()
        // 默认 /data/user/0/com.commsource.beautyplus/files/mmkv
        val rootDir: String = MMKV.initialize(app, PathUtil.getMMKVDir())
        Log.d("MMKV_Migrate", ">>>MMKV rootDir>>>$rootDir")
        val migrateState = fetchMigratedState()
        Log.d("MMKV_Migrate", ">>>>MMKV Init耗时>>>>" + (System.currentTimeMillis() - temp))
        if (migrateState != MMKVMigrateHelper.MMKVMigrateState.Migrated) {
            // 设置迁移的表
            setMigrateSpList(fetchMigrateList())
            if (migrateState == MMKVMigrateHelper.MMKVMigrateState.NONE) {
                val sp: SharedPreferences = app.getSharedPreferences(SpTableName.SETTING_INFO, Context.MODE_PRIVATE)
                val isNewUser = !sp.contains(ApplicationConfig.IS_NEW_USER)
                Log.d("MMKV_Migrate", ">>>>SP 读取耗时>>>>" + isNewUser + ">>>>>" + (System.currentTimeMillis() - temp))
                if (isNewUser) {
                    setNoNeedMigrated()
                } else {
                    val versionCode = BuildConfig.VERSION_CODE
                    val canMigrate = isCurVersionCanMigrated(versionCode)
                    Log.d("MMKV_Migrate", ">>>>本次是否可以迁移>>>$canMigrate")
                    if (canMigrate) {
                        SPConfig.prepareMigrate()
                    }
                }
            } else {
                val versionCode = BuildConfig.VERSION_CODE
                val canMigrate = isCurVersionCanMigrated(versionCode)
                Log.d("MMKV_Migrate", ">>>>本次是否可以迁移>>>$canMigrate")
                if (canMigrate) {
                    SPConfig.prepareMigrate()
                }
            }
        }
        Log.d("MMKV_Migrate", ">>>>MMKV 完成耗时>>>>" + (System.currentTimeMillis() - temp))
    }
}