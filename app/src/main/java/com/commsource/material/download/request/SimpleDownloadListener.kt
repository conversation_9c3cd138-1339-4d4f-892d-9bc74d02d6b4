package com.commsource.material.download.request

/**
 * @Description : 下载监听
 * <AUTHOR> bear
 * @Date : 2021/12/9
 */
class SimpleDownloadListener : OnDownloadListener {

    var onStart: (() -> Unit)? = null

    var onProgressChange: ((progress: Int) -> Unit)? = null

    var onSuccess: (() -> Unit)? = null

    var onError: ((e: Throwable) -> Unit)? = null

    override fun onStart() {
        onStart?.invoke()
    }

    override fun onProgressChange(progress: Int) {
        onProgressChange?.invoke(progress)
    }

    override fun onError(e: Throwable) {
        onError?.invoke(e)
    }

    override fun onSuccess() {
        onSuccess?.invoke()
    }

}

fun DownloadListener(content: SimpleDownloadListener.() -> Unit): SimpleDownloadListener {
    return SimpleDownloadListener().apply { content() }
}