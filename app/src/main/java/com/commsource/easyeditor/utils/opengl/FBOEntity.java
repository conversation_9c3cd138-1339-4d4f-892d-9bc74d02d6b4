package com.commsource.easyeditor.utils.opengl;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.opengl.GLES20;

/**
 * FBO与绑定纹理的封装类。
 * <p>
 * note:FBOEntity有个问题，FBO属于管理资源，多线程FBO不共享，在风格化固化配方 这里有坑，而且感觉既然不能共享，封装上没必要带上fboId
 * 之后建议剥离
 */
public class FBOEntity {
    public int textureId;
    public int fboId;
    public int width;
    public int height;
    public String key;
    public boolean needTraceRelease;

    public FBOEntity(int textureId, int fboId, int width, int height) {
        this(textureId, fboId, width, height, true);
    }

    public FBOEntity(int textureId, int fboId, int width, int height, String key) {
        this.textureId = textureId;
        this.fboId = fboId;
        this.width = width;
        this.height = height;
        this.key = key;
        this.needTraceRelease = false;
    }

    public FBOEntity(int textureId, int fboId, int width, int height, boolean needTraceRelease) {
        this.textureId = textureId;
        this.fboId = fboId;
        this.width = width;
        this.height = height;
        this.needTraceRelease = needTraceRelease;
    }

    @GlThread
    public void release() {
        if (fboId == 0 && textureId == 0) {
            return;
        }
        // 原因未知，如果不调用BindFrameBuffer，内存也会一直往上涨。
        if (fboId != 0) {
            GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, fboId);
            GLES20.glDeleteFramebuffers(1, new int[]{fboId}, 0);
            fboId = 0;
            GLES20.glDeleteTextures(1, new int[]{textureId}, 0);
            textureId = 0;
            GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0);
        } else {
            GLES20.glDeleteTextures(1, new int[]{textureId}, 0);
            textureId = 0;
        }
    }

    public void releaseFBO() {
        if (fboId != 0) {
            GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, fboId);
            GLES20.glDeleteFramebuffers(1, new int[]{fboId}, 0);
            fboId = 0;
            GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0);
        }
    }

    /**
     * 重新绑定一个Texture。
     */
    @GlThread
    public void rebindBitmap(Bitmap bitmap) {
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, fboId);
        TextureEntity textureEntity = TextureHelper.createTexture(bitmap);
        textureEntity.needTraceRelease = false;
        GLES20.glFramebufferTexture2D(GLES20.GL_FRAMEBUFFER, GLES20.GL_COLOR_ATTACHMENT0, GLES20.GL_TEXTURE_2D,
                textureEntity.textureId, 0);
        if (textureId != 0) {
            GLES20.glDeleteTextures(1, new int[]{textureId}, 0);
        }
        width = textureEntity.width;
        height = textureEntity.height;
        textureId = textureEntity.textureId;
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0);
    }

    /**
     * 重新绑定一个Texture。
     */
    @GlThread
    public void rebindTexture(int textureId, int width, int height) {
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, fboId);
        GLES20.glFramebufferTexture2D(GLES20.GL_FRAMEBUFFER, GLES20.GL_COLOR_ATTACHMENT0, GLES20.GL_TEXTURE_2D,
                textureId, 0);
        if (textureId != 0) {
            GLES20.glDeleteTextures(1, new int[]{textureId}, 0);
        }
        this.width = width;
        this.height = height;
        this.textureId = textureId;
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0);
    }

    public boolean isValid() {
        return textureId != 0 && width > 0 && height > 0;
    }

    /**
     * 之前为了修复多线程渲染FBO 不共享
     *
     * @param image
     */
    public void resetTexture(Bitmap image) {
        release();
        FBOEntity temp = TextureHelper.createFboWithImg(image);
        fboId = temp.fboId;
        needTraceRelease = temp.needTraceRelease;
        this.textureId = temp.textureId;
        this.width = temp.width;
        this.height = temp.height;
    }

    /**
     * 判断宽高是否相等。
     *
     * @param fboEntity
     * @return
     */
    public boolean equalSize(FBOEntity fboEntity) {
        if (fboEntity == null) {
            return false;
        }
        return width == fboEntity.width && height == fboEntity.height;
    }

    public boolean equalSize(int w, int h) {
        return width == w && height == h;
    }

    /**
     * 判断宽高比是否相等。
     *
     * @param fboEntity
     * @return
     */
    public boolean equalSizeAspectRatio(FBOEntity fboEntity) {
        if (fboEntity == null || fboEntity.height == 0 || height == 0) {
            return false;
        }
        return Math.abs(width / (float) height - fboEntity.width / (float) fboEntity.height) < 0.01f;
    }

    // @Override
    // protected void finalize()
    // throws Throwable {
    // super.finalize();
    // if (AppTools.isDebug() && needTraceRelease) {
    // if (textureId != 0 || fboId != 0) {
    // Debug.e(FBOPool.TAG, "FBOEntity没有释放，会导致GL内存泄漏,纹理ID:" + textureId + ",FBOID:" + fboId);
    // }
    // }
    // }

    /**
     * 生成特定尺寸的FBO，如果本身尺寸相同，则返回自身。
     *
     * @param fboEntity
     * @param width
     * @param height
     * @return
     */
    public static FBOEntity tryGenerateTargetSizeFbo(FBOEntity fboEntity, int width, int height) {
        if (fboEntity == null || fboEntity.width != width || fboEntity.height != height) {
            if (fboEntity != null) {
                fboEntity.release();
            }
            fboEntity = TextureHelper.createFBO(width, height);
        }
        return fboEntity;
    }

    @GlThread
    public Bitmap generateBitmap() {
        return TextureHelper.loadBitmapFromFbo(this);
    }

    @GlThread
    public Bitmap generateBitmap(boolean needFixPreMultiple) {
        return TextureHelper.loadBitmapFromFbo(fboId, width, height, needFixPreMultiple);
    }

    @GlThread
    public Bitmap generateBitmap(int maxSize) {
        return TextureHelper.loadBitmapFromFbo(this, maxSize);
    }

    @GlThread
    public void clear() {
        TextureHelper.clear(this);
    }

    @GlThread
    public void fillColor(int color) {
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, this.fboId);
        GLES20.glViewport(0, 0, this.width, this.height);
        GLES20.glClearColor(Color.red(color) / 255f, Color.green(color) / 255f, Color.blue(color) / 255f, 1f);
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0);
        GLES20.glFlush();
        GLES20.glFinish();
    }

    /**
     * 获取size的Key
     *
     * @return
     */
    public String getSizeKey() {
        return width + "*" + height;
    }

}
