package com.commsource.util;

import java.util.HashMap;
import java.util.Map;

import org.json.JSONException;
import org.json.JSONObject;

import com.commsource.aibeauty.AiBeautySpConfig;
import com.commsource.beautyplus.BeautyPlusApplication;
import com.commsource.beautyplus.util.NetUtil;
import com.commsource.config.ApplicationConfig;
import com.commsource.statistics.MTAnalyticsAgent;
import com.commsource.statistics.constant.MTAnalyticsConstant;
import com.commsource.widget.GDPRUtils;
import com.meitu.common.AppContext;
import com.meitu.http.XHttp;
import com.meitu.library.application.BaseApplication;
import com.meitu.library.util.Debug.Debug;
import com.meitu.library.util.device.DeviceUtils;
import com.meitu.library.util.net.NetUtils;
import com.meitu.mtlab.hmacsha.NetTimeIntentService;
import com.meitu.mtlab.hmacsha.TimeUtils;
import com.meitu.mtlab.mtaibeautysdk.config.SignConfig;
import com.meitu.mtlab.mtaibeautysdk.config.SpanConfig;
import com.meitu.mtlab.mtaibeautysdk.config.TraceTagConfig;
import com.meitu.mtlab.mtaibeautysdk.iface.CallBack;
import com.meitu.mtlab.mtaibeautysdk.okhttp.Headers;
import com.meitu.mtlab.mtaibeautysdk.okhttp.HttpClient;
import com.meitu.mtlab.mtaibeautysdk.okhttp.HttpSingle;
import com.meitu.mtlab.mtaibeautysdk.util.CompressImageUtil;
import com.meitu.mtlab.mtaibeautysdk.util.JsonUtil;
import com.meitu.mtlab.mtaibeautysdk.util.SPUtil;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.text.TextUtils;

public class AiCloundUtil {
    private static final String DEBUG_GID = "70";
    private static final String DEBUG_ADDRESS_2 = XHttp.OPEN_API_DEBUG_HOST + "/v2/AIBeauty";
    /**
     * 日本地区
     */
    private static final String JAPAN_GID = "306";
    private static final String DEBUG_JAPAN_GID = "307";
    /**
     * 韩国地区
     */
    private static final String KOREA_GID = "303";
    private static final String DEBUG_KOREA_GID = "304";

    /**
     * 印度，印尼
     */
    private static final String INDIA_AREA_GID = "294";
    private static final String DEBUG_INDIA_AREA_GID = "296";

    /**
     * 新的ai美颜效果
     */
    private static final String AI_TEST_GID = "1965";
    /**
     * 日本，泰国，其他国家
     */
    private static final String ASIA_GID = "293";
    private static final String DEBUG_ASIA_GID = "295";
    private static final String RELEASE_ADDRESS_1 = "https://openflow.pixocial.com/strategy/byQuery";
    private static final String RELEASE_ADDRESS_2 = XHttp.OPEN_API_RELEASE_HOST + "/v2/AIBeauty";
    private static final String AK = "W6BzCxteYJ3x2X7KjM41iA_ZmJr1f4Ci";
    private static final String AS = "kKp0HEQ5qpk6t5V5fF5L1ABSIwZnmcsf";
    private static RequestResult sDebugRequestResult;
    private static TimeLog sTimeLog;
    private static TimeLog requestTimeLog;

    public static void getStrategyInfo(String path, Bitmap bmp, CallBack aiBeautyCallback, int picSource,
                                       int cameraId) {
        if (AppTools.isDebug()) {
            sDebugRequestResult = new RequestResult();
            sDebugRequestResult.oriSize = new Point(bmp.getWidth(), bmp.getHeight());
        }
        sTimeLog = TimeLog.createAndStart();
        requestTimeLog = TimeLog.createAndStart();
        Headers.Builder header = new Headers.Builder();
        String gid = getAiBeautyGid();
        if (sDebugRequestResult != null) {
            sDebugRequestResult.gid = gid;
        }
        header.add("group_id", gid);
        header.add("api_key", AK);
        HttpClient client = new HttpClient.Builder().url(RELEASE_ADDRESS_1).headers(header.build()).build();
        client.newCallGetSplicing(new SpanConfig.Builder().setParentName("开始美颜")
                .setChildName("获取策略")
                .setForeverTrace(true)
                .setTraceFailInfo(true)
                .setHeaderTrace(false)
                .setFinishChild(true)
                .setParentTag(
                        new TraceTagConfig.Builder().add("gid", "111").add("uid", "222").add("picSource", "3").build())
                .startTrace(), new CallBack() {
            @Override
            public void onFailure(int code, String msg) {
                getAiBeautyUrl(path, bmp, aiBeautyCallback, picSource, cameraId);
            }

            @Override
            public void onResponse(String json) {
                // 解析保存json
                JsonUtil.strategyJson(json);
                if (sDebugRequestResult != null) {
                    sDebugRequestResult.compressInfoTime = sTimeLog.update();
                }
                getAiBeautyUrl(path, bmp, aiBeautyCallback, picSource, cameraId);
            }
        });


    }

    private static String getOpenApiMtLabUrl() {
        return ApplicationConfig.isTestMaterialEnvironment() ? DEBUG_ADDRESS_2 : RELEASE_ADDRESS_2;
    }

    /**
     * @param path
     * @param bmp
     * @param aiBeautyCallback
     * @param picSource
     * @param cameraId         0代表人像美颜，1代表前置摄像头，2带表后置摄像头
     */
    private static void getAiBeautyUrl(String path, Bitmap bmp, CallBack aiBeautyCallback, int picSource, int cameraId) {
        String sign = new SignConfig.Builder().setGid(getAiBeautyGid())
                .setApiKey(AK)
                .setApiSecret(AS)
                .setCurrentTime(TimeUtils.getTime())
                .signGid();
        Headers.Builder header = new Headers.Builder();
        header.add("Gid", getAiBeautyGid());
        header.add("phone_gid", getGid());
        header.add("phone_uid", getUserId());
        header.add("Authorization", sign);
        header.add("AuthorizationType", "1");
        header.add("X-App-Id", MtOkHttpRequestUtil.BEAUTYPLUS_SOFT_ID);
        SpanConfig spanConfig = new SpanConfig.Builder().setChildName("压缩图片").startTrace();
        String[] base64Array = CompressImageUtil.compress(NetUtil.isWIFI(BeautyPlusApplication.getApplication()),
                SPUtil.get(SPUtil.RULE, 0), bmp, null, false); // ⚠️注意这里不要用PNG. 会让上传失败率大大提高。
        spanConfig.finishChild();
        if (sDebugRequestResult != null) {
            sDebugRequestResult.compressTime = sTimeLog.update();
        }
        JSONObject object = new JSONObject();
        try {
            String value = "";
            if (cameraId == 1) {
                value = "front camera";
            } else if (cameraId == 2) {
                value = "back camera";
            }
            object.put("lensModel", value);
            object.put("model", DeviceUtils.getDeviceMode());
            object.put("network", getNetworkType());
            String hwGid = ApplicationConfig.getOverseaGID();
            if (!TextUtils.isEmpty(hwGid)) {
                object.put("hwgid", hwGid);
            }
            String firebaseId = ApplicationConfig.getFireBaseId(AppContext.getContext());
            if (!TextUtils.isEmpty(firebaseId)) {
                object.put("firebaseid", firebaseId);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String exifJson = JsonUtil.getExifJson(path, 0, getGid(), getUserId(), picSource, object);
        Debug.v("AiBeauty", "Ai美颜上传Exif信息：" + exifJson);
        String json = JsonUtil.assemblePostParams(exifJson, null, null, true, base64Array[0]);
        String url = getOpenApiMtLabUrl();
        Debug.d("AiBeauty", "Ai美颜url : " + url);
        HttpClient client = new HttpClient.Builder().json(json).url(url).headers(header.build()).build();

        client.newCallPost(
                new SpanConfig.Builder().setChildName("变美").setTraceFailInfo(true).setFinishTrace(true).startTrace(),
                new CallBack() {
                    @Override
                    public void onFailure(int code, String msg) {
                        logEventOnTimeConsumed(false);
                        if (aiBeautyCallback != null) {
                            aiBeautyCallback.onFailure(code, msg);
                        }

                    }

                    @Override
                    public void onResponse(String json) {
                        logEventOnTimeConsumed(true);
                        if (sDebugRequestResult != null) {
                            sDebugRequestResult.aiBeautyTime = sTimeLog.update();
                        }
                        if (aiBeautyCallback != null) {
                            aiBeautyCallback.onResponse(json);
                        }

                    }
                });
    }

    public static RequestResult getsDebugRequestResult() {
        return sDebugRequestResult;
    }

    private static String getGid() {
        return ApplicationConfig.getOverseaGID() == null ? "" : ApplicationConfig.getOverseaGID();
    }

    private static String getUserId() {
        return "";
    }

    private static int getNetworkType() {
        String networkType = NetUtils.getNetWorkType(BaseApplication.getApplication());
        int type = 4;
        if (!TextUtils.isEmpty(networkType)) {
            if ("2G".equalsIgnoreCase(networkType)) {
                type = 3;
            } else if ("3G".equalsIgnoreCase(networkType)) {
                type = 2;
            } else if ("4G".equalsIgnoreCase(networkType)) {
                type = 1;
            } else if ("WIFI".equalsIgnoreCase(networkType)) {
                type = 0;
            }
        }
        return type;
    }

    public static void cancelRequest() {
        HttpSingle.getInstance().cancel();
    }

    private static void logEventOnTimeConsumed(boolean isSuccess) {
        String value = isSuccess ? "是" : "否";
        Map<String, String> map = new HashMap<>(4);
        map.put("请求时长", String.valueOf(requestTimeLog.get()));
        map.put("是否成功", value);

        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.AIBEAUTY_REQUEST_TIME, map);
    }

    private static String getAiDefaultGid() {
        Context context = BeautyPlusApplication.getApplication();
        boolean isDebug = ApplicationConfig.isTestMaterialEnvironment();
        if (BPLocationUtils.isJapan(context)) {
            return isDebug ? DEBUG_JAPAN_GID : JAPAN_GID;
        } else if (BPLocationUtils.isKorea(context)) {
            return isDebug ? DEBUG_KOREA_GID : KOREA_GID;
        } else if (BPLocationUtils.isIndia(context) || BPLocationUtils.isIndonesia(context)) {
            return isDebug ? DEBUG_INDIA_AREA_GID : INDIA_AREA_GID;
        } else {
            return isDebug ? DEBUG_ASIA_GID : ASIA_GID;
        }
    }

    private static String getAiBeautyGid() {
        // 欧盟地区直接使用默认的GID
        if (GDPRUtils.isEuroArea(AppContext.context)) {
            boolean isDebug = ApplicationConfig.isTestMaterialEnvironment();
            return isDebug ? DEBUG_ASIA_GID : ASIA_GID;
        } else {
            return AiBeautySpConfig.getStringWithDefault(AiBeautySpConfig.KEY_AI_BEAUTY_GID, getAiDefaultGid());
        }
    }

    public static class RequestResult {
        public Point oriSize = new Point();
        public Point compressSize = new Point();
        public long compressInfoTime;
        public long compressTime;
        public long aiBeautyTime;
        public long downloadAiBeautyTime;
        public long afterAiBeautyTime;
        public String gid;
    }

}
