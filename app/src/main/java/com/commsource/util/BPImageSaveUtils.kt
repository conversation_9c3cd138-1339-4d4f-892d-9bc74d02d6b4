package com.commsource.util

import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import com.commsource.beautyplus.util.PathUtil
import com.commsource.camera.fastcapture.SelfiePhotoData
import com.commsource.config.ApplicationConfig
import com.commsource.puzzle.patchedworld.imageware.utils.MediaScanUtil
import com.commsource.statistics.DataStudioAgent
import com.commsource.statistics.DataStudioEvent
import com.commsource.statistics.PixPerformanceCode
import com.commsource.util.common.BitmapUtil
import com.meitu.common.AppContext
import com.meitu.library.util.io.FileUtils
import java.io.File
import kotlin.math.abs

/**
 * @Desc : 保存图片工具类
 * <AUTHOR> Bear - 2021/1/11
 */
object BPImageSaveUtils {

    /**
     * 保存bitmap
     *
     * @param bitmap
     * @param orientation
     * @param path
     * @return
     */
    @JvmStatic
    @JvmOverloads
    fun saveImageWithExif(
        bitmap: Bitmap?,
        orientation: Int,
        path: String?,
        source: String = "default",
        format: CompressFormat? = CompressFormat.JPEG,
        shouldFlush: Boolean = true
    ): Boolean {
        if (bitmap == null || bitmap.isRecycled) {
            return false
        }
        val height = bitmap.height
        val width = bitmap.width
        val preSaveTime = System.currentTimeMillis()
        var result = false
        var hasDir = true
        val saveDir = PathUtil.CAMERA_DIR

        // 创建存储目录。
        val saveDirFile = File(saveDir)
        if (!saveDirFile.exists()) {
            hasDir = saveDirFile.mkdirs()
        }
        if (hasDir) {
            result = if (PathUtil.isScopedStorage()) {
                val imageUri = MediaScanUtil.insertMedia(path, AppContext.context)
                BitmapUtil.saveImageToDisk(bitmap, imageUri, 100, format)
            } else {
                BitmapUtil.saveImageToDisk(bitmap, path, 100, format)
            }
        }
        if (result) {
            // 添加exif信息
            ExifManager.setExifOrientation(path, orientation)
            if (!FileUtils.isFileExist(path)) {
                // BugFix: 红米3中添加exif信息会导致图片被删除
                result = if (PathUtil.isScopedStorage()) {
                    val imageUri = MediaScanUtil.insertMedia(path, AppContext.context)
                    BitmapUtil.saveImageToDisk(bitmap, imageUri, 100, format)
                } else {
                    BitmapUtil.saveImageToDisk(bitmap, path, 100, format)
                }
            }
            if (!PathUtil.isScopedStorage() && shouldFlush) {
                MyPro.flushPhoto(path)
            }
        }
        val duration = System.currentTimeMillis() - preSaveTime
        DataStudioAgent.logEvent(DataStudioEvent.performance_save_photo) {
            this["pixelSize"] = "${abs(width)}x${abs(height)}"
            this["size"] = "${FileUtils.fileSize(path)}"
            this["format"] = when (format) {
                CompressFormat.JPEG -> "jpg"
                else -> "png"
            }
            this["saveDuration"] = duration.toString()
            this["source"] = source
            this["code"] = if (result) {
                PixPerformanceCode.success
            } else {
                PixPerformanceCode.unknown
            }.toString()
            this["remainingDiskSpace"] = "${SDCardUtil.readSDCard()}"
        }
        return result
    }

    /**
     * 保存图片
     *
     * @param selfiePhotoData
     * @param arBitmap
     * @return
     */
    @JvmStatic
    fun save(selfiePhotoData: SelfiePhotoData, arBitmap: Bitmap): Boolean {
        selfiePhotoData.savePath = PathUtil.getSavePicPath()
        if (!BitmapUtils.isAvailableBitmap(selfiePhotoData.screenShotBitmap)) {
            return false
        }

        var saveResult = if (ApplicationConfig.getSaveOptimizeSwitch()) {
            MediaSaver.saveImageToSDCard(
                bitmap = arBitmap,
                savePath = selfiePhotoData.savePath,
                source = "自拍",
                orientation = 0,
                format = CompressFormat.JPEG,
                quality = 100
            )
        } else {
            saveImageWithExif(
                arBitmap,
                0,
                selfiePhotoData.savePath,
                "自拍",
                CompressFormat.JPEG,
                true
            )
        }

        if (saveResult) {
            // 保存后去判断是否该路径下文件存在确保真的有保存成功
            saveResult = FileUtils.isFileExist(selfiePhotoData.savePath)
            selfiePhotoData.savePath = selfiePhotoData.savePath
        }
        return saveResult
    }

    @JvmStatic
    fun saveCaptureToCache(selfiePhotoData: SelfiePhotoData): Boolean {
        // 单次拍摄 burstIndex 默认为 0， 连拍时，burstIndex为拍摄的第几张
        selfiePhotoData.originCachePath = PathUtil.getCaptureCachePath(selfiePhotoData.burstIndex)
        var isSuccess = false
        // 保存原图
        selfiePhotoData.glOriBitmap?.let {
            isSuccess = BitmapUtil.saveImageToDisk(
                it,
                selfiePhotoData.originCachePath,
                100,
                CompressFormat.JPEG
            )
        }
        // 这里也一并保存效果图，便于用户在第一张拍完之后结束连拍 为单次拍后
        return isSuccess && saveEffectToCache(selfiePhotoData)
    }

    @JvmStatic
    fun saveEffectToCache(selfiePhotoData: SelfiePhotoData): Boolean {
        // 单次拍摄 burstIndex 默认为 0， 连拍时，burstIndex为拍摄的第几张
        selfiePhotoData.effectCachePath = PathUtil.getCaptureCachePath(selfiePhotoData.burstIndex)
        // 区分ar 和 非 ar
        if (selfiePhotoData.isAr || selfiePhotoData.isArGiphy) {
            //处理Ar的图片拍照
            selfiePhotoData.screenShotBitmap?.let {
                val saveResult = BitmapUtil.saveImageToDisk(
                    it,
                    selfiePhotoData.effectCachePath,
                    100,
                    CompressFormat.JPEG
                )
                return saveResult
            }
        } else {
            //处理非Ar的图片拍照
            selfiePhotoData.glEffectBitmap?.let {
                val saveResult = BitmapUtil.saveImageToDisk(
                    it,
                    selfiePhotoData.effectCachePath,
                    100,
                    CompressFormat.JPEG
                )
                return saveResult
            }
        }
        return false
    }

    @JvmStatic
    fun saveEffectMarkToCache(selfiePhotoData: SelfiePhotoData, bitmap: Bitmap): Boolean {
        selfiePhotoData.effectMarkCachePath = PathUtil.getCaptureCachePath(selfiePhotoData.burstIndex)
        // 区分ar 和 非 ar
        val saveResult = BitmapUtil.saveImageToDisk(
            bitmap,
            selfiePhotoData.effectMarkCachePath,
            100,
            CompressFormat.JPEG
        )
        return saveResult
    }
}