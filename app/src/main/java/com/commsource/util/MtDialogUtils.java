package com.commsource.util;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.commsource.beautyplus.R;
import com.commsource.beautyplus.home.OnAdFeedbackDialogItemClickListener;
import com.commsource.billing.SubSource;
import com.commsource.config.SubscribeConfig;
import com.commsource.statistics.SpmParamConstant;
import com.meitu.library.hwanalytics.spm.SPMShare;

/**
 * <AUTHOR>
 * Created by zpb on 2017/11/22.
 */

public class MtDialogUtils {

    public static boolean isActivityFinishing(Context context) {
        if (context == null) {
            return true;
        }
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            return activity.isFinishing() || activity.isDestroyed();
        }
        return false;
    }


    /**
     * 反馈弹窗
     *
     * @param activity
     * @param onAdFeedbackDialogItemClickListener
     * @return
     */
    public static Dialog getFeedBackDialog(Activity activity,
                                           OnAdFeedbackDialogItemClickListener onAdFeedbackDialogItemClickListener) {
        if (isActivityFinishing(activity)) {
            return null;
        }
        Dialog feedbackDialog = new Dialog(activity, R.style.homeBannerAdFeedbackDialog);
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_home_banner_ad_feedback, null, false);
        if (SubscribeConfig.isSubSwitchOn(activity)) {
            view.findViewById(R.id.ll_subscribe).setVisibility(View.VISIBLE);
//            SubsUtil.displaySubsIcon(AppContext.getContext(), view.findViewById(R.id.iv_subscribe),
//                SubscribeConfig.getSubCornerMarkerCircleIcUrl(), R.drawable.ic_sub_mark);
        }
        RelativeLayout.LayoutParams layoutParams =
                new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        feedbackDialog.setContentView(view, layoutParams);
        feedbackDialog.setCancelable(true);
        feedbackDialog.setCanceledOnTouchOutside(true);
        if (onAdFeedbackDialogItemClickListener != null) {
            view.findViewById(R.id.tv_report).setOnClickListener((v) -> {
                feedbackDialog.dismiss();
                onAdFeedbackDialogItemClickListener.onReportClick();
            });
            view.findViewById(R.id.ll_subscribe).setOnClickListener(v -> {
                feedbackDialog.dismiss();
                SPMShare.INSTANCE.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "广告反馈");
                onAdFeedbackDialogItemClickListener.onSubClick(SubSource.FROM_AD_FEEDACK);
            });
        }
        return feedbackDialog;

    }

}
