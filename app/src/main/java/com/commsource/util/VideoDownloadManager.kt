package com.commsource.util

import android.annotation.SuppressLint
import com.commsource.beautyplus.util.PathUtil
import com.meitu.common.AppContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.cancellation.CancellationException

object VideoDownloadManager {

    const val TAG = "VideoDownloadManager"

    // 视频下载任务列表
    private val videoTasks = ConcurrentHashMap<String, Job>()

    // 清理配置参数
    private const val MAX_CACHE_SIZE_BYTES = 1024L * 1024L * 512L // 512MB
    private const val CLEANUP_PERCENTAGE = 0.2f // 每次清理20%的文件

    private const val VIDEO_CACHE_DIR = "videoCache"

    fun download(
        scope: CoroutineScope,
        url: String,
        moduleName: String,
        md5: String?,
        onSuccess: (String) -> Unit,
        onError: (Exception) -> Unit
    ) {
        // 首先检查本地是否已有缓存
        val modulePath = "$VIDEO_CACHE_DIR/$moduleName"
        val localVideoPath = FileDownloader.getLocalVideoPathOrNull(url, modulePath, md5)
        if (!localVideoPath.isNullOrEmpty()) {
            "Using cached video: $localVideoPath".logV(TAG)
            // 更新文件访问时间
            updateFileAccessTime(localVideoPath)
            onSuccess(localVideoPath)
            return
        }

        // 如果已有相同URL的任务在进行，直接返回
        if (videoTasks.containsKey(url)) {
            "Download task for $url already exists".logV(TAG)
            return
        }

        // 在开始下载前执行清理检查
        scope.launch(Dispatchers.IO) {
            cleanupOldVideos()
        }
        // 如果没有缓存，则下载
        val job = scope.launch(Dispatchers.IO) {
            try {
                val videoPath = FileDownloader.downloadVideoFile(url, modulePath)
                ensureActive()
                withContext(Dispatchers.Main) {
                    videoTasks.remove(url)
                    if (isActive) {
                        if (videoPath.isNullOrEmpty()) {
                            onError(Exception("Failed to download video"))
                        } else {
                            "Video downloaded successfully: $videoPath".logV(TAG)
                            onSuccess(videoPath)
                        }
                    }
                }
            } catch (e: CancellationException) {
                // 协程被取消，清理资源但不回调错误
                "Video download cancelled for $url".logV(TAG)
                videoTasks.remove(url)
                throw e // 重新抛出 CancellationException
            } catch (e: Exception) {
                "Video download failed $e".logV(TAG)
                withContext(Dispatchers.Main) {
                    videoTasks.remove(url)
                    if (isActive) {
                        onError(e)
                    }
                }
            }
        }
        videoTasks[url] = job
    }

    fun cancel(url: String) {
        videoTasks[url]?.cancel()
        videoTasks.remove(url)
    }

    fun isDownloading(url: String): Boolean {
        return videoTasks[url]?.isActive == true
    }

    /**
     * 更新文件访问时间
     */
    private fun updateFileAccessTime(filePath: String) {
        try {
            val file = File(filePath)
            if (file.exists()) {
                file.setLastModified(System.currentTimeMillis())
            }
        } catch (e: Exception) {
            "Failed to update file access time: $filePath, error: $e".logV(TAG)
        }
    }

    /**
     * 清理旧的视频文件
     */
    private fun cleanupOldVideos() {
        try {
            "Starting video cache cleanup".logV(TAG)

            val allVideoFiles = mutableListOf<VideoFileInfo>()

            // 收集所有视频缓存目录中的文件
            val videoCacheDir = PathUtil.getExternalFileDir(AppContext.context, VIDEO_CACHE_DIR)
            val videoCacheDirFile = File(videoCacheDir)
            if (videoCacheDirFile.exists() && videoCacheDirFile.isDirectory) {
                videoCacheDirFile.listFiles()?.forEach {
                    if (it.isDirectory) {
                        collectVideoFiles(it, allVideoFiles)
                    }
                }
            }

            if (allVideoFiles.isEmpty()) {
                "No video files found for cleanup".logV(TAG)
                return
            }

            // 计算总大小和文件数量
            val totalSize = allVideoFiles.sumOf { it.size }
            val totalCount = allVideoFiles.size

            "Found $totalCount video files, total size: ${formatFileSize(totalSize)}".logV(TAG)

            // 检查是否需要清理
            val needCleanupBySize = totalSize > MAX_CACHE_SIZE_BYTES

            if (!needCleanupBySize) {
                "No cleanup needed. Size: ${formatFileSize(totalSize)}/${formatFileSize(MAX_CACHE_SIZE_BYTES)}".logV(TAG)
                return
            }

            // 执行清理
            performCleanup(allVideoFiles)

        } catch (e: Exception) {
            "Video cleanup failed: $e".logV(TAG)
        }
    }

    /**
     * 收集视频文件信息
     */
    private fun collectVideoFiles(dir: File, fileList: MutableList<VideoFileInfo>) {
        try {
            dir.listFiles()?.forEach { file ->
                if (file.isFile && isVideoFile(file)) {
                    fileList.add(
                        VideoFileInfo(
                            file = file,
                            size = file.length(),
                            lastModified = file.lastModified()
                        )
                    )
                }
            }
        } catch (e: Exception) {
            "Failed to collect files from ${dir.absolutePath}: $e".logV(TAG)
        }
    }

    /**
     * 执行清理操作
     */
    private fun performCleanup(allVideoFiles: List<VideoFileInfo>) {
        // 按最后修改时间排序，最旧的文件在前面
        val sortedFiles = allVideoFiles.sortedBy { it.lastModified }

        // 计算需要删除的文件数量
        val filesToDelete = (sortedFiles.size * CLEANUP_PERCENTAGE).toInt().coerceAtLeast(1)
        val filesToDeleteList = sortedFiles.take(filesToDelete)

        var deletedCount = 0
        var deletedSize = 0L

        filesToDeleteList.forEach { fileInfo ->
            try {
                if (fileInfo.file.delete()) {
                    deletedCount++
                    deletedSize += fileInfo.size
                    "Deleted video file: ${fileInfo.file.absolutePath}".logV(TAG)
                } else {
                    "Failed to delete video file: ${fileInfo.file.absolutePath}".logV(TAG)
                }
            } catch (e: Exception) {
                "Error deleting video file: ${fileInfo.file.absolutePath}, error: $e".logV(TAG)
            }
        }

        "Video cleanup completed. Deleted $deletedCount files, freed ${formatFileSize(deletedSize)}".logV(TAG)
    }

    /**
     * 判断是否为视频文件
     */
    private fun isVideoFile(file: File): Boolean {
        val name = file.name.lowercase()
        return name.endsWith(".mp4") || name.endsWith(".mov") || name.endsWith(".avi") ||
               name.endsWith(".mkv") || name.endsWith(".3gp") || name.endsWith(".webm")
    }

    /**
     * 格式化文件大小
     */
    @SuppressLint("DefaultLocale")
    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0))
            bytes >= 1024 * 1024 -> String.format("%.2f MB", bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> String.format("%.2f KB", bytes / 1024.0)
            else -> "$bytes B"
        }
    }

    /**
     * 视频文件信息数据类
     */
    private data class VideoFileInfo(
        val file: File,
        val size: Long,
        val lastModified: Long
    )
}