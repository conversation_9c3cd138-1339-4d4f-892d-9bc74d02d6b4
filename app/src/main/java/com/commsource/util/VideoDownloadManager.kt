package com.commsource.util

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.cancellation.CancellationException

object VideoDownloadManager {

    const val TAG = "VideoDownloadManager"

    // 视频下载任务列表
    private val videoTasks = ConcurrentHashMap<String, Job>()

    fun download(
        scope: CoroutineScope,
        url: String,
        moduleName: String,
        md5: String?,
        onSuccess: (String) -> Unit,
        onError: (Exception) -> Unit
    ) {
        // 首先检查本地是否已有缓存
        val localVideoPath = FileDownloader.getLocalVideoPathOrNull(url, moduleName, md5)
        if (!localVideoPath.isNullOrEmpty()) {
            "Using cached video: $localVideoPath".logV(TAG)
            onSuccess(localVideoPath)
            return
        }

        // 如果已有相同URL的任务在进行，直接返回
        if (videoTasks.containsKey(url)) {
            "Download task for $url already exists".logV(TAG)
            return
        }

        // 如果没有缓存，则下载
        val job = scope.launch(Dispatchers.IO) {
            try {
                val videoPath = FileDownloader.downloadVideoFile(url, moduleName)
                ensureActive()
                withContext(Dispatchers.Main) {
                    videoTasks.remove(url)
                    if (isActive) {
                        if (videoPath.isNullOrEmpty()) {
                            onError(Exception("Failed to download video"))
                        } else {
                            "Video downloaded successfully: $videoPath".logV(TAG)
                            onSuccess(videoPath)
                        }
                    }
                }
            } catch (e: CancellationException) {
                // 协程被取消，清理资源但不回调错误
                "Video download cancelled for $url".logV(TAG)
                videoTasks.remove(url)
                throw e // 重新抛出 CancellationException
            } catch (e: Exception) {
                "Video download failed $e".logV(TAG)
                withContext(Dispatchers.Main) {
                    videoTasks.remove(url)
                    if (isActive) {
                        onError(e)
                    }
                }
            }
        }
        videoTasks[url] = job
    }

    fun cancel(url: String) {
        videoTasks[url]?.cancel()
        videoTasks.remove(url)
    }

    fun cancelAll() {
        videoTasks.values.forEach { it.cancel() }
        videoTasks.clear()
        "Cancelled all download tasks".logV(TAG)
    }

    fun isDownloading(url: String): Boolean {
        return videoTasks[url]?.isActive == true
    }

}