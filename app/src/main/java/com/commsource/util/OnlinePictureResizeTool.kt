package com.commsource.util

import android.net.Uri
import android.text.TextUtils
import androidx.annotation.Keep
import com.commsource.util.common.SPConfig
import com.commsource.util.common.SpTableName
import com.meitu.common.AppContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 在线图片Resize工具
 */
object OnlinePictureResizeTool {

    /**
     * sp存储字段key
     */
    const val KEY_RESIZE_RULE = "KeyResizeRule"
    val spConfig by lazy {
        SPConfig(
            AppContext.context, SpTableName.onlinePictureResize
        )
    }

    /**
     * 在线高级开关的尺寸定义配置
     */
    @Keep
    data class ResizeRule(val urls: HashMap<String, String>, val rules: HashMap<String, String>)

    /**
     * 替换字段规则
     */
    private const val REPLACE_WIDTH = "{width}"
    private const val REPLACE_HEIGHT = "{height}"
    private const val REPLACE_SCHEME = "{image_scheme}"
    private const val REPLACE_HOST = "{image_host}"
    private const val REPLACE_PATH = "/{image_path}"
    private const val REPLACE_QUERY_STRING = "{image_query_string}"

    /**
     * resize配置
     * 可能含有多个配置 针对不同云存储的图片地址，包装不同输出Url
     */
    private var resizeRule: ResizeRule? = null

    /**
     * 格式白名单
     */
    private val formatWhitelist = arrayListOf(".jpg", ".jpeg", ".png", ".gif")

    /**
     * 初始化
     */
    fun init() {
        AppContext.scope.launch(Dispatchers.IO) {
            try {
                val urlJson = spConfig.getString(KEY_RESIZE_RULE, "")
                resizeRule = GsonUtils.getInstance().fromJson(urlJson, ResizeRule::class.java)
            } catch (e: Exception) {

            }
        }
    }

    /**
     * 获取尺寸优化的图片地址
     * @param originUrl 原图地址
     * @param compatSize 期望的尺寸
     */
    private fun _getUrl(
        originUrl: String?,
        widthSize: Int?,
        heightSize: Int?,
        widthHeightRatio: Float
    ): String? {
        val start = System.currentTimeMillis()
        val resizeRule = this.resizeRule ?: return originUrl
        originUrl ?: return null
        var validFormat = false
        formatWhitelist.forEach {
            if (originUrl.endsWith(it, true)) {
                validFormat = true
            }
        }
        if (!validFormat) {
            return originUrl
        }
        val uri = Uri.parse(originUrl)
        val host = uri.host
        val rule = resizeRule.urls[host] ?: return originUrl
        val baseUrl = resizeRule.rules[rule] ?: return originUrl
        var width = 0
        var height = 0
        if (widthSize != null) {
            width = widthSize
            height = (widthSize / widthHeightRatio).toInt()
        } else {
            height = heightSize!!
            width = (height * widthHeightRatio).toInt()
        }
        val sb = StringBuilder(baseUrl)
        sb.indexOf(REPLACE_WIDTH).takeIf { it >= 0 }?.let { index ->
            sb.replace(index, index + REPLACE_WIDTH.length, width.toString())
        }

        sb.indexOf(REPLACE_HEIGHT).takeIf { it >= 0 }?.let { index ->
            sb.replace(index, index + REPLACE_HEIGHT.length, height.toString())
        }

        sb.indexOf(REPLACE_SCHEME).takeIf { it >= 0 }?.let { index ->
            uri?.scheme?.let { replace ->
                sb.replace(index, index + REPLACE_SCHEME.length, replace)
            }
        }

        sb.indexOf(REPLACE_HOST).takeIf { it >= 0 }?.let { index ->
            uri.host?.let { replace ->
                sb.replace(index, index + REPLACE_HOST.length, replace)
            }
        }

        sb.indexOf(REPLACE_PATH).takeIf { it >= 0 }?.let { index ->
            uri.path?.let { replace ->
                sb.replace(index, index + REPLACE_PATH.length, replace)
            }
        }

        sb.indexOf(REPLACE_QUERY_STRING).takeIf { it >= 0 }?.let { index ->
            if (!TextUtils.isEmpty(uri.query)) {
                "?${uri.query}".let { replace ->
                    sb.replace(index, index + REPLACE_QUERY_STRING.length, replace)
                }
            } else {
                "".let { replace ->
                    sb.replace(index, index + REPLACE_QUERY_STRING.length, replace)
                }
            }
        }
        val url = sb.toString()
//        "计算尺寸(w:$width-h:$height):原图地址:${originUrl},耗时:${System.currentTimeMillis() - start},结果地址:${url}".print(
//            "csx"
//        )
        return url
    }

    /**
     * 获取resizeUrl
     */
    fun getUrl(
        originUrl: String?,
        widthSize: Int?,
        heightSize: Int?,
        widthHeightRatio: Float
    ): String? {
        require(widthSize != 0 || heightSize != 0) { "至少传入Width、Height一侧适配" }
        return _getUrl(originUrl, widthSize, heightSize, widthHeightRatio)
    }

}

/**
 * 在线图片Url调整尺寸
 *
 * @param widthSize 宽度适配
 * @param heightSize 高度适配
 * @param widthHeightRatio 精准计算
 */
fun String?.resize(
    widthSize: Int? = null,
    heightSize: Int? = null,
    widthHeightRatio: Float = 2 / 3f,
): String? {
    //转换长宽比（无论长图 or 宽图）
    require(widthHeightRatio > 0f) { "图片比例传入为ratio <= 0f!!" }
    return OnlinePictureResizeTool.getUrl(
        this,
        widthSize,
        heightSize,
        widthHeightRatio
    )
}