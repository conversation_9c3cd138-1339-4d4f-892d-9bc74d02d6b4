package com.commsource.util

import android.app.Activity
import android.content.Intent
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.os.Parcel
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.Keep
import androidx.core.graphics.toRectF
import androidx.core.view.doOnPreDraw
import com.commsource.beautyplus.AndroidBugFixHelper
import com.commsource.beautyplus.databinding.LayoutImageTransitionBinding
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.camera.util.animationTransition
import com.commsource.camera.util.clearTransition
import com.commsource.util.ImageTransition.ImageCapture
import com.commsource.widget.image.ImageLoader
import com.commsource.widget.image.ImageTarget
import com.meitu.common.utils.GradientDrawableFactory
import com.meitu.library.util.Debug.Debug
import com.meitu.library.util.device.DeviceUtils

/**
 * 图片过渡 支持Activity间单图过渡动画
 * 页面内单图过渡，本身Android共享元素可以支持，但是随着更深的支持，共享元素像是一个半成品，无法控制很多参数，使用鸡肋，光一个图片过渡圆角都无法实现
 *
 * 这个图片过渡为了实现目前项目多数场景，简易封装的一个结构。支持单一场景。提供AA动画过渡代码示例
 * 实现步骤:
 * 页面A - 页面B （假设都是Activity）
 * 1.页面B 替换theme 支持透明背景，且常规A转场过渡去掉
 * 2.页面A [Intent] [View]目标图片View 装饰调用[ImageTransition.Companion.capture]会采集目标控件位置、圆角、placeHolder颜色,用key[ImageTransition.Companion.KEY_transition_image]装饰[Intent]
 * 3.页面B 构建创建对象[ImageTransition]
 * 4.页面B [getContentView]包裹页面B原本目标主体页面内容[Activity.setContentView]
 * 5.页面B 调用[prepareTransition] 构建转场动画、并发起动画
 * 7.页面B 调用[onFirstFrameLoadedSuccess] 完成转场动画
 * 8.页面B 复写[Activity.finish] 针对退出动效的处理调用[finish]即可
 *
 * [ImageCapture.equalTag] 用于判断是否是转场目标主体的判断、一般通过id、path、或者session等标记目标
 */
class ImageTransition @JvmOverloads constructor(
    val activity: Activity,
    val onCalculateDisplayRectFEvent: (isEnter: Boolean, whRatio: Float) -> RectF,
    val onCalculateDisplayRadiusEvent: (isEnter: Boolean) -> Float,
    val onTransitionEnd: (() -> Unit)? = null
) {

    /**
     * 图片转场动效binding包裹
     */
    val binding by lazy {
        LayoutImageTransitionBinding.inflate(
            LayoutInflater.from(activity),
            null,
            false
        )
    }

    /**
     * 获取ContentView
     */
    fun getContentView(contentView: View): View {
        binding.executePendingBindings()
        binding.flActivityContent.addView(contentView, -1, -1)
        return binding.root
    }

    /**
     * 准备进入过渡动画
     */
    private var imageCapture: ImageCapture? = null
    private var cornerRadiusValuer = XAnimatorCalculateValuer(0f)
    fun prepareTransition() {
        if (!AndroidBugFixHelper.isSupportTranslucentActivity()) {
            onTransitionEnd(_isFirstFrameLoaded = true, _isTransitionEnd = true)
            return
        }
        //解析配方动画
        val capture = activity.intent?.getParcelableExtra(KEY_transition_image) as? ImageCapture
        this.imageCapture = capture
        capture?.let { capture ->
            binding.flActivityContent.alpha = 0f
            binding.vPlaceholder.setBackgroundDrawable(
                GradientDrawableFactory.createDrawable(
                    capture.placeHolderColor,
                    capture.radius
                )
            )
            "动画元素:${capture.imagePath}".print("csx")
            ImageLoader.with(activity)
                .load(capture.imagePath)
                .error(ImageLoader.with(activity).load(capture.originPath))
                .into(object : ImageTarget {

                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        onTransitionEnd(_isFirstFrameLoaded = true, _isTransitionEnd = true)
                    }

                    override fun onResourceReady(resource: Drawable) {
                        binding.ivTransition.setImageDrawable(resource)
                        binding.ccTransition.alpha = 0f
                        binding.ccTransition.corner(capture.radius)
                        binding.ccTransition.visible()
                        binding.ivTransition.setSize(
                            width = capture.fullRectf.width().toInt(),
                            height = capture.fullRectf.height()
                                .toInt()
                        )
                        binding.ccTransition.translationX =
                            (if (RTLTool.isLayoutRtl()) DeviceUtils.getScreenWidth() - capture.fullRectf.right else capture.fullRectf.left).toRtl
                        binding.ccTransition.translationY = capture.fullRectf.top
                        if (activity.isFinishing) {
                            return
                        }
                        binding.ccTransition.animationTransition(duration = 150) {
                            binding.ccTransition.alpha = 1f
                            onTransitionEnd = {
                                binding.vBackground.visible()
                                binding.vBackground.alpha = 0f

                                binding.vPlaceholder.visible()
                                // todo renyong.huang
                                binding.vPlaceholder.translationX =
                                    (if (RTLTool.isLayoutRtl()) DeviceUtils.getScreenWidth() - capture.fullRectf.right else capture.fullRectf.left).toRtl
                                binding.vPlaceholder.translationY = capture.fullRectf.top
                                binding.vPlaceholder.setSize(
                                    width = capture.fullRectf.width().toInt(),
                                    height = capture.fullRectf.height().toInt()
                                )
                                cornerRadiusValuer.mark(
                                    capture.radius,
                                    onCalculateDisplayRadiusEvent.invoke(true)
                                )
                                if (!activity.isFinishing) {
                                    binding.root.doOnPreDraw {
                                        val endRectF =
                                            onCalculateDisplayRectFEvent.invoke(
                                                true,
                                                capture.whRatio
                                            )
                                        binding.root.animationTransition(duration = 250) {
                                            binding.vBackground.alpha = 1f
                                            binding.ccTransition.translationX =
                                                (if (RTLTool.isLayoutRtl()) DeviceUtils.getScreenWidth() - endRectF.right else endRectF.left).toRtl
                                            binding.ccTransition.translationY = endRectF.top
                                            binding.ivTransition.setSize(
                                                width = endRectF.width().toInt(),
                                                height = endRectF.height().toInt()
                                            )
                                            onTransitionEnd = {
                                                "onTransitionEnd::onTransitionEnd".print("csx")
                                                onTransitionEnd(_isTransitionEnd = true)
                                            }

                                            onTransitionUpdate = {
                                                binding.ccTransition.corner(
                                                    cornerRadiusValuer.calculateValue(
                                                        it
                                                    )
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                })
        }
    }

    private var isFirstFrameLoadedSuccess = false

    /**
     * 首帧加载完成
     * @param equalTag 转场过渡目标元素
     */
    fun onFirstFrameLoadedSuccess(equalTag: String) {
        if (!isFirstFrameLoadedSuccess && equalTag == imageCapture?.equalTag) {
            isFirstFrameLoadedSuccess = true
            "onFirstFrameLoadedSuccess::onTransitionEnd".print("csx")
            onTransitionEnd(_isFirstFrameLoaded = true)
        }
    }

    /**
     * 重置动画，主要用于折叠屏幕配置发生改变的时候
     */
    fun reSetTransitionWhenConfigurationChanged(capture: ImageCapture?) {
        imageCapture = capture
    }

    /**
     * 转场过渡完成
     */
    private var isFirstFrameLoaded = false
    private var isTransitionEnd = false
    private fun onTransitionEnd(
        _isFirstFrameLoaded: Boolean = isFirstFrameLoaded,
        _isTransitionEnd: Boolean = isTransitionEnd
    ) {
        this.isFirstFrameLoaded = _isFirstFrameLoaded
        this.isTransitionEnd = _isTransitionEnd
        if (!this.isTransitionEnd || !this.isFirstFrameLoaded || activity.isFinishing) {
            return//如果转场过渡未完成、首帧未加载完成、不显示整体页面
        }
        binding.flActivityContent.animationTransition(duration = 300) {
            binding.flActivityContent.alpha = 1f
            onTransitionEnd = {
                binding.flActivityContent.doOnPreDraw {
                    binding.ccTransition.gone()
                    binding.vPlaceholder.gone()
                    <EMAIL>?.invoke()
                }
            }
        }
    }

    /**
     * 结束页面
     *
     * @param equalTag 转场过渡目标元素
     * @param onFinished 页面B结束调用
     */
    fun finish(equalTag: String, onFinished: (withAnimation: Boolean) -> Unit) {
        binding.root.clearTransition()
        val capture = imageCapture
        if (equalTag == capture?.equalTag) {
            if (binding.ccTransition.alpha != 1f) {
                //处于动画首个阶段 图片渐显阶段
                if (binding.ccTransition.alpha == 0f) {
                    onFinished.invoke(false)
                } else {
                    binding.ccTransition.animationTransition(duration = 150) {
                        binding.ccTransition.alpha = 0f
                        onTransitionEnd = {
                            onFinished.invoke(false)
                        }
                    }
                }
            } else {
                //相同目标 退出动画
                binding.flActivityContent.alpha = 0f
                binding.flActivityContent.clearTransition()
                binding.ccTransition.visible()
                binding.vPlaceholder.visible()
                if (isTransitionEnd) {
                    val startRectF = onCalculateDisplayRectFEvent(false, capture.whRatio)
                    binding.ivTransition.setSize(
                        width = startRectF.width().toInt(),
                        height = startRectF.height()
                            .toInt()
                    )
                    binding.ccTransition.translationX =
                        (if (RTLTool.isLayoutRtl()) DeviceUtils.getScreenWidth() - startRectF.right else startRectF.left).toRtl
                    binding.ccTransition.translationY = startRectF.top
                    val radius = onCalculateDisplayRadiusEvent.invoke(false)
                    cornerRadiusValuer.mark(radius, capture.radius)
                } else {
                    cornerRadiusValuer.mark(
                        binding.ccTransition.cornerDelegate.corner,
                        capture.radius
                    )
                }
                binding.root.animationTransition(duration = 250) {
                    binding.vBackground.alpha = 0f
                    binding.ccTransition.translationX =
                        (if (RTLTool.isLayoutRtl()) DeviceUtils.getScreenWidth() - capture.fullRectf.right else capture.fullRectf.left).toRtl
                    binding.ccTransition.translationY = capture.fullRectf.top
                    "适配  finish capture= left = ${capture?.fullRectf?.left}  right = ${capture?.fullRectf?.right}".print(debugLevel = Debug.DebugLevel.ERROR)
                    "适配  finish translationX = ${(if (RTLTool.isLayoutRtl()) DeviceUtils.getScreenWidth() - capture.fullRectf.right else capture.fullRectf.left).toRtl}".print(debugLevel = Debug.DebugLevel.ERROR)
                    binding.ivTransition.setSize(
                        width = capture.fullRectf.width().toInt(),
                        height = capture.fullRectf.height().toInt()
                    )
                    onTransitionEnd = {
                        binding.vPlaceholder.gone()
                        binding.ccTransition.animationTransition(duration = 150) {
                            binding.ccTransition.alpha = 0f
                            onTransitionEnd = {
                                onFinished.invoke(true)
                            }
                        }
                    }

                    onTransitionUpdate = {
                        binding.ccTransition.corner(cornerRadiusValuer.calculateValue(it))
                    }
                }
            }
            return
        }
        onFinished.invoke(false)
    }

    companion object {

        /**
         * 写入Intent
         */
        const val KEY_transition_image = "KEY_transition_image"

        /**
         * 采集
         */
        fun capture(
            view: View,
            whRatio: Float,
            radius: Float,
            displayPath: String,
            placeHolderColor: Int,
            originPath: String,
            equalTag: String
        ): ImageCapture {
            val leftTop = IntArray(2)
            //直接给到itemView宽高位置占用即可
            view.getLocationInWindow(leftTop)
            val fullRectF = Rect(
                leftTop[0],
                leftTop[1],
                leftTop[0] + view.measuredWidth,
                leftTop[1] + view.measuredHeight
            ).toRectF()
            return ImageCapture(
                fullRectF,
                whRatio,
                radius,
                displayPath,
                originPath,
                equalTag,
                placeHolderColor
            )
        }

    }


    /**
     * 图片展示数据采集
     * @param fullRectf 在页面A展示位置
     * @param whRatio 宽高比
     * @param radius 目前展示圆角
     * @param imagePath 图片加载路径（最好同动画发起页面同一个Path，图片加载框架有缓存，加载快）
     * @param equalTag 用于判定部分动画可生效的判断
     */
    @Keep
    data class ImageCapture(
        val fullRectf: RectF,
        val whRatio: Float,
        val radius: Float,
        val imagePath: String,
        val originPath: String,
        val equalTag: String,
        val placeHolderColor: Int
    ) :
        Parcelable {

        constructor(parcel: Parcel) : this(
            parcel.readParcelable(RectF::class.java.classLoader)!!,
            parcel.readFloat(),
            parcel.readFloat(),
            parcel.readString()!!,
            parcel.readString()!!,
            parcel.readString()!!,
            parcel.readInt()
        )

        override fun describeContents(): Int {
            return 0
        }

        override fun writeToParcel(dest: Parcel, flags: Int) {
            dest.writeParcelable(fullRectf, flags)
            dest.writeFloat(whRatio)
            dest.writeFloat(radius)
            dest.writeString(imagePath)
            dest.writeString(originPath)
            dest.writeString(equalTag)
            dest.writeInt(placeHolderColor)
        }

        companion object CREATOR : Parcelable.Creator<ImageCapture> {
            override fun createFromParcel(parcel: Parcel): ImageCapture {
                return ImageCapture(parcel)
            }

            override fun newArray(size: Int): Array<ImageCapture?> {
                return arrayOfNulls(size)
            }
        }
    }
}