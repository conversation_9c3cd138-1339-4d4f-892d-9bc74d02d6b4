package com.commsource.util.common;

import com.meitu.library.util.Debug.Debug;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR> by zpb
 */
public class JSONTool {

	/** 成功 */
	public final String SUCCESS = "success";
	/** JSON错误 */
	public final String ERROR_JSON = "json_error";
	public final String ERROR = "error";
	public final String ERROR_NOTJSON = "can not create a json with zhe string";
	/**常量*/
	String TAG = "json";

	/** 读取json字符串，如果res==1，返回“success”，否则返回msg */
	public String getJsonResult(String response) throws JSONException {
		JSONObject jsonObject = new JSONObject(response);
		int res = jsonObject.getInt("res");
		String msg = jsonObject.getString("msg");
		if (res == 1) {
			return SUCCESS;
		}
		return msg;
	}

	/**
	 * 从JSON字符串中获取制定name的value
	 * 
	 * @return 对应name的value或者出现异常风返回null
	 */
	public Object getValueFromJsonStr(String jsonStr, String name) {
		try {
			JSONObject jsonObject = new JSONObject(jsonStr);
			Object value = jsonObject.get(name);
			return value;
		} catch (JSONException e) {

			Debug.w(e);
		}
		return null;
	}

	/** 读取json字符串name="msg"的值 */
	public String getJsonMsg(String response) throws JSONException {
		JSONObject jsonObject = new JSONObject(response);
		String msg = jsonObject.getString("msg");
		return msg;
	}

	/**
	 * 读取Json字符串，装载到Map<String, Object>中,方法体内递归调用
	 * 
	 * @param map
	 *            必须实例化
	 * @param jsonStr
	 * @return
	 */
	public String readJSONObject(Map<String, Object> map, String jsonStr) {
		try {
			if (isJSONObjectStr(jsonStr)) {
				JSONObject jsonObject = new JSONObject(jsonStr);
				@SuppressWarnings("rawtypes")
				Iterator it = jsonObject.keys();
				while (it.hasNext()) {
					String jsonItemName = (String) it.next();
					Object jsonItemValue = null;
					String strValue = null;
					try {// 如果该键在字符串中没有对应的值，则JSONException，如{"msg":,"res":"0"}
						jsonItemValue = jsonObject.get(jsonItemName);
						strValue = jsonItemValue.toString();
					} catch (Exception e) {
						map.put(jsonItemName, jsonItemValue);
						continue;
					}
					if (isJSONObjectStr(strValue)) {
						// JSONObject对象的字符串
						Map<String, Object> valueMap = new HashMap<String, Object>(16);
						readJSONObject(valueMap, strValue);
						map.put(jsonItemName, valueMap);
					} else if (isJSONArrayStr(strValue)) {
						// JSONArray对象的字符串
						List<Map<String, Object>> valueList = new ArrayList<Map<String, Object>>();
						readJSONArray(valueList, strValue);
						map.put(jsonItemName, valueList);
					} else {
						map.put(jsonItemName, jsonItemValue);
					}
				}
			} else if (isJSONArrayStr(jsonStr)) {
				JSONArray jsonArr = new JSONArray(jsonStr);
				for (int i = 0; i < jsonArr.length(); i++) {
					JSONObject jsonObject = jsonArr.getJSONObject(i);
					readJSONObject(map, jsonObject.toString());
				}
			} else {
				return ERROR_NOTJSON;
			}
			// log("mapFromJsonObject=" + map);
			return SUCCESS;
		} catch (JSONException e) {
			Debug.w(e);
			return ERROR_JSON;
		} catch (Exception e) {
			Debug.w(e);
			return ERROR;
		}
	}

	/**
	 * 读取JSONArray对象， list如果为空，是new出来 还是抛异常？？？
	 * 
	 * @param list
	 *            不为空，直接add，即保留原来的
	 * @param jsonArrStr
	 *            JSONArray对象的字符串，以“[”开始以“]”结尾
	 * @return "success" / "数据解析错误"
	 */
	public String readJSONArray(List<Map<String, Object>> list,
			String jsonArrStr) {
		try {
			JSONArray jsonArr = new JSONArray(jsonArrStr);
			for (int i = 0; i < jsonArr.length(); i++) {
				JSONObject jsonObject = jsonArr.getJSONObject(i);
				Map<String, Object> map = new HashMap<String, Object>(16);
				readJSONObject(map, jsonObject.toString());
				list.add(map);
			}
			return SUCCESS;
		} catch (JSONException e) {
			Debug.w(e);
			return ERROR_JSON;
		} catch (Exception e) {
			Debug.w(e);
			return ERROR;
		}
	}

	/**
	 * 把JSON字符串转换成Map<String,Object>
	 * 
	 * @param response
	 * @param mapFromJsonObject
	 *            之前必须实例化，不然不是同一个对象
	 * @return 返回"success"/"数据解析错误"
	 */
	public String changeToMap(String response,
			Map<String, Object> mapFromJsonObject) {

		try {
			JSONObject jsonObject = new JSONObject(response);
			@SuppressWarnings("rawtypes")
			Iterator it = jsonObject.keys();
			while (it.hasNext()) {
				String jsonItemName = (String) it.next();
				Object jsonItemValue = jsonObject.get(jsonItemName);
				mapFromJsonObject.put(jsonItemName, jsonItemValue);
			}
			// log("mapFromJsonObject=" + mapFromJsonObject);
			return SUCCESS;
		} catch (JSONException e) {

			Debug.w(e);
			return ERROR_JSON;
		}
	}

	/**
	 * 判断字符串是否能构造JSONObject对象
	 */
	public static boolean isJSONObjectStr(String strValue) {
		try {
			if (strValue != null) {
				new JSONObject(strValue);
				return true;
			}
		} catch (JSONException e) {
		}
		return false;
	}

	/**
	 * 判断字符串是否能构造JSONArray对象
	 */
	public static boolean isJSONArrayStr(String strValue) {
		try {
			if (strValue != null) {
				new JSONArray(strValue);
				return true;
			}
		} catch (JSONException e) {
		}
		return false;
	}

	/** 获得Map<String,Object> map的字符串 */
	String getMapString(Map<String, Object> map) {
		StringBuilder sb = new StringBuilder();
		Set<String> key = map.keySet();
		for (Iterator<String> it = key.iterator(); it.hasNext();) {
			String s = it.next();
			// 这里的s就是map中的key，map.get(s)就是key对应的value。
			sb.append(s + ":" + map.get(s) + ", ");
		}
		sb.substring(0, sb.length() - 2);
		return sb.toString();
	}

	/**
	 * 从Map<String, Object>对象转换成JSONObject
	 * 
	 * @throws JSONException
	 */
	public JSONObject getJSON4Map(Map<String, Object> map) throws JSONException {
		if (map == null || map.size() == 0) {
			return null;
		}
		Set<String> key = map.keySet();
		JSONObject json = new JSONObject();
		JSONArray jsonArr = new JSONArray();
		for (Iterator<String> it = key.iterator(); it.hasNext();) {
			String name = it.next();
			JSONObject item = new JSONObject();
			item.put("id", name);
			item.put("downcount", map.get(name));
			jsonArr.put(item);
		}
		json.put("counts", jsonArr);
		return json;
	}

	public static ArrayList<String> changeJSONArrString2List(JSONArray jsonArray) {
		ArrayList<String> dataList = null;
		if (jsonArray != null && jsonArray.length() > 0) {
			dataList = new ArrayList<String>(jsonArray.length());
			String device = null;
			for (int i = 0; i < jsonArray.length(); i++) {
				try {
					device = jsonArray.getString(i);
				} catch (JSONException e) {
					Debug.w(e);
				}
				dataList.add(i, device);
			}
		}
		return dataList;
	}
}