package com.commsource.studio.layer

import android.app.Activity
import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.view.View
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.webp.decoder.WebpDrawable
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.commsource.beautyplus.R
import com.commsource.camera.util.XAnimator
import com.commsource.camera.util.animationListener
import com.commsource.util.gone
import com.commsource.util.isRtl
import com.commsource.util.visible
import com.meitu.library.util.bitmap.BitmapUtils
import com.meitu.library.util.device.DeviceUtils

/**
 * 上层绘制Mask的Layer，不需要实时绘制路径层级也可以使用它来监听手势。
 */
class EffectTranslateLayer(context: Context) : BaseSubLayer(context) {

    var originBitmap: Bitmap? = null
    var effectBitmap: Bitmap? = null

    /**
     * 渐变距离。
     */
    private val gradientLengthInViewPort = DeviceUtils.dip2fpx(20f)

    /**
     * 渐变左边坐标
     */
    private var gradientLeft = 0
    private var gradientLength = 0

    /**
     * 前景图绘制矩形。
     */
    private val mFrontSrcRect = Rect()

    /**
     * 背景图绘制矩形。
     */
    private val mBackSrcRect = Rect()

    /**
     * 前景图渐变区域绘制矩形。
     */
    private val mFrontGradientSrcRect = Rect()
    private val mFrontGradientDisRect = Rect()

    /**
     * bling动画的gif。
     */
    private var webpDrawable: WebpDrawable? = null

    /**
     * 绘制画笔。
     */
    private val paint = Paint().apply {
        xfermode = PorterDuffXfermode(PorterDuff.Mode.MULTIPLY);
    }

    /**
     * 效果图程度值。
     */
    var effectAlpha = 0f

    /**
     * 绘制照片使用的画笔。
     */
    private val bitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    /**
     * 是否开启动画。
     */
    private var isStartAnimate = false

    override fun onAttachToContainer() {
        mFrontSrcRect.set(0, 0, imageWidth.toInt(), imageHeight.toInt())
        mBackSrcRect.set(0, 0, 0, imageHeight.toInt())
        mFrontGradientSrcRect.set(0, 0, 0, imageHeight.toInt())
        gradientLength = (gradientLengthInViewPort / canvasInitMatrixBox.getScale()).toInt()
        paint.shader = LinearGradient(
            0f,
            0f,
            gradientLength.toFloat(),
            0f,
            0x00FFFFFF,
            0xFFFFFFFF.toInt(),
            Shader.TileMode.CLAMP
        )
        mFrontGradientDisRect.set(0, 0, gradientLength, imageHeight.toInt())
    }

    /**
     * 开始过渡动画。
     */
    fun start(onAnimatorEnd: (() -> Unit)? = null) {
        if (context is Activity && (context as Activity).isDestroyed) {
            return
        }
        // 需要先加载WebP.
        webpDrawable = null
        Glide.with(context)
            .load(R.drawable.bling_transition)
            .into(object : CustomTarget<Drawable?>() {
                override fun onResourceReady(
                    resource: Drawable,
                    transition: Transition<in Drawable?>?
                ) {
                    webpDrawable = (resource as WebpDrawable).apply {
                        callback = object : Drawable.Callback {
                            override fun invalidateDrawable(who: Drawable) {}
                            override fun scheduleDrawable(
                                who: Drawable,
                                what: Runnable,
                                `when`: Long
                            ) {
                            }

                            override fun unscheduleDrawable(who: Drawable, what: Runnable) {}
                        }
                        loopCount = 1
                        setVisible(true, true)
                        start()
                        startAnimate(onAnimatorEnd)
                    }
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                }

                override fun onLoadCleared(placeholder: Drawable?) {

                }
            })
        isStartAnimate = true
        layerView.invalidate()
    }

    private fun startAnimate(onAnimatorEnd: (() -> Unit)? = null) {
        XAnimator.ofFloat(0f, 1f)
            .duration(1333)
            .animationListener {
                onUpdate = { fraction, _value ->
                    val isRtl = <EMAIL>()
                    val value = if (isRtl) 1 - fraction else fraction
                    val width: Int = imageWidth.toInt() + gradientLength
                    val height: Int = imageHeight.toInt()
                    gradientLeft = (width * value).toInt()
                    if (isRtl) {
                        mFrontSrcRect.right = gradientLeft.coerceAtMost(imageWidth.toInt())
                        mBackSrcRect.left = gradientLeft
                        mBackSrcRect.right = width
                    } else {
                        mFrontSrcRect.left = gradientLeft.coerceAtMost(imageWidth.toInt())
                        mBackSrcRect.right = gradientLeft.coerceAtMost(imageWidth.toInt())
                        mBackSrcRect.left = 0
                    }
                    mFrontGradientSrcRect.right = gradientLeft
                    mFrontGradientSrcRect.left = gradientLeft - gradientLength
                    webpDrawable?.let { drawable ->
                        try {
                            drawable.setBounds(
                                gradientLeft - height * drawable.intrinsicWidth / drawable.intrinsicHeight / 2,
                                0,
                                gradientLeft + height * drawable.intrinsicWidth / drawable.intrinsicHeight / 2,
                                height
                            )
                        } catch (e: NullPointerException) {
                            e.printStackTrace()
                        }
                    }
                    layerView.invalidate()
                }

                onStart = {
                    layerView.alpha = 1f
                    layerView.visible()
                }

                onEnd = {
                    isStartAnimate = false
                    originBitmap = null
                    effectBitmap = null
                    layerView.alpha = 0f
                    layerView.gone()
                    onAnimatorEnd?.invoke()
                }
            }.start()
    }


    override fun onCreateView(): View {
        return DrawTranslateView(context)
    }


    inner class DrawTranslateView(context: Context) : View(context) {
        override fun onDraw(canvas: Canvas) {
            if (!isStartAnimate) {
                return
            }
            canvas.let {
                it.concat(canvasInitMatrixBox.matrix)
                it.clipRect(0f, 0f, imageWidth, imageHeight)
                bitmapPaint.alpha = 255
                originBitmap?.let {
                    if (BitmapUtils.isAvailableBitmap(it)) {
                        canvas.drawBitmap(it, mFrontSrcRect, mFrontSrcRect, bitmapPaint)
                        canvas.drawBitmap(it, mBackSrcRect, mBackSrcRect, bitmapPaint)
                    }
                }
                bitmapPaint.alpha = (effectAlpha * 255).toInt()
                effectBitmap?.let {
                    if (BitmapUtils.isAvailableBitmap(it)) {
                        canvas.drawBitmap(it, mBackSrcRect, mBackSrcRect, bitmapPaint)
                    }
                }
                // 绘制渐隐部分，由于有适配问题，不能使用bitmap缓存的策略。
                val sc = it.saveLayer(
                    gradientLeft - gradientLength.toFloat(), 0f, gradientLeft.toFloat(),
                    imageHeight, null, Canvas.ALL_SAVE_FLAG
                )
                originBitmap?.let {
                    if (BitmapUtils.isAvailableBitmap(it)) {
                        canvas.drawBitmap(it, mFrontGradientSrcRect, mFrontGradientSrcRect, null)
                    }
                }
                // paint的惠子偏移只能通过localMatrix和canvas的改变来实现。
                canvas.translate(gradientLeft - gradientLength.toFloat(), 0f)
                it.drawRect(mFrontGradientDisRect, paint)
                it.restoreToCount(sc)
                webpDrawable?.draw(canvas)
            }
        }
    }
}