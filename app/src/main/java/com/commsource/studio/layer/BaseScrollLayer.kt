package com.commsource.studio.layer

import android.content.Context
import android.graphics.PointF
import android.view.MotionEvent
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelStoreOwner
import com.commsource.studio.BpGestureDetector
import com.commsource.studio.component.LayerScrollViewModel
import java.util.LinkedList

/**
 * 子功能界面内的Layer鸡肋，可以获取子功能生命周期，获取VM。
 */
abstract class BaseScrollLayer(context: Context) : BaseSubLayer(context) {

    /**
     * 滚动手势分发。
     */
    private lateinit var layerScrollViewModel: LayerScrollViewModel

    /**
     * 手指滚动监听。
     */
    private val scrollListeners = LinkedList<ScrollListener>()

    /**
     * 是否开始滚动手势。
     */
    var isStartScroll = false

    /**
     * 禁用双指缩放。
     */
    open var disableDoubleFinger = false

    open var consumeSingleTap = true

    /**
     * 添加滚动监听。
     */
    fun addScrollListener(scrollListener: ScrollListener, index: Int = -1) {
        if (index == -1) {
            scrollListeners.add(scrollListener)
        } else {
            scrollListeners.add(0, scrollListener)
        }
    }


    /**
     * Owner设置完成后的回调，子类在里面获取LV和VM。
     */
    override fun onInitOwner(storeOwner: ViewModelStoreOwner, lifecycleOwner: LifecycleOwner) {
        layerScrollViewModel = getViewModel(LayerScrollViewModel::class.java)
        layerScrollViewModel.layer = this
        addScrollListener(layerScrollViewModel)
    }

    /**
     * 是否触发了多指。
     */
    var hasTriggerMultiFinger = false

    override fun onCreateGestureListener(): BpGestureDetector.NestOnGestureListener {
        return object : BpGestureDetector.NestOnGestureListener() {

            /**
             * 判断上一次手势是不是单指操作。
             */
            var isLastSingleFinger = false


            override fun onLongPress(downEvent: MotionEvent): Boolean {
                // 所有的二级界面禁用长按对比。
                return true
            }

            override fun onLongPressUp(upEvent: MotionEvent): Boolean {
                // 所有的二级界面禁用长按对比。
                return true
            }

            override fun onTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean {
                floatArrayOf(downEvent.x, downEvent.y).let {
                    mapPointContainerToViewPort(it)
                    scrollListeners.forEach { listener -> listener.onTap(it[0], it[1], downEvent) }
                }
                return consumeSingleTap
            }

            override fun onDoubleGestureMove(scale: Float, angle: Float, focus: PointF): Boolean {
                if (!isEnable) {
                    return false
                }
                scrollListeners.forEach { listener ->
                    if (listener.onDoubleGestureMove(scale, angle, focus)) {
                        return true
                    }
                }
                return disableDoubleFinger
            }

            override fun onDoubleGestureStart(firstDownEvent: PointF, secondDownEvent: PointF, focus: PointF): Boolean {

                return disableDoubleFinger
            }

            override fun onScroll(downEvent: MotionEvent?, moveEvent: MotionEvent?, distanceX: Float, distanceY: Float): Boolean {
                scrollListeners.forEach { listener ->
                    if (listener.onScroll(downEvent, moveEvent, distanceX, distanceY)) {
                        return true
                    }
                }
                return disableDoubleFinger
            }

            override fun onGestureEnd(touchEvent: MotionEvent): Boolean {
                return true
            }

            override fun onMajorFingerUp(upEvent: MotionEvent?): Boolean {
                if (!isEnable) {
                    return false
                }
                super.onMajorFingerUp(upEvent)
                if (hasTriggerMultiFinger) {
                    return false
                }
                upEvent?.apply {
                    floatArrayOf(x, y).let {
                        mapPointContainerToViewPort(it)
                        scrollListeners.forEach { listener ->
                            listener.onStopSingleFinger(
                                it[0],
                                it[1],
                                isStartScroll,
                                isLastSingleFinger,
                                upEvent
                            )
                        }
                    }
                }
                isStartScroll = false
                layerView.invalidate()
                return true
            }

            override fun onMajorScroll(
                downEvent: MotionEvent,
                moveEvent: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                if (!isEnable) {
                    return false
                }
                super.onMajorScroll(downEvent, moveEvent, distanceX, distanceY)
                if (hasTriggerMultiFinger) {
                    return false
                }
                isLastSingleFinger = true
                if (!isStartScroll) {
                    // 只有开始这个事件回调才认为是真正的单指滚动手势开始。
                    downEvent?.apply {
                        floatArrayOf(x, y).let {
                            mapPointContainerToViewPort(it)
                            scrollListeners.forEach { listener -> listener.onStartSingleFingerScroll(it[0], it[1], downEvent) }
                        }
                    }
                    isStartScroll = true
                } else {
                    moveEvent?.apply {
                        floatArrayOf(x, y).let {
                            mapPointContainerToViewPort(it)
                            scrollListeners.forEach { listener -> listener.onSingleFingerScroll(it[0], it[1], moveEvent) }
                        }
                    }
                }
                layerView.invalidate()
                return true
            }

            override fun onMajorFingerDown(downEvent: MotionEvent): Boolean {
                if (!isEnable) {
                    return false
                }
                hasTriggerMultiFinger = false
                isLastSingleFinger = true
                downEvent?.apply {
                    floatArrayOf(x, y).let {
                        mapPointContainerToViewPort(it)
                        scrollListeners.forEach { listener -> listener.onSingleFingerDown(it[0], it[1], downEvent) }
                    }
                    layerView.invalidate()
                }
                return true
            }

            override fun onMinorFingerDown(downEvent: MotionEvent?): Boolean {
                if (!isEnable) {
                    return false
                }
                hasTriggerMultiFinger = true
                isLastSingleFinger = false
                scrollListeners.forEach { listener -> listener.onStopSingleFinger(0f, 0f, isStartScroll, false, null) }
                isStartScroll = false
                layerView.invalidate()
                return super.onMinorFingerDown(downEvent)
            }
        }
    }

    interface ScrollListener {

        /**
         * 手指按下。
         */
        fun onSingleFingerDown(viewPortX: Float, viewPortY: Float, motionEvent: MotionEvent) {

        }

        /**
         * 开始单指拖动
         */
        fun onStartSingleFingerScroll(viewPortX: Float, viewPortY: Float, motionEvent: MotionEvent) {

        }

        /**
         * 单指拖动停止
         */
        fun onStopSingleFinger(
            viewPortX: Float,
            viewPortY: Float,
            isStartScroll: Boolean,
            isMajorFingerUp: Boolean,
            motionEvent: MotionEvent?
        ) {

        }

        /**
         * 拖动事件。
         */
        fun onSingleFingerScroll(viewPortX: Float, viewPortY: Float, motionEvent: MotionEvent) {

        }

        /**
         * 单击 准确来说 单击不能在手指滑动类型的layer中 但是因为目前需求需要有单击对于一些涂抹操作 也放在其中
         */
        fun onTap(viewPortX: Float, viewPortY: Float, motionEvent: MotionEvent) {

        }

        fun onDoubleGestureMove(scale: Float, angle: Float, focus: PointF): Boolean {
            return false
        }

        fun onScroll(downEvent: MotionEvent?, moveEvent: MotionEvent?, distanceX: Float, distanceY: Float): Boolean {
            return false
        }
    }
}