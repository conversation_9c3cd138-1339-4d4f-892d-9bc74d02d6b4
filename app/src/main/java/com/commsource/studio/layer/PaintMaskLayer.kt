package com.commsource.studio.layer

import android.content.Context
import android.graphics.*
import android.view.View
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelStoreOwner
import com.commsource.beautyplus.R
import com.commsource.studio.MagnifyComponent
import com.commsource.studio.MatrixBox
import com.commsource.studio.component.PaintSelectComponent
import com.commsource.studio.onDrawListener
import com.commsource.util.LOGV
import com.commsource.util.ResourcesUtils
import com.commsource.util.UIHelper
import com.meitu.common.animutil.buildAnimSet
import com.meitu.library.util.device.DeviceUtils

/**
 * 上层绘制Mask的Layer，不需要实时绘制路径层级也可以使用它来监听手势。
 */
open class PaintMaskLayer(context: Context) : BaseScrollLayer(context), onDrawListener,
    BaseScrollLayer.ScrollListener {

    /**
     *  是否带硬度
     */
    var withHardness: Boolean = false
    var isNeedFitScale = true
    var penState = true

    /**
     * 绘制圆形画笔。
     */
    private val penDraw = PenLayerDrawable(this).apply {
        strokeWidth = DeviceUtils.dip2fpx(2f)
        isEnable = false
    }

    /**
     * 绘制Mask的Paint。
     */
    private val bitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        alpha = 0
    }

    private var paintXfermode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)


    var isNeedDrawPath = true

    /**
     * Mask绘制的Alpha
     */
    var maskAlpha = 1f
        set(value) {
            field = value
            layerView.postInvalidate()
        }

    /**
     * 是否需要记忆Mask堆栈。
     */
    var needRecordMaskStack = false

    /**
     *  用作动画的Mask
     */
    private var animBitmap: Bitmap? = null
    private var showMaskAnim: Boolean = false


    init {
        addScrollListener(this, 0)
    }

    override fun onInitOwner(storeOwner: ViewModelStoreOwner, lifecycleOwner: LifecycleOwner) {
        super.onInitOwner(storeOwner, lifecycleOwner)
        // 监听画笔。
        getViewModel(PaintSelectComponent.PaintSelectViewModel::class.java).apply {
            paintSizeChangeEvent.observe(lifecycleOwner, Observer {
                updateSize(it)
            })
            paintStartSelectEvent.observe(lifecycleOwner, Observer {
                showPenInCenter(it)
                layerView.invalidate()
            })
        }

        // 设置放大镜。
        getViewModel(MagnifyComponent.MagnifyViewModel::class.java).addOnDrawListener(this)
    }

    override fun onCreateView(): View {
        return DrawPathView(context)
    }


    /**
     *  更新尺寸
     */
    fun updateSize(size: Float, isNeedShowPaint: Boolean = false) {
        penDraw.radius = size
        penDraw.isNeedFitScale = isNeedFitScale
        //如果显示mask动画 要只显示画布部分
        if (!showMaskAnim) {
            skipCanvasLimit = true
        }
        if (isNeedShowPaint) {
            showPenInCenter()
        }
        layerView.invalidate()
    }

    /**
     *  更新透明度
     */
    fun updateOpacity(alpha: Float, isNeedShowPaint: Boolean = true) {
        penDraw.updateOpacity(alpha)
        //如果显示mask动画 要只显示画布部分
        if (!showMaskAnim) {
            skipCanvasLimit = true
        }
        if (isNeedShowPaint) {
            showPenInCenter()
        }
        layerView.invalidate()
    }

    fun updateHardness(radius: Float, isNeedShowPaint: Boolean = true) {
        if (withHardness) {
            penDraw.isNeedFitScale = isNeedFitScale
            penDraw.hardnessRadius = radius
            if (isNeedShowPaint) {
                showPenInCenter()
                layerView.invalidate()
            }
        }
    }

    private val scaleMatrix = Matrix()

    override fun onDrawCanvas(canvas: Canvas) {
        if (showMaskAnim) {
            bitmapPaint.alpha = (128 * maskAlpha).toInt()
            animBitmap?.let {
                scaleMatrix.setScale(viewPortWidth / it.width, viewPortHeight / it.height)
                bitmapPaint.xfermode = null
                canvas.drawColor(ResourcesUtils.getColor(R.color.Primary_A))
                bitmapPaint.xfermode = paintXfermode
                canvas.drawBitmap(it, scaleMatrix, bitmapPaint)
            }
        } else if (penState) {
            penDraw.onDraw(canvas)
        }
    }

    inner class DrawPathView(context: Context) : View(context) {

        override fun onDraw(canvas: Canvas) {
            canvas.save()
            canvas.concat(canvasGestureMatrixBox.matrix)
            canvas.translate(viewPortLeft, viewPortTop)
            if (!skipCanvasLimit) {
                canvas.clipRect(0f, 0f, viewPortWidth, viewPortHeight)
            }
            skipCanvasLimit = false
            onDrawCanvas(canvas)
            canvas.restore()
        }
    }

    /**
     * 短暂的展示一下Mask.
     */
    fun showMaskBriefly(bitmap: Bitmap, enAction: (() -> Unit)? = null) {
        showMaskAnim = true
        animBitmap = bitmap
        maskAlpha = 1.0f
        buildAnimSet {
            valueAnim {
                values = floatArrayOf(0.0f, 1.0f)
                duration = 300
                onUpdateFloat = {
                    maskAlpha = it
                    layerView.invalidate()
                }
            } next valueAnim {
                values = floatArrayOf(1.0f, 0.0f)
                startDelay = 200
                duration = 300
                onUpdateFloat = {
                    maskAlpha = it
                    layerView.invalidate()
                }
            }
            // 动画结束
            onEnd = {
                showMaskAnim = false
                enAction?.invoke()
            }
        }.start()
    }

    override fun onCanvasGestureMatrixChange(matrixBox: MatrixBox) {
//        super.onCanvasGestureMatrixChange(matrixBox)
        canvasGestureMatrixBox.set(matrixBox)
        if (UIHelper.isMainThread()) {
            layerView.invalidate()
        } else {
            layerView.postInvalidate()
        }
    }

    val canvasGestureMatrixBox = MatrixBox()

    var skipCanvasLimit = false

    fun showPenInCenter(isShow: Boolean = true) {
        penDraw.isEnable = isShow
        floatArrayOf(viewPortRectF.centerX(), viewPortRectF.centerY()).apply {
            mapPointContainerToViewPort(this)
            penDraw.drawPoint.set(this[0], this[1])
            //记录此点。
            layerView.invalidate()
        }
    }
}