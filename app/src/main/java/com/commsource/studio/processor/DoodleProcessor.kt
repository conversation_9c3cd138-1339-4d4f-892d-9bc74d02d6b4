package com.commsource.studio.processor

import android.graphics.Bitmap
import android.graphics.Color
import android.opengl.GLES20
import android.os.Build
import android.view.MotionEvent
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.easyeditor.utils.opengl.FBOEntity
import com.commsource.easyeditor.utils.opengl.GlThread
import com.commsource.easyeditor.utils.opengl.TextureHelper
import com.commsource.statistics.Meepo
import com.commsource.studio.DecorateConstant
import com.commsource.studio.IStackAction
import com.commsource.studio.component.LayerScrollViewModel
import com.commsource.studio.component.UndoRedoComponent
import com.commsource.studio.doodle.*
import com.commsource.studio.layer.BaseScrollLayer
import com.commsource.util.DeviceLevelAdapter
import com.commsource.util.GlideProxy
import com.commsource.util.print
import com.meitu.common.AppContext
import com.meitu.core.magicpen.NativeGLMagicPen
import com.meitu.library.util.Debug.Debug
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.io.FileUtils
import com.meitu.webview.utils.GsonHelper
import java.io.File
import java.util.concurrent.Flow


/**
 *
 * Created on 2020/8/20
 * <AUTHOR>
 */
class DoodleProcessor() : BaseEffectProcessor(), IStackAction {


    val undoStack = ArrayList<DoodleWrapper?>()
    private val redoStack = ArrayList<DoodleWrapper?>()

    private lateinit var magicPen: NativeGLMagicPen

    private var currentDoodle: DoodleWrapper? = null

    private var currentColor: Int? = null

    @Volatile
    private var isRenderMove: Boolean = false

    private val isLowDevice: Boolean =
        DeviceLevelAdapter.isLowDevice() || DeviceLevelAdapter.isMidDevice()

    /**
     * 撤销重做。
     */
    lateinit var undoRedoViewModel: UndoRedoComponent.UndoRedoViewModel

    private lateinit var doodleViewModel: DoodleViewModel

    private val tempPoint = FloatArray(2)

    /**
     * 半径
     */
    private var penSize: Float = 0.5f
    var density: Float = 1.0f

    private var eraserPenHardness: Float = 0f
    private var flow = 1.0f


    /**
     * 是否响应手势
     */
    var isEnable = true

    var isNativeInit = false

    /**
     * 在fragment中添加观察者，注册一些行为监听。
     */
    fun addObserver(fragment: BaseFragment) {
        doodleViewModel = ViewModelProvider(fragment.ownerActivity).get(DoodleViewModel::class.java)
        undoRedoViewModel =
            ViewModelProvider(fragment.ownerActivity).get(UndoRedoComponent.UndoRedoViewModel::class.java)

        // 监听手势。
        ViewModelProvider(fragment).get(LayerScrollViewModel::class.java).apply {
            addScrollListener(object : BaseScrollLayer.ScrollListener {
                /**
                 * 是否有滚动到viewPort中。
                 */
                private var hasScrollInViewPort = false

                fun isPointInViewPort(viewPortX: Float, viewPortY: Float): Boolean {
                    return layer.inViewPort(viewPortX, viewPortY)
                }


                override fun onStartSingleFingerScroll(
                    viewPortX: Float,
                    viewPortY: Float,
                    motionEvent: MotionEvent
                ) {
                    if (!isEnable) {
                        return
                    }
                    doodleViewModel.startDrawEvent.value = true
                    if (!canEraser && currentDoodle == null) {
                        return
                    }
                    hasScrollInViewPort = false
                    tempPoint[0] = viewPortX
                    tempPoint[1] = viewPortY
                    layer.mapPointViewPortToCanvas(tempPoint)
                    val x = tempPoint[0] / srcFBOEntity.width.toFloat()
                    val y = tempPoint[1] / srcFBOEntity.height.toFloat()
                    queueEvent {
                        // 橡皮擦
                        if (currentDoodle == null) {
                            // 控制硬度
                            magicPen.SetEraserBrushParam(flow, eraserPenHardness, 0f)
                            magicPen.SetMagicPenSize(penSize * 2 / (layer.canvasInitMatrixBox.getScale()), true)
//                            if (Meepo.isBrushOptTest()) {
//                                magicPen.SetEraserBrushParam(flow, eraserPenHardness, 0f)
//                                magicPen.SetMagicPenSize(penSize * 2 / (layer.canvasInitMatrixBox.getScale()), true)
//                            } else {
//                                magicPen.SetMagicPenSize(
//                                    penSize * 2 / (layer.containerScale * layer.canvasInitMatrixBox.getScale()),
//                                    true
//                                )
//                                magicPen.SetViewScale(layer.containerScale)
//                            }
                        } else {
                            magicPen.SetEraserBrushParam(0.8f, 0f, 0f)
                            currentDoodle?.doodle?.let {
                                magicPen.SetMagicPenSize(
                                    it.getPenSize(transSizeToRatio(penSize)) / layer.canvasInitMatrixBox.getScale(),
                                    false
                                )
                            }
                        }
                        magicPen.OnFingerDown(x, y, true)
                    }
                    requestRender()
                }

                override fun onSingleFingerScroll(
                    viewPortX: Float,
                    viewPortY: Float,
                    motionEvent: MotionEvent
                ) {
                    if (!isEnable || (isLowDevice && isRenderMove)) {
                        Debug.i("RLog", " ignore rendering move")
                        return
                    }
                    isRenderMove = true
                    if (!canEraser && currentDoodle == null) {
                        return
                    }
                    if (isPointInViewPort(viewPortX, viewPortY)) {
                        hasScrollInViewPort = true
                    }

                    if (!hasScrollInViewPort) {
                        return
                    }

                    tempPoint[0] = viewPortX
                    tempPoint[1] = viewPortY
                    layer.mapPointViewPortToCanvas(tempPoint)
                    val x = tempPoint[0] / srcFBOEntity.width.toFloat()
                    val y = tempPoint[1] / srcFBOEntity.height.toFloat()
                    queueEvent {
                        magicPen.OnFingerMove(x, y, true)
                    }
                    requestRender()
                }

                override fun onStopSingleFinger(
                    viewPortX: Float,
                    viewPortY: Float,
                    isStartScroll: Boolean,
                    isMajorFingerUp: Boolean,
                    motionEvent: MotionEvent?
                ) {
                    if (!isEnable) {
                        return
                    }
                    doodleViewModel.startDrawEvent.value = false
                    if (!canEraser && currentDoodle == null) {
                        return
                    }
                    if (!hasScrollInViewPort) {
                        return
                    }
                    if (isStartScroll) {
                        tempPoint[0] = viewPortX
                        tempPoint[1] = viewPortY
                        layer.mapPointViewPortToCanvas(tempPoint)
                        val x = tempPoint[0] / srcFBOEntity.width.toFloat()
                        val y = tempPoint[1] / srcFBOEntity.height.toFloat()
                        queueEvent {
                            if (isLowDevice) {
                                magicPen.OnFingerMove(
                                    x,
                                    y,
                                    true
                                ) // 低端机会丢弃部分move渲染，故抬起手指的时候，加一个move渲染确保效果匹配
                            }
                            magicPen.OnFingerUp(x, y, true)
                            undoStack.add(currentDoodle)
                            undoRedoViewModel.updateState()
                            checkProState()
                        }
                        requestRender()
                    }
                }

                override fun onTap(viewPortX: Float, viewPortY: Float, motionEvent: MotionEvent) {
                    tempPoint[0] = viewPortX
                    tempPoint[1] = viewPortY
                    layer.mapPointViewPortToCanvas(tempPoint)
                    val x = tempPoint[0] / srcFBOEntity.width.toFloat()
                    val y = tempPoint[1] / srcFBOEntity.height.toFloat()
                    queueEvent {
                        // 橡皮擦
                        if (currentDoodle == null) {
                            // 控制硬度
                            magicPen.SetEraserBrushParam(flow, eraserPenHardness, 0f)
                            magicPen.SetMagicPenSize(
                                penSize * 2 / (layer.canvasInitMatrixBox.getScale()), true
                            )
//                            if (Meepo.isBrushOptTest()) {
//
//                            } else {
//                                magicPen.SetEraserBrushParam(0.8f, 0f, 0f)
//                                magicPen.SetMagicPenSize(
//                                    penSize * 2 / (layer.containerScale * layer.canvasInitMatrixBox.getScale()),
//                                    true
//                                )
//                                magicPen.SetViewScale(layer.containerScale)
//                            }
                        } else {
                            currentDoodle?.doodle?.let {
                                magicPen.SetMagicPenSize(
                                    it.getPenSize(transSizeToRatio(penSize)) / layer.canvasInitMatrixBox.getScale(),
                                    false
                                )
                            }
                        }
                        magicPen.OnFingerDown(x, y, true)
                        magicPen.OnFingerMove(x, y, true)
                        magicPen.OnFingerUp(x, y, true)
                        undoStack.add(currentDoodle)
                        undoRedoViewModel.updateState()
                        checkProState()
                    }
                    requestRender()
                }
            })
        }
    }

    private fun transSizeToRatio(penSize: Float): Float {
        return (penSize - DoodleAlphaViewModel.minPenSize) / (DoodleAlphaViewModel.maxPenSize - DoodleAlphaViewModel.minPenSize)
    }


    override fun onGlResourceInit() {
        super.onGlResourceInit()
        magicPen = NativeGLMagicPen()
        magicPen.SetDensity(DeviceUtils.getDmDensity(AppContext.context))
        undoRedoViewModel.subStackAction = this
        undoRedoViewModel.updateState()
        isNativeInit = true
    }

    fun initMagicPenEffect() {
        if (currentDoodle != null) {
            currentDoodle?.let { it ->
                applyDoodle(it)
                if (it.doodle.canEditColor == DecorateConstant.EDITABLE) {
                    currentColor?.let { applyPenColor(it) }
                }
            }
            setPenSize(penSize)
        } else {
            applyEraser()
        }
    }

    fun initBackground(background: Bitmap) {
        magicPen.backGroundInit(background)
    }


    override fun onGlResourceRelease() {
        super.onGlResourceRelease()
        isNativeInit = false
        magicPen.GLRelease()
        magicPen.Release()
        undoStack.clear()
        redoStack.clear()
        undoRedoViewModel.subStackAction = null
        undoRedoViewModel.updateState()
    }

    override fun onRender(disFBO: FBOEntity) {
        if (hasRelease) {
            return
        }
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, disFBO.fboId)
        GLES20.glViewport(0, 0, disFBO.width, disFBO.height)
        magicPen.OnDrawFrame(
            srcFBOEntity.textureId,
            srcFBOEntity.fboId,
            disFBO.textureId,
            disFBO.fboId,
            srcFBOEntity.width,
            srcFBOEntity.height
        )
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0)
        isRenderMove = false
    }


    @GlThread
    fun applyDoodle(doodleWrapper: DoodleWrapper) {
        if (!isNativeInit) {
            return
        }
        currentDoodle = doodleWrapper
        val doodle = doodleWrapper.doodle
        val resourcePath = DoodleRepo.getMaterialPath(doodle)
        val isFromAssert = doodle.internalState == DecorateConstant.INTERNAL
        if (currentDoodle?.doodle?.param == null) {
            val paramPath = resourcePath + File.separator + DoodleConfig.DOODLE_PARAM_FILE
            currentDoodle?.doodle?.param = GsonHelper.fromJsonNoException(
                if (isFromAssert) {
                    FileUtils.readFromAssets(AppContext.context, paramPath)
                } else {
                    FileUtils.readFile(paramPath)
                },
                DoodleMaterial.DoodleParam::class.java
            )
            "doodle size ======= ${currentDoodle?.doodle?.param?.minBrushSize}".print("yyj")
        }
        val configPath = resourcePath + File.separator + DoodleConfig.DOODLE_CONFIG_FILE
        setMagicPen(magicPen, configPath, isFromAssert, resourcePath)
        "doodle category = ${doodle.categoryId}".print("yyj")
        "doodle id = ${doodle.id}".print("yyj")
        "resourcePath = $resourcePath".print("yyj")
        val configColorPath = if (isFromAssert) {
            GlideProxy.ASSETS_TYPE + resourcePath + File.separator + DoodleConfig.DOODLE_COLOR_CONFIG_FILE
        } else {
            resourcePath + File.separator + DoodleConfig.DOODLE_COLOR_CONFIG_FILE
        }
        if (FileUtils.isFileExist(configColorPath)) {
            setMagicDoubleColorPen(magicPen, configColorPath, isFromAssert, resourcePath)
        } else {
            setMagicDoubleColorPen(magicPen, null, false, resourcePath)
        }
    }

    fun applyEraser() {
        currentDoodle?.let {
            currentDoodle = null
            val resoucePath = "scrawl/eraser"
            val configPath = "$resoucePath/eraser.mtpe"
            setMagicPen(magicPen, configPath, true, resoucePath)
            setMagicDoubleColorPen(magicPen, null, false, null)
        }
    }

    fun getCurrentDoodle(): DoodleMaterial? {
        return currentDoodle?.doodle
    }

    fun setPenParams(flow: Float, hardness: Float) {
        this.flow = flow
        this.eraserPenHardness = hardness
    }

    /**
     * 设置画笔颜色
     */
    @GlThread
    fun applyPenColor(color: Int) {
        currentColor = color
        magicPen.SetParticleColor(Color.red(color), Color.green(color), Color.blue(color), 255)
    }

    fun setPenSize(size: Float) {
        penSize = size
    }

    fun getUseDoodle(): List<DoodleWrapper> {
        val doodles = ArrayList<DoodleWrapper>()
        undoStack.distinct().forEach {
            it?.let { doodle ->
                doodles.add(doodle)
            }
        }
        return doodles
    }

    override fun redo() {
        queueEvent {
            if (redoStack.size <= 0) {
                return@queueEvent
            }
            magicPen.Redo()
            undoStack.add(redoStack.removeAt(redoStack.lastIndex))
            undoRedoViewModel.updateState()
            checkProState()
        }
        requestRender()
    }

    override fun undo() {
        queueEvent {
            if (undoStack.size <= 0) {
                return@queueEvent
            }
            magicPen.Undo()
            redoStack.add(undoStack.removeAt(undoStack.lastIndex))
            checkProState()
            undoRedoViewModel.updateState()
        }
        requestRender()
    }


    private fun checkProState() {
        val proState = undoStack.find {
            it?.doodle?.needPaid() ?: false
        } != null
        doodleViewModel.doodleProChangeEvent.postValue(proState)
    }

    override fun canRedo(): Boolean {
        return magicPen.CanRedo()
    }

    override fun canUndo(): Boolean {
        return magicPen.CanUndo()
    }

    override fun clear() {
        magicPen.Release()
        magicPen.GLRelease()
        undoStack.clear()
        redoStack.clear()
    }

    /**
     * 是否可以涂抹
     */
    private val canEraser: Boolean
        get() = undoStack.size > 0


    private fun setMagicPen(
        mtNativePen: NativeGLMagicPen,
        configPath: String,
        isFromAsset: Boolean,
        materialFolderPath: String?
    ) {
        val result = mtNativePen.nativeRenderInit(false, isFromAsset, configPath, materialFolderPath);
        val size = if (Build.MODEL == "PFGM00" || DeviceLevelAdapter.isLowDevice() || DeviceLevelAdapter.isMidDevice()) {
            400
        } else {
            800
        }
        mtNativePen.SetEraserBrushTextureMaxSize(size)
        // 特殊机型，底层要强制GLFinish
        // TODO: PDSM00 特殊处理，此接口底层去掉了
//        if ("PDSM00" == Build.MODEL) {
//            mtNativePen.SetEraserBrushGLSync(true)
//        }
        "setMagicPen result = $result".print("yyj")
    }

    private fun setMagicDoubleColorPen(
        mtNativePen: NativeGLMagicPen,
        configPath: String?,
        isFromAsset: Boolean,
        materialFolderPath: String?
    ) {
        val result = mtNativePen.nativeRenderInit(true, isFromAsset, configPath, materialFolderPath);
        "setMagicDoubleColorPen result = $result".print("yyj")
    }


    fun save2Bitmap(callback: (bitmap: Bitmap?, rect: FloatArray?) -> Unit) {
        if (magicPen.resultIsFixedEffect()) {
            val bitmap = magicPen.RenderForSave2Bitmap()
            val rect = FloatArray(4)
            rect[0] = 0f
            rect[1] = 0f
            rect[2] = bitmap.getWidth().toFloat()
            rect[3] = bitmap.getHeight().toFloat()
            callback.invoke(bitmap, rect)
        } else {
            val rect = magicPen.RenderForSave2Rect()
            if (rect != null && rect.size >= 3 && rect[2] > 0 && rect[3] > 0) {
                val bitmap = magicPen.RenderForSave2RectBitmap(rect)
//                // 坐标换算
//                rect[1] = imageBg.height - rect[1] - rect[3]
                callback.invoke(bitmap, rect)
            } else {
                callback.invoke(null, null)
            }
        }
    }

    /**
     * 获取当前渲染的图片
     */
    @GlThread
    fun getImage(): Bitmap {
        return TextureHelper.loadBitmapFromFbo(disFBOEntity)
    }

}