package com.commsource.studio.processor

import android.graphics.Bitmap
import android.view.MotionEvent
import androidx.annotation.CallSuper
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautymain.widget.gesturewidget.ImageMasker
import com.commsource.easyeditor.utils.opengl.GlThread
import com.commsource.studio.component.LayerScrollViewModel
import com.commsource.studio.component.PaintEraserViewModel
import com.commsource.studio.component.PaintSelectComponent
import com.commsource.studio.function.BrushParamsConfig
import com.commsource.studio.layer.BaseScrollLayer

open class PaintEffectProcessor : BaseEffectProcessor() {

    var maskGenerator: MaskGenerator? = null

    var isEraserMode = false

    // 当前使用的Mask
    private val curImageMasker = ImageMasker()

    open var enableBrushPaint = false
    open var isBrushOnlySupportTapEvent = false // 笔刷仅仅点击生效
    open var isEnableEraserParams = false // 启用后橡皮擦流量和涂抹流量会是倍数关系。
    open var enableBrushParams = false
    open var enableReverseEraserPaint = false // 橡皮擦模式也是涂抹的而不是擦除。
    open var maskGenMode = MaskGenerator.ONE_TOTAL
    open var isEnable = true
        set(value) {
            field = value
            maskGenerator?.isEnable = value
        }


    var scrollStateFun: ((isStart: Boolean, isEraser: Boolean) -> Unit)? = null
    var scrollStateFun3: ((isStart: Boolean, isEraser: Boolean, hasScrollIntoArea: Boolean) -> Unit)? = null

    var paintModeChangeFun: ((isEraser: Boolean) -> Unit)? = null


    @CallSuper
    open fun addObserver(fragment: Fragment) {
        maskGenerator?.attachFragment(fragment)

        //监听画笔大小改变。
        ViewModelProvider(fragment).get(PaintSelectComponent.PaintSelectViewModel::class.java)
            .apply {
                paintSizeChangeEvent.observe(fragment.viewLifecycleOwner, Observer {
                    maskGenerator?.setBrushPenSize(it)
                })
            }
        //监听橡皮擦。
        ViewModelProvider(fragment).get(PaintEraserViewModel::class.java).apply {
            selectEraserEvent.observe(fragment.viewLifecycleOwner, Observer {
                maskGenerator?.setBrushMode(it)
                isEraserMode = it
                onPaintModeChange(it)
                paintModeChangeFun?.invoke(it)
            })
        }
        // 监听手势
        ViewModelProvider(fragment).get(LayerScrollViewModel::class.java).apply {
            addScrollListener(object : BaseScrollLayer.ScrollListener {
                var hasScrollIntoArea = false
                override fun onStartSingleFingerScroll(viewPortX: Float, viewPortY: Float, motionEvent: MotionEvent) {
                    if (isEnable) {
                        onScrollStateChange(isEraserMode, false)
                        scrollStateFun?.invoke(true, isEraserMode)
                        hasScrollIntoArea = layer.inViewPort(viewPortX, viewPortY)
                        scrollStateFun3?.invoke(true, isEraserMode, hasScrollIntoArea)
                    }
                }

                override fun onSingleFingerScroll(viewPortX: Float, viewPortY: Float, motionEvent: MotionEvent) {
                    super.onSingleFingerScroll(viewPortX, viewPortY, motionEvent)
                    if (!hasScrollIntoArea) {
                        hasScrollIntoArea = layer.inViewPort(viewPortX, viewPortY)
                    }
                }

                override fun onStopSingleFinger(
                    viewPortX: Float,
                    viewPortY: Float,
                    isStartScroll: Boolean,
                    isMajorFingerUp: Boolean,
                    motionEvent: MotionEvent?
                ) {
                    if (isEnable) {
                        if (isMajorFingerUp) {
                            requestRender(true, before = {
                                maskGenerator?.fetchOverlayMaskBitmap(
                                    layer.viewPortWidth.toInt(),
                                    layer.viewPortHeight.toInt()
                                )?.also {
                                    curImageMasker.setMaskBitmap(it)
                                }
                                // 必须是单手指涂抹
                                scrollStateFun?.invoke(false, isEraserMode)
                                scrollStateFun3?.invoke(false, isEraserMode, hasScrollIntoArea)
                            })
                        }
                        // 不管是不是单个手指。
                        onScrollStateChange(isEraserMode, true)
                    }
                }
            })
        }
    }


    override fun onGlResourceInit() {
        super.onGlResourceInit()
        if (enableBrushPaint) {
            maskGenerator = MaskGenerator(this).apply {
                enableReverseEraserDrawPaint = enableReverseEraserPaint
                isNeedEraserParams = isEnableEraserParams
                enableBrushSizeParams = enableBrushParams
                setBrushGenerateMode(maskGenMode)
                prepare(imageData.image)
                brushModeOnlySupportTap = isBrushOnlySupportTapEvent
                BrushParamsConfig.fetchBrushParams(curModule)?.let {
                    setParams(it[0], it[1], it[2])
                }
                BrushParamsConfig.fetchGlSwellParams(curModule)?.let {
                    setSwellParams(it[0], it[1])
                }
            }

            maskFboEntity = if (maskGenMode == MaskGenerator.ONE_STEP) {
                maskGenerator?.fetchOneStepMaskFBO()
            } else {
                maskGenerator?.fetchOverlayMaskFBO()
            }
        }
    }


    override fun onGlResourceRelease() {
        super.onGlResourceRelease()
        maskGenerator?.release()
    }


    open fun onScrollStateChange(isEraser: Boolean, isStop: Boolean) {}

    @GlThread
    fun generateOnePaintMask(): Bitmap? {
        return maskGenerator?.fetchOneStepMaskBitmap(disFBOEntity.width, disFBOEntity.height)
    }

    @GlThread
    fun generateTotalMask(): Bitmap? {
        return maskGenerator?.fetchOverlayMaskBitmap(disFBOEntity.width, disFBOEntity.height)
    }

    fun getCurImageMasker(): ImageMasker {
        return curImageMasker
    }

    open fun onPaintModeChange(isEraser: Boolean) {}

}