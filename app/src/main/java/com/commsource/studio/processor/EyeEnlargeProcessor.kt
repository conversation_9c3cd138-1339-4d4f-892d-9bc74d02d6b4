package com.commsource.studio.processor

import android.graphics.Bitmap
import com.commsource.studio.layer.EyeEnlargeLayer
import com.meitu.core.processor.EyeZoomProcessor
import com.meitu.core.processor.RemoveBlackEyeProcessor
import com.meitu.core.types.NativeBitmap
import com.meitu.core.util.MixingUtil

/**
 * 淡化黑眼圈涂抹效果。
 */
class EyeEnlargeProcessor : CppPaintProcessor<EyeEnlargeLayer.EyesEnlargeEntity>() {
    /**
     * 涂抹确认回调，外面传入一张涂抹Mask。
     */
    override fun onPaintConfirm(lastEffectBitmap: Bitmap, eyesEnlargeEntity: EyeEnlargeLayer.EyesEnlargeEntity) {
        EyeZoomProcessor.renderProc(lastEffectBitmap, eyesEnlargeEntity.eyesRatio, eyesEnlargeEntity.radius, eyesEnlargeEntity.intensity)
    }
}