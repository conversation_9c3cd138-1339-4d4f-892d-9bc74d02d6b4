package com.commsource.studio.function.automanual

import android.graphics.Bitmap
import com.commsource.easyeditor.utils.opengl.FBOEntity
import com.commsource.easyeditor.utils.opengl.TextureHelper
import com.commsource.studio.AutoManualState
import com.commsource.studio.BitmapCache
import com.commsource.studio.MagnifyComponent
import com.commsource.studio.processor.CppPaintProcessor

/**
 * C++画笔的的处理操作模型。
 */
class CppManualProcessorModel(
    private var cppPaintProcessor: CppPaintProcessor<*>,
    var magnifyComponent: MagnifyComponent? = null
) : ManualProcessorModel(cppPaintProcessor) {

    override fun createManualState(): AutoManualState {
        return AutoManualState().apply {
            cppPaintProcessor.let {
                // 手动原图
                manualOriginBitmap = BitmapCache(it.srcFBOEntity.generateBitmap())
                manualEffectBitmap = BitmapCache(it.effectBitmap.run { copy(config, true) })
                manualMaskBitmap = BitmapCache(it.getCurImageMasker().maskBitmap?.run { copy(config, true) }, false)
            }
        }
    }

    override fun initManualProcessor(fboEntity: FBOEntity) {
        super.initManualProcessor(fboEntity)
        cppPaintProcessor.initOriginBitmap()
        cppPaintProcessor.getCurImageMasker().clearMask()
    }

    /**
     * 重置手动效果。
     */
    override fun resetManualProcessor(autoManualState: AutoManualState) {
        if (autoManualState.isAutoMode) {
            autoManualState.autoEffectBitmap?.restore()?.run {
                cppPaintProcessor.let {
                    TextureHelper.copyImgToFbo(this, it.srcFBOEntity)
                }
            }
        }else{
            autoManualState.manualOriginBitmap?.restore()?.run {
                cppPaintProcessor.let {
                    TextureHelper.copyImgToFbo(this, it.srcFBOEntity)
                }
            }
        }
        cppPaintProcessor.initOriginBitmap()
        cppPaintProcessor.getCurImageMasker().clearMask()
    }

    /**
     * 恢复手动Mask状态。
     */
    override fun applyManualMask(autoManualState: AutoManualState?) {
        if (autoManualState == null) {
            cppPaintProcessor.run {
                effectBitmap = oriBitmap.copy(Bitmap.Config.ARGB_8888, true)
                TextureHelper.copyImgToFbo(effectBitmap, disFBOEntity)
                getCurImageMasker().maskBitmap?.apply {
                    maskGenerator?.setMaskSource(this)
                    TextureHelper.clear(maskFboEntity)
                }
                render(disFBOEntity)
            }
            cppPaintProcessor.maskGenerator?.fetchEraserFBO()?.clear()
            cppPaintProcessor.getCurImageMasker().clearMask()
        } else {
            cppPaintProcessor.maskGenerator?.fetchEraserFBO()?.clear()
            autoManualState.manualMaskBitmap?.restore()?.run {
                cppPaintProcessor.maskGenerator?.setMaskSource(this)
                cppPaintProcessor.getCurImageMasker().setMaskBitmap(copy(config, true))
            }
            // 拷贝手动效果图和手动Mask图。
            autoManualState.manualEffectBitmap?.restore()?.run {
                cppPaintProcessor.let {
                    it.effectBitmap = copy(config, true)
                    TextureHelper.copyImgToFbo(this, it.disFBOEntity)
                    it.render(it.disFBOEntity)
                }
            }

        }
    }

}