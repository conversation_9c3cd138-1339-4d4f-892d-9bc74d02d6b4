package com.commsource.studio.function.bodyshape

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.*
import android.text.TextPaint
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import com.commsource.beautyplus.R
import com.commsource.studio.BpGestureDetector
import com.commsource.util.LanguageUtil
import com.commsource.util.RTLTool
import com.commsource.util.ResourcesUtils
import com.commsource.util.common.MathUtil
import com.commsource.util.dpf
import com.meitu.common.AppContext
import com.meitu.library.util.device.DeviceUtils
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.roundToInt

/**
 * 图片拉伸控件。
 */
class ImageStretchView : View {
    companion object {
        const val BAR_SCALE = 3 / 2f
    }

    /**
     * 初始情况下视口的上下Margin.
     */
    private val initialViewPortMarginTop = DeviceUtils.dip2fpx(30f)
    private val initialViewPortMarginBottom = DeviceUtils.dip2fpx(30f)

    /**
     * 最小绘制文字高度。
     */
    private val minDrawTextSize = DeviceUtils.dip2fpx(20f)

    /**
     * 图片绘制区域与view两边的padding，因为操作bar要超出图片区域。
     */
    private val viewPortPadding = DeviceUtils.dip2fpx(15f)

    /**
     * 图片画笔。
     */
    private val bitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    /**
     * 普通画笔。
     */
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    /**
     * 操作按钮Paint。
     */
    private val adjustBitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    var adjustBitmapTargetWidth = 32.dpf

    /**
     * 需要拉取的Bitmap。
     */
    var targetBitmap: Bitmap? = null
        set(value) {
            field = value
            lastConfirmBitmap = value!!
            stretchLimitProgress = Int.MAX_VALUE
            stretchProgress = 0
            calculateInitRect()
            calculateStretchRect()
            stretchLimitProgress = calculateLimitProgress()
            postInvalidate()
        }

    /**
     * 是否开启高度调整模式。
     * true: 高度调整模式，false: 宽度调整模式。
     */
    var adjustHeightMode: Boolean = true
        set(value) {
            field = value
            postInvalidate()
        }

    /**
     * 进入模块时的原始图片。
     */
    var originBitmap: Bitmap? = null
        set(value) {
            field = value
            originRectF.set(calculateBitmapRect(value!!))
        }

    private var originRectF = RectF()

    /**
     * 是否展示原图。
     */
    var isShowOrigin = false
        set(value) {
            field = value
            invalidate()
        }

    /**
     * 上一次确认的拉升图片。
     */
    private lateinit var lastConfirmBitmap: Bitmap

    /**
     * 初始情况下图片的绘制矩阵。
     */
    private val initViewPortRect = Rect()

    /**
     * 当前图片的DisRectF.
     */
    val currentViewPortRect = RectF()

    /**
     * 图片拉伸点1，范围（0，1），含义为[lastConfirmBitmap]的高度百分比。
     */
    private var stretchHeightArea = PointF(0.33f, 0.67f)

    private var stretchWidthArea = PointF(0.33f, 0.67f)

    /**
     * 拉升程度值，外部滑杆控制0～100。
     */
    var stretchProgress: Int = 0
        set(value) {
            field = value
            calculateStretchRect()
            invalidate()
        }

    /**
     * 拉伸最大值。
     */
    var stretchLimitProgress = Int.MAX_VALUE

    /**
     * 拉取图片的长度。
     */
    var stretchBitmapHeight = 0f

    /**
     * 拉取图片的宽度。
     */
    var stretchBitmapWidth = 0f

    /**
     * 拉取区域原本图片RectF.
     */
    private var stretchOriginBitmapRect = Rect()

    /**
     * 拉取区域结果图RectF.
     */
    private var stretchResultBitmapRect = RectF()

    /**
     * 拉取区域在viewPort中的位置。
     */
    private var stretchViewPortRect = RectF()
    private var adjustAreaRect = RectF()

    /**
     * 绘制时使用的rectF缓存。
     */
    private var drawBitmapRectF = Rect()
    private var drawViewPortRectF = RectF()

    /**
     * 操作Bar的bitmap。
     */
    private var heightAdjustBarBitmap =
        ResourcesUtils.getBitmapFromDrawable(R.drawable.edit_height_icon)

    private var widthAdjustBarBitmap =
        ResourcesUtils.getBitmapFromDrawable(R.drawable.edit_width_icon)

    /**
     * 提示文案
     */
    private val tipsText = ResourcesUtils.getString(R.string.height_drag_to_select_area)

    /**
     * 文字画笔。
     */
    private val textPaint = TextPaint().apply {
        color = Color.WHITE
        textSize =
            if (LanguageUtil.getLanguage(AppContext.context) == LanguageUtil.LANGUAGE_FR ||
                LanguageUtil.getLanguage(AppContext.context) == LanguageUtil.LANGUAGE_DE
            ) {
                DeviceUtils.dip2fpx(11f)
            } else {
                DeviceUtils.dip2fpx(15f)
            }
        textAlign = Paint.Align.CENTER
    }

    /**
     * 文字的中心偏移。
     */
    private val textBaseLineOffsetY =
        (textPaint.fontMetrics.bottom + textPaint.fontMetrics.top) / 2f

    /**
     * 手势监听。
     */
    private val gestureDetector = BpGestureDetector(GestureListener()).apply {
        setAllowLongPressMove(true)
    }

    /**
     * 确认拉升回调。
     */
    var stretchConfirmCallback: (() -> Unit)? = null

    /**
     * 是否展示操作bar。
     */
    var showAdjustBar = true

    var hasInitHeightStretchArea: Boolean = false

    var hasInitWidthStretchArea: Boolean = false


    constructor (context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    fun initStretchWidthArea(left: Float, right: Float) {
        stretchWidthArea.x = left
        stretchWidthArea.y = right
        calculateStretchRect()
        postInvalidate()
        hasInitWidthStretchArea = true
    }

    fun initStretchHeightArea(top: Float, bottom: Float) {
        stretchHeightArea.x = top
        stretchHeightArea.y = bottom
        calculateStretchRect()
        postInvalidate()
        hasInitHeightStretchArea = true
    }

    private fun calculateInitRect(bitmap: Bitmap? = originBitmap) {
        if (bitmap == null || width == 0 || height == 0) {
            return
        }
        calculateBitmapRect(bitmap).apply {
            initViewPortRect.left = left.toInt()
            initViewPortRect.right = right.toInt()
            initViewPortRect.top = top.toInt()
            initViewPortRect.bottom = bottom.toInt()
        }
    }

    private fun calculateBitmapRect(bitmap: Bitmap): RectF {
        if (width == 0 || height == 0) {
            return RectF()
        }
        return MathUtil.generateInscribeRect(
            RectF(
                viewPortPadding,
                initialViewPortMarginTop,
                width - viewPortPadding,
                height - initialViewPortMarginBottom
            ), bitmap.width, bitmap.height
        )
    }


    //TODO:
    private fun calculateStretchRect() {
        if (targetBitmap == null || width == 0 || height == 0) {
            return
        }

        // 拉伸区域原始高度
        val stretchOriginHeight =
            lastConfirmBitmap.height * abs(stretchHeightArea.x - stretchHeightArea.y)

        if (stretchOriginHeight < 1f) {
            return
        }
        // 拉伸总高度。(这个公式看不懂是啥意思)
        if (adjustHeightMode) {
            stretchBitmapHeight =
                lastConfirmBitmap.height / 1500f * (stretchProgress * stretchProgress * stretchProgress / (100 * 100 * 1.5f))


            if (stretchBitmapHeight + stretchOriginHeight < 0) {
                stretchBitmapHeight = -stretchOriginHeight
            }

            MathUtil.generateInscribeRect(
                RectF(
                    initViewPortRect.left.toFloat(),
                    0f,
                    initViewPortRect.right.toFloat(),
                    height.toFloat()
                ), lastConfirmBitmap.width, (lastConfirmBitmap.height + stretchBitmapHeight).toInt()
            ).apply {
                currentViewPortRect.left = left
                currentViewPortRect.right = right
                currentViewPortRect.top = top
                currentViewPortRect.bottom = bottom
            }

            // 被拉伸区域的原始位置。
            stretchOriginBitmapRect.apply {
                left = 0
                right = lastConfirmBitmap.width
                top =
                    (lastConfirmBitmap.height * stretchHeightArea.x.coerceAtMost(stretchHeightArea.y)).toInt()
                bottom = (top + stretchOriginHeight).toInt()
            }
            // 被拉伸区域拉伸后的位置。
            stretchResultBitmapRect.apply {
                left = 0f
                right = lastConfirmBitmap.width.toFloat()
                top =
                    (lastConfirmBitmap.height * stretchHeightArea.x.coerceAtMost(stretchHeightArea.y))
                bottom = (top + stretchOriginHeight + stretchBitmapHeight)
            }

            val viewPortBitmapRatio =
                currentViewPortRect.width().toFloat() / stretchOriginBitmapRect.width()

            // 拉伸区域展示在ViewPort上的位置。
            stretchViewPortRect.apply {
                left = currentViewPortRect.left
                right = currentViewPortRect.right
                top =
                    (currentViewPortRect.top + stretchOriginBitmapRect.top * viewPortBitmapRatio)
                bottom =
                    (top + (stretchOriginHeight + stretchBitmapHeight) * viewPortBitmapRatio)
                adjustAreaRect.set(this)
            }
        } else {
            stretchBitmapWidth =
                lastConfirmBitmap.width / 1500f * (stretchProgress * stretchProgress * stretchProgress / (100 * 100 * 1.5f))

            MathUtil.generateInscribeRect(
                RectF(
                    0f,
                    initViewPortRect.top.toFloat(),
                    width.toFloat(),
                    initViewPortRect.bottom.toFloat()
                ), (lastConfirmBitmap.width + stretchBitmapWidth).toInt(), lastConfirmBitmap.height
            ).apply {
                currentViewPortRect.left = left
                currentViewPortRect.right = right
                currentViewPortRect.top = top
                currentViewPortRect.bottom = bottom
            }
            // 拉伸区域原始宽度
            val stretchOriginWidth =
                lastConfirmBitmap.width * abs(stretchWidthArea.x - stretchWidthArea.y)
            // 被拉伸区域的原始位置。
            stretchOriginBitmapRect.apply {
                left =
                    (lastConfirmBitmap.width * stretchWidthArea.x.coerceAtMost(stretchWidthArea.y)).toInt()
                right = (left + stretchOriginWidth).toInt()
                top =
                    0
                bottom = lastConfirmBitmap.height
            }
            // 被拉伸区域拉伸后的位置。
            stretchResultBitmapRect.apply {
                left =
                    (lastConfirmBitmap.width * stretchWidthArea.x.coerceAtMost(stretchWidthArea.y))
                right = (left + stretchOriginWidth + stretchBitmapWidth)
                top = 0f
                bottom = lastConfirmBitmap.height.toFloat()
            }

            val viewPortBitmapRatio =
                currentViewPortRect.height().toFloat() / stretchOriginBitmapRect.height()

            // 拉伸区域展示在ViewPort上的位置。

            stretchViewPortRect.apply {
                left =
                    (currentViewPortRect.left + stretchOriginBitmapRect.left * viewPortBitmapRatio)
                right =
                    (left + (stretchOriginWidth + stretchBitmapWidth) * viewPortBitmapRatio)
                top =
                    currentViewPortRect.top
                bottom =
                    currentViewPortRect.bottom
                adjustAreaRect.set(this)
            }
        }

    }

    /**
     * 确认上一次的拉升结果。
     */
    fun confirmLastStretchBitmap() {
        if (stretchProgress == 0) {
            return
        }
        val confirmBitmap = generateCurrentBitmap()
        lastConfirmBitmap = confirmBitmap!!
        stretchProgress = 0
        stretchLimitProgress = calculateLimitProgress()
        stretchBitmapHeight = 0f
        stretchBitmapWidth = 0f
        if (adjustHeightMode) {
            if (stretchHeightArea.x < stretchHeightArea.y) {
                stretchHeightArea.x = stretchResultBitmapRect.top.toFloat() / confirmBitmap.height
                stretchHeightArea.y =
                    stretchResultBitmapRect.bottom.toFloat() / confirmBitmap.height
            } else {
                stretchHeightArea.y = stretchResultBitmapRect.top.toFloat() / confirmBitmap.height
                stretchHeightArea.x =
                    stretchResultBitmapRect.bottom.toFloat() / confirmBitmap.height
            }
        } else {
            if (stretchWidthArea.x < stretchWidthArea.y) {
                stretchWidthArea.x = stretchResultBitmapRect.left.toFloat() / confirmBitmap.width
                stretchWidthArea.y = stretchResultBitmapRect.right.toFloat() / confirmBitmap.width
            } else {
                stretchWidthArea.y = stretchResultBitmapRect.left.toFloat() / confirmBitmap.width
                stretchWidthArea.x = stretchResultBitmapRect.right.toFloat() / confirmBitmap.width
            }
        }

        calculateStretchRect()
    }

    /**
     * 刷新拉伸区域的比例
     */
    private fun refreshStretchArea() {
        val confirmWidth = (stretchBitmapWidth + lastConfirmBitmap.width).toInt()
        val confirmHeight = (stretchBitmapHeight + lastConfirmBitmap.height).toInt()
        if (adjustHeightMode) {
            if (stretchHeightArea.x < stretchHeightArea.y) {
                stretchHeightArea.x = stretchResultBitmapRect.top.toFloat() / confirmHeight
                stretchHeightArea.y =
                    stretchResultBitmapRect.bottom.toFloat() / confirmHeight
            } else {
                stretchHeightArea.y = stretchResultBitmapRect.top.toFloat() / confirmHeight
                stretchHeightArea.x =
                    stretchResultBitmapRect.bottom.toFloat() / confirmHeight
            }
        } else {
            if (stretchWidthArea.x < stretchWidthArea.y) {
                stretchWidthArea.x = stretchResultBitmapRect.left.toFloat() / confirmWidth
                stretchWidthArea.y = stretchResultBitmapRect.right.toFloat() / confirmWidth
            } else {
                stretchWidthArea.y = stretchResultBitmapRect.left.toFloat() / confirmWidth
                stretchWidthArea.x = stretchResultBitmapRect.right.toFloat() / confirmWidth
            }
        }
    }

    private fun calculateLimitProgress(): Int {
        if (initViewPortRect.width() == 0 || height == 0) {
            return Int.MAX_VALUE
        }
        if (adjustHeightMode) {
            val limitStretchHeight = lastConfirmBitmap.width * height / initViewPortRect.width()
                .toFloat() - lastConfirmBitmap.height
            return if (limitStretchHeight < 3) {
                0
            } else {
                (limitStretchHeight * 1500f * (100 * 100 * 1.5f) / lastConfirmBitmap.height).pow(1 / 3f)
                    .toInt()
            }
        } else {
            val limitStretchWidth = lastConfirmBitmap.height * width / initViewPortRect.height()
                .toFloat() - lastConfirmBitmap.width
            return if (limitStretchWidth < 3) {
                0
            } else {
                (limitStretchWidth * 1500f * (100 * 100 * 1.5f) / lastConfirmBitmap.width).pow(1 / 3f)
                    .toInt()
            }
        }

    }

    /**
     * 生成当前拉升图片。
     */
    fun generateCurrentBitmap(): Bitmap? {
        if (targetBitmap == null) {
            return null
        }
        return Bitmap.createBitmap(
            (stretchBitmapWidth + lastConfirmBitmap.width).toInt(),
            (stretchBitmapHeight + lastConfirmBitmap.height).toInt(),
            Bitmap.Config.ARGB_8888
        ).apply {
            drawStretchImage(Canvas(this), stretchResultBitmapRect, 0f, 0f, width.toFloat(), height.toFloat())
        }
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        calculateInitRect()
        calculateStretchRect()
        originBitmap?.let { originRectF.set(calculateBitmapRect(it)) }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (targetBitmap == null) {
            return
        }
        if (isShowOrigin) {
            canvas?.drawBitmap(originBitmap!!, null, originRectF, bitmapPaint)
        } else {
            drawStretchImage(
                canvas!!,
                stretchViewPortRect,
                currentViewPortRect.left,
                currentViewPortRect.top,
                currentViewPortRect.right,
                currentViewPortRect.bottom
            )

            if (showAdjustBar) {
                if (adjustHeightMode && hasInitHeightStretchArea) {
                    drawHeightAdjustBar(canvas, adjustAreaRect)
                } else if (!adjustHeightMode && hasInitWidthStretchArea) {
                    drawWidthAdjustBar(canvas, adjustAreaRect)
                }
            }
        }
    }

    /**
     * 绘制拉伸图。
     */
    private fun drawStretchImage(
        canvas: Canvas,
        drawRect: RectF,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float
    ) {
        if (adjustHeightMode) {
            // 绘制拉伸区域上半部分。
            drawBitmapRectF.set(0, 0, stretchOriginBitmapRect.right.toInt(), stretchOriginBitmapRect.top.toInt())
            val b1Bottom = drawRect.top.roundToInt()
            val b2Top = drawRect.bottom.roundToInt()
            drawViewPortRectF.set(drawRect.left, top, drawRect.right, b1Bottom.toFloat())
            canvas.drawBitmap(lastConfirmBitmap, drawBitmapRectF, drawViewPortRectF, bitmapPaint)
            drawViewPortRectF.set(drawRect.left, b1Bottom.toFloat(), drawRect.right, b2Top.toFloat())
            // 绘制拉伸区域
            canvas.drawBitmap(lastConfirmBitmap, stretchOriginBitmapRect, drawViewPortRectF, bitmapPaint)
            // 绘制拉伸区域下半部分。
            drawBitmapRectF.set(
                0,
                stretchOriginBitmapRect.bottom,
                stretchOriginBitmapRect.right,
                lastConfirmBitmap.height
            )
            drawViewPortRectF.set(drawRect.left, b2Top.toFloat(), drawRect.right, bottom)
            canvas.drawBitmap(lastConfirmBitmap, drawBitmapRectF, drawViewPortRectF, bitmapPaint)
        } else {
            // 绘制拉伸区域左半部分。
            drawBitmapRectF.set(0, 0, stretchOriginBitmapRect.left, stretchOriginBitmapRect.bottom)
            drawViewPortRectF.set(left, drawRect.top, drawRect.left, drawRect.bottom)
            canvas.drawBitmap(lastConfirmBitmap, drawBitmapRectF, drawViewPortRectF, bitmapPaint)
            // 绘制拉伸区域
            canvas.drawBitmap(lastConfirmBitmap, stretchOriginBitmapRect, drawRect, bitmapPaint)
            // 绘制拉伸区域右半部分。
            drawBitmapRectF.set(
                stretchOriginBitmapRect.right,
                0,
                lastConfirmBitmap.width,
                stretchOriginBitmapRect.bottom
            )
            drawViewPortRectF.set(drawRect.right, drawRect.top, right, drawRect.bottom)
            canvas.drawBitmap(lastConfirmBitmap, drawBitmapRectF, drawViewPortRectF, bitmapPaint)
        }

    }

    private fun drawHeightAdjustBar(canvas: Canvas, adjustRect: RectF) {
        if (isDragLine1 || isDragLine2 || isDragArea) {
            // 绘制中间阴影。
            paint.color = 0x80fb5986.toInt()
            paint.style = Paint.Style.FILL
            canvas.drawRect(adjustRect, paint)

//            if (adjustRect.height() > minDrawTextSize) {
//                // 绘制文字
//                canvas.drawText(
//                    tipsText,
//                    adjustRect.centerX().toFloat(),
//                    adjustRect.centerY() - textBaseLineOffsetY,
//                    textPaint
//                )
//            }
        }

        // 绘制上面的操作bar。
        if (RTLTool.isLayoutRtl()) {
            drawViewPortRectF.apply {
                left = (adjustRect.left - DeviceUtils.dip2fpx(5f))
                right = (left + adjustBitmapTargetWidth)
                top = (adjustRect.top - adjustBitmapTargetWidth / 2f)
                bottom = (adjustRect.top + adjustBitmapTargetWidth / 2f)
            }
        } else {
            drawViewPortRectF.apply {
                right = (adjustRect.right + DeviceUtils.dip2fpx(5f))
                left = (right - adjustBitmapTargetWidth)
                top = (adjustRect.top - adjustBitmapTargetWidth / 2f)
                bottom = (adjustRect.top + adjustBitmapTargetWidth / 2f)
            }
        }
        canvas.drawBitmap(heightAdjustBarBitmap, null, drawViewPortRectF, adjustBitmapPaint)

        // 绘制下面的操作bar。
        drawViewPortRectF.apply {
            top = (adjustRect.bottom - adjustBitmapTargetWidth / 2f)
            bottom = (adjustRect.bottom + adjustBitmapTargetWidth / 2f)
        }
        canvas.drawBitmap(heightAdjustBarBitmap, null, drawViewPortRectF, adjustBitmapPaint)

        // 绘制两条线。
        paint.color = 0x80ffffff.toInt()
        paint.strokeWidth = DeviceUtils.dip2fpx(1f)
        paint.style = Paint.Style.STROKE
        if (RTLTool.isLayoutRtl()) {
            canvas.drawLine(
                drawViewPortRectF.right.toFloat(),
                adjustRect.top.toFloat(),
                adjustRect.right.toFloat(),
                adjustRect.top.toFloat(),
                paint
            )
            canvas.drawLine(
                drawViewPortRectF.right.toFloat(),
                adjustRect.bottom.toFloat(),
                adjustRect.right.toFloat(),
                adjustRect.bottom.toFloat(),
                paint
            )
        } else {
            canvas.drawLine(
                adjustRect.left.toFloat(),
                adjustRect.top.toFloat(),
                drawViewPortRectF.left.toFloat(),
                adjustRect.top.toFloat(),
                paint
            )
            canvas.drawLine(
                adjustRect.left.toFloat(),
                adjustRect.bottom.toFloat(),
                drawViewPortRectF.left.toFloat(),
                adjustRect.bottom.toFloat(),
                paint
            )
        }
    }

    private fun drawWidthAdjustBar(canvas: Canvas, adjustRect: RectF) {


//        // 绘制左边的操作bar。
//        drawViewPortRectF.apply {
//            left = ((adjustRect.left - widthAdjustBarBitmap.width / 2f).toInt())
//            right = (left + widthAdjustBarBitmap.width).toInt()
//            top = (adjustRect.top - DeviceUtils.dip2fpx(20f)).toInt()
//            bottom = (top + widthAdjustBarBitmap.height).toInt()
//
//        }
//        canvas.drawBitmap(widthAdjustBarBitmap, null, drawViewPortRectF, adjustBitmapPaint)
//
//        // 绘制右边的操作bar。
//        drawViewPortRectF.apply {
//            left = (adjustRect.right - widthAdjustBarBitmap.width / 2f).toInt()
//            right = (left + widthAdjustBarBitmap.width).toInt()
//        }
//        canvas.drawBitmap(widthAdjustBarBitmap, null, drawViewPortRectF, adjustBitmapPaint)
//
//        // 绘制两条线。
//        paint.color = 0x80ffffff.toInt()
//        paint.strokeWidth = DeviceUtils.dip2fpx(1f)
//        paint.style = Paint.Style.STROKE
//
//        canvas.drawLine(
//            adjustRect.left.toFloat(),
//            drawViewPortRectF.bottom.toFloat(),
//            adjustRect.left.toFloat(),
//            adjustRect.bottom.toFloat(),
//            paint
//        )
//
//        canvas.drawLine(
//            adjustRect.right.toFloat(),
//            drawViewPortRectF.bottom.toFloat(),
//            adjustRect.right.toFloat(),
//            adjustRect.bottom.toFloat(),
//            paint
//        )

    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        gestureDetector.onTouchEvent(event)
        return true
    }

    /**
     * 是否有确认过图片。
     */
    fun hasConfirmBitmap(): Boolean {
        return targetBitmap != null && targetBitmap != lastConfirmBitmap
    }

    fun getLastConfirmBitmap(): Bitmap {
        return lastConfirmBitmap
    }

    /**
     * 重置。
     */
    fun reset() {
        targetBitmap = targetBitmap
    }

    private var isDragLine1 = false
    private var isDragLine2 = false

    private var isDragArea = false
    private var isDragLine3 = false
    private var isDragLine4 = false

    inner class GestureListener : BpGestureDetector.SimpleOnGestureListener() {
        /**
         * 拖拽区域。
         */
        private val dragArea = PointF()

        /**
         * 判断拖拽区域是否改变过。
         */
        private var hasScroll = false
        override fun onMajorFingerDown(downEvent: MotionEvent): Boolean {
            if (stretchProgress != 0) {
                refreshStretchArea()
            }
            if (adjustHeightMode) {
                downEvent.let {
                    dragArea.set(stretchHeightArea)
                    val line1Y =
                        currentViewPortRect.top + currentViewPortRect.height() * stretchHeightArea.x
                    if (line1Y - heightAdjustBarBitmap.width * BAR_SCALE / 2 < downEvent.y && line1Y + heightAdjustBarBitmap.width * BAR_SCALE / 2 > downEvent.y) {
                        isDragLine1 = true
                        return true
                    }
                    val line2Y =
                        currentViewPortRect.top + currentViewPortRect.height() * stretchHeightArea.y
                    if (line2Y - heightAdjustBarBitmap.width * BAR_SCALE / 2 < downEvent.y && line2Y + heightAdjustBarBitmap.width * BAR_SCALE / 2 > downEvent.y) {
                        isDragLine2 = true
                        return true
                    }

                    isDragArea = true
                }
                hasScroll = false
                return true
            } else {
                downEvent.let {
                    dragArea.set(stretchWidthArea)
                    val line1X =
                        currentViewPortRect.left + currentViewPortRect.width() * stretchWidthArea.x
                    if (line1X - widthAdjustBarBitmap.width * BAR_SCALE / 2 < downEvent.x && line1X + widthAdjustBarBitmap.width * BAR_SCALE / 2 > downEvent.x) {
                        isDragLine3 = true
                        return true
                    }
                    val line2X =
                        currentViewPortRect.left + currentViewPortRect.width() * stretchWidthArea.y
                    if (line2X - widthAdjustBarBitmap.width * BAR_SCALE / 2 < downEvent.x && line2X + widthAdjustBarBitmap.width * BAR_SCALE / 2 > downEvent.x) {
                        isDragLine4 = true
                        return true
                    }
                }
                hasScroll = false
                return true
            }

        }

        override fun onMajorScroll(
            downEvent: MotionEvent,
            moveEvent: MotionEvent,
            distanceX: Float,
            distanceY: Float
        ): Boolean {
            if (isDragLine1) {
                dragArea.x =
                    (dragArea.x - distanceY / currentViewPortRect.height()).coerceAtLeast(0f)
                        .coerceAtMost(1.0f)
            }
            if (isDragLine2) {
                dragArea.y =
                    (dragArea.y - distanceY / currentViewPortRect.height()).coerceAtLeast(0f)
                        .coerceAtMost(1.0f)
            }

            if (isDragArea) {
                if (dragArea.x < dragArea.y) {
                    val diff = dragArea.y - dragArea.x
                    if (dragArea.x - distanceY / currentViewPortRect.height() < 0) {
                        dragArea.x = 0f
                        dragArea.y = diff
                    } else if (dragArea.y - distanceY / currentViewPortRect.height() > 1) {
                        dragArea.y = 1f
                        dragArea.x = 1 - diff
                    } else {
                        dragArea.x =
                            (dragArea.x - distanceY / currentViewPortRect.height()).coerceAtLeast(0f)
                                .coerceAtMost(1.0f)
                        dragArea.y = dragArea.x + diff
                    }
                } else {
                    val diff = dragArea.x - dragArea.y
                    if (dragArea.y - distanceY / currentViewPortRect.height() < 0) {
                        dragArea.y = 0f
                        dragArea.x = diff
                    } else if (dragArea.x - distanceY / currentViewPortRect.height() > 1) {
                        dragArea.x = 1f
                        dragArea.y = 1 - diff
                    } else {
                        dragArea.y =
                            (dragArea.y - distanceY / currentViewPortRect.height()).coerceAtLeast(0f)
                                .coerceAtMost(1.0f)
                        dragArea.x = dragArea.y + diff
                    }
                }
            }
            if (isDragLine2 || isDragLine1 || isDragArea) {
                hasScroll = true
                // 拉伸区域展示在ViewPort上的位置。
                adjustAreaRect.apply {
                    top =
                        (currentViewPortRect.top + currentViewPortRect.height() * dragArea.x.coerceAtMost(
                            dragArea.y
                        ))
                    bottom =
                        (currentViewPortRect.top + currentViewPortRect.height() * dragArea.x.coerceAtLeast(
                            dragArea.y
                        ))
                }
                invalidate()
            }
            if (isDragLine3) {
                dragArea.x =
                    (dragArea.x - distanceX / currentViewPortRect.width()).coerceAtLeast(0f)
                        .coerceAtMost(1.0f)
            }
            if (isDragLine4) {
                dragArea.y =
                    (dragArea.y - distanceX / currentViewPortRect.width()).coerceAtLeast(0f)
                        .coerceAtMost(1.0f)
            }
            if (isDragLine3 || isDragLine4) {
                hasScroll = true
                // 拉伸区域展示在ViewPort上的位置。
                adjustAreaRect.apply {
                    left =
                        (currentViewPortRect.left + currentViewPortRect.width() * dragArea.x.coerceAtMost(
                            dragArea.y
                        ))
                    right =
                        (currentViewPortRect.left + currentViewPortRect.width() * dragArea.x.coerceAtLeast(
                            dragArea.y
                        ))
                }
                invalidate()
            }
            return super.onMajorScroll(downEvent, moveEvent, distanceX, distanceY)
        }

        override fun onMajorFingerUp(upEvent: MotionEvent?): Boolean {
            isDragLine1 = false
            isDragLine2 = false
            isDragLine3 = false
            isDragLine4 = false
            isDragArea = false
            if (hasScroll) {
                if (adjustHeightMode) {
                    stretchHeightArea.set(dragArea)
                } else {
                    stretchWidthArea.set(dragArea)
                }
                confirmLastStretchBitmap()
                stretchConfirmCallback?.invoke()
            }
            invalidate()
            return super.onMajorFingerUp(upEvent)
        }
    }


}