package com.commsource.studio.function.bodyshape.effect

import android.graphics.PointF
import android.util.Log
import com.pixocial.pixrendercore.node.PEFrame
import com.pixocial.pixrendercore.params.PEBodyBeautyManualParams

class ResizeEffect : BaseBodyEffect() {
    private val smallHeadParams by lazy {
        PEBodyBeautyManualParams()
    }

    override fun prepare(renderFrame: PEFrame) {
        smallHeadParams.type = 1
        smallHeadParams.changeOriImgSize = false
        renderFrame.setSingleParams(smallHeadParams)
        smallHeadParams.updateEffect()
    }

    fun updateCircle(center: PointF, radius: Float) {
        smallHeadParams.circleCenterX = center.x
        smallHeadParams.circleCenterY = center.y
        smallHeadParams.circleRadius = radius
        smallHeadParams.updateEffect()
    }

    fun updateCircleScale(scale: Float) {
        smallHeadParams.circleScale = scale
        smallHeadParams.updateEffect()
    }

    fun updateVerticalMove(value: Float) {
        smallHeadParams.circleVerticalMovePos = value
        smallHeadParams.updateEffect()
    }

    override fun release() {
        smallHeadParams.release()
    }


}