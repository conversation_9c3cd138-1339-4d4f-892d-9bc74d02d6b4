package com.commsource.studio.function.bodyshape.layer

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.RectF
import android.util.Log
import android.view.MotionEvent
import android.view.View
import com.commsource.beautyplus.R
import com.commsource.studio.BpGestureDetector
import com.commsource.studio.MatrixBox
import com.commsource.studio.layer.BaseSubLayer
import com.commsource.util.ResourcesUtils
import com.commsource.util.dpf
import kotlin.math.sqrt

class SmallHeadLayer(context: Context) : BaseSubLayer(context) {


    private var hasPositionChanged: Boolean = false
    private val center = PointF()
    private var radius = 50.dpf
    override fun onCreateView(): View {
        return SmallHeadView(context)
    }

    private val currentMotionPointF = PointF()
    private val lastMotionPointF: PointF = PointF()

    private val tempInvertMatrix = Matrix()
    private val centerNormalized = PointF()
    private var radiusNormalized = 0f

    private val canvasGestureMatrix = Matrix()

    //当前画面显示的矩形
    private val currentShowRectF = RectF()

    var isInit: Boolean = false
    private val srcRect = Rect()
    private val dstRect = RectF()

    // 0: 无操作 1: 缩放 2: 移动
    private var currentDragMode = 0

    private var isStartScroll = false

    private var isUserOperate: Boolean = false


    private var adjustBitmap: Bitmap? = ResourcesUtils.getBitmapFromDrawable(R.drawable.ic_body_shape_resize)

    private val paint = Paint().apply {
        isAntiAlias = true
        color = Color.WHITE
        style = Paint.Style.STROKE
        strokeWidth = 2.dpf
    }

    /**
     * 控件位置回调
     */
    var onCircleChanged: ((center: PointF, radius: Float, positionChanged: Boolean) -> Unit)? = null


    inner class SmallHeadView(context: Context) : View(context) {
        override fun onDraw(canvas: Canvas) {
            super.onDraw(canvas)
            canvas.save()

            onDrawCanvas(canvas)
            canvas.restore()
        }
    }

    /**
     * 控件不跟着画布缩放
     */
    override fun onCanvasGestureMatrixChange(matrixBox: MatrixBox) {
        if (!isEnable) {
            return
        }
        canvasGestureMatrix.set(matrixBox.matrix)
        if (isUserOperate) {
            hasPositionChanged = true
        }
        onCircleChanged(true)
    }

    private fun getMinRadius(): Float {
        return 18.dpf
    }

    /**
     * 获取最大半径
     */
    private fun getMaxRadius(): Float {
        return (canvasContainer.width - 40.dpf) / 2f
    }

    private fun onDrawCanvas(canvas: Canvas) {
        canvas.drawCircle(center.x, center.y, radius, paint)
        adjustBitmap?.let {
            srcRect.set(0, 0, it.width, it.height)
            val xLength = (radius + 20.dpf) / sqrt(2.0)
            val x = (center.x + xLength).toFloat()
            val y = (center.y + xLength).toFloat()
            dstRect.set(x - 12.dpf, y - 12.dpf, x + 12.dpf, y + 12.dpf)
            canvas.drawBitmap(it, srcRect, dstRect, null)
        }
        // Draw something
    }

    fun initDefault() {
        //UI要求的默认位置
        center.set(canvasContainer.validRectF.centerX(), canvasContainer.validRectF.centerY() - 40.dpf)
        checkBounds()
        checkSize()
        isInit = true
        onCircleChanged(false)

        layerView.invalidate()
    }

    fun initCircle(center: PointF, radius: Float) {
        this.center.set(center.x * canvasWidth, center.y * canvasHeight)
        val array = floatArrayOf(this.center.x, this.center.y)
        canvasContainer.canvasMatrix.matrix.mapPoints(array)
        this.center.set(array[0], array[1])
        this.radius = radius * canvasWidth * canvasContainer.canvasMatrix.getScale()
        checkBounds()
        checkSize()

        isInit = true
        onCircleChanged(false)

        layerView.invalidate()
    }

    private fun checkBounds() {
        if (center.x < canvasContainer.validRectF.left) {
            center.x = canvasContainer.validRectF.left
        }
        if (center.x > canvasContainer.validRectF.right) {
            center.x = canvasContainer.validRectF.right
        }
        if (center.y < canvasContainer.validRectF.top) {
            center.y = canvasContainer.validRectF.top
        }
        if (center.y > canvasContainer.validRectF.bottom) {
            center.y = canvasContainer.validRectF.bottom
        }
    }

    private fun checkSize() {
        if (radius < getMinRadius()) {
            radius = getMinRadius()
        }
        if (radius > getMaxRadius()) {
            radius = getMaxRadius()
        }
    }

    override fun onAttachToContainer() {
        super.onAttachToContainer()
    }

    override fun onCreateGestureListener(): BpGestureDetector.NestOnGestureListener {


        return object : BpGestureDetector.NestOnGestureListener() {
            override fun onMajorFingerDown(downEvent: MotionEvent): Boolean {

                if (isScaleType(downEvent.x, downEvent.y)) {
                    currentDragMode = 1
                } else if (isMoveType()) {
                    currentDragMode = 2
                }
                isUserOperate = true
                return true
            }

            override fun onMajorScroll(downEvent: MotionEvent, moveEvent: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
                if (currentDragMode == 0) {
                    return false
                }
                currentMotionPointF.set(moveEvent.x, moveEvent.y)
                if (!isStartScroll) {
                    lastMotionPointF.set(currentMotionPointF)
                    isStartScroll = true
                    return true
                }
                if (currentDragMode == 1) {
                    //根据手指滑动，放大或缩小圆
                    val diffx = currentMotionPointF.x - lastMotionPointF.x
                    val diffy = currentMotionPointF.y - lastMotionPointF.y
                    if (diffx < 0 && diffy < 0) {
                        radius += maxOf(diffx, diffy)
                    } else if (diffx > 0 && diffy > 0) {
                        radius += minOf(diffx, diffy)
                    } else {
                        radius += diffx + diffy
                    }


                } else if (currentDragMode == 2) {
                    center.x -= distanceX
                    center.y -= distanceY
                }
                checkSize()
                checkBounds()
                hasPositionChanged = true
//                onCircleChanged(currentDragMode == 2)
                layerView.invalidate()
                lastMotionPointF.set(currentMotionPointF)
                return true
            }

            override fun onMajorFingerUp(upEvent: MotionEvent?): Boolean {
                if (hasPositionChanged) {
                    onCircleChanged(false)
                    hasPositionChanged = false
                }
                currentDragMode = 0
                isUserOperate = false
                isStartScroll = false
                return super.onMajorFingerUp(upEvent)
            }
        }


    }

    private fun reverseCanvasPoint(pointF: PointF) {
        val array = floatArrayOf(pointF.x, pointF.y)
        canvasContainer.canvasMatrix.matrix.invert(tempInvertMatrix)
        tempInvertMatrix.mapPoints(array)
        pointF.set(array[0], array[1])
    }


    private fun onCircleChanged(changedPosition: Boolean) {
        if (!isInit) {
            return
        }
        Log.d("lyddd", "onCircleChanged:${canvasContainer.viewPortRectF}")
        currentShowRectF.set(canvasContainer.viewPortRectF)
        canvasGestureMatrix.mapRect(currentShowRectF)
        centerNormalized.set(
            (center.x - currentShowRectF.left) / currentShowRectF.width(),
            (center.y - currentShowRectF.top) / currentShowRectF.height()
        )

        radiusNormalized = radius / currentShowRectF.width()
        onCircleChanged?.invoke(centerNormalized, radiusNormalized, changedPosition)
    }

    private fun isScaleType(touchX: Float, touchY: Float): Boolean {
        val xLength = (radius + 20.dpf) / sqrt(2.0).toFloat()
        val x = (center.x + xLength).toFloat()
        val y = (center.y + xLength).toFloat()
        return x - 12.dpf < touchX && touchX < x + 12.dpf && y - 12.dpf < touchY && touchY < y + 12.dpf
    }

    private fun isMoveType(): Boolean {
        return center.x - radius < center.x && center.x < center.x + radius && center.y - radius < center.y && center.y < center.y + radius
    }


}