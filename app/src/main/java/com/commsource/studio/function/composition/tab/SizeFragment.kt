package com.commsource.studio.function.composition.tab

import android.graphics.Point
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnKeyListener
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentSizeBinding
import com.commsource.camera.util.XKeyboardDetector
import com.commsource.camera.util.animationTransition
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.config.ImageConfig
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.function.composition.CropMode
import com.commsource.studio.function.composition.CropViewModel
import com.commsource.studio.function.composition.MultiCropFragment
import com.commsource.util.BitmapUtils
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.setMarginCompat
import com.commsource.util.text
import com.commsource.util.visible
import com.meitu.common.AppContext

/**
 * 构图尺寸调整页面
 */
class SizeFragment : BaseBottomSubFragment() {

    val viewbinding by lazy { FragmentSizeBinding.inflate(layoutInflater) }

    val studioViewModel by lazy { ViewModelProvider(ownerActivity)[ImageStudioViewModel::class.java] }

    val cropViewModel by lazy { ViewModelProvider(requireParentFragment())[CropViewModel::class.java] }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return viewbinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        //键盘高度变更
        studioViewModel.keyboardHeightEvent.observe(viewLifecycleOwner) {
            if (it == 0) {
                viewbinding.etWide.clearFocus()
                viewbinding.etHigh.clearFocus()
                showConfirm(false)
                if (fromConfirm) {
                    //尺寸确认逻辑
                    val widthText = viewbinding.etWide.text
                    //非空一定是保护在范围内的 在文本变更动态保护
                    if (TextUtils.isEmpty(widthText)) {
                        parentFragment?.takeIf { it is MultiCropFragment }?.let {
                            it as MultiCropFragment
                            val size = it.calculateCropOutputSize()
                            correctSize(size)
                        }
                    }
                    //上报保存到调整控件 确认输出尺寸
                    parentFragment?.takeIf { it is MultiCropFragment }?.let {
                        it as MultiCropFragment
                        var width = viewbinding.etWide.text.toString().toInt()
                        var height = viewbinding.etHigh.text.toString().toInt()
                        val minSize = calculateRatioSize(20)
                        //最小值保护
                        if (width < minSize.x || height < minSize.y) {
                            parentFragment?.takeIf { it is MultiCropFragment }?.let {
                                it as MultiCropFragment
                                val size = it.calculateCropOutputSize()
                                correctSize(size)
                            }
                            return@let
                        }
                        width = viewbinding.etWide.text.toString().toInt()
                        height = viewbinding.etHigh.text.toString().toInt()
                        it.confirmInputSize(Point(width, height))
                    }
                } else {
                    //不是确认 恢复之前的数值
                    parentFragment?.takeIf { it is MultiCropFragment }?.let {
                        it as MultiCropFragment
                        val size = it.calculateCropOutputSize()
                        correctSize(size)
                    }
                }
            } else {
                fromConfirm = false
                showConfirm(true)
            }
        }
        //裁剪模式切换
        cropViewModel.cropModeEvent.observe(viewLifecycleOwner) {
            if (it.cropMode == CropMode.Size) {
                parentFragment?.takeIf { it is MultiCropFragment }?.let {
                    it as MultiCropFragment
                    val size = it.calculateCropOutputSize()
                    if (size.x == 0 || size.y == 0) {
                        studioViewModel.imageData.image.takeIf { BitmapUtils.isAvailableBitmap(it) }
                            ?.let {
                                viewbinding.etHigh.setText("${it.height}")
                                viewbinding.etWide.setText("${it.width}")
                            }
                    } else {
                        viewbinding.etHigh.setText("${size.y}")
                        viewbinding.etWide.setText("${size.x}")
                    }
                }
                val minSize = calculateRatioSize(20)
                val maxSize = calculateRatioSize()
                viewbinding.tvDes.text =
                    "${R.string.t_range.text()}:${R.string.t_width.text()}:${minSize.x}-${maxSize.x}px\t${R.string.t_height.text()}:${minSize.y}-${maxSize.y}px"
            }
        }

        //确认尺寸设定
        viewbinding.ifvConfirm.setOnClickListener {
            //收起尺寸
            fromConfirm = true
            XKeyboardDetector.hideSoftKeyboard(viewbinding.root)
            viewbinding.etHigh.clearFocus()
            viewbinding.etWide.clearFocus()
        }

        val onKeyListener = object : OnKeyListener {
            override fun onKey(p0: View?, p1: Int, p2: KeyEvent?): Boolean {
                when (p1) {
                    KeyEvent.KEYCODE_ENTER -> {
                        viewbinding.ifvConfirm.performClick()
                        return true
                    }
                }
                return false
            }
        }
        viewbinding.etHigh.setOnKeyListener(onKeyListener)
        viewbinding.etWide.setOnKeyListener(onKeyListener)

        //输入框宽度变化调整
        viewbinding.etWide.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                if (viewbinding.etWide.isFocused && studioViewModel.keyboardHeightEvent.value != 0) {
                    p0?.let {
                        val widthText = it.toString()
                        if (TextUtils.isEmpty(widthText)) {
                            viewbinding.etHigh.setText("")
                            return@let
                        }
                        val maxSize = calculateRatioSize()
                        try {
                            val width = widthText.toLong()
                            if (width > maxSize.x) {
                                correctSize(maxSize)
                                viewbinding.etWide.setSelection(viewbinding.etWide.length())
                                return
                            }
                            val height = (width / getRatio()).toLong()
                            viewbinding.etHigh.setText("$height")
                        } catch (e: Exception) {
                            //过限保护
                            correctSize(maxSize)
                            viewbinding.etWide.setSelection(viewbinding.etWide.length())
                        }
                    }
                }
            }

            override fun afterTextChanged(p0: Editable?) {

            }
        })

        //输入框高度变化调整
        viewbinding.etHigh.addTextChangedListener(object : TextWatcher {

            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                if (viewbinding.etHigh.isFocused && studioViewModel.keyboardHeightEvent.value != 0) {
                    p0?.let {
                        val heightText = it.toString()
                        if (TextUtils.isEmpty(heightText)) {
                            viewbinding.etWide.setText("")
                            return@let
                        }
                        val maxSize = calculateRatioSize()
                        try {
                            val height = heightText.toLong()
                            if (height > maxSize.y) {
                                correctSize(maxSize)
                                viewbinding.etHigh.setSelection(viewbinding.etHigh.length())
                                return
                            }
                            val width = (height * getRatio()).toLong()
                            viewbinding.etWide.setText("$width")
                        } catch (e: Exception) {
                            //过限保护
                            correctSize(maxSize)
                            viewbinding.etHigh.setSelection(viewbinding.etHigh.length())
                        }
                    }
                }
            }

            override fun afterTextChanged(p0: Editable?) {

            }

        })
    }

    /**
     * 标记是否用户点击确认做 确认打勾的处理
     */
    private var fromConfirm = false

    /**
     * 矫正最大尺寸
     */
    private fun correctSize(size: Point) {
        viewbinding.etWide.setText("${size.x}")
        viewbinding.etHigh.setText("${size.y}")
    }

    /**
     * 计算最大尺寸
     */
    fun calculateRatioSize(expectSize: Int = ImageConfig.getImageQualitys(AppContext.context)): Point {
        val ratio = getRatio()
        val limitSize =
            if (expectSize > 0) expectSize else ImageConfig.getImageQualitys(ImageConfig.QUALITY_ULTRA)
        if (ratio >= 1f) {
            val width = limitSize
            val height = (limitSize / ratio).toInt()
            return Point(width, height)
        } else {
            val height = limitSize
            val width = (limitSize * ratio).toInt()
            return Point(width, height)
        }
    }

    /**
     * 获取当前尺寸宽高比
     */
    fun getRatio(): Float {
        parentFragment?.takeIf { it is MultiCropFragment }?.let {
            it as MultiCropFragment
            val size = it.calculateCropOutputSize()
            return size.x.toFloat() / size.y
        }
        return 1f
    }

    /**
     * 显示size确认按钮
     */
    private var showConfirm: Boolean = false
    private fun showConfirm(showConfirm: Boolean) {
        if (this.showConfirm == showConfirm) {
            return
        }
        this.showConfirm = showConfirm
        if (showConfirm) {
            viewbinding.ifvConfirm.alpha = 0f
            viewbinding.ifvConfirm.visible()
        }
        viewbinding.root.animationTransition(duration = 200) {
            if (showConfirm) {
                //展示面板瞬间 定位到输入末尾
                if (viewbinding.etWide.isFocused) {
                    viewbinding.etWide.setSelection(viewbinding.etWide.length())
                }
                if (viewbinding.etHigh.isFocused) {
                    viewbinding.etHigh.setSelection(viewbinding.etHigh.length())
                }
                viewbinding.clContent.setMarginCompat(end = 41.dp)
                viewbinding.ifvConfirm.alpha = 1f
            } else {
                viewbinding.clContent.setMarginCompat(end = 0)
                viewbinding.ifvConfirm.alpha = 0f
            }
            onTransitionEnd = {
                if (!showConfirm) {
                    viewbinding.ifvConfirm.gone()
                }
            }
        }

    }

}