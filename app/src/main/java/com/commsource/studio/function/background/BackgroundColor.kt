package com.commsource.studio.function.background

import androidx.annotation.Keep
import java.io.Serializable

/**
 * 背景纹理
 * <AUTHOR> meitu - 3/23/21
 */
@Keep
data class BackgroundColor(
    var id: String,//id
    val icon: String,//对应icon Android目前使用自主绘制显示item
    val type: Int,//类型
    val isPaid: Boolean,// 是否付费
    val startPoint: String,//x1,y1 逗号分割
    val endPoint: String,//x2,y2 逗号分割
    val colors: Array<String>,//颜色数组 不带#
    val locations: FloatArray,//渐变控件locations
    val isInternal: Boolean = false,
    val path: String
) : Serializable//纹理透明度