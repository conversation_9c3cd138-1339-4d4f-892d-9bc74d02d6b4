package com.commsource.studio.function

import com.commsource.studio.function.background.BackgroundType
import com.commsource.studio.layer.BackgroundLayer
import com.commsource.studio.sub.AdjustEffectEnum
import com.commsource.studio.text.TextGroupParam
import com.google.gson.annotations.SerializedName
import java.util.*

/**
 * 针对用户绘制图片的JsonBean。
 */
class RecipeJsonBean {
    /**
     * 图像位置。
     */
    @SerializedName("imagePosition")
    var imagePositionJson: ImagePositionJsonBean? = null

    /**
     * 背景层信息。
     */
    @SerializedName("background")
    var backgroundJson: BackgroundJsonBean? = null

    /**
     * 贴纸。
     */
    @SerializedName("stickers")
    var stickersJson = LinkedList<StickerJsonBean>()

    /**
     * 涂鸦笔
     */
    @SerializedName("doodles")
    var doodlesJson = LinkedList<DoodleJsonBean>()

    /**
     * 文字
     */
    @SerializedName("texts")
    var textsJson = LinkedList<TextJsonBean>()

    /**
     * 非固化的调色效果。
     */
    @SerializedName("colours")
    var colourEffectsJson = LinkedList<ColourJsonBean>()

}

/**
 * 贴纸Json。
 */
class StickerJsonBean {
    /**
     * 帖子素材ID。
     */
    @SerializedName("stickerId")
    var stickerId = 0

    /**
     * 图像大小与画幅比例,这边指的是图像宽度与画布宽度的比例。
     */
    @SerializedName("ratio")
    var ratio = 1f

    /**
     * 中心点位置， 归一化参数，画布宽高位置的比例，左上角为0。
     */
    @SerializedName("centerX")
    var centerX = 0f

    @SerializedName("centerY")
    var centerY = 0f

    /**
     * 旋转角度，角度值。
     */
    @SerializedName("angle")
    var angle = 0f

    /**
     * 层级。
     */
    @SerializedName("layerNumber")
    var layerNumber = 0

    /**
     * 是否翻转。
     */
    @SerializedName("isFlip")
    var isFlip = false
}

/**
 * 文字Json。
 */
class TextJsonBean {
    /**
     * 文字参数
     */
    @SerializedName("textParam")
    var textGroupParam: TextGroupParam? = null

    /**
     * 图片所使用模版的Id。
     */
    @SerializedName("textTemplateId")
    var textTemplateId: String? = ""

    /**
     * 图像大小与画幅比例,这边指的是图像宽度与画布宽度的比例。
     */
    @SerializedName("ratio")
    var ratio = 1f

    /**
     * 中心点位置， 归一化参数，画布宽高位置的比例，左上角为0。
     */
    @SerializedName("centerX")
    var centerX = 0f

    @SerializedName("centerY")
    var centerY = 0f

    /**
     * 旋转角度，角度值。
     */
    @SerializedName("angle")
    var angle = 0f

    /**
     * 层级。
     */
    @SerializedName("layerNumber")
    var layerNumber = 0

    /**
     * 图片地址。
     */
    @SerializedName("imageUrl")
    var imageUrl = ""

    /**
     * 文字原始尺寸。
     */
    @SerializedName("width")
    var width = 0

    @SerializedName("height")
    var height = 0

    /**
     * 是否需要付费。
     */
    @SerializedName("isNeedPaid")
    var isNeedPaid = false

}

/**
 * 涂鸦笔Json。
 */
class DoodleJsonBean {
    /**
     * 涂鸦笔的ID。
     */
    var doodleIds: IntArray? = null

    /**
     * 贴纸大小与画幅比例,这边指的是图像宽度与画布宽度的比例。
     */
    @SerializedName("ratio")
    var ratio = 1f

    /**
     * 中心点位置， 归一化参数，画布宽高位置的比例，左上角为0。
     */
    @SerializedName("centerX")
    var centerX = 0f

    @SerializedName("centerY")
    var centerY = 0f

    /**
     * 旋转角度，角度值。
     */
    @SerializedName("angle")
    var angle = 0f

    /**
     * 层级。
     */
    @SerializedName("layerNumber")
    var layerNumber = 0

    /**
     * 图片地址。
     */
    @SerializedName("imageUrl")
    var imageUrl = ""

    /**
     * 是否翻转。
     */
    @SerializedName("isFlip")
    var isFlip = false
    /**
     * 是否需要付费。
     */
    @SerializedName("isNeedPaid")
    var isNeedPaid = false
}

/**
 * 调色效果Json
 */
class ColourJsonBean {
    /**
     * 效果枚举[AdjustEffectEnum]中的Id.
     */
    @SerializedName("effectId")
    var colourEffectId = 0

    /**
     * 效果程度值。
     */
    @SerializedName("alpha")
    var effectAlpha = 0f
}

/**
 * 图像层位置Json。
 */
class ImagePositionJsonBean {
    /**
     * 贴纸大小与画幅比例,这边指的是图像宽度与画布宽度的比例。
     */
    @SerializedName("ratio")
    var ratio = 1f

    /**
     * 中心点位置， 归一化参数，画布宽高位置的比例，左上角为0。
     */
    @SerializedName("centerX")
    var centerX = 0f

    @SerializedName("centerY")
    var centerY = 0f

    /**
     * 旋转角度，角度值。
     */
    @SerializedName("angle")
    var angle = 0f

    override fun toString(): String {
        return "ImagePositionJsonBean(ratio=$ratio, centerX=$centerX, centerY=$centerY, angle=$angle)"
    }
}

/**
 * 背景Json
 */
class BackgroundJsonBean {
    /**
     * 背景的宽高比
     */
    @SerializedName("aspectRatio")
    var aspectRatio = 0f

    /**
     * 背景颜色。
     */
    @SerializedName("color")
    var backgroundType:BackgroundType = BackgroundLayer.DEFAULT_BACKGROUND_TYPE

    override fun toString(): String {
        return "BackgroundJsonBean(aspectRatio=$aspectRatio, color=$backgroundType)"
    }
}