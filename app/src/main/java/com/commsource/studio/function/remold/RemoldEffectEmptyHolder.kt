package com.commsource.studio.function.remold

import android.content.Context
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.studio.effect.remold.RemoldEntity
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

/**
 * 在一级界面子功能的VH。
 */
class RemoldEffectEmptyHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<RemoldEntity>(context, parent, R.layout.item_remold_empty_layout) {

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<RemoldEntity>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)

    }
}