package com.commsource.studio.function.hair.style

import android.content.Context
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHairStyleBinding
import com.commsource.repository.child.hair.HairStyle
import com.commsource.statistics.ABTestManager
import com.commsource.statistics.Meepo
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.gone
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder

/**
 * 美发-发型
 */
class HairStyleViewHolder(context: Context, viewGroup: ViewGroup) :
    BaseViewHolder<HairStyle>(context, viewGroup, R.layout.item_hair_style) {

    val mViewBinding: ItemHairStyleBinding by lazy {
        DataBindingUtil.bind<ItemHairStyleBinding>(
            itemView
        ) as ItemHairStyleBinding
    }

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<HairStyle>?,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        item?.entity?.let {
            if (payloads == null || payloads.isEmpty()) {
                mViewBinding.tc.loadThumbnail {
                    load(it.icon)
                }
                mViewBinding.tvName.text = it.name
            }
            if (item.entity.isCrimps()) {
                mViewBinding.ivAiLabel.gone()
            } else {
                mViewBinding.ivAiLabel.gone()
            }

            //选中状态
            if (item.isSelect) {
                mViewBinding.flSelectMask.visible()
            } else {
                mViewBinding.flSelectMask.gone()
            }

            if (ABTestManager.isNeedHideVipIcon(ABTestManager.TYPE_PHOTO_EDIT, true)) {
                mViewBinding.ivRightCorner.gone()
            } else {
                if (item.entity.isMember) {
                    mViewBinding.ivRightCorner.visible()
                } else {
                    mViewBinding.ivRightCorner.gone()
                }
            }

        }
    }
}