package com.commsource.studio.function.hair.process.style

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.text.TextUtils
import com.commsource.beautyplus.util.PathUtil
import com.commsource.repository.child.hair.HairStyle
import com.commsource.studio.function.hair.cache_stack.HairCacheStack
import com.commsource.studio.function.hair.process.HairProcess
import com.commsource.util.CloudStorageTool
import com.commsource.util.ErrorNotifier
import com.commsource.util.ExternalStorageUtils
import com.commsource.util.LOGV
import com.commsource.util.LoggerUtils
import com.commsource.util.ThreadExecutor
import com.commsource.util.UIHelper
import com.commsource.util.V
import com.commsource.util.common.BitmapUtil
import com.commsource.util.coroutine.launch
import com.commsource.util.getChannelMode
import com.commsource.util.saveDebug
import com.commsource.util.urlLoadBitmapSync
import com.meitu.common.AppContext
import com.meitu.http.api.OnlineProcessApi
import com.meitu.http.kotex.api
import com.meitu.http.kotex.response
import com.meitu.http.kotex.syncResponse
import com.meitu.library.util.bitmap.BitmapUtils
import com.meitu.library.util.net.NetUtils
import com.pixocial.pixrendercore.node.PEDRTypeEnum
import com.pixocial.pixrendercore.node.PERenderImage
import com.pixocial.pixrendercore.params.PEMixMaskParams
import com.pixocial.pixrendercore.tools.PEImageConvertUtils
import java.util.UUID
import kotlin.random.Random

/**
 * 卷发
 */
class CrimpsHairProcess(private val hairProcess: HairProcess) {

    //key - type  value - 图片缓存路径
    private val netImageCache = mutableMapOf<String, String>()

    private lateinit var mixMaskParams: PEMixMaskParams

    private var process: Int = 0

    fun onRelease() {
        if (this::mixMaskParams.isInitialized) {
            mixMaskParams.release()
        }
        netImageCache.clear()
    }

    fun applyCrimps(entity: HairStyle, isCuringEffect: Boolean, callback: () -> Unit?) {
        //TODO  记得删除
//        if (AppTools.isDebug()) {
//            val name = getImageName(0)
//            val path = "${HairCacheStack.CACHE_PATH}/$name"
//            netImageCache[name] = path
//        }

        if (isCuringEffect) {
            hairProcess.originBitmap?.run {
                hairProcess.peFrame.prepare()
            }
            netImageCache.clear()
        }
        hairProcess.peFrame.getOriginImage()?.run {
            if (netImageCache[getImageName(entity.crimpsType)] != null) {
                ThreadExecutor.executeSlowTask("cacheBitmap") {
                    val imageBitmap =
                        BitmapFactory.decodeFile(netImageCache[getImageName(entity.crimpsType)])
                    val maskImageBitmap =
                        BitmapFactory.decodeFile(netImageCache[getMaskImageName(entity.crimpsType)])
                    hairProcess.manualStyleProcess.cacheImage(imageBitmap, maskImageBitmap)
                    hairProcess.requestRender(before = {
                        updateParams(true, imageBitmap, maskImageBitmap, entity.crimpsType)
                    }, after = {
                        callback?.invoke()
                        hairProcess.styleHairProcess.afterRenderPosition =
                            hairProcess.styleHairProcess.position
                    })
                    requestSueecss()
                }
            } else {
                process = 0
                doNetworkCrimps(this, entity, callback)
            }
        }
    }

    private fun doNetworkCrimps(
        srcImage: PERenderImage,
        hairStyle: HairStyle,
        callback: () -> Unit?
    ) {

        val bitmap = PEImageConvertUtils.bitmapFromPEBaseImage(srcImage.readBaseImage(), true)!!
        if (!BitmapUtils.isAvailableBitmap(bitmap)) {
            requestFail()
        } else {
            showLoading()
            // 判断是否需要压缩图片尺寸
            val resizeBitmap = resizeBitmap(bitmap)
            updateCrimpsImage(resizeBitmap,
                success = { url ->
                    hairProcess.hairViewModel.launch({
                        onlineTask(url, hairStyle, callback)
                    })
                }, fail = {
                    "图片上送服务器失败 $it".V()
                    requestFail()
                })

        }
    }

    private fun onlineTask(url: String, hairStyle: HairStyle, callback: () -> Unit?) {
        OnlineProcessApi::class.java.api()
            .onlineHairStyle(hairStyle.crimpsName(), url)
            .responseOnBackground()
            .synRequest()
            .response {
                onNext = {
                    if (hairProcess.hairViewModel.aiLoadingEvent.value != LoadStatus.CANCEL) {
                        if (!TextUtils.isEmpty(it.data.taskId)) {
                            loopQueryMsg(it.data.taskId, 0, hairStyle, callback)
                        } else {
                            requestFail()
                        }
                    }
                }
                onError = {
                    it.printStackTrace()
                    onNetError(it)
                    requestFail()
                }
            }

    }

    private fun loopQueryMsg(
        msgId: String,
        loopCount: Int,
        hairStyle: HairStyle,
        callback: () -> Unit?
    ) {
        val labResponse = OnlineProcessApi::class.java.api()
            .queryHairStyleImage(msgId).syncResponse { }

        when (labResponse?.data?.status) {
            "success" -> {
                labResponse.data.resultUrl.let { url ->
                    com.commsource.util.BitmapUtils.downBitmapProgress(
                        url,
                        onProgress = { percent, call ->
                            if (hairProcess.hairViewModel.aiLoadingEvent.value != LoadStatus.CANCEL) {
                                updateProgress(3, percent)
                            }
                        },
                        onComplete = { bitmap ->

                            if (hairProcess.hairViewModel.aiLoadingEvent.value != LoadStatus.CANCEL) {
                                if (BitmapUtils.isAvailableBitmap(bitmap)) {
                                    "图片生成成功 $url  mask ${labResponse.data.maskUrl}".V()
                                    val maskImage = labResponse.data.maskUrl.urlLoadBitmapSync()
                                    cacheImage(maskImage, hairStyle.crimpsType, true)
                                    hairProcess.requestRender(before = {
                                        updateParams(false, bitmap, maskImage, hairStyle.crimpsType)
                                    }, after = {

                                        mixMaskParams.setMaxEffectImage(
                                            hairProcess.peFrame.getImage().readBaseImage(),
                                            true
                                        )

                                        mixMaskParams.needUpdateMaxEffectImage = true
                                        mixMaskParams.updateEffect()
                                        hairProcess.peFrame.process()

                                        PEImageConvertUtils.bitmapFromPEBaseImage(
                                            hairProcess.peFrame.getImage().readBaseImage(), false
                                        )?.run {
//                                            this.saveDebug("混合后效果图")
                                            cacheImage(this, hairStyle.crimpsType, false)
                                            hairProcess.manualStyleProcess.cacheImage(this, maskImage)
                                        }

                                        callback?.invoke()
                                        hairProcess.styleHairProcess.afterRenderPosition =
                                            hairProcess.styleHairProcess.position
                                    })
                                    requestSueecss()
                                } else {
                                    LoggerUtils.LOGE("图片生成失败")
                                    requestFail()
                                }
                            }
                        },
                        onError = {
                            requestFail()
                        }
                    )
                }
            }

            "running" -> {
                if (hairProcess.hairViewModel.aiLoadingEvent.value != LoadStatus.CANCEL) {
                    //重试
                    Thread.sleep(1000)
                    "轮询中...".V()
                    var count = loopCount + 1
                    updateProgress(2, -1.0f, count)
                    loopQueryMsg(msgId, count, hairStyle, callback)
                }
            }

            else -> {
                "轮询请求失败 ${labResponse?.data?.message}  ${labResponse?.data?.status}".V()
                requestFail()
            }
        }
    }

    private fun updateCrimpsImage(
        bitmap: Bitmap,
        success: ((url: String) -> Unit)? = null,
        fail: ((debugMsg: String) -> Unit)? = null
    ) {
        val savePath = PathUtil.getStudioCacheDir(
            AppContext.context,
            "cache_dir/"
        ) + "${UUID.randomUUID()}.jpg"

        val result = BitmapUtil.saveImageToDisk(
            bitmap,
            savePath,
            100,
            Bitmap.CompressFormat.JPEG
        )
        if (result) {
            CloudStorageTool.uploadTemp(savePath, onProgress = { progress ->
                val uploadProgress = progress / 100f
                updateProgress(1, uploadProgress)
            }, onError = { message ->
                fail?.invoke(message)
            }, onSuccess = { url ->
                success?.invoke(url)
            })
        } else {
            fail?.invoke("保存图片失败")
        }
    }

    /**
     * 更新进度 type 1 上传 2 生成 3 下载
     */
    private fun updateProgress(type: Int, updownProgress: Float, loopCount: Int = 0) {

        when (type) {
            1 -> {
                process = (updownProgress * 100 * 0.3).toInt()
//                "continue 上传 $process".LOGV()
            }

            2 -> {
                if (process <= 54) {
                    val tmpProgress = Random.nextInt(3, 7)
                    if (process + tmpProgress > 55 || loopCount == 5) {
                        process = 55
                    } else {
                        process += tmpProgress
                    }
                } else if (process <= 74) {
                    val tmpProgress = Random.nextInt(1, 3)
                    if (process + tmpProgress > 75 || loopCount == 10) {
                        process = 75
                    } else {
                        process += tmpProgress
                    }
                } else if (process <= 89) {
                    val tmpProgress = Random.nextInt(1, 3)
                    if (process + tmpProgress > 90) {
                        process = 90
                    } else {
                        process += tmpProgress
                    }
                }
            }

            3 -> {
//                "continue 下载2 $updownProgress".LOGV()
                process = 90 + (updownProgress * 100 * 0.1).toInt()
//                "continue 下载 $process".LOGV()
            }
        }

//        "continue 更新progress $process".LOGV()
        if (UIHelper.isMainThread()) {
            hairProcess.hairViewModel.aiProgressEvent.value = process
        } else {
            hairProcess.hairViewModel.aiProgressEvent.postValue(process)
        }
    }

    private fun requestFail() {
        if (UIHelper.isMainThread()) {
            hairProcess.hairViewModel.aiLoadingEvent.value = LoadStatus.FAILED
        } else {
            hairProcess.hairViewModel.aiLoadingEvent.postValue(LoadStatus.FAILED)
        }
    }

    private fun showLoading() {
        if (UIHelper.isMainThread()) {
            hairProcess.hairViewModel.aiLoadingEvent.value = LoadStatus.LOADING
            hairProcess.hairViewModel.aiProgressEvent.value = 0
        } else {
            hairProcess.hairViewModel.aiLoadingEvent.postValue(LoadStatus.LOADING)
            hairProcess.hairViewModel.aiProgressEvent.postValue(0)
        }
    }

    private fun requestSueecss() {
        if (UIHelper.isMainThread()) {
            hairProcess.hairViewModel.aiLoadingEvent.value = LoadStatus.SUCCEED
            hairProcess.hairViewModel.showManualEvent.value = true
        } else {
            hairProcess.hairViewModel.aiLoadingEvent.postValue(LoadStatus.SUCCEED)
            hairProcess.hairViewModel.showManualEvent.postValue(true)
        }
    }

    private fun onNetError(exception: Throwable) {
        "网络异常  ${!NetUtils.canNetworking(AppContext.context)}".LOGV()
        ErrorNotifier.showNetworkErrorToast()
    }

    private fun getImageName(crimpsType: Int) =
        "image-$crimpsType-crimps.jpg"

    private fun getMaskImageName(crimpsType: Int) =
        "image-mask-$crimpsType-crimps.jpg"

    private fun cacheImage(bitmap: Bitmap, crimpsType: Int, isMask: Boolean) {
        ThreadExecutor.executeSlowTask("cacheMaskBitmap") {
            val name = if (isMask) {
                getMaskImageName(crimpsType)
            } else {
                getImageName(crimpsType)
            }
            val path = "${HairCacheStack.CACHE_PATH}/$name"
            val result =
                ExternalStorageUtils.saveImage(
                    bitmap,
                    path,
                    true
                )
            if (result) {
                netImageCache[name] = path
            }
        }
    }

    fun hasCache(crimpsType: Int) = netImageCache[getImageName(crimpsType)] != null

    private fun resizeBitmap(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        if (width > 2048 || height > 2048) {
            val scale = 2048.0f / Math.max(width, height)
            val newWidth = (width * scale).toInt()
            val newHeight = (height * scale).toInt()
            return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
        }
        return bitmap
    }

    private fun updateParams(isCache: Boolean, maxImage: Bitmap, maskImage: Bitmap, type: Int) {
        if (!this::mixMaskParams.isInitialized) {
            mixMaskParams = PEMixMaskParams().apply {
                progress = 1.0f
            }
        }
        hairProcess.peFrame.setSingleParams(mixMaskParams)

        if (isCache) {
            mixMaskParams.setMaxEffectImage(
                PEImageConvertUtils.peBaseImageFromBitmap(
                    maxImage,
                    false
                )!!, true
            )
            mixMaskParams.needUpdateMaxEffectImage = true
            mixMaskParams.updateEffect()
            hairProcess.peFrame.process()
        } else {
            if(BitmapUtils.isAvailablePERenderImage(hairProcess.peFrame.getOriginImage())){
                //取出头发mask区域
                val maskResize2Ori = Bitmap.createScaledBitmap(
                    maskImage,
                    hairProcess.peFrame.getOriginImage().width,
                    hairProcess.peFrame.getOriginImage().height,
                    true
                )

                mixMaskParams.brushParams.resetMaskImage(
                    PEImageConvertUtils.peBaseImageFromBitmap(
                        maskResize2Ori,
                        false,
                        channelMode = maskResize2Ori.getChannelMode()
                    )!!,
                    true, 0, false, 1f
                )

                //混合后端效果图头发mask区域和原图其他区域为图B
                mixMaskParams.setMaxEffectImage(
                    PEImageConvertUtils.peBaseImageFromBitmap(
                        maxImage,
                        false
                    )!!, true
                )
                //设置笔刷mask
                mixMaskParams.needUpdateMaxEffectImage = true
                mixMaskParams.updateEffect()
                hairProcess.peFrame.process()
            }

        }
    }

    fun replaceFrameAndParam() {
        LoggerUtils.LOGV("重置PeFrame和Param")
        hairProcess.run {
            replaceFrame()
            if (this@CrimpsHairProcess::mixMaskParams.isInitialized) {
                hairProcess.replaceParams(mixMaskParams)
            }
        }
    }

    fun cacheUnable() {
        netImageCache.clear()
    }
}