package com.commsource.studio.function.relight

import com.commsource.camera.util.XCollectionUtils

/**
 *
 * Created on 5/24/21
 * <AUTHOR>
 */
class RelightManualEntity {

    val faceLights = ArrayList<FaceLight>()

    val biasLight = BiasLight(light = LightSource(lightRadius = 1f, brighttness = 50, soft = 100, hsv = 0))

    val ambienceLight = AmbienceLight(leftLight = LightSource(lightRadius = 1f, brighttness = 50, soft = 100, hsv = 65),
            rightLight = LightSource(lightRadius = 1f, brighttness = 50, soft = 100, hsv = 85))

    var faceIndex = 0
        set(value) {
            field = value
            if (faceLights.size > value) {
                faceLights[value].enable = true
            }
        }

    fun getSelectFaceLight(): FaceLight? {
        return when {
            XCollectionUtils.inRange(faceLights, faceIndex) -> faceLights[faceIndex]
            else -> null
        }
    }
}


data class FaceLight(val light: LightSource, var enable: Boolean = false)

data class BiasLight(val light: LightSource)

data class AmbienceLight(val leftLight: LightSource, val rightLight: LightSource)