package com.commsource.studio.function.beauty_pen.entity

import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.commsource.widget.IconFrontView
import com.commsource.widget.mask.MaskContainer
import java.io.Serializable

data class BeautyPenUiEntity(
    var content: LinearLayoutCompat,
    var icon: IconFrontView,
    var text: TextView,
    var cl: ConstraintLayout?,//面板
    var rv: RecyclerView?,//素材
    var mask: View?,//底部蒙层
    @BeautyPenType var type: Int,
    var enable: Boolean = true,
    var tooHeight: Boolean = false,//是否数据过多，需要显示mask
) : Serializable {

    fun type2Name() = when (type) {
        0 -> {
            "底妆"
        }

        1 -> {
            "彩妆"
        }

        2 -> {
            "亮片"
        }

        3 -> {
            "线条"
        }

        else -> {
            "线条"
        }
    }
}