package com.commsource.studio.function

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.commsource.beautyplus.databinding.FragmentStudioAutoBinding
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.*
import com.commsource.studio.bean.FocusLayerInfo
import com.commsource.studio.bean.GroupLayerInfo
import com.commsource.studio.bean.PictureLayerInfo
import com.commsource.studio.effect.*
import com.commsource.util.*
import com.commsource.widget.XSeekBar

class OpacityFragment : BaseSubFragment<OpacityResult>() {
    /**
     * 重写面板高度
     */
    override var panelHeight: Float = StudioLayoutConstants.SHORT_PANEL_HEIGHT.toFloat()

    private lateinit var mViewBinding: FragmentStudioAutoBinding

    /**
     * 磨皮效果参数。
     */
    override var effectResult = OpacityResult()

    override var showOtherLayers = true

    private var targetObj: FocusLayerInfo? = null


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        showLoadingOnInit = false
        mViewBinding = FragmentStudioAutoBinding.inflate(inflater)
        targetObj = studioViewModel.layerInfoManager.currentSelectLayerInfo as FocusLayerInfo?
        if (targetObj is GroupLayerInfo) {
            (targetObj as GroupLayerInfo).subLayerInfos.forEach {
                effectResult.groupOriAlpha.add(it.first.alpha)
            }
            mViewBinding.xsbAuto.setProgress((targetObj!!.alpha * 100).toInt())
        } else {
            effectResult.oriAlpha = targetObj?.alpha ?: 1.0f
            mViewBinding.xsbAuto.setProgress((effectResult.oriAlpha * 100).toInt())
        }
        mViewBinding.fragment = this
        mViewBinding.contrast.gone()
        mViewBinding.preview.gone()
        return mViewBinding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 隐藏选中框
        studioViewModel.canvasContainer.gestureLayer.decorateFrame.extraHideSubIconState = true

        mViewBinding.xsbAuto.addOnProgressChangeListener(object : XSeekBar.OnProgressChangeListener {
            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                // 变更当前有焦点的
                targetObj?.alpha = progress / 100f
                // targetObj?.updateLayerNode()
                studioViewModel.glPipeline.requestRender(true)
            }
        })
    }

    override fun animateIn(animationView: View?, action: () -> Unit) {
        super.animateIn(mViewBinding.flBottom, action)
    }

    override fun onClickConfirm() {
        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.OPACITY_YES)
        studioViewModel.canvasContainer.gestureLayer.decorateFrame.extraHideSubIconState = false
        targetObj?.takeIf { it is PictureLayerInfo }?.apply {
            val picInfo = this as PictureLayerInfo
            picInfo.imageResults.add(effectResult)
        }
        //入栈
        if (hasChangeAlpha()) {
            studioViewModel.pushCurrentLayerInfoToStack()
        }
        exit()
    }

    override fun onClickExit() {
        studioViewModel.canvasContainer.gestureLayer.decorateFrame.extraHideSubIconState = false
        if (hasChangeAlpha()) {
            resetFocusLayerAlpha()
            targetObj?.updateLayerNode()
            studioViewModel.glPipeline.requestRender(true)
        }
        exit()
    }

    private fun hasChangeAlpha(): Boolean {
        if (targetObj is GroupLayerInfo) {
            (targetObj as GroupLayerInfo).subLayerInfos.forEachIndexed { index, pair ->
                if (pair.first.alpha != effectResult.groupOriAlpha[index]) {
                    return true
                }
            }
            return false
        } else {
            return targetObj?.alpha != effectResult.oriAlpha
        }
    }

    private fun resetFocusLayerAlpha() {
        if (targetObj is GroupLayerInfo) {
            (targetObj as GroupLayerInfo).subLayerInfos.forEachIndexed { index, pair ->
                pair.first.alpha = effectResult.groupOriAlpha[index]
            }
        } else {
            targetObj?.alpha = effectResult.oriAlpha
        }
    }
}