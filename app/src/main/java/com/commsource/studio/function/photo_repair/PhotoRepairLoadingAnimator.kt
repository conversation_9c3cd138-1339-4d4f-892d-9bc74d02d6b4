package com.commsource.studio.function.photo_repair

import androidx.lifecycle.MutableLiveData
import com.commsource.camera.util.XTimerTools
import kotlin.random.Random

/**
 * 模拟进度处理
 * @param progressDesign 模拟进度设计
 */
class PhotoRepairLoadingAnimator(
    private val progressDesign: ((oldProgress: Int, count: Int) -> Int) = { oldProgress, count ->
        var progress = oldProgress
        val randomProgress = Random.nextInt(3, 5)
        progress += randomProgress
        if (progress >= 68) {
            progress = 68
        }

        progress
    }
) {

    /**
     * 进度监听 处于0~fakeProgress 之间
     */
    val progressEvent by lazy { MutableLiveData<Int>() }

    /**
     * 每秒计数
     */
    private var count = 0

    /**
     * 内部模拟的progress （0～100）
     */
    private var progress = 0

    /**
     * 模拟进度timer
     */
    private val timer by lazy {
        XTimerTools.infinite({
            count++
            progress = progressDesign.invoke(progress, count)
            progressEvent.value = progress
        }, 1000)
    }

    /**
     * 开启模拟进度
     */
    fun startFakeLoading() {
        cancel()
        timer.cancel()
        count = 0
        progress = 0
        timer.startDelay(1000L)
    }

    /**
     * 是否正在执行
     */
    fun isRunning(): Boolean {
        return timer.isRunning
    }

    /**
     * 取消
     */
    fun cancel() {
        timer.cancel()
        count = 0
        progress = 0
    }
}