package com.commsource.studio.function.expression

import android.content.Context
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemHolderExpressionBinding
import com.commsource.beautyplus.free.FreeFeatureManager
import com.commsource.studio.sub.SubModuleEnum
import com.commsource.util.gone
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder
import com.meitu.library.util.app.ResourcesUtils

class OpenEyesViewHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<OpenEyes>(context, parent, R.layout.item_holder_expression) {

    val viewBinding = ItemHolderExpressionBinding.bind(itemView)

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<OpenEyes>,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        
        viewBinding.ifvIcon.setText(item.entity.iconRes)
        viewBinding.tvName.setText(item.entity.nameRes)

        if (item.isSelect) {
            viewBinding.ifvIcon.setTextColor(ResourcesUtils.getColor(R.color.Primary_A))
            viewBinding.tvName.setTextColor(ResourcesUtils.getColor(R.color.Primary_A))
        } else {
            viewBinding.ifvIcon.setTextColor(ResourcesUtils.getColor(R.color.Gray_A))
            viewBinding.tvName.setTextColor(ResourcesUtils.getColor(R.color.Gray_A))
        }

        if (item.entity.needPaid) {
            viewBinding.ivVip.visible()
        } else {
            viewBinding.ivVip.gone()
        }
    }
}