package com.commsource.studio.function.hair.sparse

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautyplus.databinding.FragmentStudioHairFillinBinding
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.studio.function.hair.HairViewModel
import com.commsource.widget.XSeekBar

/**
 * @Desc : 补发
 */
class HairSparseFragment : BaseFragment() {


    companion object {
        fun newInstance(): HairSparseFragment {
            return HairSparseFragment()
        }
    }

    val hairViewModel by lazy { ViewModelProvider(requireParentFragment())[HairViewModel::class.java] }


    val mViewBinding by lazy {
        FragmentStudioHairFillinBinding.inflate(
            layoutInflater,
            null,
            false
        )
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = mViewBinding.root


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mViewBinding.xsbAuto.addOnProgressChangeListener(object :
            XSeekBar.OnProgressChangeListener {
            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
//                hairViewModel.hairProcess.applyFillin(progress, false)
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                hairViewModel.hairProcess.applySparse(progress, true)
            }
        })

        hairViewModel.sparseSelectEvent.observe(viewLifecycleOwner) {
            if (it.first == -1)
                return@observe

            mViewBinding.xsbAuto.setProgress(it.first)
        }


        hairViewModel.canSparseEvent.observe(viewLifecycleOwner) {
            mViewBinding.xsbAuto.setSeekEnable(it)
        }
    }
}