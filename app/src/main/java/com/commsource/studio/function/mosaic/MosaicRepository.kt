package com.commsource.studio.function.mosaic

import androidx.lifecycle.MutableLiveData
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.util.PathUtil
import com.commsource.material.DownloadManager
import com.commsource.material.download.request.DownloadObserver
import com.commsource.material.download.request.MaterialRequest
import com.commsource.material.download.request.OnDownloadListener
import com.commsource.material.download.task.CommonDownloadTask
import com.commsource.repository.LoadState
import com.commsource.repository.MaterialRepository
import com.commsource.repository.OnlineLocalMaterialCompator
import com.commsource.repository.Timing
import com.commsource.repository.VersionControlPoint
import com.commsource.studio.DecorateConstant
import com.commsource.util.ThreadExecutor
import com.meitu.common.AppContext
import com.meitu.http.HttpResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

/**
 * author: admin
 * Date: 2022/8/15
 * Des:
 */
object MosaicRepository : MaterialRepository(2, "MosaicRepository") {

    private const val materialType = "beautyplus_mosaic"

    val localSDRootPath: String = PathUtil.getExternalFileDir(AppContext.context, "mosaic")

    private val mosaicDao by lazy { db.mosaicDao }

    val downloadObserver = DownloadObserver<MosaicMaterial>()

    val loadStateEvent = NoStickLiveData<Int?>()
    var isMaterialLoading = false

    private val mosaicComparator by lazy {
        object :
                OnlineLocalMaterialCompator<HttpResult<List<MosaicMaterial>>, MosaicMaterial, MosaicMaterial>(
                        materialType
                ) {
            override fun getCompareLocalMaterials(): List<MosaicMaterial>? {
                return mosaicDao.loadAll()
            }

            override fun convertRoomEntity(onlineData: List<MosaicMaterial>): List<MosaicMaterial>? {
                onlineData.forEachIndexed { index, mosaicMaterial ->
                    mosaicMaterial.sort = index
                }
                return onlineData
            }

            override fun onCompareResult(
                    inserts: List<MosaicMaterial>?,
                    update: List<MosaicMaterial>?,
                    remove: List<MosaicMaterial>?
            ) {
                inserts?.let { mosaicDao.insertAll(it.toTypedArray()) }
                update?.let { mosaicDao.updateAll(it.toTypedArray()) }
                remove?.let { mosaicDao.deleteAll(it.toTypedArray()) }
                handleLinkNext()

                // 加载成功
                isMaterialLoading = false
                loadStateEvent.postValue(LoadState.SUCCEED)
            }

            override fun onError(throwable: Throwable?) {
                super.onError(throwable)
                if (!hasLinked()) {
                    handleLinkError()
                }
                // 加载成功
                isMaterialLoading = false
                loadStateEvent.postValue(LoadState.FAILED)
            }
        }
    }

    suspend fun loadAll(): List<MosaicMaterial> = withContext(Dispatchers.IO) {
        mosaicDao.loadAll()
    }

    override fun getLinkStateFlow(): Flow<Boolean> {
        return mosaicComparator.linkStateEvent
    }


    override fun onBuildVersionControlPoint(list: MutableList<VersionControlPoint>) {
        //删除内置素材
        list.add(object : VersionControlPoint(2) {
            override fun onUpdate() {
                ThreadExecutor.executeSlowTask("InitLocalMosaicData") {
                    // 删除旧版本内置的
                    mosaicDao.loadAll().filter { it.isInternal() }.takeIf { it.isNotEmpty() }
                            ?.let { list ->
                                list.forEach {
                                    it.internalState = 0
                                    it.downloadState = 0
                                }
                                mosaicDao.updateAll(list.toTypedArray())
                            }
                }
            }

        })
    }

    override fun onLoadTiming(timing: Int) {
        if (timing == Timing.ALBUM_TIMING || timing == Timing.BEAUTY_TIMING) {
            loadData()
        }
    }

    fun loadData() {
        mosaicComparator.executeHttpCompare() {
            isMaterialLoading = true
            loadStateEvent.postValue(LoadState.LOADING)
        }
    }

    fun download(
            material: MosaicMaterial,
            isHighPriority: Boolean = false,
            needProgressDialog: Boolean = false
    ) {
        if (material.isDownloading()) {
            return
        }
        if (material.getUrl().isNullOrEmpty()) {
            return
        }
        val targetPath = "${localSDRootPath}/${material.id}.zip"
        MaterialRequest.Executor().setHighPriority(isHighPriority)
                .addTask(
                        CommonDownloadTask(
                                downloadUrl = material.getUrl(),
                                targetPath = targetPath,
                                needUnzip = true,
                                unzipPath = material.getMaterialPath()
                        ),
                        DownloadManager.mosaicQueue
                )
                .setWithProgressDialog(needProgressDialog)
                .execute(getDownloadListener(material))
    }

    fun getMaterialPath(material: MosaicMaterial): String {
        return if (material.isInternal()) {
            val parent = "mosaicMaterial/release/"
            parent + material.id
        } else {
            localSDRootPath + material.id
        }
    }


//    /**
//     * 自动下载
//     * 网络环境区分WiFi和移动数据
//     */
//    fun autoDownload() {
//        materialQueryHelper.executeDataSafe(Runnable {
//            val isNetEnable = NetUtils.canNetworking(AppContext.context)
//            if (!isNetEnable) {
//                return@Runnable
//            }
//            val isWifiEnable = NetUtils.isWIFI(AppContext.context)
//            dataEvent.value?.forEach {
//                if (it.needDownload()) {
//                    if (it.downloadType == DecorateConstant.AUTO_DOWNLOAD || it.downloadType == DecorateConstant.WIFI_AUTO_DOWNLOAD && isWifiEnable) {
//                        download(it)
//                    }
//                }
//            }
//        })
//    }

    private fun getDownloadListener(material: MosaicMaterial): OnDownloadListener {
        return object : OnDownloadListener {
            override fun onStart() {
                material.downloadProgress = 1
                downloadObserver.notifyStart(material)
            }

            override fun onProgressChange(progress: Int) {
                material.downloadProgress = progress
                downloadObserver.notifyProgress(material)
            }

            override fun onError(e: Throwable) {
                material.downloadState = DecorateConstant.NOT_DOWNLOADED
                material.downloadProgress = 0
                downloadObserver.notifyFail(material, e)
            }

            override fun onSuccess() {
                material.downloadState = DecorateConstant.DOWNLOADED
                material.downloadProgress = 0
                mosaicDao.update(material)
                downloadObserver.notifySuccess(material)
            }

        }
    }

}