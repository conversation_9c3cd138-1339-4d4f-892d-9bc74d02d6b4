package com.commsource.studio.function.background

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentStudioBackgroundAdjustBinding
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.camera.util.XAnimator
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.widget.FastCenterScrollLayoutManager
import com.commsource.easyeditor.entity.CropEnum
import com.commsource.repository.child.GradientRepository
import com.commsource.repository.child.TextureRepository
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.bean.Step
import com.commsource.studio.component.ColorSelectComponent
import com.commsource.studio.function.composition.BackgroundCropRotateViewHolder
import com.commsource.studio.gesture.LayerSelectComponent
import com.commsource.util.ErrorNotifier
import com.commsource.util.MaterialVisibleTracker
import com.commsource.util.RTLTool
import com.commsource.util.UIHelper
import com.commsource.util.ViewShowState
import com.commsource.util.dp
import com.commsource.util.dpf
import com.commsource.util.resColor
import com.commsource.util.setMarginStart
import com.commsource.util.setRTL
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.library.util.net.NetUtils
import kotlin.collections.set

/**
 * 编辑器-背景-画幅&颜色
 *
 * <AUTHOR> meitu - 3/23/21
 */
class BackgroundAdjustFragment : BaseFragment() {

    val mViewBinding: FragmentStudioBackgroundAdjustBinding by lazy {
        DataBindingUtil.inflate(
            layoutInflater,
            R.layout.fragment_studio_background_adjust,
            null,
            false
        ) as FragmentStudioBackgroundAdjustBinding
    }

    private val backgroundViewModel by lazy { ViewModelProvider(mActivity as BaseActivity)[BackgroundViewModel::class.java] }

    private val colorPickerViewModel by lazy { ViewModelProvider(parentFragment!!)[ColorSelectComponent.ColorPickerViewModel::class.java] }

    private val layerSelectViewModel by lazy { ViewModelProvider(mActivity as BaseActivity)[LayerSelectComponent.LayerSelectViewModel::class.java] }

    private val studioViewModel by lazy { ViewModelProvider(ownerActivity)[ImageStudioViewModel::class.java] }

    val gradientAdapter by lazy {
        BaseRecyclerViewAdapter(ownerActivity).apply {
            // 选中裁剪比例。
            setOnEntityClickListener(GradientMaterial::class.java) { position, entity ->
                mViewBinding.rvGradient.smoothScrollToPosition(position)
                backgroundViewModel.shouldApplyGradient = entity
                onClickGradient(entity)
                false
            }
        }
    }

    val colorAdapter by lazy {
        BaseRecyclerViewAdapter(ownerActivity).apply {
            // 选中裁剪比例。
            setOnEntityClickListener(BackgroundType::class.java) { position, entity ->
                mViewBinding.rvColorList.smoothScrollToPosition(position)
                backgroundViewModel.onSelectColor(entity)
                backgroundViewModel.pickColor = entity.pureColor
                backgroundViewModel.pickColorStateEvent.value = false
                false
            }
        }
    }

    val aspectRatioAdapter by lazy {
        BaseRecyclerViewAdapter(ownerActivity).apply {
            // 选中裁剪比例。
            setOnEntityClickListener(CropEnum::class.java) { position, entity ->
                mViewBinding.rvAspectRatio.smoothScrollToPosition(position)
                backgroundViewModel.onSelectAspectRatio(entity)
                backgroundViewModel.pickColorStateEvent.value = false
                false
            }
        }
    }

    /**
     * 素材曝光追踪
     */
    val tracker: MaterialVisibleTracker<String> by lazy {
        object :
            MaterialVisibleTracker<String>(backgroundViewModel.visibleArray, isHorizon = true) {

            override fun isScrollCheck(): Boolean {
                return isSupportVisible
            }

            override fun onCallback(int: Int?, viewHolder: RecyclerView.ViewHolder?) {
                if (viewHolder is GradientViewHolder && (int == ViewShowState.SHOW_PART || int == ViewShowState.SHOW_COMPLETE)) {
                    viewHolder.item?.entity?.let {
                        if (isFirstVisible(it.id)) {
                            // 建议不要开线程。
                            MTAnalyticsAgent.logEvent(
                                MTAnalyticsConstant.beauty_background_gradient_imp,
                                HashMap<String, String>().apply {
                                    this["background_gradient"] = it.id
                                }
                            )
                        }
                    }
                }
            }
        }
    }

    var hasPickColor = false
    val contentMarginStartValuer = XAnimatorCalculateValuer(96.dpf())
    val colorItemMarginStartValuer = XAnimatorCalculateValuer(53.dpf())
    val colorItemAlphaValuer = XAnimatorCalculateValuer(1f)

    private var needRestoreCanvasAdjustOnCancel = false

    /**
     * 步骤
     */
    var step: Step? = null

    /**
     * 选择颜色的Animator
     */
    private val pickAnimator = XAnimator.ofFloat(0f, 1f)
        .duration(300)
        .setAnimationListener(object : XAnimator.XAnimationListener {
            override fun onAnimationUpdate(fraction: Float, value: Float) {
                contentMarginStartValuer.calculateValue(fraction)
                mViewBinding.vLine.setMarginStart(contentMarginStartValuer.value.toInt())
                mViewBinding.rvColorList.setMarginStart(contentMarginStartValuer.value.toInt())
                mViewBinding.biv.alpha = colorItemAlphaValuer.calculateValue(fraction)
                colorItemMarginStartValuer.calculateValue(fraction)
                mViewBinding.biv.setMarginStart(colorItemMarginStartValuer.value.toInt())
                mViewBinding.bivBg.setMarginStart(colorItemMarginStartValuer.value.toInt())
            }

            override fun onAnimationStart(animation: XAnimator?) {
                if (hasPickColor) {
                    colorItemAlphaValuer.to(1f)
                    colorItemMarginStartValuer.to(92.dpf())
                    contentMarginStartValuer.to(134.dpf())
                } else {
                    mViewBinding.biv.gradientDrawer.isSelect = false
                    colorItemAlphaValuer.to(0f)
                    colorItemMarginStartValuer.to(53f.dpf())
                    contentMarginStartValuer.to(96.dpf())
                }
            }

            override fun onAnimationEnd(animation: XAnimator?) {
            }

            override fun onAnimationCancel(animation: XAnimator?) {
            }
        })

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return mViewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mViewBinding.rvAspectRatio.apply {
            adapter = aspectRatioAdapter
            layoutManager = FastCenterScrollLayoutManager(
                mActivity,
                RecyclerView.HORIZONTAL,
                false
            )
            addItemDecoration(object : RecyclerView.ItemDecoration() {

                var space = 10f.dpf()

                val paint by lazy {
                    Paint(Paint.ANTI_ALIAS_FLAG).apply {
                        color = R.color.black10.resColor()
                        strokeWidth = .5f.dpf()
                        style = Paint.Style.STROKE
                    }
                }

                override fun onDrawOver(
                    c: Canvas,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    super.onDrawOver(c, parent, state)
                    for (i in 0 until childCount) {
                        val child = parent.getChildAt(i)
                        val position = parent.getChildAdapterPosition(child)
                        if (backgroundViewModel.spacePoint.contains(position)) {
                            if (RTLTool.isLayoutRtl()) {
                                c.drawLine(
                                    child.left - space,
                                    parent.height / 2f - 14f.dpf(),
                                    child.left - space,
                                    parent.height / 2f + 14f.dpf(),
                                    paint
                                )
                            } else {
                                c.drawLine(
                                    child.right + space,
                                    parent.height / 2f - 14f.dpf(),
                                    child.right + space,
                                    parent.height / 2f + 14f.dpf(),
                                    paint
                                )
                            }
                        }
                    }
                }

                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    super.getItemOffsets(outRect, view, parent, state)
                    val position = parent.getChildAdapterPosition(view)
                    var end = space.toInt()
                    if (backgroundViewModel.spacePoint.contains(position)) {
                        end = (space * 2).toInt()
                    }
                    outRect.setRTL(0, 0, end, 0)
                }
            })
        }
        mViewBinding.rvColorList.run {
            adapter = colorAdapter
            layoutManager = FastCenterScrollLayoutManager(
                mActivity,
                RecyclerView.HORIZONTAL,
                false
            )
        }
        mViewBinding.rvGradient.run {
            adapter = gradientAdapter
            layoutManager = FastCenterScrollLayoutManager(
                mActivity,
                RecyclerView.HORIZONTAL,
                false
            )
            addItemDecoration(object : ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
                ) {
                    val position = parent.getChildAdapterPosition(view)
                    var start = 0
                    var end = 0
                    if (position == 0) {
                        start = 10.dp()
                    } else if (position == (gradientAdapter.itemCount - 1)) {
                        end = 10.dp()
                    }
                    outRect.setRTL(start, 0, end, 0)
                }
            })
            addOnScrollListener(tracker)
        }

        //手指点击选择颜色
        mViewBinding.colorPickIcon.setOnClickListener {
            backgroundViewModel.pickColorStateEvent.run {
                if (value != true) {
                    // 只允许开启选色，不允许反旋。
                    value = true
                    mViewBinding.biv.alpha = 1f
                    // 选择取色器时这边要都取消选中。
                    colorAdapter.currentSelectEntity = null
                    gradientAdapter.currentSelectEntity = null
                    studioViewModel.layerInfoManager.layer.gestureEnable = false
                }
            }
        }

        // 选取取色器中的颜色。
        mViewBinding.biv.gradientDrawer.needStoke = true
        mViewBinding.biv.setOnClickListener {
            mViewBinding.biv.gradientDrawer.backgroundType?.let {
                mViewBinding.biv.gradientDrawer.isSelect = true
                backgroundViewModel.onSelectColor(it)
                backgroundViewModel.pickColorStateEvent.value = false
            }
        }

        //前往颜色选择器
        mViewBinding.piv.setOnClickListener {
            backgroundViewModel.pickColorStateEvent.value = false
            studioViewModel.layerInfoManager.layer.hasSecondFunction = true
            //BugFix:记录当前进入选色器点击取消是否恢复背景处理
            needRestoreCanvasAdjustOnCancel =
                studioViewModel.layerInfoManager.getBgLayerInfo() == null
            if (needRestoreCanvasAdjustOnCancel) {
                step = Step(studioViewModel.layerInfoManager.layerInfoChain)
            }
            backgroundViewModel.restorePlaceHolderBackgroundType =
                mViewBinding.biv.gradientDrawer.backgroundType
            backgroundViewModel.restoreBackgroundType =
                backgroundViewModel.applyBackgroundTypeEvent.value
            colorPickerViewModel.show()
            studioViewModel.adjustBarShowEvent.value = false
            layerSelectViewModel.showLayerSelectComponentEvent.value = false
            studioViewModel.layerInfoManager.layer.gestureEnable = false
        }

        backgroundViewModel.restoreBackgroundState.observe(viewLifecycleOwner) {
            it?.let {
                step?.let {
                    studioViewModel.refreshLayerInfos(
                        it.layerInfos,
                        isFormUndo = false,
                        isFormRedo = false,
                        gestureEnableState = true
                    ) {
                        UIHelper.runOnUiThread {
                            it.layerInfos.find { it.parentKey == studioViewModel.canvasContainer.gestureLayer.decorateFrame.attachLayerInfo?.parentKey }
                                ?.let {
                                    studioViewModel.layerInfoManager.requestLayerSelect(it)
                                }
                            studioViewModel.layerInfoManager.layer.hasSecondFunction = false
                        }
                    }
                    step = null
                }
            }
        }

        colorPickerViewModel.cancelEvent.observe(viewLifecycleOwner) {
            //恢复当前选色占位
            backgroundViewModel.restorePlaceHolderBackgroundType?.let {
                mViewBinding.biv.gradientDrawer.applyBackgroundType(it)
            }
            //BugFix：选色器恢复
            if (needRestoreCanvasAdjustOnCancel) {
                backgroundViewModel.restoreBackgroundState.value = true
                mViewBinding.biv.gradientDrawer.isSelect = false
            } else {
                studioViewModel.layerInfoManager.layer.hasSecondFunction = false
                //恢复效果
                backgroundViewModel.restoreBackgroundType?.let {
                    backgroundViewModel.onSelectColor(it, false)
                }
            }
            studioViewModel.adjustBarShowEvent.postValue(true)
            layerSelectViewModel.showLayerSelectComponentEvent.postValue(true)
            studioViewModel.layerInfoManager.layer.gestureEnable = true
        }

        colorPickerViewModel.colorChangeEvent.observe(viewLifecycleOwner) {
            applyBackgroundColor(it, false)
        }

        colorPickerViewModel.confirmEvent.observe(viewLifecycleOwner) {
            step = null
            studioViewModel.layerInfoManager.layer.hasSecondFunction = false
            applyBackgroundColor(it, true)
            studioViewModel.pushCurrentLayerInfoToStack()
            studioViewModel.adjustBarShowEvent.postValue(true)
            layerSelectViewModel.showLayerSelectComponentEvent.postValue(true)
            studioViewModel.layerInfoManager.layer.gestureEnable = true
        }

        backgroundViewModel.pickColorChangeEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                mViewBinding.civ.setFillColor(it)
                if (Color.red(it) > 240 && Color.green(it) > 240 && Color.blue(it) > 240) {
                    // 吸管变色
                    mViewBinding.colorPickIcon.setTextColor(Color.BLACK)
                } else {
                    // 吸管变色
                    mViewBinding.colorPickIcon.setTextColor(Color.WHITE)
                }
            }
        })

        backgroundViewModel.pickColorStateEvent.observe(viewLifecycleOwner, Observer {
            it?.takeIf { !it }?.let {
                mViewBinding.civ.setFillColor(Color.WHITE)
                mViewBinding.colorPickIcon.setTextColor(Color.BLACK)
//                backgroundViewModel.applyBackgroundTypeEvent.value?.let {
//                    backgroundViewModel.onSelectColor(it)
//                }
                // showColorPickResult(false)
            }
        })

        backgroundViewModel.applyBackgroundAspectRatioEvent.observe(viewLifecycleOwner, Observer {
            aspectRatioAdapter.currentSelectEntity = it
        })

        backgroundViewModel.applyBackgroundTypeEvent.observe(viewLifecycleOwner, Observer {
            colorAdapter.currentSelectEntity = it

            var position = -1
            gradientAdapter.items?.forEachIndexed { index, item ->
                if (item.entity is GradientMaterial) {
                    if ((item.entity as GradientMaterial).id == it?.backgroundColor?.id) {
                        position = index
                        return@forEachIndexed
                    }
                }
            }
            gradientAdapter.setCurrentSelectPosition(position)
            mViewBinding.biv.gradientDrawer.isSelect = it?.isFromPicker ?: false
        })

        backgroundViewModel.defaultPickColorEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                //只有在确认颜色后 生成backgrounditem
                mViewBinding.civ.setFillColor(Color.WHITE)
                mViewBinding.colorPickIcon.setTextColor(Color.BLACK)
                mViewBinding.biv.gradientDrawer.applyBackgroundType(it)
                showColorPickResult(true)
            }
        })

        backgroundViewModel.pickConfirmColorEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                //只有在确认颜色后 生成backgrounditem
                mViewBinding.civ.setFillColor(Color.WHITE)
                mViewBinding.colorPickIcon.setTextColor(Color.BLACK)
                // val backgroundType = ShaderHelper.createColorBackgroundType(it, true)
                mViewBinding.biv.gradientDrawer.isSelect = true
                mViewBinding.biv.gradientDrawer.applyBackgroundType(it)
                showColorPickResult(true)
            }
        })

        GradientRepository.dataEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                gradientAdapter.updateItemEntities(
                    AdapterDataBuilder
                        .create()
                        .addEntities(it, GradientViewHolder::class.java)
                        .build()
                )
                mViewBinding.rvGradient.post {
                    backgroundViewModel.studioViewModel.layerInfoManager.getBgLayerInfo()?.backgroundType?.apply {
                        gradientAdapter.items?.forEachIndexed { index, item ->
                            if (item.entity is GradientMaterial) {
                                if ((item.entity as GradientMaterial).id == this.backgroundColor?.id) {
                                    gradientAdapter.setCurrentSelectPosition(index)
                                    mViewBinding.rvGradient.smoothScrollToPosition(gradientAdapter.currentPosition)
                                    return@forEachIndexed
                                }
                            }
                        }
                    }
                }
            }
        })

        GradientRepository.downloadObserver.run {
            progressChangeEvent.observe(
                viewLifecycleOwner,
                object : NoStickLiveData.CustomObserver<GradientMaterial?>() {
                    override fun onAccept(o: GradientMaterial?) {
                        o?.let {
                            gradientAdapter.notifyItemChanged(o)
                        }
                    }
                })

            successEvent.observe(
                viewLifecycleOwner,
                object : NoStickLiveData.CustomObserver<GradientMaterial?>() {
                    override fun onAccept(o: GradientMaterial?) {
                        super.onAccept(o)
                        o?.let {
                            gradientAdapter.notifyItemChanged(it)
                            if (it == backgroundViewModel.shouldApplyGradient) {
                                onClickGradient(it)
                            }
                        }
                    }
                })

            failedEvent.observe(
                viewLifecycleOwner,
                object : NoStickLiveData.CustomObserver<GradientMaterial?>() {
                    override fun onAccept(o: GradientMaterial?) {
                        o?.let {
                            gradientAdapter.notifyItemChanged(it)
                        }
                    }
                })
            networkErrorEvent.observe(
                viewLifecycleOwner,
                object : NoStickLiveData.CustomObserver<Boolean?>() {
                    override fun onAccept(o: Boolean?) {
                        o?.let {
                            if (isResumed) {
                                ErrorNotifier.showNetworkErrorToast()
                            }
                        }
                    }
                })
            serverErrorEvent.observe(
                viewLifecycleOwner,
                object : NoStickLiveData.CustomObserver<Boolean?>() {
                    override fun onAccept(o: Boolean?) {
                        o?.let {
                            if (isResumed) {
                                ErrorNotifier.showServerErrorToast()
                            }
                        }
                    }
                })
        }

        backgroundViewModel.colorsDataEvent.observe(viewLifecycleOwner, Observer {
            it?.let {
                colorAdapter.updateItemEntities(
                    AdapterDataBuilder
                        .create()
                        .addEntities(it, ColorViewHolder::class.java)
                        .build()
                )
                colorAdapter.tag = ColorViewHolder.NEED_STROKE
                mViewBinding.rvColorList.post {
                    //纯色素材的选中 如果从配方中来的纯色素材 选中 是不需要处理的
                    backgroundViewModel.studioViewModel.layerInfoManager.getBgLayerInfo()?.backgroundType?.takeIf { !it.isFromPicker }
                        ?.apply {
                            colorAdapter.items?.forEachIndexed { index, item ->
                                if (item.entity is BackgroundType) {
                                    if ((item.entity as BackgroundType).pureColor == this.pureColor) {
                                        colorAdapter.setCurrentSelectPosition(index)
                                        mViewBinding.rvColorList.smoothScrollToPosition(colorAdapter.currentPosition)
                                        return@forEachIndexed
                                    }
                                }
                            }
                        }
                }
            }
        })
        backgroundViewModel.cropListChangeEvent.observe(viewLifecycleOwner, Observer {
            aspectRatioAdapter.run {
                updateItemEntities(
                    AdapterDataBuilder.create()
                        .addSelectableEntities(it, BackgroundCropRotateViewHolder::class.java)
                        .flush(), false
                )
                currentSelectEntity =
                    backgroundViewModel.studioViewModel.layerInfoManager.getBgLayerInfo()?.cropEnum
            }
        })

        backgroundViewModel.ratioVisibleCheckEvent.observe(viewLifecycleOwner) {
            if (backgroundViewModel.curTabPos == 0) {
                tracker.checkMaterialVisible(mViewBinding.rvGradient)
            }
        }

        GradientRepository.autoDownload()
        TextureRepository.autoDownload()
    }

    /**
     * 设置颜色选择结果
     */
    fun showColorPickResult(hasPickColor: Boolean) {
        if (this.hasPickColor == hasPickColor) {
            return
        }
        this.hasPickColor = hasPickColor
        if (hasPickColor) {
            pickAnimator.cancel()
            pickAnimator.start()
        }
    }

    private fun onClickGradient(gradientMaterial: GradientMaterial) {
        if (gradientMaterial.needDownload()) {
            if (!NetUtils.canNetworking(AppContext.context)) {
                ErrorNotifier.showNetworkErrorToast()
            } else {
                GradientRepository.download(gradientMaterial)
            }
            return
        }
        gradientMaterial.config?.run {
            val backgroundColor = BackgroundColor(
                gradientMaterial.id,
                gradientMaterial.icon
                    ?: "",
                type,
                gradientMaterial.needPaid(),
                startPoint,
                endPoint,
                colors,
                locations,
                gradientMaterial.isInternal(),
                GradientRepository.getMaterialPath(gradientMaterial)
            )
            val backGroundType = ShaderHelper.translate2BackgroundType(backgroundColor)
                ?: return@run
            if (backgroundViewModel.applyBackgroundTypeEvent.value != backGroundType) {
                backGroundType?.backgroundColor?.let {
                    MTAnalyticsAgent.logEvent(
                        MTAnalyticsConstant.beau_background_gradient_use,
                        "background_gradient",
                        it.id
                    )
                }
                backgroundViewModel.onSelectColor(backGroundType)
                backgroundViewModel.pickColorStateEvent.value = false
            }
        }
    }

    override fun onSupportVisible() {
        super.onSupportVisible()
        backgroundViewModel.ratioVisibleCheckEvent.value = true
        //需要动态设置 图层选中
        studioViewModel.layerInfoManager.getBgLayerInfo()?.let {
            backgroundViewModel.applyBackgroundAspectRatioEvent.value = it.cropEnum
        }
    }


    /**
     * 套用背景颜色
     */
    private fun applyBackgroundColor(color: String?, savePickColor: Boolean = true) {
        color?.let {
            val _color = Color.parseColor(it)
            if (savePickColor) {
                backgroundViewModel.pickColor = _color
            }
            val backgroundType = ShaderHelper.createColorBackgroundType(
                _color,
                true
            )
            mViewBinding.civ.setFillColor(R.color.white.resColor())
            mViewBinding.colorPickIcon.setTextColor(Color.BLACK)
            backgroundViewModel.currentPickBackgroundType = backgroundType
            if (savePickColor) {
                mViewBinding.biv.gradientDrawer.applyBackgroundType(backgroundType)
                mViewBinding.biv.gradientDrawer.isSelect = true
            }
            backgroundViewModel.onSelectColor(backgroundType, savePickColor, savePickColor)
        }
    }
}