package com.commsource.studio.bean

import android.graphics.Matrix
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.RectF
import androidx.core.graphics.toRect
import com.commsource.studio.MatrixBox
import com.commsource.studio.SVGTools
import com.commsource.studio.formula.convert.LocateInfo
import com.commsource.studio.layer.Position
import com.commsource.util.common.MathUtil
import com.commsource.util.dpf
import com.commsource.util.print
import com.meitu.mtpasterrender.MTPasterRenderFilter
import java.lang.Math.pow
import kotlin.math.sqrt

/**
 * @Description : 基础的Layer
 * <AUTHOR> bear
 * @Date : 2021/11/23
 */
abstract class FocusLayerInfo : BaseLayerInfo() {

    companion object {
        const val DEFAULT_SCALE = 0.8f
    }

    /**
     * 当前是否翻转 这是因为把镜像效果作为 固化效果和非固化效果两种情况下 导致的问题（目前项目翻转是固化效果 每次翻转实时应用在图片上）
     */
    var isFlipX = false
    var isFlipY = false

    /**
     * 相对于图片导图的情况下 是否真实翻转了
     */
    var realFlipX = false
    var realFlipY = false

    open var alpha = 1.0f
        set(value) {
            field = value
        }

    /**
     * 混合模式
     */
    open var mixMode: MTPasterRenderFilter.MtPsBlendType? =
        MTPasterRenderFilter.MtPsBlendType.MTPSBlend_Normal

    /**
     * 基于画布的Matrix调整
     */
    var drawMatrixBox: MatrixBox = MatrixBox()

    /**
     * 本质和[drawMatrixBox]表达的是一样的意思。
     */
    var position: Position = Position()

    /**
     * 蒙板图片ID
     *
     */
    var svg: SVGTools.SVG? = null

    /**
     * 配方svg
     */
    var formulaSvg: SVGTools.SVG? = null

    /**
     * 蒙板裁剪RectF
     *
     * 蒙板当前的矩阵 初始的矩阵是图片大小相同的矩阵
     * 可以认为是可见范围
     * 目前仅图片图层使用 但是这个参数在所有图片渲染中都是可以使用的
     *
     */
    var originBitmapRect = Rect()
    var maskCropRectF = RectF()

    /**
     * 相对裁剪框定位的人脸框
     * 人脸框相对裁剪框相对位置 归一化
     */
    var faceLocateInfo: LocateInfo? = null
    var locateFaceRectF = RectF()
    var maxFaceRectF = RectF()

    /**
     * 在画布上位置的外切矩形
     */
    var circumscribedRect = RectF()

    /**
     * 图片位置
     */
    var bitmapPosition: Position = Position()

    /**
     * 图片相对于蒙板裁剪框的变换矩阵
     */
    var bitmapMatrixBox: MatrixBox = MatrixBox()

    /**
     * 是否携带蒙板效果 是通过动态位置计算
     */
    fun hasMaskEffect(): Boolean {
        return !bitmapMatrixBox.matrix.isIdentity || maskCropRectF.width() != 0f || maskCropRectF.height() != 0f
    }

    /**
     * 蒙板编辑
     */
    @Transient
    var isMaskEdit = false

    var width: Int = 0

    var height: Int = 0

    val aspectRatio: Float
        get() {
            return if (width == 0 || height == 0) {
                1f
            } else {
                width.toFloat() / height
            }
        }

    /**
     * 当前图层附属的图层组，可能为空。
     */
    @Transient
    var groupLayerInfo: GroupLayerInfo? = null

    /**
     * 更新选中框尺寸 !!非图片尺寸
     */
    open fun updateSize(w: Int, h: Int) {
        this.width = w
        this.height = h
    }

    /**
     * 更新位置矩阵
     */
    open fun onUpdateMatrix(canvasWidth: Int, canvasHeight: Int) {
        if (width > 0 && height > 0) {
            position.set(
                drawMatrixBox,
                canvasWidth.toFloat(),
                canvasHeight.toFloat(),
                width.toFloat(),
                height.toFloat()
            )
        }
    }

    /**
     * 测试点击区域是否被消耗。
     */
    fun hitTest(hitPosition: FloatArray): Boolean {
        floatArrayOf(hitPosition[0], hitPosition[1]).apply {
            drawMatrixBox.calculateInvertMatrix().mapPoints(this)
            if (RectF(
                    0f,
                    0f,
                    width.toFloat(),
                    height.toFloat()
                ).apply { inset(-15f / drawMatrixBox.getScale(), -15f / drawMatrixBox.getScale()) }
                    .contains(this[0], this[1])
            ) {
                return true
            }
        }
        return false
    }

    /**
     * 获取裁剪框绘制的位置
     */
    fun getDrawMatrixConsiderFlip(): MatrixBox {
        return MatrixBox(drawMatrixBox).apply {
            if (isFlipX) {
                matrix.preScale(-1f, 1f, width / 2f, height / 2f)
                flipX = true
            }
            if (isFlipY) {
                matrix.preScale(1f, -1f, width / 2f, height / 2f)
                flipY = true
            }
            updateMatrixFloat()
        }
    }

    /**
     * 获取图片真实位置 主要的位置关系是 bitmap相对容器 容器相对canvas
     */
    fun getPictureRealMatrix(): MatrixBox {
        return MatrixBox().apply {
            postConcat(bitmapMatrixBox)
            postConcat(getDrawMatrixConsiderFlip())
        }
    }

    fun isInDefaultPosition(canvasWidth: Int, canvasHeight: Int): Boolean {
        if (drawMatrixBox.matrix.isIdentity) {
            return true
        }
        if (!MathUtil.isEqual(
                position.centerOffset.x,
                0.5f
            ) || !MathUtil.isEqual(position.centerOffset.y, 0.5f)
        ) {
            return false
        }
        if (!MathUtil.isEqual(position.rotate, 0f)) {
            return false
        }
        return MathUtil.isEqual(
            (width * drawMatrixBox.getScale() / canvasWidth).coerceAtLeast(
                height * drawMatrixBox.getScale() / canvasHeight
            ), DEFAULT_SCALE
        )
    }

    fun getRectInCanvas(): RectF {
        return RectF(0f, 0f, width.toFloat(), height.toFloat()).apply {
            drawMatrixBox.matrix.mapRect(this)
        }
    }

    /**
     * 草稿箱的部分数据 和堆栈 要看自己的数据是否使用深度拷贝
     */
    override fun copy(isSameKey: Boolean): FocusLayerInfo {
        return (super.copy(isSameKey) as FocusLayerInfo).apply {
            isMaskEdit = false
            circumscribedRect = RectF(<EMAIL>)
            originBitmapRect = Rect(<EMAIL>)
            maskCropRectF = RectF(<EMAIL>)
            position = Position(<EMAIL>)
            svg = this.svg?.copy()
            formulaSvg = this.formulaSvg?.copy()
            drawMatrixBox = MatrixBox(<EMAIL>)
            bitmapPosition = Position(this.bitmapPosition)
            bitmapMatrixBox = MatrixBox(<EMAIL>)
        }
    }

    /**
     * 更新容器图片改变
     * @param newWidth 新图宽
     * @param newHeight 新图高
     * @param canvasWidth 画布宽
     * @param canvasHeight 画布高
     * @param force 是否强制刷新
     * @param autoFitMode 是否是自适应模式，就是重新用图片centerCrop容器
     *
     * 内部区分带蒙版处理和不带蒙版处理的结构
     */
    fun updateBitmapReplaceChange(
        newWidth: Int,
        newHeight: Int,
        canvasWidth: Int,
        canvasHeight: Int,
        force: Boolean = false,
        autoFitMode: Boolean = false,
        isShortSame: Boolean = false,
    ) {
        if (hasMaskEffect()) {
            updateInnerBitmapChange(
                RectF(
                    0f,
                    0f,
                    newWidth.toFloat(),
                    newHeight.toFloat()
                ),
                force = force,
                autoFitMode = autoFitMode
            )
            // 如果此时携带了蒙板信息 那么计算又是一个新的计算规则 这里可以不需要 但是直接换算一次合适尺寸
            updateMaskCropInfo(
                Rect(0, 0, newWidth, newHeight),
                canvasWidth = canvasWidth,
                canvasHeight = canvasHeight
            )
        } else {
            //之前的宽度对比
            val widthScale = newWidth / width.toFloat()
            //需要的短边缩放一致
            if (isShortSame) {
                val oldMin = minOf(width, height)
                val newMin = minOf(newWidth, newHeight)
                val scale = oldMin / newMin.toFloat()
                position.ratio *= widthScale * scale
            } else {
                position.ratio *= widthScale
            }
            updateSize(newWidth, newHeight)
            drawMatrixBox.set(
                position.toMatrixBox(
                    newWidth,
                    newHeight,
                    canvasWidth,
                    canvasHeight
                )
            )
            onUpdateMatrix(
                canvasWidth,
                canvasHeight
            )
        }
    }

    /**
     * 适用于子功能模块效果确认后 发生底图尺寸实时变化 对蒙板的影响计算
     * 要动态计算合法区域 并和裁剪框居中
     * 固定裁剪框maskCropRectF不变
     *
     * @param newBitmapRect 新图片真实尺寸
     * @param force 是否强制刷新
     * @param autoFitMode 是否自动适应
     */
    fun updateInnerBitmapChange(
        newBitmapRect: RectF,
        force: Boolean = false,
        autoFitMode: Boolean = false
    ) {
        if (newBitmapRect == RectF(originBitmapRect) && !force) {
            return
        }
        //裁剪框在图片中居中显示且角度要保持一致
        val maskBitmapCoordinateRectF = RectF(maskCropRectF)
        //计算居中的合法区域
        if (!autoFitMode && (newBitmapRect.contains(maskBitmapCoordinateRectF) || (newBitmapRect.width() >= maskBitmapCoordinateRectF.width() && newBitmapRect.height() >= maskBitmapCoordinateRectF.height()))) {
            //如果尺寸合适 直接中心对齐
            bitmapMatrixBox.reset()
            bitmapMatrixBox.postTranslate(
                maskBitmapCoordinateRectF.centerX() - newBitmapRect.centerX(),
                maskBitmapCoordinateRectF.centerY() - newBitmapRect.centerY()
            )
        } else {
            //要调整缩放到正确的尺寸
            val scale = (maskBitmapCoordinateRectF.width() / newBitmapRect.width())
                .coerceAtLeast(maskBitmapCoordinateRectF.height() / newBitmapRect.height())
            val scaleNextRectF = RectF(newBitmapRect)
            val tempMatrix = Matrix()
            tempMatrix.postScale(scale, scale)
            tempMatrix.mapRect(scaleNextRectF)
            bitmapMatrixBox.reset()
            bitmapMatrixBox.postScale(scale)
            bitmapMatrixBox.postTranslate(
                maskBitmapCoordinateRectF.centerX() - scaleNextRectF.centerX(),
                maskBitmapCoordinateRectF.centerY() - scaleNextRectF.centerY()
            )
        }
        originBitmapRect.set(newBitmapRect.toRect())
    }

    /**
     * 更新蒙板数据
     * 这个蒙板裁剪数据 确认时更新
     */
    fun updateMaskCropInfo(
        bitmapRect: Rect,
        newMaskCropRectF: RectF = RectF(maskCropRectF),
        bitmapChangeMatrixBox: MatrixBox = MatrixBox(bitmapMatrixBox),
        newShape: SVGTools.SVG? = svg,
        canvasWidth: Int,
        canvasHeight: Int
    ) {
        if (newShape == null) {
            //更新尺寸
            updateSize(bitmapRect.width(), bitmapRect.height())
            drawMatrixBox.set(MatrixBox().apply {
                postConcat(bitmapChangeMatrixBox)
                postConcat(drawMatrixBox)
            })
            maskCropRectF.setEmpty()
            originBitmapRect.setEmpty()
            bitmapMatrixBox.reset()
            this.svg = null
            position.set(
                drawMatrixBox,
                canvasWidth.toFloat(),
                canvasHeight.toFloat(),
                bitmapRect.width().toFloat(),
                bitmapRect.height().toFloat()
            )
            //去掉蒙层操作
            return
        }
        //保存初始图片的矩阵
        if (originBitmapRect.isEmpty || originBitmapRect == bitmapRect) {
            originBitmapRect.set(bitmapRect)
        } else {
            return
        }
        val leftTop = floatArrayOf(newMaskCropRectF.left, newMaskCropRectF.top).apply {
            bitmapChangeMatrixBox.calculateInvertMatrix().mapPoints(this)
        }
        val leftBottom = floatArrayOf(newMaskCropRectF.left, newMaskCropRectF.bottom).apply {
            bitmapChangeMatrixBox.calculateInvertMatrix().mapPoints(this)
        }
        val rightTop = floatArrayOf(newMaskCropRectF.right, newMaskCropRectF.top).apply {
            bitmapChangeMatrixBox.calculateInvertMatrix().mapPoints(this)
        }
        //设置最小尺寸后 是为了防止绘制的尺寸过小的问题 导致绘制模糊的问题
        val minSize = 30.dpf()
        var widthF = MathUtil.getDistance(leftTop[0], leftTop[1], rightTop[0], rightTop[1])
        var heightF = MathUtil.getDistance(leftTop[0], leftTop[1], leftBottom[0], leftBottom[1])
        //最小尺寸纠正
        if (widthF <= 1.0f || heightF <= 1.0f) {
            if (newMaskCropRectF.width() > newMaskCropRectF.height()) {
                heightF = minSize
                widthF = newMaskCropRectF.width() * minSize / newMaskCropRectF.height()
            } else {
                widthF = minSize
                heightF = newMaskCropRectF.height() * minSize / newMaskCropRectF.width()
            }
        }
        //最大尺寸保护
        if (widthF > originBitmapRect.width() || heightF > originBitmapRect.height()) {
            val validRectF = MathUtil.generateInscribeRect(
                RectF(originBitmapRect),
                widthF.toInt(),
                heightF.toInt()
            )
            widthF = validRectF.width()
            heightF = validRectF.height()
        }
        val width = widthF.toInt()
        val height = heightF.toInt()
        "蒙层图层宽高:${width}:${height}".print("csx")
        val scale = newMaskCropRectF.width() / widthF

        //更新蒙板尺寸
        maskCropRectF.set(0f, 0f, width.toFloat(), height.toFloat())
        //矫正矩阵 是平衡尺寸和缩放的作用
        //所以canvas 相对 mask 要有一个优化的scale, mask相对bitmap 也要有一个优化的scale
        //因为不断的进入蒙板界面 每次进入是携带图层的缩放值进入的 如果进入后手势的缩放叠加maskRectF的变化，那么尺寸是一定不断的变大的，所以这里需要做一个矫正尺寸对应缩放的变换
        val correctMatrixBox = MatrixBox().apply {
            postScale(scale)
            postTranslate(newMaskCropRectF.left, newMaskCropRectF.top)
        }
        //在图片坐标下的选中框新尺寸
        updateSize(width, height)
        drawMatrixBox.set(MatrixBox().apply {
            postConcat(correctMatrixBox)
            postConcat(drawMatrixBox)
        })
        //更新位置
        position.set(
            drawMatrixBox,
            canvasWidth.toFloat(),
            canvasHeight.toFloat(),
            width.toFloat(),
            height.toFloat()
        )
        //更新图片蒙板ID
        this.svg = newShape
        //更新图片相对蒙板的变换信息
        bitmapMatrixBox.apply {
            reset()
            postConcat(bitmapChangeMatrixBox)
            postConcat(correctMatrixBox.calculateInvertMatrixBox())
        }
        //更新图片纹理层 位置信息
        bitmapPosition.set(
            bitmapMatrixBox,
            maskCropRectF.width(),
            maskCropRectF.height(),
            bitmapRect.width().toFloat(),
            bitmapRect.height().toFloat()
        )
    }

    /**
     * 更新最新人脸框位置
     */
    private fun updateFaceLocateRectF() {
        faceLocateInfo?.takeIf { hasMaskEffect() }?.let {
            locateFaceRectF.set(
                -it.widthRatio * width / 2f,
                -it.heightRatio * height / 2f,
                it.widthRatio * width / 2f,
                it.heightRatio * height / 2f
            )
            locateFaceRectF.offset(it.centerX * width, it.centerY * height)
        }
    }

    /**
     * 尝试定位人脸框位置
     * @param faceDataMaxFaceRectF 人脸最大归一化人脸位置
     */
    fun tryLocateFaceRectF(faceDataMaxFaceRectF: RectF, facePointsArray: FloatArray) {
        updateFaceLocateRectF()
        if (this.locateFaceRectF.isEmpty || !hasMaskEffect() || faceLocateInfo == null) {
            return
        }

        //人脸相对于图片的位置
        val faceRectF = try {
            //人脸膨胀处理这类问题 同多人脸选择器一种膨胀算法
//            val left = PointF()
//            val top = PointF()
//            val right = PointF()
//            val bottom = PointF()
//            FaceOval.calculateFacePoint(bottom, facePointsArray, 16, 95, 0.2f)
//            FaceOval.calculateFacePoint(left, facePointsArray, 0, 32, 0.05f)
//            FaceOval.calculateFacePoint(right, facePointsArray, 32, 0, 0.05f)
//            FaceOval.calculateFacePoint(top, facePointsArray, 71, 89, 1f)
            //按照IOS人脸算法 计算，其实ios的计算不对 但是为了效果统一 这里人脸框计算和ios保持一致
            //132点人脸图 71-80 点偏移补充额头下巴distance
            val point71 = PointF(facePointsArray[71 * 2], facePointsArray[72 * 2 + 1])
            val point80 = PointF(facePointsArray[80 * 2], facePointsArray[80 * 2 + 1])
            val distance = sqrt(
                pow(
                    (point71.x - point80.x) * originBitmapRect.width().toDouble(),
                    2.0
                ) + pow(
                    (point71.y - point80.y) * originBitmapRect.height().toDouble(),
                    2.0
                )
            )
            RectF(
                faceDataMaxFaceRectF.left * originBitmapRect.width(),
                faceDataMaxFaceRectF.top * originBitmapRect.height() - distance.toFloat(),
                faceDataMaxFaceRectF.right * originBitmapRect.width(),
                faceDataMaxFaceRectF.bottom * originBitmapRect.height() + distance.toFloat()
            )

//            RectF().apply {
//                set(
//                    left.x.coerceAtMost(right.x).coerceAtMost(top.x)
//                        .coerceAtMost(bottom.x) * originBitmapRect.width(),
//                    left.y.coerceAtMost(right.y).coerceAtMost(top.y)
//                        .coerceAtMost(bottom.y) * originBitmapRect.height(),
//                    left.x.coerceAtLeast(right.x).coerceAtLeast(top.x)
//                        .coerceAtLeast(bottom.x) * originBitmapRect.width(),
//                    left.y.coerceAtLeast(right.y).coerceAtLeast(top.y)
//                        .coerceAtLeast(bottom.y) * originBitmapRect.height()
//                )
//            }
        } catch (e: Exception) {
            RectF(
                faceDataMaxFaceRectF.left * originBitmapRect.width(),
                faceDataMaxFaceRectF.top * originBitmapRect.height(),
                faceDataMaxFaceRectF.right * originBitmapRect.width(),
                faceDataMaxFaceRectF.bottom * originBitmapRect.height()
            )
        }
        maxFaceRectF.set(faceRectF)
        bitmapMatrixBox.matrix.mapRect(faceRectF)
        val matrix = MatrixBox().apply {
            setRectToRect(
                faceRectF,
                locateFaceRectF
            )
        }
        bitmapMatrixBox.postConcat(matrix)
        bitmapPosition.set(
            bitmapMatrixBox,
            maskCropRectF.width(),
            maskCropRectF.height(),
            originBitmapRect.width().toFloat(),
            originBitmapRect.height().toFloat()
        )
    }

    /**
     * 创建蒙版Key 用于判断蒙层是否更新的字段
     */
    fun generateMaskKey(): String {
        return "${maskCropRectF.width()}_${maskCropRectF.height()}_${svg?.shapeId}_${svg?.filePath}_${realFlipX}_${realFlipY}"
    }
}