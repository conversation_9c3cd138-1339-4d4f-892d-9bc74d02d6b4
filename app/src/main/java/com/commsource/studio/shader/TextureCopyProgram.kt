package com.commsource.studio.shader

import android.opengl.GLES20
import com.commsource.easyeditor.utils.opengl.FBOEntity

/**
 * 绘制图片的。
 */
class TextureCopyProgram : BaseProgram<CommonVertexShader, ImageFragmentShader>(CommonVertexShader(), ImageFragmentShader()) {

    /**
     * 绘制纹理。
     */
    fun copyTexture(srcFBOEntity: FBOEntity, disFBOEntity: FBOEntity) {
        // 绘制到FBO上。
        vertexShader.params.inFBO = true
        fragmentShader.params.textureId = srcFBOEntity.textureId
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, disFBOEntity.fboId)
        GLES20.glViewport(0, 0, srcFBOEntity.width, srcFBOEntity.height)
        super.drawArrays()
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0)
    }

    fun copyTextureWithBlend(srcFBOEntity: FBOEntity, disFBOEntity: FBOEntity) {
        // 绘制到FBO上。
        vertexShader.params.inFBO = true
        fragmentShader.params.textureId = srcFBOEntity.textureId
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, disFBOEntity.fboId)
        GLES20.glViewport(0, 0, srcFBOEntity.width, srcFBOEntity.height)
        GLES20.glEnable(GLES20.GL_BLEND)
        GLES20.glBlendFunc(GLES20.GL_ONE, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        super.drawArrays()
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0)
        GLES20.glDisable(GLES20.GL_BLEND)
    }

    /**
     * 绘制纹理。
     */
    fun copyTexture(srcTexture: Int, disFBOEntity: FBOEntity) {
        // 绘制到FBO上。
        vertexShader.params.inFBO = true
        fragmentShader.params.textureId = srcTexture
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, disFBOEntity.fboId)
        GLES20.glViewport(0, 0, disFBOEntity.width, disFBOEntity.height)
        super.drawArrays()
        GLES20.glBindFramebuffer(GLES20.GL_FRAMEBUFFER, 0)
    }
}