package com.commsource.studio.shader

import android.opengl.GLES20
import android.opengl.Matrix
import com.commsource.easyeditor.utils.opengl.VertexHelper
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer

/**
 * 通用的顶点着色器,添加了矩阵变换支持。
 */
class CommonMatrixVertexShader(val params: ParamsCollection = ParamsCollection()) : BaseShader(GLES20.GL_VERTEX_SHADER) {

    /**
     * 着色器中变量的Location记录。
     */
    private var shaderLocationPosition: Int = 0
    private var shaderLocationTextureCoordinates: Int = 0
    private var shaderLocationMatrix: Int = 0



    override fun onCreateShaderString(): String {
        return "uniform mat4 $SHADER_UNIFORM_MATRIX;\n" +
                "\n" +
                "attribute vec4 $SHADER_ATTR_POSITION;\n" +
                "attribute vec2 $SHADER_ATTR_TEXTURE_COORDINATES;\n" +
                "\n" +
                "varying vec2 $SHADER_VAR_TEXTURE_COORDINATES;\n" +
                "\n" +
                "void main()\n" +
                "{\n" +
                "    gl_Position = $SHADER_UNIFORM_MATRIX * $SHADER_ATTR_POSITION;\n" +
                "    $SHADER_VAR_TEXTURE_COORDINATES = $SHADER_ATTR_TEXTURE_COORDINATES;\n" +
                "}"
    }

    override fun onGetShaderLocation(programId: Int) {
        shaderLocationPosition = GLES20.glGetAttribLocation(programId, SHADER_ATTR_POSITION)
        shaderLocationTextureCoordinates = GLES20.glGetAttribLocation(programId, SHADER_ATTR_TEXTURE_COORDINATES)
        shaderLocationMatrix = GLES20.glGetUniformLocation(programId, SHADER_UNIFORM_MATRIX)
    }

    override fun onPrepareShaderAttrOrUniform() {
        GLES20.glVertexAttribPointer(shaderLocationPosition, 2, GLES20.GL_FLOAT, false, 2 * 4, params.positionBuffer)
        GLES20.glEnableVertexAttribArray(shaderLocationPosition)

        GLES20.glVertexAttribPointer(shaderLocationTextureCoordinates, 2, GLES20.GL_FLOAT, false, 2 * 4, params.textureCoordinateBuffer)
        GLES20.glEnableVertexAttribArray(shaderLocationTextureCoordinates)

        GLES20.glUniformMatrix4fv(shaderLocationMatrix, 1, false, params.matrix, 0)
    }

    /**
     * 着色器中需要赋值的参数集合。
     */
    class ParamsCollection {
        /**
         * 是否是绘制在FBO中，主要影响默认值。
         */
        var inFBO: Boolean = false

        /**
         * 顶点缓存，如果没有设置会使用默认值。
         */
        var positionBuffer: FloatBuffer? = null
            get() {
                if (field == null) {
                    field = ByteBuffer.allocateDirect(positionFloatArray!!.size * VertexHelper.BYTES_PER_FLOAT)
                            .order(ByteOrder.nativeOrder())
                            .asFloatBuffer()
                }
                field?.let {
                    it.put(positionFloatArray)
                    it.position(0)
                }
                return field
            }

        /**
         * 纹理顶点缓存，如果没有设置会使用默认值。
         */
        var textureCoordinateBuffer: FloatBuffer? = null
            get() {
                if (field == null) {
                    field = ByteBuffer.allocateDirect(textureCoordinateFloatArray!!.size * VertexHelper.BYTES_PER_FLOAT)
                            .order(ByteOrder.nativeOrder())
                            .asFloatBuffer()
                }
                field?.let {
                    it.put(textureCoordinateFloatArray)
                    it.position(0)
                }
                return field
            }

        /**
         * 在点梳理固定，但是值不固定的情况下可以设置此参数，避免重复申请Buffer。
         */
        var positionFloatArray: FloatArray? = null
            get() {
                if (field == null) {
                    field = if (inFBO) {
                        VertexHelper.GL_VERTEX_FLOATS_IN_FBO
                    } else {
                        VertexHelper.GL_VERTEX_FLOATS
                    }
                }
                return field
            }

        /**
         * 在点梳理固定，但是值不固定的情况下可以设置此参数，避免重复申请Buffer。
         */
        var textureCoordinateFloatArray: FloatArray? = null
            get() {
                if (field == null) {
                    field = VertexHelper.TEXTURE_VERTEX_FLOATS
                }
                return field
            }

        /**
         * 位置矩阵信息，默认值为单位矩阵。
         */
        var matrix: FloatArray? = null
            get() {
                if (field == null) {
                    field = FloatArray(16).apply {
                        Matrix.setIdentityM(this, 0)
                    }
                }
                return field
            }

    }
}