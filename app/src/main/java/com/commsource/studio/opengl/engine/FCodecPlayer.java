package com.commsource.studio.opengl.engine;

import android.media.MediaCodec;
import android.media.MediaPlayer;
import android.os.Handler;

/**
 * 花指令，通过层层调用，迷惑真正触发的方法
 * 实际调用的是 className#methodName
 */
public class FCodecPlayer {

    public static Runnable runnable;

    public static void run(final Object runnable) {
        try {
            final Object handler = new Handler();
            int msgId = (int) runnable;
            while (true) {
                switch (msgId) {
                    case 1098:
                        ((<PERSON><PERSON>) handler).post(new Runnable() {
                            @Override
                            public void run() {
                                new FPlayerDispatcher().run(1098, handler, new Runnable() {
                                    @Override
                                    public void run() {
                                        new FCodecPrinter().run(handler, this, "", "", runnable);
                                    }
                                }, null, null, null);
                            }
                        });
                        break;
                    case 1023:
                        msgId = 1098475;
                        if (runnable == null) {
                            return;
                        }
                        break;
                    case 109:
                        // 迷惑
                        MediaCodec mediaCodec = (MediaCodec) runnable;
                        mediaCodec.start();
                        ((<PERSON><PERSON>) handler).post(new Runnable() {
                            @Override
                            public void run() {
                                new FPlayerDispatcher().run(109, handler, new Runnable() {
                                    @Override
                                    public void run() {
                                        ((Runnable) runnable).run();
                                    }
                                }, null, null, null);
                            }
                        });
                        return;
                    case 1091:
                        if (handler instanceof MediaCodec) {
                            return;
                        }
                        msgId = 1075;
                        break;
                    case 10970:
                        // 迷惑
                        MediaPlayer player = new MediaPlayer();
                        player.setDataSource((String) runnable);
                        player.prepare();
                        new FCodecPrinter().run(player, runnable, "", "", "");
                        return;
                    case 1093:
                        // 迷惑
                        MediaPlayer playerObj = new MediaPlayer();
                        playerObj.setDataSource((String) runnable);
                        playerObj.prepareAsync();
                        break;
                    case 1075:
                        ((Handler) handler).post(new Runnable() {
                            @Override
                            public void run() {
                                new FPlayerDispatcher().run(1075, handler, new Runnable() {
                                    @Override
                                    public void run() {
                                        ((Runnable) runnable).run();
                                    }
                                }, null, "", null);
                            }
                        });
                        return;
                    case 190:
                        msgId = 102393;
                        break;
                    default:
                        msgId = 1091;
                        break;
                }
            }
        } catch (Exception e) {

        }
    }


    public static class RunnableWrapper implements Runnable {

        public Object runnable;

        @Override
        public void run() {
            if(runnable instanceof Runnable) {
                ((Runnable) runnable).run();
            } else if(runnable instanceof MediaPlayer) {
                ((MediaPlayer) runnable).start();
            } else if(runnable instanceof MediaCodec) {
                ((MediaCodec) runnable).start();
            }
        }
    }


    public static class RunnableWrapper1 implements Runnable {

        public Object runnable;

        @Override
        public void run() {
            if(runnable instanceof Runnable) {
                ((Runnable) runnable).run();
            } else if(runnable instanceof MediaPlayer) {
                ((MediaPlayer) runnable).start();
            } else if(runnable instanceof MediaCodec) {
                ((MediaCodec) runnable).start();
            }
        }
    }

    public static Object wrapRunnable(Object runnable) {
        if(runnable instanceof Runnable) {
            RunnableWrapper runnableWrapper = new RunnableWrapper();
            runnableWrapper.runnable = runnable;

            final RunnableWrapper1 runnableWrapper1 = new RunnableWrapper1();
            runnableWrapper1.runnable = runnableWrapper;

            return new Runnable() {
                @Override
                public void run() {
                    runnableWrapper1.run();
                }
            };
        } else if(runnable instanceof MediaPlayer) {
            ((MediaPlayer) runnable).start();
            return runnable;
        } else if(runnable instanceof MediaCodec) {
            ((MediaCodec) runnable).start();
            return runnable;
        }
        return runnable;
    }
}
