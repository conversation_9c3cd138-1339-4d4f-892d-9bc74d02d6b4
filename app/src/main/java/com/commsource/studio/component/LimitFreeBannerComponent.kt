package com.commsource.studio.component

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.commsource.ad.DailyMembershipUnlocker
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ComponentLimitFreeBannerBinding
import com.commsource.beautyplus.free.FreeFeatureManager
import com.commsource.billing.SubSource
import com.commsource.config.SubscribeConfig
import com.commsource.home.entity.DialogDataEntity
import com.commsource.statistics.SpmParamConstant
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.function.BaseSubFragment
import com.commsource.studio.sub.SubModuleEnum
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.delegate.process.SubscribeProcess
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.isVisible
import com.commsource.util.text
import com.commsource.util.visible
import com.meitu.library.hwanalytics.spm.SPMShare

class LimitFreeBannerComponent : ComponentView {
    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, 0)

    constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    private val viewBinding =
        ComponentLimitFreeBannerBinding.inflate(LayoutInflater.from(context)).apply {
            addView(this.root, ViewGroup.LayoutParams.MATCH_PARENT, 52.dp())
        }

    private val imageStudioViewModel by lazy { ViewModelProvider(getActivity()!!)[ImageStudioViewModel::class.java] }
    private lateinit var studioProViewModel: StudioProViewModel
    private var subModuleEnum: SubModuleEnum? = null

    /**
     * 如果设置过，那就不会自动上报点击事件的埋点。
     */
    var bannerClickListener: (() -> Unit)? = null

    override fun onInitOwner(storeOwner: ViewModelStoreOwner, lifecycleOwner: LifecycleOwner) {
        super.onInitOwner(storeOwner, lifecycleOwner)
        studioProViewModel = getViewModel(StudioProViewModel::class.java)

        subModuleEnum = studioProViewModel.subModuleEnum

        viewBinding.flLimitFree.setOnClickListener {
            getActivity()?.let { activity ->
                if (bannerClickListener != null) {
                    bannerClickListener?.invoke()
                } else {
                    SPMShare.put(SpmParamConstant.KEY_SOURCE_CLICK_POSITION, "限免横幅")
                    subModuleEnum?.let { enum ->
                        SPMShare.put(
                            SpmParamConstant.KEY_SOURCE_FEATURE_CONTENT, enum.limitFreeName
                        )
                        SPMShare.put(SpmParamConstant.KEY_PRF_FUNC, enum.limitFreeName)
                    }
                }

                IProcessHandler(activity)
                    .execute(object : SubscribeProcess(SubSource.FROM_EDIT) {
                        override fun onSubscribeResult(isSubcribe: Boolean) {
                            if (isSubcribe) {
                                // 刷新选中框，刷新订阅按钮，订阅hint横幅
                                imageStudioViewModel.fixProState()
                                initLimitFreeBanner()
                            }
                        }
                    })
            }
        }

        initLimitFreeBanner()
    }

    fun initLimitFreeBanner() {
        if (SubscribeConfig.isSubValid() || DailyMembershipUnlocker.hasUserEarnReward() || canFreeUseOnce()) {
            autoHide()
        } else {
            subModuleEnum?.let { subModuleEnum ->
                if (FreeFeatureManager.featureFree(subModuleEnum)) {
                    if (FreeFeatureManager.getLimitFreeType(subModuleEnum) == DialogDataEntity.FREE_TYPE_TIME) {
                        val time = FreeFeatureManager.getFeatureFreeTime(subModuleEnum)
                        autoShow(String.format(R.string.t_free_trail_up.text(), time))
                    } else { // 按次免费
                        val freeNum = FreeFeatureManager.getRemainingLimitFreeCount(subModuleEnum)
                        if (freeNum > 0) {
                            autoShow(
                                R.string.v78010_A_2.text(
                                    subModuleEnum.moduleName.text(),
                                    freeNum
                                )
                            )
                        } else {
                            autoHide()
                        }
                    }
                } else {
                    autoHide()
                }
            }
        }
    }

    fun hide() {
        if (viewBinding.flLimitFree.isVisible) {
            viewBinding.flLimitFree.gone()
            (parent as? BannerContainer)?.show(0, 12.dp(), false)
        }
    }

    fun showOrHide(subModuleEnum: SubModuleEnum) {
        this.subModuleEnum = subModuleEnum
        initLimitFreeBanner()
    }

    private fun autoShow(text: String) {
        viewBinding.tvProBanner.text = text

        viewBinding.flLimitFree.visible()
        (parent as? BannerContainer)?.show(0, 52.dp(), true)
    }

    private fun autoHide() {
        viewBinding.flLimitFree.gone()
        (parent as? BannerContainer)?.show(0, 12.dp(), false)
    }

    private fun getActivity(): FragmentActivity? {
        if (storeOwner is FragmentActivity) {
            return storeOwner as FragmentActivity
        }
        if (storeOwner is Fragment) {
            return (storeOwner as Fragment).requireActivity()
        }
        return null
    }

    private fun canFreeUseOnce(): Boolean {
        // 是否已通过看广告解锁1次
        return (storeOwner as? BaseSubFragment<*>)?.freeUseInfo?.canFreeUseOnce == true
    }
}