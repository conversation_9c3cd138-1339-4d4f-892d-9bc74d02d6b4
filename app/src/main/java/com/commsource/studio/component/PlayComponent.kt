package com.commsource.studio.component

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelStoreOwner
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ComponentPlayBinding

/**
 * 子模块确认取消的组件。
 */
class PlayComponent : ComponentView {

    constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : super(context, attrs, defStyleAttr)
    constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, 0)
    constructor(context: Context) : this(context, null)

    private lateinit var viewModel: PlayViewModel

    var isPlay = false

    private val viewBinding = ComponentPlayBinding.inflate(LayoutInflater.from(context)).apply {
        addView(this.root)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onInitOwner(storeOwner: ViewModelStoreOwner, lifecycleOwner: LifecycleOwner) {
        viewModel = getViewModel(PlayViewModel::class.java)
        viewBinding.ivPlay.setOnClickListener {
            isPlay = !isPlay
            if (isPlay) {
                viewBinding.ivPlay.setText(R.string.ar_pause)
            } else {
                viewBinding.ivPlay.setText(R.string.ar_play)
            }
            viewModel.playEvent.value = isPlay
        }
    }

    fun play() {
        isPlay = true
        viewBinding.ivPlay.setText(R.string.ar_pause)
        viewModel.playEvent.value = (true)
    }

    fun reset() {
        isPlay = false
        viewBinding.ivPlay.setText(R.string.ar_play)
        viewModel.playEvent.value = (false)
    }

    /**
     * 撤销重做逻辑的VM。
     */
    class PlayViewModel(application: Application) : AndroidViewModel(application) {

        val playEvent = MutableLiveData<Boolean>()
    }
}