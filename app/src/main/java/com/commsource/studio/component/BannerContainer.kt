package com.commsource.studio.component

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import android.widget.RelativeLayout
import com.commsource.camera.util.animationTransition
import com.commsource.util.alphaVisibleOrGone
import com.commsource.util.dp
import com.pixocial.androidx.core.extension.setLayoutHeight

/**
 * 包含多个Banner的Container，处理多个Child Banner展示不同高度的逻辑，取最大。
 *
 */
class BannerContainer : FrameLayout {

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, 0)

    constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
    ) : super(context, attrs, defStyleAttr)

    /**
     * 当前是否时展示状态。
     */
    private var showState: Boolean = false

    /**
     * 缓存每个Child展示时请求的Height
     */
    private val childHeightMap = HashMap<Int, Int>()
    private val childShowMap = HashMap<Int, Boolean>()


    fun show(index: Int, height: Int, isShow: Boolean) {
        childHeightMap[index] = height
        childShowMap[index] = isShow
        val targetHeight = getTargetHeight()
        showState = getTargetShowState()
        animationTransition {
            setLayoutHeight(targetHeight)
            alphaVisibleOrGone(showState)
        }
    }

    fun hasShowBanner(): Boolean {
        childShowMap.values.forEach {
            if (it) {
                return true
            }
        }
        return false
    }

    private fun getTargetHeight(): Int {
        return childHeightMap.maxOfOrNull { it.value } ?: 0
    }

    private fun getTargetShowState(): Boolean {
        return childShowMap.firstNotNullOfOrNull { it.value } != null
    }

}