package com.commsource.studio.formula

import android.app.Application
import android.graphics.Bitmap
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.commsource.beautyfilter.NoStickLiveData
import com.commsource.beautyplus.router.RouterEntity
import com.commsource.beautyplus.router.UriConstant
import com.commsource.beautyplus.web.WebConstant
import com.commsource.duffle.formula.FormulaRepo
import com.commsource.search_common.entity.SearchInfo
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.SpmAnalytics
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.DecorateConstant
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.bean.PictureLayerInfo
import com.commsource.studio.bean.Step
import com.commsource.studio.formula.convert.FormulaConvertFactory
import com.commsource.studio.formula.convert.FormulaWrapper
import com.commsource.util.LOGV_Formula
import com.commsource.util.coroutine.launch
import com.commsource.widget.mask.MaskType
import com.meitu.library.util.net.NetUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FormulaViewModel(application: Application) : AndroidViewModel(application) {

    /**
     * 用到的所有数据
     */
    var formulaCategoryData = ArrayList<FormulaCategory>() // 分类数据
    private var formulaData = ArrayList<JsFormula>() // 配方数据
    private var recentFormulaData = ArrayList<JsFormula>() // 最近使用数据
    val categorySelectEvent = MutableLiveData<Int>() // 分类列表选中
    val formulaSelectEvent = MutableLiveData<Int>() // 配方列表选中
    val formulaApplyEvent = MutableLiveData<JsFormula>()
    val protocolFormulaEvent = MutableLiveData<JsFormula>()
    private var curSelectCategoryPos: Int = 0 // 当前选中的分类Index
    val formulaShowedMap: HashMap<String, HashMap<String, Int>> = HashMap()
    val formulaRepoDataEvent = MutableLiveData<Pair<List<FormulaCategory>, List<JsFormula>>>()
    val cateUpdateEvent = MutableLiveData<FormulaCategory>()
    val formulaCollectStateEvent = MutableLiveData<JsFormula>()

    val applyFormulaEvent = MutableLiveData<SearchInfo>()

    val cancelSearchEvent = NoStickLiveData<Boolean>()

    //套用配方事件
    val applySearchEvent = MutableLiveData<SearchInfo>()

    val refreshDataEvent = NoStickLiveData<Boolean>()

    @Volatile
    private var isLoadedPanelData = false

    @Volatile
    var shouldSPM = true

    @Volatile
    private var protocolFormula: JsFormula? = null

    @Volatile
    private var protocolCatID: String? = null

    val maskTypeEvent = MutableLiveData<String?>()
    var hasOnlineMaterials = false


    var stepOnEnterFormula: Step? = null
    private var stepBeforeApplyFormula: Step? = null

    private var tempRouterEntity: RouterEntity? = null

    private var protocolInfo: Pair<String, JsFormula?>? = null

    fun loadPanelData(
        loadCacheFirst: Boolean = true,
        srcImage: Bitmap? = null,
        isNeedRequestOnline: Boolean = true
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                FormulaRepo.loadFormulaInPanel(loadCacheFirst, isNeedRequestOnline, srcImage)
                    .collect { data ->
                        data.takeIf { it.isNotEmpty() }?.let {
                            withContext(Dispatchers.Main) {
                                val lastRecCate = formulaCategoryData.find {
                                    it.categoryId == DecorateConstant.NEW_CATEGORY_RECENT
                                }
                                val lastColCate = formulaCategoryData.find {
                                    it.categoryId == DecorateConstant.NEW_CATEGORY_COLLECT
                                }
                                formulaCategoryData.clear()
                                formulaData.clear()
                                recentFormulaData.clear()
                                it.forEach { cate ->
                                    when (cate.categoryId) {
                                        DecorateConstant.NEW_CATEGORY_RECENT -> {
                                            lastRecCate?.let {
                                                cate.allFormulaList = it.allFormulaList
                                                cate.displayFormulaList = it.displayFormulaList
                                            }
                                        }

                                        DecorateConstant.NEW_CATEGORY_COLLECT -> {
                                            lastColCate?.let {
                                                cate.allFormulaList = it.allFormulaList
                                                cate.displayFormulaList = it.displayFormulaList
                                            }
                                        }

                                        else -> {
                                            if (cate.displayFormulaList.isNotEmpty()) {
                                                cate.displayFormulaList.let { formulaData.addAll(it) }
                                            }
                                        }
                                    }
                                    formulaCategoryData.add(cate)
                                }

                                val onlineMaterials =
                                    formulaData.takeIf { it.isNotEmpty() }
                                        ?.filter { !it.isInternal() }
                                if (onlineMaterials?.isNotEmpty() == true) {
                                    hasOnlineMaterials = true
                                }
                                if (hasOnlineMaterials) {
                                    maskTypeEvent.postValue(null)
                                } else {
                                    if (FormulaRepo.isMaterialLoading || NetUtils.canNetworking()) {
                                        maskTypeEvent.postValue(MaskType.Loading)
                                    } else {
                                        maskTypeEvent.postValue(MaskType.NetError)
                                    }
                                }

                                formulaRepoDataEvent.value = Pair(formulaCategoryData, formulaData)
                                loadPanelLocalFormulas(true)
                            }
                        }

                        // 数据为空也要处理
                        data.takeIf { it.isEmpty() }?.let {
                            val onlineMaterials =
                                formulaData.takeIf { it.isNotEmpty() }?.filter { !it.isInternal() }
                            if (onlineMaterials?.isNotEmpty() == true) {
                                hasOnlineMaterials = true
                            }
                            if (hasOnlineMaterials) {
                                maskTypeEvent.postValue(null)
                            } else {
                                if (FormulaRepo.isMaterialLoading) {
                                    maskTypeEvent.postValue(MaskType.Loading)
                                } else {
                                    maskTypeEvent.postValue(MaskType.NetError)
                                }
                            }
                        }
                    }
            } catch (ignore: Exception) {
                ignore.printStackTrace()
            }

            withContext(Dispatchers.Main) {
                updateFormulaUIStateIfNeed()
            }
        }
    }

    fun hasOnlineData(): Boolean {
        return formulaCategoryData.find { it.internalState != DecorateConstant.SIGN_1 } != null
    }


    private fun updateFormulaUIStateIfNeed() {
        if (formulaCategoryData.size > 3 && protocolInfo != null) {
            protocolInfo?.let { pair ->
                updateLocalFormulaState(pair.second, isRecently = true, isAdd = true)
                // 定位分类位置
                val index = formulaCategoryData.indexOfFirst { it.categoryId == pair.first }
                if (index >= 0) {
                    triggerChildListScrollIfNeed(index, 0, pair.second?.materialId)
                    categorySelectEvent.postValue(index)
                }
            }
            protocolInfo = null
        }
    }

    fun processProtocolIfNeed(studioViewModel: ImageStudioViewModel, routerEntity: RouterEntity? = null) {
        routerEntity?.takeIf { it.lastPathSegment == UriConstant.PATH_T_FORMULA }?.let { temp ->
            val materialID = temp.getParameter(UriConstant.KEY_PARAM_CONTENT)
            if (materialID?.isNotEmpty() == true) {
                studioViewModel.showLoadingEvent.value = true
                viewModelScope.launch(Dispatchers.IO) {
                    FormulaRepo.searchFormula(materialID, true)?.let { formula ->
                        studioViewModel.routerEntity = null
                        protocolInfo = Pair(formula.categoryId, formula)
                        applyFromStore = false
                        withContext(Dispatchers.Main) {
                            applyFormula(
                                formula,
                                studioViewModel.layerInfoManager.getUserPictures(),
                                studioViewModel, isRecommendFirst = false,
                                isNeedTriggerScroll = false
                            )
                            updateFormulaUIStateIfNeed()
                        }
                        // 统计展示
                        val map = HashMap<String, String>(8)
                        map["template_id"] = formula.materialId
                        map["tem_tag"] = SpmAnalytics.transFormulaCatId(formula.categoryId)
                        MTAnalyticsAgent.logEvent(
                            MTAnalyticsConstant.beauty_template_material_appr,
                            map
                        )
                    }
                }
            } else {
                temp.getParameter(UriConstant.KEY_PARAM_CATEGORY)?.let { catID ->
                    studioViewModel.routerEntity = null
                    protocolInfo = Pair(catID, null)
                    updateFormulaUIStateIfNeed()
                }
            }
        }
    }


    fun handleProtocolIfNeed(studioViewModel: ImageStudioViewModel) {
        studioViewModel.routerEntity?.takeIf {
            it.lastPathSegment == UriConstant.PATH_T_FORMULA
        }?.apply {
            var defaultSelectFormulaId = this.getParameter(WebConstant.KEY_ID)
            // 获取新协议的参数
            if (defaultSelectFormulaId == null) {
                defaultSelectFormulaId = this.getParameter(UriConstant.KEY_PARAM_CONTENT)
            }
            // 套用配方效果
            defaultSelectFormulaId?.let {
                studioViewModel.routerEntity = null
                viewModelScope.launch(Dispatchers.IO) {
                    FormulaRepo.searchFormula(it, false)?.let { formula ->
                        applyFromStore = false
                        withContext(Dispatchers.Main) {
                            studioViewModel.showLoadingEvent.value = true
                            if (getParameter(UriConstant.FROM_SEARCH) != null) {
                                formula.formulaSearchCategory = "BP_cat_TEM_SCH "
                            }
                            applyFormula(
                                formula,
                                studioViewModel.layerInfoManager.getUserPictures(),
                                studioViewModel, isRecommendFirst = false,
                                isNeedTriggerScroll = false
                            )
                            if (isLoadedPanelData) {
                                updateLocalFormulaState(formula, isRecently = true, isAdd = true)
                            } else {
                                protocolFormula = formula
                            }
                        }
                        val map = HashMap<String, String>(8)
                        map["template_id"] = formula.materialId
                        map["tem_tag"] = SpmAnalytics.transFormulaCatId(formula.categoryId)
                        MTAnalyticsAgent.logEvent(
                            MTAnalyticsConstant.beauty_template_material_appr,
                            map
                        )
                    }
                }
            } ?: kotlin.run {
                // 获取分类ID
                getParameter(WebConstant.KEY_CATEGORY)?.let { catID ->
                    studioViewModel.routerEntity = null
                    viewModelScope.launch(Dispatchers.Main) {
                        val index = formulaCategoryData.indexOfFirst { it.categoryId == catID }
                        if (index >= 0) {
                            triggerChildListScrollIfNeed(index, 0)
                            categorySelectEvent.postValue(index)
                        } else {
                            protocolCatID = catID
                        }
                    }
                }
            }
        }
    }


    fun triggerGroupListScrollIfNeed(firstChildPos: Int) {
        var formulaSize = 0
        formulaCategoryData.forEachIndexed { pos, entity ->
            // Recent 是单独的不算
            if (entity.categoryId != DecorateConstant.NEW_CATEGORY_RECENT &&
                entity.categoryId != DecorateConstant.NEW_CATEGORY_COLLECT
            ) {
                formulaSize += entity.displayFormulaList.size
                if (firstChildPos <= formulaSize - 1) {
                    if (pos != curSelectCategoryPos) {
                        curSelectCategoryPos = pos
                        categorySelectEvent.value = pos
                    }
                    return
                }
            }
        }
    }

    private var isLoadedRecent = false
    private var isLoadedCollect = false

    fun loadPanelLocalFormulas(isRecently: Boolean) {
        if ((isLoadedRecent && isRecently) || (isLoadedCollect && !isRecently)) {
            return
        }

        val targetId = if (isRecently) {
            DecorateConstant.NEW_CATEGORY_RECENT
        } else {
            DecorateConstant.NEW_CATEGORY_COLLECT
        }
        formulaCategoryData.find { it.categoryId == targetId }
            ?.takeIf { it.displayFormulaList.isEmpty() }?.let {
                if (isRecently) {
                    isLoadedRecent = true
                } else {
                    isLoadedCollect = true
                }
                viewModelScope.launch(Dispatchers.IO) {
                    FormulaRepo.loadFavoriteOrCollectFormulas(it).collect {
                        // 通知刷新。
                        withContext(Dispatchers.Main) {
                            cateUpdateEvent.value = it
                        }
                    }
                }
            }
    }

    //关闭搜索弹窗 或者重新resume的时候检查是否需要刷新收藏状态
    fun refreshCollection() {
        if (FormulaRepo.shouldRefreshTabCollection) {
            "刷新收藏列表".LOGV_Formula()
            formulaCategoryData.find { it.categoryId == DecorateConstant.NEW_CATEGORY_COLLECT }
                ?.let { formulaCategory ->
                    isLoadedCollect = true
                    launch({
                        withContext(Dispatchers.IO) {
                            FormulaRepo.materialDao.loadFavoriteEntity().toMutableList().let {
                                formulaCategory.allFormulaList = it
                                formulaCategory.displayFormulaList = it
                                // 通知刷新。
                                withContext(Dispatchers.Main) {
                                    cateUpdateEvent.value = formulaCategory
                                    FormulaRepo.shouldRefreshTabCollection = false
                                }
                            }
                        }
                    })
                }
            refreshDataEvent.value = true
        }
    }

    fun updateLocalFormulaState(
        item: JsFormula?,
        isRecently: Boolean = false,
        isAdd: Boolean = true
    ) {
        val targetCate = if (isRecently) {
            formulaCategoryData.elementAtOrNull(0)
        } else {
            formulaCategoryData.elementAtOrNull(1)
        }

        item ?: return
        targetCate ?: return
        if (isRecently) {
            item.recentUseTime = System.currentTimeMillis()
        } else {
            item.favoriteTime = if (isAdd) System.currentTimeMillis() else 0L
            // 同步本地所有的素材的收藏状态
            formulaCategoryData.flatMap { it.displayFormulaList }
                .filter { it.materialId == item.materialId }.forEach {
                    it.favoriteTime = item.favoriteTime
                    formulaCollectStateEvent.value = it
                }
        }
        if ((isRecently && !isLoadedRecent) || (!isRecently && !isLoadedCollect)) {
            FormulaRepo.collect(item)
        } else {
            targetCate.displayFormulaList.let { list ->
                if (isAdd) {
                    list.find { it.materialId == item.materialId }?.let {
                        list.remove(it)
                        list.add(0, it)
                    } ?: kotlin.run {
                        if (list.size >= 30) {
                            list.removeLast()
                        }
                        list.add(0, item)
                    }
                } else {
                    list.find { it.materialId == item.materialId }?.let {
                        list.remove(it)
                    }
                }
                targetCate.subMaterials = list.map { it.materialId }.toMutableList()
                viewModelScope.launch(Dispatchers.IO) {
                    FormulaRepo.collect(item)
                    FormulaRepo.updateCategory2Db(targetCate)
                    targetCate.let { cateUpdateEvent.postValue(it) }
                }
            }
        }
    }


    fun triggerChildListScrollIfNeed(
        selectGroupPos: Int,
        firstVisible: Int,
        childID: String? = null
    ) {
        curSelectCategoryPos = selectGroupPos
        var formulaSize = 0
        formulaCategoryData.forEachIndexed { pos, entity ->
            // Recent 是单独的不算
            if (entity.categoryId != DecorateConstant.NEW_CATEGORY_RECENT
                && entity.categoryId != DecorateConstant.NEW_CATEGORY_COLLECT
            ) {
                formulaSize += if (selectGroupPos == pos) {
                    if (entity.displayFormulaList.isEmpty()) 0 else 1
                } else {
                    entity.displayFormulaList.size
                }
                if (selectGroupPos == pos) {
                    val tempIndex = childID?.let { entity.displayFormulaList.indexOfFirst { it.materialId == childID } } ?: -1
                    if (tempIndex >= 0) {
                        formulaSelectEvent.value = formulaSize - 1 + tempIndex
                    } else if (firstVisible != formulaSize - 1) {
                        formulaSelectEvent.value = formulaSize - 1
                    }
                    return
                }
            }
        }
    }

    /**
     * 配方解析流程
     */
    val formulaFlowEvent = MutableLiveData<FormulaWrapper>()

    /**
     * 自动套用的配方
     */
    var autoApplyFormula: JsFormula? = null

    /**
     * 从商店页返回套用素材
     */
    var applyFromStore: Boolean = false

    /**
     * 套用配方
     */
    fun applyFormula(
        entity: JsFormula?,
        pictureLayerInfo: List<PictureLayerInfo>,
        studioViewModel: ImageStudioViewModel,
        isRecommendFirst: Boolean = true,
        isNeedTriggerScroll: Boolean = true
    ) {

        entity?.run {
            //复选中的图片图层
            var reselectPictureLayerInfo: PictureLayerInfo? =
                studioViewModel.findReselectPictureLayerInfo(pictureLayerInfo)
            // 下载
            var tempIndex = -1
            if (isNeedTriggerScroll) {
                for (index in if (isRecommendFirst) 0 until formulaData.size else formulaData.lastIndex downTo -1) {
                    if (entity.materialId == formulaData[index].materialId) {
                        tempIndex = index
                        break
                    }
                }
            }
            if (isNeedTriggerScroll && tempIndex != -1) {
                formulaSelectEvent.value = tempIndex
            }
            /// 如果是从收藏/推荐分类下套用的话，需要检查下配方的有效性。
            /// 因为本地的配方可能已经被服务端下了。
            if ((entity.favoriteTime > 0 || entity.recentUseTime > 0)
                && entity.isNeedCheckValid
            ) {
                viewModelScope.launch(Dispatchers.IO) {
                    FormulaRepo.checkFormulaValid(entity)
                }
            }
            autoApplyFormula = this
            val wrapper = FormulaWrapper(
                this,
                pictureLayerInfo,
                layerInfoManager = studioViewModel.layerInfoManager,
                selectPictureLayerInfo = reselectPictureLayerInfo
            )

            // 记录进入配方面板前产生的操作记录。
            studioViewModel.stepStack.getTopStep()?.let {
                if (stepBeforeApplyFormula == null) {
                    stepBeforeApplyFormula = it
                }
            }

            FormulaConvertFactory.applyFormula(wrapper, studioViewModel) {
                if (it.jsFormula == autoApplyFormula && !it.cancelFromUser) {
                    formulaFlowEvent.postValue(it)
                }
            }
        }
    }

    /**
     * 是否第一次展示
     */
    fun isFormulaFirstVisible(category: String, formulaId: String): Boolean {
        var result = formulaShowedMap[category]
        if (result == null) {
            result = HashMap<String, Int>()
            formulaShowedMap[category] = result
        }
        if (!result.contains(formulaId)) {
            result[formulaId] = 1
            return true
        }
        return false
    }


    /**
     * 记录重置节点
     */
    fun recordFormulaResetPoint() {
        if (stepOnEnterFormula == null) {
            stepBeforeApplyFormula?.let {
                stepOnEnterFormula = it
                stepBeforeApplyFormula = null
            }
        }
    }


    fun clearResetPoint() {
        stepBeforeApplyFormula = null
        stepOnEnterFormula = null
    }


    fun getCurrentSelectCategory(): String? {
        val pos = categorySelectEvent.value ?: -1
        return formulaCategoryData.takeIf { it.size > pos && pos >= 0 }
            ?.run {
                formulaCategoryData[pos].categoryId
            }
    }
}