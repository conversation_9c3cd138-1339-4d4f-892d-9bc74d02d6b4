package com.commsource.studio.formula

import android.content.Context
import android.view.View
import android.view.ViewGroup
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemFormulaViewholderBinding
import com.commsource.duffle.formula.FormulaRepo
import com.commsource.statistics.ABTestManager
import com.commsource.studio.ImageStudioViewModel
import com.commsource.util.GlideProxy
import com.commsource.util.LOGV
import com.commsource.util.LOGV_Formula
import com.commsource.util.ResourcesUtils
import com.commsource.util.coroutine.CloseableCoroutineScope
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.resize
import com.commsource.util.visible
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FormulaViewHolder(context: Context, parent: ViewGroup) :
    BaseViewHolder<JsFormula>(context, parent, R.layout.item_formula_viewholder) {

    val mViewBinding by lazy { ItemFormulaViewholderBinding.bind(itemView) }

    private val defaultHeight = 90.dp()

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<JsFormula>?,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        item?.entity?.let { formula ->
            // 调整宽高
            val layoutParams = mViewBinding.card.layoutParams
            layoutParams.height = defaultHeight
            layoutParams.width = (defaultHeight * formula.getWHRatio()).toInt()
            mViewBinding.card.layoutParams = layoutParams
            // New icon
            mViewBinding.newIcon.visibility =
                if (formula.needShowNew() && item.entity.favoriteTime <= 0) View.VISIBLE else View.GONE
            // 付费角标
            mViewBinding.premiumIcon.visibility =
                if (ABTestManager.isNeedHideVipIcon(ABTestManager.TYPE_PHOTO_EDIT, true)) {
                    View.GONE
                } else {
                    if (formula.isNeedPaid()) View.VISIBLE else View.GONE
                }
            // 缩略图
            mViewBinding.card.loadThumbnail {
                load(item.entity.formulaThumbnail.resize(heightSize = 90.dp, widthHeightRatio = 1f))
                    .error(GlideProxy.with(mContext).load(item.entity.formulaThumbnail))
                    .fadeTransition(150)
            }
            // 收藏角标
            if (item.entity.favoriteTime > 0) {
                mViewBinding.ivCollect.visible()
            } else {
                mViewBinding.ivCollect.gone()
            }

            //自动化
            mViewBinding.card.tag = formula.materialId
        }
    }

}