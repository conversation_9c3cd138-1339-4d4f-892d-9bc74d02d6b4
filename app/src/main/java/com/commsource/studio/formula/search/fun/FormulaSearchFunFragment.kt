package com.commsource.studio.formula.search.`fun`

import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautyplus.BR
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentFormulaSearchFunBinding
import com.commsource.camera.xcamera.cover.bottomFunction.BaseBottomSubFragment
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.shop.FragmentBuilder
import com.commsource.home.SearchDataTasks
import com.commsource.library_mvvm.AndroidViewModelFactory
import com.commsource.search_common.entity.FORMULA_WAY
import com.commsource.search_common.entity.FormulaData
import com.commsource.search_common.entity.KeyWordInfo
import com.commsource.search_common.entity.NET_KEY_FORMULA
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.StudioLayoutConstants.SEARCH_FUNCTION_INIT_HEIGHT
import com.commsource.studio.formula.FormulaViewModel
import com.commsource.studio.formula.search.SearchFormulaItemHolder
import com.commsource.util.FoldableScreenUtils
import com.commsource.util.LOGV
import com.commsource.util.LOGV_Formula
import com.commsource.util.UIHelper
import com.commsource.util.ViewUtils
import com.commsource.util.alphaDismiss
import com.commsource.util.alphaShow
import com.commsource.util.coroutine.launch
import com.commsource.widget.mask.MaskType
import com.meitu.common.AppContext.application
import com.meitu.library.util.device.DeviceUtils

class FormulaSearchFunFragment : BaseBottomSubFragment() {

    val viewBinding by lazy { FragmentFormulaSearchFunBinding.inflate(layoutInflater) }

    private lateinit var imageStudioViewModel: ImageStudioViewModel

    private val formulaViewModel by lazy { ViewModelProvider(ownerActivity).get(FormulaViewModel::class.java) }


    private val formulaSearchViewModel by lazy {
        ViewModelProvider(
            this,
            AndroidViewModelFactory(application) {
                FormulaSearchFunViewModel(
                    ownerActivity,
                    formulaViewModel,
                )
            }
        )[FormulaSearchFunViewModel::class.java]
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        //限定面板高度
        ViewUtils.setHeight(viewBinding.searchLoadMore, SEARCH_FUNCTION_INIT_HEIGHT)
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewBinding.setVariable(BR.viewModel, formulaSearchViewModel)
        viewBinding.lifecycleOwner = this
        //关联ViewModel
        //让ViewModel拥有View的生命周期感应
        lifecycle.addObserver(formulaSearchViewModel)
        imageStudioViewModel =
            ViewModelProvider(requireActivity())[ImageStudioViewModel::class.java]

        imageStudioViewModel.fullOutCoverEvent.value = true
        imageStudioViewModel.collapsedPanelEvent.value = false
        formulaSearchViewModel.searchEditViewModel.showInput()

        //执行退出搜索/面板收起
        imageStudioViewModel.fullCoverClickEvent.observe(viewLifecycleOwner) {
            "隐藏对话框".LOGV()
            FragmentBuilder.instance.hide(this.javaClass)
            imageStudioViewModel.fullOutCoverEvent.value = false
            formulaSearchViewModel.searchEditViewModel.closeInput()
        }

        viewBinding.mask.maskContainerHelper.newBuilder()
            .bindView(MaskType.NetError, R.id.tv_action) {
                formulaSearchViewModel.netInitData<FormulaData>(SearchFormulaItemHolder::class.java)
            }
            .build()


        launch({
            SearchDataTasks.initSearchTags(FORMULA_WAY, arrayOf(NET_KEY_FORMULA))
        })

        SearchDataTasks.initHotWord(this, KeyWordInfo.TYPE_TEMPLATES) {
            formulaSearchViewModel.searchHotViewModel.loadView(formulaViewModel.shouldSPM)
            formulaViewModel.shouldSPM = false
        }

        formulaSearchViewModel.showTipsEvent.observe(viewLifecycleOwner) {
            ViewUtils.setWidth(
                viewBinding.favoriteTips,
                (DeviceUtils.getScreenWidth() * 0.595f).toInt()
            )
            viewBinding.favoriteTips.alphaShow()
            UIHelper.getInstance().postDelayed({
                viewBinding.favoriteTips.alphaDismiss()
            }, 3000)
        }

    }


    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        "hidden $hidden".LOGV()
        if (!hidden) {
            imageStudioViewModel.fullOutCoverEvent.value = true
            imageStudioViewModel.collapsedPanelEvent.value = false
            formulaSearchViewModel.searchHotViewModel.trendWord()
        }
    }

    private var hasPause = false

    override fun onResume() {
        super.onResume()
        if (hasPause) {
            formulaSearchViewModel.shouldReloadData()
            hasPause = false
        }

    }

    override fun onPause() {
        super.onPause()
        hasPause = true
    }

    override fun onScreenSizeConfigurationChanged() {
        super.onScreenSizeConfigurationChanged()
        //限定面板高度,折叠屏可能是上下折叠的，重置一下高度
        ViewUtils.setHeight(viewBinding.searchLoadMore, SEARCH_FUNCTION_INIT_HEIGHT)
        formulaSearchViewModel.searchAdapter.notifyDataSetChanged()
    }
}