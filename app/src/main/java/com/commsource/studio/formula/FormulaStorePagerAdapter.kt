package com.commsource.studio.formula

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.commsource.duffle.sticker.StiCategory

/**
 *
 * Created on 2020/7/29
 * <AUTHOR>
 */
class FormulaStorePagerAdapter(activity: FragmentActivity) : FragmentStateAdapter(activity) {

    private val dataSource = mutableListOf<FormulaCategory>()

    fun setDataSource(list: List<FormulaCategory>?) {
        dataSource.clear()
        list?.let { dataSource.addAll(it) }
        notifyDataSetChanged()
    }


    override fun getItemCount(): Int {
        return dataSource.size
    }

    override fun createFragment(position: Int): Fragment {
        return FormulaChildStoreFragment().apply {
            arguments = Bundle().apply {
                putString("CategoryId", dataSource[position].categoryId)
            }
        }
    }

    override fun getItemId(position: Int): Long {
        return dataSource[position].categoryId.hashCode().toLong()
    }

    override fun containsItem(itemId: Long): Boolean {
        return dataSource.find { it.categoryId.hashCode().toLong() == itemId } != null
    }

}