package com.commsource.studio.formula

import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.commsource.beautyplus.BaseActivity
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ActivityFormulaShopBinding
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.billing.SubSource
import com.commsource.widget.FastCenterScrollLayoutManager
import com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.shop.FragmentBuilder
import com.commsource.duffle.formula.detail.FormulaDetailActivity
import com.commsource.home.SearchDataTasks
import com.commsource.search_common.entity.FORMULA_WAY
import com.commsource.search_common.entity.KeyWordInfo
import com.commsource.search_common.entity.NET_KEY_FORMULA
import com.commsource.search_common.holder.SearchStoreTagViewHolder
import com.commsource.search_common.repo.KeyWordRepo
import com.commsource.search_common.view.SearchEditTextView
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.formula.search.store.FormulaStoreSearchFragment
import com.commsource.util.ErrorNotifier
import com.commsource.util.LOGV
import com.commsource.util.LOGV_Formula
import com.commsource.util.NetworkMonitor
import com.commsource.util.RTLTool
import com.commsource.util.ViewUtils
import com.commsource.util.alphaDismiss
import com.commsource.util.alphaShow
import com.commsource.util.coroutine.launch
import com.commsource.util.delegate.IProcessHandler
import com.commsource.util.delegate.process.SubscribeProcess
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.invisible
import com.commsource.util.isVisible
import com.commsource.util.setMarginTop
import com.commsource.util.translateXTo
import com.commsource.util.visible
import com.commsource.widget.DisplayExtension
import com.commsource.widget.mask.DataMask
import com.commsource.widget.mask.MaskType
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.library.hwanalytics.spm.SPMManager
import com.meitu.library.hwanalytics.spm.bean.PositionModel
import com.meitu.library.util.device.DeviceUtils
import com.meitu.library.util.net.NetUtils
import com.pixocial.androidx.core.extension.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class FormulaStoreActivity : BaseActivity() {

    private lateinit var viewBinding: ActivityFormulaShopBinding

    private val mTagAdapter: BaseRecyclerViewAdapter by lazy { BaseRecyclerViewAdapter(this) }

    private val formulaStoreViewModel: FormulaStoreViewModel by lazy {
        ViewModelProvider(this).get(FormulaStoreViewModel::class.java)
    }

    private var searchFragment: BaseFragment? = null

    private val vpAdapter by lazy { FormulaStorePagerAdapter(this) }

    private var isCancel: Boolean = false

    //是否成功拉取到搜索热词
    @Volatile
    private var hasFetchHotWord = false

    companion object {
        const val TARGET_FORMULA_ID = "TARGET_FORMULA_ID"
        const val SEARCH_FORMULA = "SEARCH_FORMULA"
        fun startFormulaShopActivityForResult(frg: Fragment, requestCode: Int) {
            val intent = Intent(frg.context, FormulaStoreActivity::class.java)
            frg.startActivityForResult(intent, requestCode)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        overridePendingTransition(R.anim.slide_right_in, R.anim.slide_left_out)
        viewBinding = DataBindingUtil.setContentView(this, R.layout.activity_formula_shop)
        viewBinding.vp.isUserInputEnabled = false
        viewBinding.vp.adapter = vpAdapter
        viewBinding.mask.maskContainerHelper.newBuilder()
            .addMaskGroup(DataMask.getCommonGroup())
            .bindView(MaskType.NetError, R.id.tv_action) {
                if (NetUtils.canNetworking(AppContext.context)) {
                    viewBinding.mask.showMask(MaskType.Loading)
                    formulaStoreViewModel.loadCategoryData()
                } else {
                    ErrorNotifier.showNetworkErrorToast()
                }
            }
            .build()

        viewBinding.imgBack.setOnClickListener {
            onBackPressed()
        }
        viewBinding.imgBack2.setOnClickListener {
            onBackPressed()
        }

        viewBinding.rvGroup.layoutManager =
            FastCenterScrollLayoutManager(
                this,
                RecyclerView.HORIZONTAL,
                false
            )
        viewBinding.rvGroup.addItemDecoration(FormulaCategoryItemDecoration())
        mTagAdapter.setOnEntityClickListener(FormulaCategory::class.java) { position, entity ->
            onTagClick(entity)
            true
        }
        viewBinding.rvGroup.adapter = mTagAdapter

        viewBinding.lineSelect.recyclerView = viewBinding.rvGroup

        //数据监听
        initObserver()

        formulaStoreViewModel.loadCategoryData()

        lifecycle.addObserver(viewBinding.searchEdit)

        //全面屏幕适配
        viewBinding.imgBack2.visible()
        viewBinding.mask.showMask(MaskType.Loading)
        if (DisplayExtension.hasCutout()) {
            viewBinding.imgBack2.setMarginTop(DeviceUtils.getStatusHeight())
            viewBinding.topBar.setMarginTop(DeviceUtils.getStatusHeight())
        }

        NetworkMonitor.registerNetworkStateListener(this) {
            if (it && (mTagAdapter.items?.size ?: 0) <= 0) {
                formulaStoreViewModel.loadCategoryData()
            }
        }

        initSearch()

    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.run {
            val materialId = getStringExtra("materialId")
            "mamaterialId $materialId".LOGV_Formula()
            formulaStoreViewModel.uiReplyEvent.value = materialId
        }
    }

    /**
     * isCancel 是否是取消退出的fragment
     */
    private fun backToStore(isCancel: Boolean = false) {
        this.isCancel = isCancel
        viewBinding.imgBack.visible()
        viewBinding.cancelSearch.alphaDismiss()
        viewBinding.searchEdit.translateXTo(0f, 300) {
            //修改搜索框的宽度为
            ViewUtils.setWidth(viewBinding.searchEdit, getSearchLayoutWidth())
        }
        viewBinding.mask.alphaShow()
        viewBinding.searchEdit.cancelSearch()

        searchFragment?.run {
            FragmentBuilder.instance.remove(this.javaClass)
            searchFragment = null
        }
        viewBinding.searchEdit.restartScroll()
        viewBinding.searchEdit.clearText()
    }

    override fun addSpm() {
        val model = PositionModel()
        model.pageId = FormulaStoreActivity::class.java.simpleName
        model.setPageObject(this)
        model.page = "1012_04"
        SPMManager.instance.pushSpm(model)
    }

    override fun updateSpmInfo() {
        val model = PositionModel()
        model.pageId = FormulaStoreActivity::class.java.simpleName
        model.setPageObject(this)
        model.page = "1012_04"
        SPMManager.instance.update(model)
    }

    /**
     * 动态计算搜索框的宽度
     */
    private fun getSearchLayoutWidth(): Int {
        val leftMargin = if (viewBinding.imgBack.isVisible) 15.dp() else 15.dp()
        val imgBackMargin = if (viewBinding.imgBack.isVisible) viewBinding.imgBack.width else 0
        val cancelMargin =
            if (viewBinding.cancelSearch.isVisible) viewBinding.cancelSearch.width else 0
        val rightMargin = if (viewBinding.cancelSearch.isVisible) 16.dp() else 15.dp()
        val cancelLeftMargin = if (viewBinding.cancelSearch.isVisible) 16.dp() else 0
        return DeviceUtils.getScreenWidth() - leftMargin - imgBackMargin - cancelMargin - rightMargin - cancelLeftMargin
    }

    private fun initObserver() {
        // 点击立即使用
        // 默认使用第一个
        formulaStoreViewModel.uiReplyEvent.observe(this) { formulaId ->
            val result = Intent()
            result.putExtra(TARGET_FORMULA_ID, formulaId)
            setResult(RESULT_OK, result)
            finish()
            overridePendingTransition(R.anim.slide_left_in, R.anim.slide_right_out)
        }

        formulaStoreViewModel.searchUIReplyEvent.observe(this) { formulaId ->
            val result = Intent()
            result.putExtra(SEARCH_FORMULA, formulaId)
            setResult(RESULT_OK, result)
            finish()
            overridePendingTransition(R.anim.slide_left_in, R.anim.slide_right_out)
        }


        // tag 显示
        formulaStoreViewModel.tagDataEvent.observe(this) { source ->
            // 刷新数据
            source?.takeIf { it.isNotEmpty() }?.let { categories ->
                mTagAdapter.updateItemEntities(
                    AdapterDataBuilder.create()
                        .addEntities(categories, SearchStoreTagViewHolder::class.java)
                        .build(), false
                )
                vpAdapter.setDataSource(categories)
                viewBinding.mask.hideAll()
                viewBinding.imgBack2.gone()
                // 当前选中保持不变
                val lastIndex =
                    (mTagAdapter.currentSelectEntity as? FormulaCategory)?.let { lastCate ->
                        source.indexOfFirst { it.categoryId == lastCate.categoryId }
                    } ?: 0
                // 默认选中第一个
                viewBinding.lineSelect.setSelectIndex(lastIndex, false)
                mTagAdapter.setCurrentSelectPosition(lastIndex)
                viewBinding.vp.setCurrentItem(lastIndex, false)
            } ?: kotlin.run {
                viewBinding.mask.showMask(MaskType.NetError)
            }
        }
        // 订阅事件
        formulaStoreViewModel.subscribeEvent.observe(this) {
            IProcessHandler(this)
                .execute(object : SubscribeProcess(SubSource.FROM_FORMULA_SHOP) {
                    override fun onSubscribeResult(isSubcribe: Boolean) {
                        // 刷新数据。
                        if (isSubcribe) {
                            formulaStoreViewModel.subscribeResultEvent.value = true
                        }
                    }
                })
        }

    }


    private fun initSearch() {

        launch({
            SearchDataTasks.initSearchTags(FORMULA_WAY, arrayOf(NET_KEY_FORMULA))
        })

        fetchHotWord()


        viewBinding.searchEdit.setOnClickSearchLayoutListener(object :
            SearchEditTextView.OnClickSearchLayoutListener {

            override fun onClearClick() {
                //如果当前在搜索loading状态，取消loading
                formulaStoreViewModel.delWord.value = true
            }

            override fun onCancelClick() {
            }

            override fun onSearchClick(hotword: String?) {
                if (searchFragment != null) {
                    "已经在搜索界面".LOGV()
                    formulaStoreViewModel.inputEvent.value = viewBinding.searchEdit.getText()
                    viewBinding.searchEdit.stopScroll()
                    viewBinding.searchEdit.requestEditFocus(true)
                    return
                }
                fetchHotWord()

                MTAnalyticsAgent.logEvent(
                    MTAnalyticsConstant.material_search_button_clk,
                    HashMap<String, String>(4).apply {
                        put(MTAnalyticsConstant.source, MTAnalyticsConstant.shop_page)
                        put(MTAnalyticsConstant.material_type, MTAnalyticsConstant.template)
                    })
                //停止轮播热搜词
                viewBinding.searchEdit.stopScroll()
                //渐隐渐显过渡
                viewBinding.imgBack.invisible()
                viewBinding.cancelSearch.alphaShow()
                val targetTransX = if (RTLTool.isLayoutRtl()) {
                    viewBinding.imgBack.width.toFloat()
                } else {
                    -(viewBinding.imgBack.width).toFloat()
                }
                viewBinding.searchEdit.translateXTo(targetTransX, 300) {
                    //修改搜索框的宽度
                    ViewUtils.setWidth(viewBinding.searchEdit, getSearchLayoutWidth())
                }
                viewBinding.mask.alphaDismiss()
                //打开搜索页面
                searchFragment = FragmentBuilder.instance.show(
                    viewBinding.detailContainer,
                    FormulaStoreSearchFragment::class.java,
                    android.R.anim.fade_in,
                    android.R.anim.fade_out
                )

                viewBinding.searchEdit.requestEditFocus(true)
            }

            override fun onInvokeSearch(searchWord: String?) {
                searchWord?.run {
                    formulaStoreViewModel.searchWord.value = this
                }
            }

            override fun onInputting(searchWord: String) {
                formulaStoreViewModel.inputEvent.value = searchWord

            }

        })

        viewBinding.cancelSearch.setOnClickListener {
            backToStore(true)
        }

        formulaStoreViewModel.searchEvent.observe(this) {
            it?.run {
                //停止轮播热搜词
                "停止轮播热搜词".LOGV()
                viewBinding.searchEdit.stopScroll()
                viewBinding.searchEdit.setText(it)
                viewBinding.searchEdit.requestEditFocus(false, "")
            }
        }

        formulaStoreViewModel.closeInputEvent.observe(this) {
            viewBinding.searchEdit.requestEditFocus(false)
        }

        formulaStoreViewModel.reScrollEvent.observe(this) {
            viewBinding.searchEdit.requestEditFocus(false, "")
            viewBinding.searchEdit.restartScroll()
        }
    }


    private fun fetchHotWord() {

        if (!hasFetchHotWord) {
            SearchDataTasks.initHotWord(this, KeyWordInfo.TYPE_TEMPLATES) {
                "获取热词成功，开始轮播".LOGV()
                setHotWord()
            }

            //无网情况先使用缓存数据
            if (!NetUtils.canNetworking() && KeyWordRepo.hasData(KeyWordInfo.TYPE_TEMPLATES)) {
                "无网，先使用缓存热词".LOGV()
                setHotWord()
            }


        }

    }

    private fun setHotWord() {

        hasFetchHotWord = true
        //拉取热搜词
        launch({
            withContext(Dispatchers.IO) {
                KeyWordRepo.fetchDataWithType(KeyWordInfo.TYPE_TEMPLATES).let {
                    withContext(Dispatchers.Main) {
                        if (it.isNotEmpty()) {
                            viewBinding.searchEdit.setHotWordList(it)
                            if (it.size > 1) {
                                //启动定时轮播热搜词,初次拉取才生效
                                if (viewBinding.searchEdit.checkTextLoop()) {
                                    viewBinding.searchEdit.initShowText(isRandom = false)
                                }
                            } else {
                                viewBinding.searchEdit.setSearchHintVisible(true)
                            }
                        } else {
                            viewBinding.searchEdit.setSearchHintVisible(false)
                        }
                    }
                }
            }
        })
    }


    private fun onTagClick(entity: FormulaCategory) {
        mTagAdapter.currentSelectEntity = entity
        viewBinding.rvGroup.smoothScrollToPosition(mTagAdapter.currentPosition)
        viewBinding.vp.setCurrentItem(mTagAdapter.currentPosition, false)
        viewBinding.lineSelect.setSelectIndex(mTagAdapter.currentPosition)
    }

    override fun onBackPressed() {
        when {
            //搜索物理返回拦截
            //搜索堆栈
            searchFragment != null -> {
                backToStore()
                return
            }

            else -> {
                finish()
                overridePendingTransition(R.anim.slide_left_in, R.anim.slide_right_out)
            }
        }

    }

    fun getViewBinding() = if (this::viewBinding.isInitialized) {
        viewBinding
    } else {
        null
    }
}