package com.commsource.studio.sub

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.TextView
import com.commsource.beautyplus.databinding.BeautyFilterTipsBinding
import com.commsource.camera.util.XAnimator
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.util.notnull
import com.commsource.util.visible
import com.meitu.library.util.device.DeviceUtils

/**
 * @Desc : 滤镜动画帮助类
 * <AUTHOR> Bear - 2020/8/26
 */
class BeautyFilterAnimHelper(val adjustLayout: FrameLayout,val collectTips:TextView,val nameTips:BeautyFilterTipsBinding) {

    private val collectAnimator = XAnimator.ofFloat(0f, 1f)
            .duration(300)
            .setAnimationListener(object : XAnimator.XAnimationListener {
                override fun onAnimationEnd(animation: XAnimator?) {
                }

                override fun onAnimationCancel(animation: XAnimator?) {
                }

                override fun onAnimationStart(animation: XAnimator?) {
                }

                override fun onAnimationUpdate(fraction: Float, value: Float) {
                    collectTips.translationY = translateYValuer.calculateValue(fraction)
                    collectTips.alpha = collectAlphaValuer.calculateValue(fraction)
                    adjustLayout.alpha = alphaValuer.calculateValue(fraction)
                }
            })

    //收藏动画
    private var translateYValuer = XAnimatorCalculateValuer()
    private var collectAlphaValuer = XAnimatorCalculateValuer()

    //收藏动画中的ALpha
    private var alphaValuer = XAnimatorCalculateValuer()

    private var isCollectShow = false

    /**
     * 显示滤镜名字悬浮Tips
     */
    fun showFloatingTips(tip1: String?, tip2: String?, leftToRight: Boolean = true) {
        tip1?.let {
            nameTips.rlFloating.animate().setStartDelay(0).setListener(null).cancel()
            nameTips.rlFloating.visible()
            nameTips.rlFloating.alpha = 0f
            nameTips.tvFloating.text = tip1
            nameTips.tvFloating2.text = tip2.notnull("")
            nameTips.rlFloating.translationX = if (leftToRight) (-DeviceUtils.dip2px(30f)).toFloat() else (DeviceUtils.dip2px(30f)).toFloat()
            nameTips.rlFloating.animate()
                    .alpha(1f)
                    .setStartDelay(0)
                    .translationX(0f)
                    .setInterpolator(DecelerateInterpolator())
                    .setDuration(1000)
                    .setListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            super.onAnimationEnd(animation)
                            nameTips.rlFloating.animate().setListener(null)
                                    .alpha(0f)
                                    .translationX(if (leftToRight) (DeviceUtils.dip2px(30f)).toFloat() else (-DeviceUtils.dip2px(30f)).toFloat())
                                    .setInterpolator(AccelerateInterpolator())
                                    .setDuration(1000)
                                    .start()
                        }
                    })
                    .start()
        }
    }

    /**
     * 隐藏收藏tips
     */
    private val runnable = Runnable {
        showCollectionTip(false)
    }

    /** 展示collect tips */
    fun showCollectionTip(isShow: Boolean) {
        if (isCollectShow == isShow) {
            return
        }
        this.isCollectShow = isShow
        collectAnimator.cancel()
        if (isCollectShow) {
            translateYValuer.mark(collectTips.translationY, -DeviceUtils.dip2fpx(50f))
            collectAlphaValuer.mark(collectTips.alpha, 1f)
            alphaValuer.mark(adjustLayout.alpha, 0f)
            collectTips.postDelayed(runnable, 2000)
        } else {
            collectTips.removeCallbacks(runnable)
            translateYValuer.mark(collectTips.translationY, 0f)
            collectAlphaValuer.mark(collectTips.alpha, 0f)
            alphaValuer.mark(adjustLayout.alpha, 1f)
        }
        collectAnimator.start()
    }

}