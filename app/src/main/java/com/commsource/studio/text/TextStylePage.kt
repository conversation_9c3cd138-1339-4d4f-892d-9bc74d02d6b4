package com.commsource.studio.text

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.ItemStylePageBinding
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.repository.child.TextTemplateRepository
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.component.ColorSelectComponent
import com.commsource.util.*
import com.commsource.widget.XSeekBar
import com.meitu.common.utils.ToastUtils

class TextStylePage(val fragment: BaseFragment, var curAttr: TextConfig.TemplateAttrEnum) {

    private val imageStudioViewModel by lazy { ViewModelProvider(fragment.ownerActivity).get(ImageStudioViewModel::class.java) }
    private val mViewModel by lazy { ViewModelProvider(fragment.ownerActivity).get(TextViewModel::class.java) }
    private var viewBinding: ItemStylePageBinding = DataBindingUtil.inflate(LayoutInflater.from(fragment.ownerActivity), R.layout.item_style_page, null, false)

    // 颜色选择面板
    private val colorPickerViewModel by lazy {
        ViewModelProvider(fragment).get(ColorSelectComponent.ColorPickerViewModel::class.java)
    }

    // 刚开始进入颜色选择面板的颜色值，这里临时记忆，因为颜色会被默认设置为Int最大值
    var originalColor: Int? = null

    init {
        initView()
    }

    private fun initView() {
        viewBinding.executePendingBindings()
        // 颜色
        viewBinding.colorPicker.apply {
            init(fragment, TextConfig.colorList)
            colorClickAction {
                mViewModel.hideColorPickerEvent.value = true
                setColorValueToEntity(it, false)
            }
            colorPickAction {
                // 自定义的颜色
                mViewModel.hideColorPickerEvent.value = false
                setColorValueToEntity(it, true)

                if (curAttr == TextConfig.TemplateAttrEnum.Shadow) {
                    mViewModel.paragraphConfig?.takeIf { it.shadowEnable }?.let {
                        if (mViewModel.enableShadow.value != true) {
                            mViewModel.enableShadow.value = true
                        }
                    }
                }
            }
            onColorPickMode {
                if (it) {
                    imageStudioViewModel.queueEventAndLoading {
                        viewBinding.colorPicker.setImage(imageStudioViewModel.generateWorkBitmap())
                    }
                }
                mViewModel.uiElementEvent.value = it

                // 颜色提取器，关闭阴影控件
                if (it) {
                    if (mViewModel.enableShadow.value != false) {
                        mViewModel.enableShadow.value = false
                    }
                }
            }
            colorCancelAction {
                mViewModel.hideColorPickerEvent.value = false
                setColorValueToEntity(TextGroupParam.CANCEL_COLOR, false)
            }

            colorPresetAction {
                mViewModel.hideColorPickerEvent.value = false
                setColorValueToEntity(TextGroupParam.PRESET_COLOR, false)
            }

            colorClickOnDisable {
                ToastUtils.showShortToast(R.string.t_edit_text_select_text_tips)
            }

            colorSelectClickAction {
                // 自定义的颜色
                mViewModel.hideColorPickerEvent.value = false
                // 显示选色面板
                colorPickerViewModel.showColorPickerEvent.value = true
            }
        }

        // 滑杆
        viewBinding.seekBar1.addOnProgressChangeListener(object : XSeekBar.OnProgressChangeListener {
            override fun onStartTracking(progress: Int, leftDx: Float) {
                mViewModel.hideColorPickerEvent.value = true
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                mViewModel.bubbleEvent.value = null
                mViewModel.attrChangeEvent.value = false
            }

            override fun onPositionChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                super.onPositionChange(progress, leftDx, fromUser)
                if (fromUser) {
                    mViewModel.bubbleParam[0] = viewBinding.seekBar1.left + leftDx
                    mViewModel.bubbleParam[1] = viewBinding.seekBar1.top - viewBinding.otherAttrContainer.scrollY.toFloat()
                    mViewModel.bubbleParam[2] = viewBinding.seekBar1.progress
                    mViewModel.bubbleEvent.value = mViewModel.bubbleParam
                }
            }

            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                // 滑杆1 可能出现在在
                if (fromUser) {
                    mViewModel.syncStyleValueToProgram(curAttr, progress, 1)
                    mViewModel.attrChangeEvent.value = true
                }
            }
        })

        // 滑杆2
        viewBinding.seekBar2.addOnProgressChangeListener(object : XSeekBar.OnProgressChangeListener {
            override fun onStartTracking(progress: Int, leftDx: Float) {
                mViewModel.hideColorPickerEvent.value = true
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                mViewModel.bubbleEvent.value = null
                mViewModel.attrChangeEvent.value = false
            }

            override fun onPositionChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                super.onPositionChange(progress, leftDx, fromUser)
                if (fromUser) {
                    mViewModel.bubbleParam[0] = viewBinding.seekBar2.left + leftDx
                    mViewModel.bubbleParam[1] = viewBinding.seekBar2.top - viewBinding.otherAttrContainer.scrollY.toFloat()
                    mViewModel.bubbleParam[2] = viewBinding.seekBar2.progress
                    mViewModel.bubbleEvent.value = mViewModel.bubbleParam
                }
            }

            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                // 滑杆2 可能出现在在
                if (fromUser) {
                    mViewModel.syncStyleValueToProgram(curAttr, progress, 2)
                    mViewModel.attrChangeEvent.value = true
                }
            }
        })


        // 滑杆3
        viewBinding.seekBar3.addOnProgressChangeListener(object : XSeekBar.OnProgressChangeListener {
            override fun onStartTracking(progress: Int, leftDx: Float) {
                mViewModel.hideColorPickerEvent.value = true
            }

            override fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {
                mViewModel.bubbleEvent.value = null
                mViewModel.attrChangeEvent.value = false
            }

            override fun onPositionChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                super.onPositionChange(progress, leftDx, fromUser)
                if (fromUser) {
                    mViewModel.bubbleParam[0] = viewBinding.seekBar3.left + leftDx
                    mViewModel.bubbleParam[1] = viewBinding.seekBar3.top - viewBinding.otherAttrContainer.scrollY.toFloat()
                    mViewModel.bubbleParam[2] = viewBinding.seekBar3.progress
                    mViewModel.bubbleEvent.value = mViewModel.bubbleParam
                }
            }

            override fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {
                // 滑杆3 可能出现在在
                if (fromUser) {
                    mViewModel.syncStyleValueToProgram(curAttr, progress, 3)
                    mViewModel.attrChangeEvent.value = true
                }
            }
        })

        //斜体、粗体啥的
        viewBinding.boldView.setOnClickListener {
            updateFontStyleUIOnClick(it)
            mViewModel.paragraphConfig?.let {
                it.isBold = viewBinding.boldView.isSelected
                mViewModel.attrChangeEvent.value = false
                mViewModel.hideColorPickerEvent.value = true
            }
        }

        viewBinding.strikeThroughView.setOnClickListener {
            updateFontStyleUIOnClick(it)
            mViewModel.paragraphConfig?.let {
                it.isStrikeThrough = viewBinding.strikeThroughView.isSelected
                mViewModel.attrChangeEvent.value = false
                mViewModel.hideColorPickerEvent.value = true
            }
        }

        viewBinding.italicView.setOnClickListener {
            updateFontStyleUIOnClick(it)
            mViewModel.paragraphConfig?.let {
                it.isItalic = viewBinding.italicView.isSelected
                mViewModel.attrChangeEvent.value = false
                mViewModel.hideColorPickerEvent.value = true
            }
        }

        viewBinding.underLineView.setOnClickListener {
            updateFontStyleUIOnClick(it)
            mViewModel.paragraphConfig?.let {
                it.isUnderline = viewBinding.underLineView.isSelected
                mViewModel.attrChangeEvent.value = false
                mViewModel.hideColorPickerEvent.value = true
            }
        }

        viewBinding.cancelTemplate.setOnClickListener {
            mViewModel.applyTemplate(TextTemplateRepository.emptyTextTemplate, pushStack = false)
            mViewModel.styleRefreshEvent.value = true
        }

        mViewModel.styleRefreshEvent.observe(fragment.viewLifecycleOwner, Observer {
            updateUI()
        })

        mViewModel.hideColorPickerEvent.observe(fragment.viewLifecycleOwner, Observer {
            if (viewBinding.colorPicker.getColorPickerLayerState()) {
                if (it) {
                    updateColorPickerStyle()
                }
                viewBinding.colorPicker.setColorPickerLayerState(false)
            }
        })

        updateUI()
    }

    // 确认颜色选中逻辑
    fun confirmColorSelect(color: Int) {
        viewBinding.colorPicker.colorPickerLayer?.colorChangeListener?.onColorSelected(color)
    }

    // 确认颜色选中逻辑
    fun onColorChange(color: Int?) {
        // 自定义的颜色
        mViewModel.hideColorPickerEvent.value = false
        val textGroupParam = mViewModel.focusTextLayerInfo?.textGroupParam
        mViewModel.paragraphConfig?.run {
            var isUseCustom = false
            if (curAttr == TextConfig.TemplateAttrEnum.Background && showBgMyColor) {
                isUseCustom = true
            }
            if (curAttr == TextConfig.TemplateAttrEnum.Shadow && showShadowMyColor) {
                isUseCustom = true
            }
            if (curAttr == TextConfig.TemplateAttrEnum.Stroke && showStrokeMyColor) {
                isUseCustom = true
            }
            if (curAttr == TextConfig.TemplateAttrEnum.Text && showTextMyColor) {
                isUseCustom = true
            }
            if (color == null || color == TextGroupParam.PRESET_COLOR) {
                textGroupParam?.resetValueToDefault(this, curAttr)
            } else if (color == TextGroupParam.CANCEL_COLOR) {
                textGroupParam?.disableAttrs(this, curAttr)
            } else {
                textGroupParam?.setColor(this, color, curAttr, isUseCustom)
            }
            mViewModel.attrChangeEvent.value = true
        }
    }

    /**
     * 当前的选中 、或者是否存在文字装饰。
     * 决定了颜色选择器的样式
     * */
    private fun updateColorPickerStyle() {
        val curAttachTextGroupParam = mViewModel.focusTextLayerInfo?.textGroupParam
        val paragraphConfig = mViewModel.paragraphConfig
        val curAttr = curAttr
        val resultList = ArrayList<String>().apply {
            this.addAll(TextConfig.colorList)
        }
        when {
            // 无选中
            curAttachTextGroupParam == null -> {
                if (curAttr.id != TextConfig.TemplateAttrEnum.Text.id) {
                    resultList.add(0, TextConfig.CANCEL)
                }
                viewBinding.colorPicker.updateColorList(resultList, false)
                pickColorItem(curAttachTextGroupParam, paragraphConfig, curAttr)
            }

            // 有文本，并且选中的是空模版的时候
            curAttachTextGroupParam.template.isEmptyTemplate -> {
                if (curAttr.id != TextConfig.TemplateAttrEnum.Text.id) {
                    resultList.add(0, TextConfig.CANCEL)
                }
                viewBinding.colorPicker.updateColorList(resultList, false)
                pickColorItem(curAttachTextGroupParam, paragraphConfig, curAttr)
            }

            //有文本，并且选中的是正常模版
            !curAttachTextGroupParam.template.isEmptyTemplate -> {
                if (curAttr.id != TextConfig.TemplateAttrEnum.Text.id) {
                    resultList.add(0, TextConfig.CANCEL)
                }
                viewBinding.colorPicker.updateColorList(resultList, true)
                pickColorItem(curAttachTextGroupParam, paragraphConfig, curAttr)
            }
        }
    }


    /**
     * 选中对应的颜色
     * */
    private fun pickColorItem(textGroupParam: TextGroupParam?, paragraphConfig:ParagraphConfig?, curAttr: TextConfig.TemplateAttrEnum) {
        if (textGroupParam == null) {
            viewBinding.colorPicker.pickColor(TextGroupParam.PRESET_COLOR, TextGroupParam.PRESET_COLOR, false, false)
            return
        }
        paragraphConfig?.let { result ->
            when (curAttr.id) {
                TextConfig.TemplateAttrEnum.Text.id -> {
                    viewBinding.colorPicker.pickColor(result.textColor, result.textCustomColor, result.showTextMyColor, result.selectTextMyColor)
                }
                TextConfig.TemplateAttrEnum.Stroke.id -> {
                    viewBinding.colorPicker.pickColor(result.strokeColor, result.strokeCustomColor, result.showStrokeMyColor, result.selectStrokeMyColor)
                }
                TextConfig.TemplateAttrEnum.Background.id -> {
                    viewBinding.colorPicker.pickColor(result.backgroundColor, result.backgroundCustomColor, result.showBgMyColor, result.selectBgMyColor)
                }
                TextConfig.TemplateAttrEnum.Shadow.id -> {
                    viewBinding.colorPicker.pickColor(result.shadowColor, result.shadowCustomColor, result.showShadowMyColor, result.selectShadowMyColor)
                }
            }
        }
    }

    private fun setTextStyleState(curFocusText: ParagraphConfig?) {
        viewBinding.underLineView.post {
            viewBinding.underLineView.isSelected = curFocusText?.isUnderline.notnull(false)
            viewBinding.underLineView.setShapeStrokeColor(
                1.dp(),
                if (viewBinding.underLineView.isSelected) 0x333333.color() else Color.TRANSPARENT
            )
        }

        viewBinding.boldView.post {
            viewBinding.boldView.isSelected = curFocusText?.isBold.notnull(false)
            viewBinding.boldView.setShapeStrokeColor(
                1.dp(),
                if (viewBinding.boldView.isSelected) 0x333333.color() else Color.TRANSPARENT
            )
        }

        viewBinding.italicView.post {
            viewBinding.italicView.isSelected = curFocusText?.isItalic.notnull(false)
            viewBinding.italicView.setShapeStrokeColor(
                1.dp(),
                if (viewBinding.italicView.isSelected) 0x333333.color() else Color.TRANSPARENT
            )
        }

        viewBinding.strikeThroughView.post {
            viewBinding.strikeThroughView.isSelected = curFocusText?.isStrikeThrough.notnull(false)
            viewBinding.strikeThroughView.setShapeStrokeColor(
                1.dp(),
                if (viewBinding.strikeThroughView.isSelected) 0x333333.color() else Color.TRANSPARENT
            )
        }
    }

    private fun setSeekBarState(attr: TextConfig.TemplateAttrEnum, seekBarEnable: Boolean, pickerEnable: Boolean) {
        viewBinding.seekBar1Title.gone()
        viewBinding.seekBar1.gone()
        viewBinding.seekBar2Title.gone()
        viewBinding.seekBar2.gone()
        viewBinding.seekBar3Title.gone()
        viewBinding.seekBar3.gone()
        viewBinding.textStyleBar.gone()
        viewBinding.colorPicker.gone()
        viewBinding.bottomSpace.gone()
        // 滑杆单向
        viewBinding.seekBar1.centerPointPercent = 0f
        viewBinding.seekBar1.isEnableCenterPoint = false
        viewBinding.seekBar2.centerPointPercent = 0f
        viewBinding.seekBar2.isEnableCenterPoint = false
        viewBinding.seekBar3.centerPointPercent = 0f
        viewBinding.seekBar3.isEnableCenterPoint = false

        when {
            attr.id == TextConfig.TemplateAttrEnum.Font.id -> return
            attr.id == TextConfig.TemplateAttrEnum.Space.id -> {
                viewBinding.seekBar1.centerPointPercent = 0.5f
                viewBinding.seekBar1.isEnableCenterPoint = true
                viewBinding.seekBar2.centerPointPercent = 0.5f
                viewBinding.seekBar2.isEnableCenterPoint = true
            }
            else -> {
                if (attr.id == TextConfig.TemplateAttrEnum.Shadow.id) {
                    viewBinding.seekBar3.centerPointPercent = 0.5f
                    viewBinding.seekBar3.isEnableCenterPoint = true
                }
                viewBinding.colorPicker.visible()
                viewBinding.colorPicker.setClickState(pickerEnable)
                if (attr.id == TextConfig.TemplateAttrEnum.Text.id) {
                    viewBinding.textStyleBar.visible()
                    viewBinding.textStyleBar.alpha = if (seekBarEnable) 1.0f else 0.15f
                }
            }
        }
        viewBinding.seekBar1Title.visible()
        viewBinding.seekBar1.visible()
        viewBinding.seekBar1.setSeekEnable(seekBarEnable, true)
        viewBinding.seekBar1Title.alpha = if (seekBarEnable) 1.0f else 0.15f

        if (attr.id == TextConfig.TemplateAttrEnum.Space.id || attr.id == TextConfig.TemplateAttrEnum.Shadow.id
                || attr.id == TextConfig.TemplateAttrEnum.Background.id) {
            viewBinding.seekBar2Title.visible()
            viewBinding.seekBar2.visible()
            viewBinding.seekBar2.setSeekEnable(seekBarEnable, true)
            viewBinding.seekBar2Title.alpha = if (seekBarEnable) 1.0f else 0.15f
        }


        if (attr.id == TextConfig.TemplateAttrEnum.Background.id) {
            viewBinding.seekBar3Title.visible()
            viewBinding.seekBar3.visible()
            viewBinding.bottomSpace.visible()
            viewBinding.seekBar3.setSeekEnable(seekBarEnable, true)
            viewBinding.seekBar3Title.alpha = if (seekBarEnable) 1.0f else 0.15f
        }
    }

    private fun setSeekBarUiContent(seekBar: XSeekBar, titleView: TextView, title: String, progress: Float, range: IntArray) {
        seekBar.visible()
        titleView.visible()
        titleView.text = title
        seekBar.minProgress = range[0]
        seekBar.maxProgress = range[1]
        seekBar.setProgress(progress.toInt())
    }

    private fun setDisablePromptState(
        attr: TextConfig.TemplateAttrEnum, curFocusText: ParagraphConfig?
    ) {
        val isEnable = TextConfig.attrEnable(attr, curFocusText?.templateConfig)
        viewBinding.disablePanelPrompt.visibility =
            if (isEnable == true) View.GONE else View.VISIBLE
        viewBinding.disablePrompt.text = ResourcesUtils.getString(R.string.t_edit_text_unable_use)
            .format(ResourcesUtils.getString(attr.attName))
    }


    /** 同步取色器变化 */
    private fun setColorValueToEntity(color: Int, isPickAction: Boolean) {
        mViewModel.syncColorToTextProgram(curAttr, isPickAction, color)
        // 当前有选中文本的时候
        // 刷新一下滑杆UI 有可能enable 发生变化。
        // 如果是切换到上次选中，其他路径触发刷新。
        updateUI()
        mViewModel.attrChangeEvent.value = false
    }

    private fun updateFontStyleUIOnClick(view: View) {
        if (mViewModel.focusTextLayerInfo?.textGroupParam == null) {
            ToastUtils.showShortToast(R.string.t_edit_text_select_text_tips)
            return
        }
        view.isSelected = !view.isSelected
        view.setShapeStrokeColor(1.dp(), if (view.isSelected) 0x333333.color() else Color.TRANSPARENT)
    }

    private fun updateUI() {
        val curFocusText = mViewModel.paragraphConfig
        setDisablePromptState(curAttr, curFocusText)
        when (curAttr.id) {
            TextConfig.TemplateAttrEnum.Text.id -> {
                updateColorPickerStyle()
                setSeekBarState(curAttr, curFocusText != null, curFocusText != null)
                setSeekBarUiContent(viewBinding.seekBar1, viewBinding.seekBar1Title, ResourcesUtils.getString(R.string.t_transparency),
                        TextConfig.toUiValue(TextConfig.PROGRESS_RANGE_0_100, TextConfig.ALPHA_REAL_RANGE, curFocusText?.textOpacity.notnull(0f)),
                        TextConfig.PROGRESS_RANGE_0_100)
                // 样式映射
                setTextStyleState(curFocusText)
            }

            TextConfig.TemplateAttrEnum.Stroke.id -> {
                //描边
                updateColorPickerStyle()
                setSeekBarState(curAttr, curFocusText != null && curFocusText.strokeEnable, curFocusText != null)
                setSeekBarUiContent(viewBinding.seekBar1, viewBinding.seekBar1Title, ResourcesUtils.getString(R.string.t_thickness),
                        TextConfig.toUiValue(TextConfig.PROGRESS_RANGE_0_100, TextConfig.STROKE_WIDTH_REAL_RANGE, curFocusText?.strokeWidth.notnull(0f)),
                        TextConfig.PROGRESS_RANGE_0_100)
            }

            TextConfig.TemplateAttrEnum.Background.id -> {
                updateColorPickerStyle()
                setSeekBarState(curAttr, curFocusText != null && curFocusText.backgroundEnable, curFocusText != null)
                setSeekBarUiContent(viewBinding.seekBar1, viewBinding.seekBar1Title, ResourcesUtils.getString(R.string.t_transparency),
                        TextConfig.toUiValue(TextConfig.PROGRESS_RANGE_0_100, TextConfig.ALPHA_REAL_RANGE, curFocusText?.backgroundOpacity.notnull(0f)), TextConfig.PROGRESS_RANGE_0_100)

                setSeekBarUiContent(viewBinding.seekBar2, viewBinding.seekBar2Title, ResourcesUtils.getString(R.string.t_rounded_corner),
                        TextConfig.toUiValue(TextConfig.PROGRESS_RANGE_0_100, TextConfig.BG_RADIUS_REAL_RANGE, curFocusText?.backgroundRadius.notnull(0f)),
                        TextConfig.PROGRESS_RANGE_0_100)

                setSeekBarUiContent(viewBinding.seekBar3, viewBinding.seekBar3Title, ResourcesUtils.getString(R.string.t_margin),
                        (curFocusText?.backgroundMargin ?: 0).toFloat(),
                        TextConfig.PROGRESS_RANGE_0_100)
            }

            TextConfig.TemplateAttrEnum.Shadow.id -> {
                //阴影
                updateColorPickerStyle()
                setSeekBarState(curAttr, curFocusText != null && curFocusText.shadowEnable, curFocusText != null)
                setSeekBarUiContent(viewBinding.seekBar1, viewBinding.seekBar1Title, ResourcesUtils.getString(R.string.t_transparency),
                        TextConfig.toUiValue(TextConfig.PROGRESS_RANGE_0_100, TextConfig.ALPHA_REAL_RANGE, curFocusText?.shadowOpacity.notnull(0f)), TextConfig.PROGRESS_RANGE_0_100)

                setSeekBarUiContent(viewBinding.seekBar2, viewBinding.seekBar2Title, ResourcesUtils.getString(R.string.t_text_blur),
                        TextConfig.toUiValue(TextConfig.PROGRESS_RANGE_0_100, TextConfig.SHADOW_BLUR_REAL_RANGE, curFocusText?.shadowBlur.notnull(0.0f)),
                        TextConfig.PROGRESS_RANGE_0_100)
            }

            TextConfig.TemplateAttrEnum.Space.id -> {
                //排列
                setSeekBarState(curAttr, curFocusText != null, curFocusText != null)
                setSeekBarUiContent(viewBinding.seekBar1, viewBinding.seekBar1Title, ResourcesUtils.getString(R.string.t_line_spacing),
                        TextConfig.toUiValueNotBalance(TextConfig.PROGRESS_RANGE_50_50, TextConfig.LINE_HEIGHT_REAL_RANGE, curFocusText?.spaceLineHeight.notnull(0f)),
                        TextConfig.PROGRESS_RANGE_50_50)

                setSeekBarUiContent(viewBinding.seekBar2, viewBinding.seekBar2Title, ResourcesUtils.getString(R.string.t_character_spacing),
                        TextConfig.toUiValueNotBalance(TextConfig.PROGRESS_RANGE_50_50, TextConfig.LETTER_SPACE_REAL_RANGE, curFocusText?.spaceLineLetterSpace.notnull(0f)),
                        TextConfig.PROGRESS_RANGE_50_50)
            }
        }
    }

    fun getPageView(): View {
        return viewBinding.root
    }


}