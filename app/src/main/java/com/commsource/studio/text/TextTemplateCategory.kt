package com.commsource.studio.text

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.commsource.store.TagEntity
import com.commsource.studio.DecorateConstant
import com.commsource.util.common.OnlineCompareEntity
import com.google.gson.annotations.SerializedName
import org.jetbrains.annotations.NotNull

/**
 * @Description:
 *
 * @Author: vinvince, @Time: 2022/7/22 17:02
 */
@Entity(tableName = "TEXT_TEMPLATE_CATEGORY")
class TextTemplateCategory : OnlineCompareEntity<TextTemplateCategory>, TagEntity {

    @PrimaryKey
    @NotNull
    @SerializedName("m_id")
    @ColumnInfo(name = "CategoryId")
    var categoryId: String = ""

    @ColumnInfo(name = "CategoryName")
    @SerializedName("name")
    var categoryName: String? = null

    @SerializedName("ended_at")
    @Ignore
    var endedAt: Long = 0

    @ColumnInfo(name = "CategorySort")
    var categorySort: Int = 0

    @ColumnInfo(name = "delivery", defaultValue = "0")
    @SerializedName("delivery")
    var delivery: Int = 0

    // 0线上，1内置分类
    @ColumnInfo(name = "InternalState")
    var internalState: Int = 0



    @SerializedName("beautyplus_text_template")
    @Ignore
    var textTemplateMaterials: MutableList<TextTemplateMaterial>? = null

    override fun getTagId(): String {
        return categoryId
    }

    override fun getTagName(): String {
        return categoryName ?: ""
    }

    override fun onCompareLocal(localEntity: TextTemplateCategory): Boolean {
        internalState = localEntity.internalState
        // categoryId 不一样就是新增
        var same = categoryName == localEntity.categoryName
        same = same and (categorySort == localEntity.categorySort)
        same = same and (delivery == localEntity.delivery)

        return same
    }

    override fun onSortCompare(nextEntity: TextTemplateCategory): Int {
        if (equals(nextEntity)) {
            return 0
        }
        return if (categoryId < nextEntity.categoryId) -1 else 1
    }

    override fun isNeedRemove(): Boolean {
        return internalState == DecorateConstant.NOT_INTERNAL
    }

    override fun hashCode(): Int {
        return super.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        return other is TextTemplateCategory && other.categoryId == categoryId
    }

    fun isInternal(): Boolean {
        return internalState == DecorateConstant.INTERNAL
    }

}