package com.commsource.studio.text

import com.commsource.beautyplus.R
import com.commsource.beautyplus.util.PathUtil
import com.commsource.util.ResourcesUtils
import com.meitu.common.AppContext
import com.meitu.library.util.Debug.Debug
import com.meitu.mtarruleparsesdk.MTARRuleParseManager
import com.meitu.mtlab.arkernelinterface.interaction.ARKernelLayerInteraction
import com.meitu.mtpasterrender.MTPasterRenderFilter
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject

object TextConfig {


    const val EMPTY_TEMPLATE_ID = "BP_TEX_00000000"

    val PROGRESS_RANGE_50_50 = intArrayOf(-50, 50)
    val PROGRESS_RANGE_0_100 = intArrayOf(0, 100)
    val ALPHA_REAL_RANGE = floatArrayOf(0f, 1f)

    // 排列
    val LETTER_SPACE_REAL_RANGE = floatArrayOf(-700f, 4000f)
    val LINE_HEIGHT_REAL_RANGE = floatArrayOf(-1200f, 5000f)

    // 阴影
    val SHADOW_BLUR_REAL_RANGE = floatArrayOf(0f, 2.5f)
    val SHADOW_DISTANCE_REAL_RANGE = floatArrayOf(-30f, 30f)

    // 描边
    val STROKE_WIDTH_REAL_RANGE = floatArrayOf(0f, 4.0f)

    //背景
    val BG_RADIUS_REAL_RANGE = floatArrayOf(0f, 1f)

    // 背景边距
    val BG_MARGIN_RANGE = floatArrayOf(0.25f, -0.65f)

    /**
     *  滑杆值正常对应区间。
     * */
    fun toUiValue(uiRange: IntArray, realRange: FloatArray, curRealValue: Float): Float {
        return ((curRealValue - realRange[0]) / (realRange[1] - realRange[0]) * (uiRange[1] - uiRange[0]) + uiRange[0])
    }

    fun toRealValue(uiRange: IntArray, realRange: FloatArray, curUiValue: Int): Float {
        return ((curUiValue - uiRange[0]).toFloat() / (uiRange[1] - uiRange[0]) * (realRange[1] - realRange[0]) + realRange[0])
    }

    /**
     *   左右不平衡时用
     *  【-1500 ， 2500】
     *   在滑杆值处于 0 ～ -50 可以调整范围为 0～ -1500
     *   在滑杆值处于 0 ～ 50 可以调整范围为 0～ 2500
     */
    fun toUiValueNotBalance(uiRange: IntArray, realRange: FloatArray, curRealValue: Float): Float {
        return if (curRealValue >= 0) {
            curRealValue / realRange[1] * uiRange[1]
        } else {
            curRealValue / realRange[0] * uiRange[0]
        }
    }

    fun toRealValueNotBalance(uiRange: IntArray, realRange: FloatArray, curUiValue: Int): Float {
        return if (curUiValue >= 0) {
            curUiValue.toFloat() / uiRange[1] * realRange[1]
        } else {
            curUiValue.toFloat() / uiRange[0] * realRange[0]
        }
    }

    const val CANCEL = "-2"
    const val PRESET = "-1"

    val colorList = arrayListOf(
            "#000000",
            "#CCCCCC",
            "#FFFFFF",
            "#FFA691",
            "#F6CB79",
            "#FBF195",
            "#B6CE90",
            "#90CE9B",
            "#C5E0E7",
            "#D3CEEE",
            "#F2D6E7",
            "#EC706B",
            "#ECB36D",
            "#FCEE77",
            "#BEDF60",
            "#7ED997",
            "#8CC2FF",
            "#9471CE",
            "#F9ABC6",
            "#D94639",
            "#F09E38",
            "#F9DA49",
            "#99BF47",
            "#6FB58F",
            "#6DC1F4",
            "#8761D9",
            "#E888C1",
            "#B82E2A",
            "#B05B2F",
            "#B38F34",
            "#718635",
            "#489390",
            "#538BBD",
            "#685175",
            "#A46E84"
    )

    val downloadPath: String = PathUtil.getExternalFileDir(AppContext.context, "text_font") + "/"

    enum class TemplateAttrEnum(var id: Int, var attName: Int) {
        Font(0, R.string.t_fonts),
        Text(1, R.string.t_text),
        Stroke(2, R.string.t_stroke),
        Background(5, R.string.t_background),
        Shadow(4, R.string.t_shadow),
        Space(3, R.string.t_order)
    }

    const val sysFontId = "BP_FON_00000000"


    fun getDefaultFont(): String {
        return when (ResourcesUtils.getSysLanguage()) {
            "en" -> "BP_FON_00000004"
            "ko" -> "BP_FON_00000005"
            "th" -> "BP_FON_00000007"
            "ja" -> "BP_FON_00000006"
            else -> sysFontId
        }
    }


    fun getTextBlendMode(type: MTPasterRenderFilter.MtPsBlendType?): Int {
        if (type == null) {
            return 1
        }
        return when (type) {
            MTPasterRenderFilter.MtPsBlendType.MTPSBlend_Normal -> {
                ARKernelLayerInteraction.LayerBlendModeEnum.LayerBlendNormal
            }
            MTPasterRenderFilter.MtPsBlendType.MTPSBlend_Overlay -> {
                ARKernelLayerInteraction.LayerBlendModeEnum.LayerBlendOverlay
            }

            MTPasterRenderFilter.MtPsBlendType.MTPSBlend_ColorDeep -> {
                ARKernelLayerInteraction.LayerBlendModeEnum.LayerBlendColorBurn
            }

            MTPasterRenderFilter.MtPsBlendType.MTPSBlend_Multiply -> {
                ARKernelLayerInteraction.LayerBlendModeEnum.LayerBlendMultiply
            }


            MTPasterRenderFilter.MtPsBlendType.MTPSBlend_SoftLight -> {
                ARKernelLayerInteraction.LayerBlendModeEnum.LayerBlendSoftLight
            }
            MTPasterRenderFilter.MtPsBlendType.MTPSBlend_HardLight -> {
                ARKernelLayerInteraction.LayerBlendModeEnum.LayerBlendHardLight
            }


            MTPasterRenderFilter.MtPsBlendType.MTPSBlend_Different -> {
                ARKernelLayerInteraction.LayerBlendModeEnum.LayerBlendDifference
            }
            MTPasterRenderFilter.MtPsBlendType.MTPSBlend_Screen -> {
                ARKernelLayerInteraction.LayerBlendModeEnum.LayerBlendScreen
            }
            MTPasterRenderFilter.MtPsBlendType.MTPSBlend_Divide -> {
                ARKernelLayerInteraction.LayerBlendModeEnum.LayerBlendDivide
            }
            else -> {
                1
            }
        }
    }

    fun parseParagraphEditConfig(textJson: String, configPath:String?=null): ArrayList<TextTemplateConfig>? {
        var configs: ArrayList<TextTemplateConfig>? = null
        var fonts = if (configPath != null) {
            parseTextFont(configPath)
        } else {
            null
        }
        try {
            val jsonObject = JSONObject(textJson)
            val textTemplateConfig = TextTemplateConfig().apply {
                appendGlow = jsonObject.getBoolean("appendGlow")
                appendStoke = jsonObject.getBoolean("appendStoke")
                canEditBg = jsonObject.getBoolean("canEditBg")
                canEditFont = jsonObject.getBoolean("canEditFont")
                canEditShadow = jsonObject.getBoolean("canEditShadow")
                canEditSpacing = jsonObject.getBoolean("canEditSpacing")
                canEditStroke = jsonObject.getBoolean("canEditStroke")
                canEditText = jsonObject.getBoolean("canEditText")
                try {
                    fontId = jsonObject.getString("fontId")
                } catch (e: JSONException) {
                }
                fonts?.getOrNull(0)?.let {
                    fontId = it
                }
            }
            configs = ArrayList<TextTemplateConfig>()
            configs?.add(textTemplateConfig)

            try {
                val jsonArray = jsonObject.getJSONArray("multiple")
                val length = jsonArray.length()
                for (index in 0 until length) {
                    val obj = jsonArray.getJSONObject(index)
                    val textTemplateConfig = TextTemplateConfig().apply {
                        appendGlow = obj.getBoolean("appendGlow")
                        appendStoke = obj.getBoolean("appendStoke")
                        canEditBg = obj.getBoolean("canEditBg")
                        canEditFont = obj.getBoolean("canEditFont")
                        canEditShadow = obj.getBoolean("canEditShadow")
                        canEditSpacing = obj.getBoolean("canEditSpacing")
                        canEditStroke = obj.getBoolean("canEditStroke")
                        canEditText = obj.getBoolean("canEditText")
                        try {
                            fontId = obj.getString("fontId")
                        } catch (e: JSONException) {
                        }
                        fonts?.getOrNull(1 + index)?.let {
                            fontId = it
                        }
                    }
                    configs?.add(textTemplateConfig)
                }
            } catch (e:JSONException) {
            }

        } catch (e: Exception) {
            Debug.e(e)
            configs = null
        }
        return configs
    }

    fun paragraphConfigToJson(config: MutableList<TextTemplateConfig>?):String? {
        if (config.isNullOrEmpty()) {
            return null
        }
        val temp = ArrayList(config)
        val first = temp.removeFirst()
        val jsonObject = JSONObject()
        jsonObject.put("appendGlow", first.appendGlow)
        jsonObject.put("appendStoke", first.appendStoke)
        jsonObject.put("canEditBg", first.canEditBg)
        jsonObject.put("canEditFont", first.canEditFont)
        jsonObject.put("canEditShadow", first.canEditShadow)
        jsonObject.put("canEditSpacing", first.canEditSpacing)
        jsonObject.put("canEditStroke", first.canEditStroke)
        jsonObject.put("canEditText", first.canEditText)
        jsonObject.put("fontId", first.fontId)
        val jsonArray = JSONArray()
        temp.forEach {
            val temp = JSONObject()
            temp.put("appendGlow", it.appendGlow)
            temp.put("appendStoke", it.appendStoke)
            temp.put("canEditBg", it.canEditBg)
            temp.put("canEditFont", it.canEditFont)
            temp.put("canEditShadow", it.canEditShadow)
            temp.put("canEditSpacing", it.canEditSpacing)
            temp.put("canEditStroke", it.canEditStroke)
            temp.put("canEditText", it.canEditText)
            temp.put("fontId", it.fontId)
            jsonArray.put(temp)
        }
        if (jsonArray.length() > 0) {
            jsonObject.put("multiple", jsonArray)
        }
        return jsonObject.toString()
    }

    fun parseTextFont(config: String): Array<String>? {
        var result: Array<String>? = null
        MTARRuleParseManager.parseConfiguration(
            config,
            AppContext.context.assets
        ) { model, dict, index ->
            result = model.bpTextFontKey
            model
        }
        return result
    }

    fun attrEnable(
        attr: TemplateAttrEnum, templateConfig: TextTemplateConfig?
    ): Boolean {
        if (templateConfig == null) {
            return false
        }
        val isEnable = when (attr.id) {
            TemplateAttrEnum.Font.id -> templateConfig.canEditFont
            TemplateAttrEnum.Text.id -> templateConfig.canEditText
            TemplateAttrEnum.Stroke.id -> templateConfig.canEditStroke
            TemplateAttrEnum.Background.id -> templateConfig.canEditBg
            TemplateAttrEnum.Shadow.id -> templateConfig.canEditShadow
            else -> templateConfig.canEditSpacing
        }
        return isEnable
    }

}