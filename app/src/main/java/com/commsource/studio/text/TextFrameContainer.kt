package com.commsource.studio.text

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.commsource.studio.MatrixBox
import com.commsource.util.print

/**
 * author: admin
 * Date: 2022/7/24
 * Des:
 */
class TextFrameContainer @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    val textFrame = TextDecorateFrame().apply {
        enableSelect = true
    }

    val drawMatrixBox = MatrixBox()

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (!isEnabled) {
            return
        }
        canvas.concat(drawMatrixBox.matrix)
        val lineScaleFactor = drawMatrixBox.getScale()
        textFrame.updateLinePaint(lineScaleFactor)
        textFrame.draw(canvas)
    }

    override fun onTouchEvent(event: MotionEvent
    ): Boolean {
        if (event.action and MotionEvent.ACTION_MASK == MotionEvent.ACTION_UP) {
            val downPoint = floatArrayOf(event.x, event.y).apply {
                drawMatrixBox.calculateInvertMatrix().mapPoints(this)
            }
            val index = textFrame.onTap(downPoint)
            if (index != -1) {
                onTextInteraction?.invoke(index)
            }
            postInvalidate()
        }
        return true
    }


    var onTextInteraction:((index:Int)->Unit)?=null
}