package com.commsource.studio

import android.graphics.Bitmap
import android.text.TextUtils
import android.util.SparseArray
import androidx.annotation.WorkerThread
import com.commsource.beautyplus.util.PathUtil
import com.commsource.util.print
import com.meitu.common.AppContext
import com.meitu.core.util.CacheUtil
import com.meitu.library.util.io.FileUtils
import java.lang.ref.WeakReference

/**
 * 图片存储堆栈。
 */
class PathStack(val name: String, maxSize: Int) : LimitStack<PathStack.Cache>(maxSize) {

    private var mCacheDir: String? = null

    private val cacheArray = SparseArray<Cache>()

    /**
     * 取缓存路径
     * @return 缓存路径
     */
    private fun getCacheDir(): String? {
        if (mCacheDir == null) {
            mCacheDir = PathUtil.getStudioCacheDir(AppContext.context, name)
            FileUtils.createDir(mCacheDir)
        }
        return "$mCacheDir/"
    }

    override fun cacheObject(startPosition: Int, offset: Int, obj: Cache?) {
        if (obj == null) {
            return
        }
        obj.cache(getCacheDir() + (startPosition + offset))
        cacheArray.put(startPosition + offset,obj)
    }

    override fun getObject(startPosition: Int, offset: Int): Cache? {
        return cacheArray.get(startPosition + offset)
    }

    override fun deleteObject(startPosition: Int, offset: Int, overMaxsize: Boolean) {
        FileUtils.deleteFile(getCacheDir() + (startPosition + offset))
        cacheArray.remove(startPosition + offset)
    }


    /**
     * 图像缓存。
     */
    class Cache(var bitmap: Bitmap?) {
        // 缓存路径。
        private var cachePath: String? = null

        fun cache(cachePath: String) {
            this.cachePath = cachePath
            CacheUtil.androidBitmap2Cache(bitmap, cachePath)
            bitmap = null
        }

        fun restore(): Bitmap? {
            // 磁盘缓存。
            if (!TextUtils.isEmpty(cachePath)) {
                return CacheUtil.cache2AndroidBitmap(cachePath)
            }
            return null
        }
    }
}