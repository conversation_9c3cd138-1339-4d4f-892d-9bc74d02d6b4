package com.commsource.studio

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.PopupWindow
import androidx.core.content.ContextCompat.getSystemService
import androidx.core.view.children
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.PopupwindowExitEditConfirmBinding
import com.commsource.util.LOGV
import com.commsource.util.ResourcesUtils
import com.commsource.util.dpf
import com.commsource.util.isVisible
import com.commsource.util.resColor
import com.commsource.util.setMarginTop
import com.meitu.common.utils.GradientDrawableFactory
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.androidx.core.extension.dp


/**
 * 创数中心页面，下拉列表点击的时候显示
 */
fun View.showTipBubble() {
    val inflater = LayoutInflater.from(context)
    val contentView = inflater.inflate(R.layout.popupwindow_tip_bubble, null)

    val popupWindow = PopupWindow(context)

    popupWindow.contentView = contentView
    popupWindow.width = ViewGroup.LayoutParams.WRAP_CONTENT
    popupWindow.height = ViewGroup.LayoutParams.WRAP_CONTENT
    //设置背景透明
    popupWindow.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
    popupWindow.isOutsideTouchable = true

    val offsetX = Math.abs(measuredWidth - contentView.measuredWidth)
    val offsetY = -(measuredHeight + contentView.measuredHeight)
    "popupwindow $offsetX  $offsetY".LOGV()
    popupWindow.showAsDropDown(this, offsetX, offsetY, Gravity.START)


}

fun View.showExitEditConfirm(
    firstItemDarkStyle: Boolean = false,
    onSaveDraftClick: ((popWindow: PopupWindow) -> Unit)? = null,
    onContinueClick: ((popWindow: PopupWindow) -> Unit)? = null,
    onExitClick: ((popWindow: PopupWindow) -> Unit)? = null,
    onDismiss: (() -> Unit)? = null
): PopupWindow {
    val inflater = LayoutInflater.from(context)
    val viewBinding = PopupwindowExitEditConfirmBinding.inflate(inflater, null, false)

    viewBinding.llSaveDraft.visibility = if (onSaveDraftClick == null) View.GONE else View.VISIBLE
    viewBinding.llContinueEdit.visibility = if (onContinueClick == null) View.GONE else View.VISIBLE
    viewBinding.llCancel.visibility = if (onExitClick == null) View.GONE else View.VISIBLE

    viewBinding.llRoot.children.forEachIndexed() { index, view ->
        if (index != 0 && view.isVisible) {
            view.setMarginTop(12.dp)
        }
    }

    //保持第一个item样式
    if (onSaveDraftClick == null) {
        val bgColor = if (firstItemDarkStyle) R.color.Gray_A.resColor() else Color.WHITE
        val icTextColor =
            if (firstItemDarkStyle) Color.WHITE else ResourcesUtils.getColor(R.color.Gray_A)

        viewBinding.llContinueEdit.background = GradientDrawableFactory.createDrawable(
            bgColor, 10f.dpf
        )
        viewBinding.ifvContinueEdit.setTextColor(icTextColor)
        viewBinding.tvContinueEdit.setTextColor(icTextColor)
    } else {
        viewBinding.llContinueEdit.background = GradientDrawableFactory.createDrawable(
            R.color.Gray_A.resColor(),
            10f.dpf
        )

        viewBinding.ifvContinueEdit.setTextColor(ResourcesUtils.getColor(R.color.white))
        viewBinding.tvContinueEdit.setTextColor(ResourcesUtils.getColor(R.color.white))
    }
    val popupWindow = PopupWindow(context)
    viewBinding.llSaveDraft.setOnClickListener {
        onSaveDraftClick?.invoke(popupWindow)
    }
    viewBinding.llContinueEdit.setOnClickListener {
        onContinueClick?.invoke(popupWindow)
    }
    viewBinding.llCancel.setOnClickListener {
        onExitClick?.invoke(popupWindow)
    }
    popupWindow.contentView = viewBinding.root
    popupWindow.width = ViewGroup.LayoutParams.WRAP_CONTENT
    popupWindow.height = ViewGroup.LayoutParams.WRAP_CONTENT
    //设置背景透明
    popupWindow.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
    popupWindow.isOutsideTouchable = true
    popupWindow.isFocusable = true

    popupWindow.setOnDismissListener {
        dimBackground(this.context, popupWindow, 1f)
        onDismiss?.invoke()
    }

    val offsetX = 0
    val offsetY = measuredHeight
    "popupwindow $offsetX  $offsetY".LOGV()
    if (this is ViewGroup) {
        popupWindow.showAtLocation(
            this,
            Gravity.START and Gravity.TOP,
            16.dp,
            52.dp + DeviceUtils.getStatusHeight()
        )
    } else {
        popupWindow.showAsDropDown(this, offsetX, offsetY, Gravity.START)
    }
    dimBackground(this.context, popupWindow, 0.5f)

    return popupWindow
}

private fun dimBackground(context: Context, popup: PopupWindow, dimAmount: Float) {
    val container = popup.contentView.takeIf { it.layoutParams is WindowManager.LayoutParams }
        ?: (popup.contentView.parent as? View)?.takeIf { it.layoutParams is WindowManager.LayoutParams }
        ?: (popup.contentView.parent?.parent as? View)?.takeIf { it.layoutParams is WindowManager.LayoutParams }
        ?: return

    val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    val lp = container.layoutParams as WindowManager.LayoutParams
    lp.flags = lp.flags or WindowManager.LayoutParams.FLAG_DIM_BEHIND
    lp.dimAmount = dimAmount
    wm.updateViewLayout(container, lp)

}