package com.commsource.studio.render

import com.commsource.camera.newrender.renderproxy.BaseRenderProxy
import com.meitu.library.media.renderarch.arch.data.frame.EffectFrameData
import java.util.*

/**
 * 多种proxy集合
 */
open class MultiRenderProxy : BaseRenderProxy() {
    /**
     * 内部真实的renderProxy。
     */
    val renderProxies = LinkedList<BaseRenderProxy>()

    /**
     * Gl资源释放。
     */
    override fun onGlResourceRelease() {
        renderProxies.forEach { it.onGlResourceRelease() }
    }

    override fun onTextureCallback(effectFrameData: EffectFrameData?) {
        renderProxies.forEach { it.onTextureCallback(effectFrameData) }
    }

    override fun <T : Any?> setRecognizeData(recognizeData: T, dataClass: Class<T>?) {
        renderProxies.forEach { it.setRecognizeData(recognizeData, dataClass) }
    }

    override fun isNeedRecognizeData(recognizeClass: Class<*>?): Boolean {
        renderProxies.forEach { return it.isNeedRecognizeData(recognizeClass) }
        return false
    }

    /**
     * 渲染调用。
     * @param fboA
     * @param fboB
     * @param texA
     * @param texB
     * @param width
     * @param height
     * @return 返回的是结果图的纹理ID。
     */
    override fun onRender(fboA: Int, fboB: Int, texA: Int, texB: Int, width: Int, height: Int, isCaptureFrame: Boolean): Int {
        var texResult = texA
        renderProxies.forEach {
            texResult = if (texResult == texA) {
                it.onRender(fboA, fboB, texA, texB, width, height, isCaptureFrame)
            } else {
                it.onRender(fboB, fboA, texB, texA, width, height, isCaptureFrame)
            }
        }
        return texResult
    }

    /**
     * GL资源初始化接口。
     */
    override fun onGlResourceInit() {
        renderProxies.forEach { it.onGlResourceInit() }
    }

}