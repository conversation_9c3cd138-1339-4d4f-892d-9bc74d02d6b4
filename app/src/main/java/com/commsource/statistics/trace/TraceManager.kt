package com.commsource.statistics.trace

import java.util.Locale
import java.util.concurrent.ConcurrentHashMap

/**
 * @Description:
 *
 * @Author: vinvince, @Time: 2024/12/24 11:12
 */
object TraceManager {

    private val traceInfos = ConcurrentHashMap<TraceModule, String>()

    fun push(traceModule: TraceModule, replace: Boolean = true) {
        if (replace) {
            traceInfos[traceModule] = "${traceModule.id}-${generateTraceId()}"
        } else {
            val traceInfo = traceInfos.filter { it.key.id == traceModule.id }
            if (traceInfo.isEmpty()) {
                traceInfos[traceModule] = "${traceModule.id}-${generateTraceId()}"
            }
        }
    }

    fun pop(traceModule: TraceModule) {
        traceInfos.remove(traceModule)
    }

    fun refreshTraceInfo(traceModule: TraceModule) {
        traceInfos.filter { it.key.id == traceModule.id }.forEach {
            traceInfos[it.key] = "${traceModule.id}-${generateTraceId()}"
        }
    }

    fun getTraceInfo(): String {
        val stringBuilder = StringBuilder()
        traceInfos.forEach {
            stringBuilder.append(it.value).append(",")
        }
        val result = stringBuilder.toString()
        if (result.endsWith(",")) {
            return result.substring(0, result.length - 1)
        }
        return result
    }

    private fun generateTraceId(): String {
        return (System.currentTimeMillis() % 100000000L).toString(16).uppercase(Locale.US)
    }
}