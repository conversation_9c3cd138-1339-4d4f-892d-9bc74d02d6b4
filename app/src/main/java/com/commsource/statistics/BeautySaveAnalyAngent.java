package com.commsource.statistics;

import android.content.Context;
import android.text.TextUtils;

import com.commsource.beautyplus.constants.ArMaterialConstants;
import com.commsource.beautyplus.constants.FilterConstants;
import com.commsource.camera.fastcapture.SelfiePhotoData;
import com.commsource.config.ApplicationConfig;
import com.commsource.repository.child.filter.FilterWrapper;
import com.commsource.statistics.constant.MTAnalyticsConstant;
import com.commsource.statistics.constant.MTFirebaseConstant;
import com.meitu.common.AppContext;

import java.util.HashMap;
import java.util.Map;

/*********************************************
 * Author: lhy 2017/3/15
 * ********************************************
 * Version: 版本
 * Author: lhy
 * Changes: 更新点
 * <AUTHOR>
 * ********************************************
 */
public class BeautySaveAnalyAngent {

    public static final String PRE_FILTER = "";

    /**
     * @param mode            保存方式：0打钩，1音量键，2分享，3快速自拍。
     * @param selfiePhotoData selfiePhotoData
     */
    public static void doPhotoSaveStatistics(int mode, SelfiePhotoData selfiePhotoData) {
        if (selfiePhotoData == null) {
            return;
        }
        if (selfiePhotoData.isMovieMode()) {
            return;
        }
        Context context = AppContext.getContext();

        if (!selfiePhotoData.isFromAlbum()) {
            if (selfiePhotoData.getFilterId() == FilterConstants.ORIGINAL_ID) {
                if (!selfiePhotoData.isFromAlbum()) {
                    if (!selfiePhotoData.isAr()) {
                        MTAnalyticsAgent.logCameraStartUpAbtest(context, MTAnalyticsConstant.SELFIESAVE_NOARNOFILTER);
                    }
                }
            }
            if (!selfiePhotoData.isUseLookPresetFilter() && !selfiePhotoData.isAr()) {
                Map<String, String> map = new HashMap<>(8);
//                    map.put(MTAnalyticsConstant.PIKA_FACE_DETECT, String.valueOf(selfiePhotoData.getFaceCount()));
                String catId = "";
                FilterWrapper wrapper = selfiePhotoData.getFilterWrapper();
                if (wrapper != null) {
                    catId = wrapper.getCategoryId();
                }
                map.put("特效ID", selfiePhotoData.getFilterId());
                map.put("滤镜分类", SpmAnalytics.transCategoryId(catId, selfiePhotoData.getFilterId()));
                MTAnalyticsAgent.logCameraStartUpAbtest(context, MTAnalyticsConstant.SELFIESAVE_ONLYFILTER, map);
            }
        }

        // Firebase统计
        if (selfiePhotoData.isFromAlbum()) {
            // 相册导入
            HashMap<String, String> hashMap = new HashMap<>(4);
            hashMap.put("beautylevel", String.valueOf(selfiePhotoData.getBeautyLevel() + 1));
            if (mode != 2) {
                MTFirebaseAnalyticsAgent.logEvent(context, MTFirebaseConstant.EVENT_QUICKBEAUTIFY_CHECK, hashMap);
            } else {
                MTFirebaseAnalyticsAgent.logEvent(context, MTFirebaseConstant.EVENT_QUICKBEAUTIFY_SHAREPAGE, hashMap);
            }
        } else {
            // 拍照导入
            if (mode != 2) {
                HashMap<String, String> hashMap =
                        SelfieStatisticBean.getSelfieStatisticParams(selfiePhotoData.getStatisticBean());
                hashMap.put("quick_selfie_settings", mode == 3 ? "on" : "off");
                MTFirebaseAnalyticsAgent.logEvent(context, MTFirebaseConstant.EVENT_TAKE_SELFIE_CHECK, hashMap);
                // 滤镜AB/Test，针对新用户统计事件
                if (ApplicationConfig.isNewUser(context)) {
                    MTFirebaseAnalyticsAgent.logEvent(context, MTFirebaseConstant.EVENT_NEW_USER_TAKE_SELFIE_CHECK,
                            hashMap);
                }
            } else {
                HashMap<String, String> hashMap =
                        SelfieStatisticBean.getSelfieStatisticParams(selfiePhotoData.getStatisticBean());
                MTFirebaseAnalyticsAgent.logEvent(context, MTFirebaseConstant.EVENT_TAKE_SELFIE_SHAREPAGE, hashMap);
            }

        }

        if (!selfiePhotoData.isFromAlbum()) {
            if (ApplicationConfig.isNewUser(context)) {
                if (!TextUtils.isEmpty(selfiePhotoData.getArMaterialId())) {
                    MTFirebaseAnalyticsAgent.logEvent(context, MTFirebaseConstant.EVENT_NEW_USER_TAKE_ARSELFIE_CHECK);
                } else {
                    MTFirebaseAnalyticsAgent.logEvent(context, MTFirebaseConstant.EVENT_NEW_USER_TAKE_NOARSELFIE_CHECK);
                }
            }
        }
    }

}
