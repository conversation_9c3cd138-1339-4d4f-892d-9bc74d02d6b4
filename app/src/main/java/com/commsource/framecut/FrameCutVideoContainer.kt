package com.commsource.framecut

import android.content.Context
import android.graphics.PointF
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout
import androidx.annotation.UiThread
import androidx.lifecycle.ViewModelProvider
import com.commsource.beautyplus.BaseActivity
import com.commsource.studio.BpGestureDetector
import com.commsource.studio.CanvasCalculator
import com.commsource.studio.CanvasGestureController
import com.commsource.studio.MatrixBox
import com.commsource.util.print
import com.commsource.util.setMarginCompat
import com.commsource.util.setSize
import com.commsource.videostudio.func.EditStyle
import com.commsource.videostudio.func.StudioFunctionViewModel
import com.commsource.videostudio.func.VideoEditTabType
import com.commsource.videostudio.layer.BaseVideoLayer
import com.commsource.videostudio.layer.CoverLayer
import com.commsource.videostudio.layer.VideoGestureLayer
import com.commsource.videostudio.viewmodel.EffectViewModel
import java.util.LinkedList


class FrameCutVideoContainer : FrameLayout {

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )


    val videoContainer: FrameLayout


    val viewPortRect = RectF()

    /**
     * 画布的全屏预览时的可视区域
     */
    val canvasViewRect = RectF()

    /**
     * 画布的合法区域
     */
    val validRect = RectF()


    var canvasWidth: Int = 0
    var canvasHeight: Int = 0
    private val canvasSize = PointF()

    private var paddingLeft: Float = 0f
    private var paddingTop: Float = 0f
    private var paddingRight: Float = 0f
    private var paddingBottom: Float = 0f

    private val canvasCalculator = CanvasCalculator()


    val canvasInitMatrix = MatrixBox()

    /**
     * 画布当前手势调整矩阵
     */
    var canvasGestureChangeMatrix: MatrixBox = MatrixBox()

    /**
     * 画布相对于View坐标的偏移矩阵
     */
    var canvasMatrix: MatrixBox = MatrixBox()

    /**
     * 画布相对于view的区域
     */
    val canvasRect = RectF()

    /**
     * 画布的手势监听
     */
    val canvasGestureController =
        object : CanvasGestureController(this@FrameCutVideoContainer) {

            override fun onTap(downEvent: MotionEvent, upEvent: MotionEvent): Boolean {
                if (super.onTap(downEvent, upEvent)) {
                    return true
                }
                return false
            }
        }.apply {
            isEnable = true
            onCanvasGestureMatrixChange = {
                canvasGestureChangeMatrix = it
                dispatchMatrixChange()
            }
        }

    /**
     * 手势检测器
     */
    private val bpGestureDetector = BpGestureDetector(canvasGestureController)

    init {
        setWillNotDraw(false)
        videoContainer = FrameLayout(context)
        addView(videoContainer)
        clipChildren = false
        clipToPadding = false
    }

    fun setCanvasSize(
        canvasWidth: Int,
        canvasHeight: Int,
        left: Float = paddingLeft,
        top: Float = paddingTop,
        right: Float = paddingRight,
        bottom: Float = paddingBottom
    ) {
        this.canvasWidth = canvasWidth
        this.canvasHeight = canvasHeight
        this.paddingLeft = left
        this.paddingTop = top
        this.paddingRight = right
        this.paddingBottom = bottom
        initCanvas()
    }

    /**
     * 计算视频控件大小
     * 计算视频在屏幕中的可视区域
     */
    fun initCanvas() {
        if (!isSizeInitialized()) {
            return
        }
        canvasSize.set(canvasWidth.toFloat(), canvasHeight.toFloat())
        val top = StudioFunctionViewModel.getVideoStudioDefaultTop()
        validRect.set(0f, top, width.toFloat(), height.toFloat())
        canvasViewRect.set(
            canvasCalculator.generateInscribeRect(
                validRect,
                canvasWidth,
                canvasHeight
            )
        )
        videoContainer.setSize(
            width = canvasViewRect.width().toInt(),
            height = canvasViewRect.height().toInt()
        )
        videoContainer.setMarginCompat(
            start = canvasViewRect.left.toInt(),
            top = canvasViewRect.top.toInt()
        )
        setPreviewArea(paddingLeft, paddingTop, paddingRight, paddingBottom)
    }

    val finalCanvasInitMatrix = MatrixBox()
    val finalViewPort = RectF()

    fun setPreviewArea(
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        anim: Boolean = false
    ) {
        this.paddingLeft = left
        this.paddingTop = top
        this.paddingRight = right
        this.paddingBottom = bottom
        val h = height - paddingBottom
        validRect.set(paddingLeft, paddingTop, width - paddingRight, h)
        finalViewPort.set(
            canvasCalculator.generateInscribeRect(validRect, canvasWidth, canvasHeight)
        )
        val newCanvasInitMatrix = MatrixBox(
            canvasCalculator.generateInscribeMatrix(
                validRect,
                canvasWidth,
                canvasHeight
            )
        )
        finalCanvasInitMatrix.set(newCanvasInitMatrix)
        if (anim) {
            canvasInitMatrix.animateToMatrix(newCanvasInitMatrix,
                animateDuration = EditStyle.ANIMATION_DURATION,
                updateAction = {
                    dispatchMatrixChange()
                })
        } else {
            canvasInitMatrix.set(newCanvasInitMatrix)
            dispatchMatrixChange()
        }
    }


    fun isSizeInitialized(): Boolean {
        return width != 0 && height != 0 && canvasWidth != 0 && canvasHeight != 0
    }


    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isSizeInitialized()) {
            return false
        }
        bpGestureDetector.onTouchEvent(event)
        return true
    }


    private fun dispatchMatrixChange() {
        canvasMatrix.apply {
            reset()
            postConcat(canvasInitMatrix)
            postConcat(canvasGestureChangeMatrix)
        }
        canvasRect.set(0f, 0f, canvasWidth.toFloat(), canvasHeight.toFloat())
        canvasInitMatrix.matrix.mapRect(viewPortRect, canvasRect)
        canvasGestureController.setValidViewPortRectF(validRect, viewPortRect)
        canvasGestureController.canvasGestureMatrix.set(canvasGestureChangeMatrix)

        canvasRect.set(0f, 0f, canvasWidth.toFloat(), canvasHeight.toFloat())
        canvasMatrix.matrix.mapRect(canvasRect)



        "validRect =            $validRect".print("lyddd")
        "viewPortRect =         $viewPortRect".print("lyddd")
        "tempCalculateRectF =   $canvasRect".print("lyddd")

        refreshContainerPosition()
    }

    fun refreshContainerPosition() {
        if (canvasViewRect.width() != 0f) {
            val scale = canvasRect.width() / canvasViewRect.width()
            videoContainer.scaleX = scale
            videoContainer.scaleY = scale
            videoContainer.translationX = canvasRect.centerX() - canvasViewRect.centerX()
            videoContainer.translationY = canvasRect.centerY() - canvasViewRect.centerY()
        }
    }
}