<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <FrameLayout
                android:id="@+id/flIcon"
                android:layout_width="72dp"
                android:layout_height="60dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="4dp"
                android:layout_gravity="top|end">

                <com.commsource.widget.IconFrontView
                    android:id="@+id/ivThumb"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:fontFamily="@font/icon_font"
                    android:textColor="@color/white"
                    android:textSize="28dp"
                    android:layout_gravity="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </FrameLayout>

            <ImageView
                android:id="@+id/ivTag"
                android:layout_width="26dp"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:visibility="gone"
                tools:src="@drawable/image_quality_hot_tag"
                tools:visibility="visible" />

        </FrameLayout>

        <com.commsource.widget.AutoFitTextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="4dp"
            android:gravity="center"
            android:maxLines="2"
            android:paddingHorizontal="2dp"
            android:textColor="@color/white"
            android:textFontWeight="400"
            android:lineSpacingExtra="0dp"
            android:textSize="11dp"
            tools:ignore="UnusedAttribute"
            tools:text="Ultra HD" />
    </LinearLayout>

</layout>