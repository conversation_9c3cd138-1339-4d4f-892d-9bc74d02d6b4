<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/makeupContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:overScrollMode="never"
                android:layout_marginBottom="64dp"
                android:layout_marginTop="30dp"
                app:layout_constraintTop_toBottomOf="@id/rv_title"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_title"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:clickable="true"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:overScrollMode="never"
                android:visibility="visible"
                app:layout_constraintTop_toTopOf="parent"/>


            <!-- 重置美型 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/flMakeupReset"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_marginBottom="9dp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="30dp"
                android:layout_gravity="end|bottom"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <com.commsource.widget.IconFrontView
                    android:id="@+id/ifvReset"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:gravity="center"
                    android:text="@string/if_camera_reset"
                    android:textColor="@color/white"
                    android:textSize="24dp"
                    app:layout_constraintBottom_toTopOf="@id/tvReset"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.commsource.widget.AutoFitTextView
                    android:id="@+id/tvReset"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:text="@string/edit_reset"
                    android:textColor="@color/white"
                    android:textSize="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ifvReset" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 清除美型 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/llRemove"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_marginBottom="9dp"
                android:layout_gravity="start|bottom"
                android:layout_alignParentBottom="true"
                android:layout_marginStart="30dp"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <com.commsource.widget.IconFrontView
                    android:id="@+id/ifvRemove"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:fontFamily="@font/video_icon_font"
                    android:gravity="center"
                    android:text="@string/edit_icon_default"
                    android:textColor="@color/white"
                    android:textSize="18dp"
                    app:layout_constraintBottom_toTopOf="@id/tvRemove"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.commsource.widget.AutoFitTextView
                    android:id="@+id/tvRemove"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:text="@string/v78000_A_47"
                    android:textColor="@color/white"
                    android:textSize="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ifvRemove" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:id="@+id/fl_makeup_shrink"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_gravity="end|bottom"
                android:layout_marginEnd="30dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:visibility="gone">

                <com.commsource.widget.IconFrontView
                    android:id="@+id/ifv_shrink"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_gravity="center"
                    android:text="@string/if_new_makeup_shrink"
                    android:textSize="35dp" />

            </FrameLayout>

            <!-- AR屏蔽罩 -->
            <FrameLayout
                android:id="@+id/fl_mask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clickable="true"
                android:visibility="gone">

                <LinearLayout
                    android:id="@+id/ll_text"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_tips"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/look_ar_use_tips"
                        android:textColor="@color/color_333333"
                        android:textSize="15dp" />

                    <TextView
                        android:id="@+id/tv_remove_ar"
                        style="@style/selectable_item"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="5dp"
                        android:gravity="center"
                        android:paddingStart="10dp"
                        android:paddingTop="3dp"
                        android:paddingEnd="10dp"
                        android:paddingBottom="3dp"
                        android:text="@string/tap_to_cancel_ar"
                        android:textColor="@color/Primary_A"
                        android:textSize="15dp" />

                </LinearLayout>

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.commsource.widget.mask.MaskContainer
            android:id="@+id/maskContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="44dp"
            android:layout_marginBottom="90dp" />

    </FrameLayout>

</layout>