<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="54dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginEnd="5dp">

        <com.commsource.widget.ThumbnailContainer
            android:id="@+id/civ_thumbnail"
            tc_corner="@{4}"
            android:layout_width="54dp"
            android:layout_height="54dp" />

        <LinearLayout
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:gravity="center">

            <com.commsource.widget.CircleDownloadProgressView
                android:id="@+id/download_progress"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:visibility="gone"
                app:cdpvCircleColor="@color/color_6a6a6b"
                app:cdpvCircleRadius="9dp"
                app:cdpvMaskCircleColor="@color/color_4d6a6a6b"
                app:cdpvMaskColor="@color/color_b3ffffff" />

        </LinearLayout>

        <ImageView
            android:id="@+id/download_sign"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="39dp"
            android:scaleType="fitCenter"
            android:src="@drawable/common_download_icon_border_white"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/rl_beauty_defocus_selected"
            android:layout_width="54dp"
            android:layout_height="54dp">

            <ImageView
                android:id="@+id/iv_mask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                radius="@{4}"
                solid="@{@color/color_4d000000}"/>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@drawable/selfie_filter_little_seekbar_icon"
                android:visibility="visible" />

        </RelativeLayout>

        <com.commsource.widget.AutoFitTextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/civ_thumbnail"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="10dp" />

        <ImageView
            android:id="@+id/ivPremium"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="1dp"
            android:layout_marginEnd="1dp"
            android:src="@drawable/common_corner_premium_white_shadow"
            android:visibility="gone" />

    </RelativeLayout>
</layout>
