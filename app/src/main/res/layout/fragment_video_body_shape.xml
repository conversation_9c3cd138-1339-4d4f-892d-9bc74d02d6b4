<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />

        <variable
            name="fragment"
            type="androidx.fragment.app.Fragment" />
    </data>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false">

        <FrameLayout
            android:id="@+id/flAdjust"
            android:layout_width="match_parent"
            android:layout_height="136dp"
            android:layout_gravity="bottom"
            android:background="@color/Gray_Background_3"
            android:clickable="true"
            android:translationY="-60dp"
            android:visibility="visible">

            <!-- 新Seekbar -->
            <com.commsource.widget.XSeekBar
                android:id="@+id/xsb_auto"
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:layout_gravity="top"
                android:layout_marginStart="35dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="35dp"
                app:isEnableStroke="false"
                app:xBackgroundColor="@color/Gray_Background_4"
                app:xProgressColor="@color/Gray_label_1" />

        </FrameLayout>


        <com.commsource.widget.BottomSheetLayout
            android:id="@+id/fl_bottom"
            radiusLeftTop="@{16}"
            radiusRightTop="@{16}"
            solid="@{@color/edit_tab_background}"
            android:layout_width="match_parent"
            android:layout_height="152dp"
            android:layout_gravity="bottom"
            android:clickable="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.commsource.widget.IconFrontView
                    android:id="@+id/ivClose"
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:layout_marginStart="11dp"
                    android:layout_marginBottom="5dp"
                    android:gravity="center"
                    android:text="@string/all_icon_indicator_down_16"
                    android:textColor="@color/Gray_label_1"
                    android:textSize="24dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />


                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_auto"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toTopOf="@+id/tvTitle"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:scrollbars="none" />


                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/v_beauty_reshape"
                    android:textColor="@color/Gray_label_1"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.commsource.widget.BottomSheetLayout>

        <!--  气泡  -->
        <com.commsource.widget.part.XSeekBubbleView
            android:id="@+id/xbv"
            bindXSeek="@{xsbAuto}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|bottom"
            android:layout_marginStart="35dp"
            android:layout_marginBottom="196dp" />

        <!-- 悬浮的textview 显示不同function -->
        <RelativeLayout
            android:id="@+id/rl_floating"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="100dp"
            android:alpha="0">

            <com.commsource.widget.round.RoundFrameLayout
                android:id="@+id/fl_color"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_centerVertical="true"
                android:layout_marginEnd="2dp"
                android:layout_toStartOf="@+id/tv_floating"
                android:visibility="gone"
                app:rv_isRadiusHalfHeight="true"
                app:rv_isRippleEnable="false"
                app:rv_strokeColor="@color/white"
                app:rv_strokeWidth="1dp" />

            <com.commsource.comic.widget.StrokeTextView
                android:id="@+id/tv_floating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:textColor="@color/white"
                android:textSize="22dp" />

        </RelativeLayout>

    </FrameLayout>

</layout>