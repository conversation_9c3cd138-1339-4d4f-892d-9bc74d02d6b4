<?xml version="1.0" encoding="utf-8"?>
<layout>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/trending_new"
        android:layout_width="wrap_content"
        android:layout_height="37dp"
        android:layout_gravity="start|bottom"
        android:clipChildren="false"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|bottom"
            android:gravity="center"
            android:orientation="vertical">


            <TextView
                android:id="@+id/tvTrending"
                android:layout_width="wrap_content"
                android:layout_height="37dp"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:maxLines="1"
                android:text="@string/selfie_filter_shop"
                android:textColor="@color/Gray_D"
                android:textSize="14dp" />


        </LinearLayout>

        <ImageView
            android:id="@+id/new_Icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|top"
            android:src="@drawable/search_new_tag" />

        <ImageView
            android:id="@+id/hot_Icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|top"
            android:src="@drawable/search_hot_tag" />

    </FrameLayout>

</layout>