<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/Gray_Background_1">

        <FrameLayout
            android:id="@+id/videoContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.commsource.widget.video.VideoContainer
                android:id="@+id/videoView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.commsource.duffle.formula.BACompareView
                android:id="@+id/baView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="invisible"
                app:anim_duration="800"
                app:auto_loop="false"
                app:autoplay_end_pos="0.5"
                app:default_pos="0.5"
                app:enable_user_control="true"
                app:indicator_src="@drawable/video_repair_indicator"
                app:show_prompt_text="true"
                app:use_dynamic_time="2000" />

            <FrameLayout
                android:id="@+id/videoMask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/color_e6121212"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/videorepair_adjust_ic_lock" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="@string/ai_clip_unlocked"
                        android:textColor="@color/Gray_label_1"
                        android:textSize="14dp" />
                </LinearLayout>
            </FrameLayout>


        </FrameLayout>


        <com.commsource.widget.IconFrontView
            android:id="@+id/ifvBack"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:gravity="center"
            android:text="@string/common_icon_navbar_back"
            android:textColor="@color/white"
            android:textSize="20dp"
            android:visibility="gone"
            app:auto_mirror="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/seek_bar_container"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/optLayout">

            <com.commsource.widget.XSeekBar
                android:id="@+id/video_progress"
                android:layout_width="0dp"
                android:layout_height="20dp"
                android:layout_weight="1"
                app:xBackgroundColor="@color/white30"
                app:xProgress="0"
                app:xProgressColor="@color/white"
                app:xSeekbarHeight="4dp"
                app:xThumbIndicatorColor="@color/white"
                app:xThumbRadius="10dp" />

            <TextView
                android:id="@+id/video_duration"
                android:layout_width="90dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="8dp"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="12dp" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/optLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/Gray_Background_2"
            android:orientation="vertical"
            android:paddingHorizontal="24dp"
            android:paddingTop="16dp"
            app:layout_constraintTop_toBottomOf="parent">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/ai_edit_description"
                android:textColor="@color/Gray_label_3"
                android:textSize="12dp" />

            <com.commsource.widget.BoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="@string/ai_repair_image_quality"
                android:textColor="@color/Gray_label_1"
                android:textSize="16dp"
                app:boldTextWidth="1" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/quality_rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal" />

            <com.commsource.widget.BoldTextView
                android:id="@+id/resolution_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="@string/t_repair_resolution"
                android:textColor="@color/Gray_label_1"
                android:textSize="16dp"
                app:boldTextWidth="1" />

            <com.commsource.airepair.LabelSeekBar
                android:id="@+id/labelSeekBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:Labelseekbar_height="2dp"
                app:LabelthumbRadius="10dp" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp">

                <FrameLayout
                    android:id="@+id/startProcess"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="16dp"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">

                        <com.commsource.widget.BoldTextView
                            android:id="@+id/price_text1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="3dp"
                            android:gravity="center"
                            android:textColor="@color/white"
                            android:textSize="16dp"
                            app:boldTextWidth="1.2" />

                        <TextView
                            android:id="@+id/price_text2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:textColor="@color/white70"
                            android:textSize="10dp" />
                    </LinearLayout>
                </FrameLayout>
            </FrameLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:gravity="center"
                android:text="@string/ar_wait_description"
                android:textColor="@color/Gray_label_3"
                android:textSize="10dp" />
        </LinearLayout>

        <com.commsource.widget.mask.MaskContainer
            android:id="@+id/outMaskContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="10dp" />

        <FrameLayout
            android:id="@+id/prompt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_6009090A"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                android:text="@string/ai_warn_tips"
                android:textColor="@color/Gray_label_1"
                android:textSize="12dp"
                app:drawableStartCompat="@drawable/ai_repair_video_safe" />
        </FrameLayout>

        <com.commsource.ad.ADContainer
            android:id="@+id/adContainer"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:elevation="10dp"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>