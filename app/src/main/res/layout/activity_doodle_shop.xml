<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:focusable="true"
        android:focusableInTouchMode="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_top_bar"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:clipChildren="false"
                android:orientation="horizontal"
                app:layout_constraintTop_toTopOf="parent">

                <ImageButton
                    android:id="@+id/ib_img_back"
                    style="@style/ActionBar_Left"
                    android:layout_width="35dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="10dp"
                    android:src="@drawable/arrow_back_icon_black_normal" />

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.commsource.search_common.view.SearchEditTextView
                        android:id="@+id/search_edit"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp"
                        app:is_need_hot="true" />

                    <TextView
                        android:id="@+id/tv_cancel_search"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="end|center_vertical"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:gravity="center"
                        android:text="@string/ai_editor_cancel"
                        android:textColor="@color/color_333333"
                        android:textSize="14dp"
                        android:visibility="gone" />

                </FrameLayout>

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_group"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                app:layout_constraintTop_toBottomOf="@+id/ll_top_bar" />

            <View
                android:id="@+id/v_line"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/Gray_Dashline"
                app:layout_constraintTop_toBottomOf="@+id/rv_group"
                tools:ignore="MissingConstraints" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_below="@+id/rv_group"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="@+id/v_line" />

            <FrameLayout
                android:id="@+id/detailSearchContainer"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ll_top_bar" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.commsource.widget.mask.MaskContainer
            android:id="@+id/mask"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </FrameLayout>

</layout>