<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="70dp"
        android:layout_height="64dp"
        android:paddingTop="8dp"
        tools:background="@color/black">

        <com.commsource.widget.IconFrontView
            android:id="@+id/ifv_effect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="30dp" />


        <com.commsource.widget.SingleLineTextView
            android:id="@+id/tv_name"
            android:layout_width="60dp"
            android:layout_height="14dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="31dp"
            android:gravity="center"
            android:text="None"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textSize="10dp" />

        <View
            android:id="@+id/v_point"
            android:layout_width="3dp"
            android:layout_height="3dp"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginBottom="6dp"
            android:background="@drawable/red_point_f95485"
            tools:visibility="visible"
            android:visibility="gone" />


    </FrameLayout>
</layout>