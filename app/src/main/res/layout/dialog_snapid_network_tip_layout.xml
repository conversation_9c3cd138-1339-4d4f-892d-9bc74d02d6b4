<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/rl_common_dialog_a_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rl_common_dialog_a_content"
            radius="@{20}"
            solid="@{@color/white}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:padding="24dp">

            <TextView
                android:id="@+id/tv_common_dialog_a_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/v77080_B_1"
                android:textColor="@color/color_333333"
                android:textSize="18sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginTop="16dp" />

            <TextView
                android:id="@+id/tv_common_dialog_a_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text="@string/v77080_B_2"
                android:textColor="@color/black"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_common_dialog_a_title" />

            <TextView
                android:id="@+id/tv_common_dialog_a_positive"
                radius="@{25}"
                solid="@{@color/color_73ec86}"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginTop="24dp"
                android:gravity="center"
                android:text="@string/v77080_B_3"
                android:textColor="@color/black"
                android:textSize="19sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_common_dialog_a_content"
                app:layout_constraintVertical_bias="0.43" />

            <TextView
                android:id="@+id/tv_common_dialog_a_negative"
                radius="@{25}"
                solid="@{@color/color_f3f3f3}"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginTop="12dp"
                android:gravity="center"
                android:text="@string/v77080_B_4"
                android:textColor="@color/black"
                android:textSize="19sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_common_dialog_a_positive" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </RelativeLayout>

    <data>

        <import type="android.view.View" />


    </data>
</layout>