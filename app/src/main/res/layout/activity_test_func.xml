<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".setting.test.TestFuncActivity">

        <com.commsource.widget.title.XTitleBar
            android:id="@+id/xtb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:xtb_title="测试功能" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/xtb">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/deviceInfo"
                    style="@style/settings_button_style"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="15dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="15dp"
                    android:text="机型参数："
                    android:textColor="@color/color_333333"
                    android:textSize="12sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <RelativeLayout
                        android:layout_width="fill_parent"
                        android:layout_height="55dp">

                        <TextView
                            style="@style/settings_button_style"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:paddingStart="15dp"
                            android:text="视频相关强制保存失败"
                            android:textColor="@color/color_333333"
                            android:textSize="16sp" />

                        <com.commsource.beautyplus.setting.widget.SwitchView
                            android:id="@+id/vSwitchVideoSaveFail"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="15dp" />

                    </RelativeLayout>

                </RelativeLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <RelativeLayout
                        android:layout_width="fill_parent"
                        android:layout_height="55dp">

                        <TextView
                            style="@style/settings_button_style"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:paddingStart="15dp"
                            android:text="Apple2_1 从SD卡读取滤镜Lut"
                            android:textColor="@color/color_333333"
                            android:textSize="16sp" />

                        <com.commsource.beautyplus.setting.widget.SwitchView
                            android:id="@+id/apple21_test"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="15dp" />

                    </RelativeLayout>

                </RelativeLayout>


                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <RelativeLayout
                        android:layout_width="fill_parent"
                        android:layout_height="55dp">

                        <TextView
                            style="@style/settings_button_style"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:paddingStart="15dp"
                            android:text="复古相机中端机配置降级"
                            android:textColor="@color/color_333333"
                            android:textSize="16sp" />

                        <com.commsource.beautyplus.setting.widget.SwitchView
                            android:id="@+id/vSame2ClassicCamera"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="15dp" />

                    </RelativeLayout>

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <RelativeLayout
                        android:layout_width="fill_parent"
                        android:layout_height="55dp">

                        <TextView
                            style="@style/settings_button_style"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:paddingStart="15dp"
                            android:text="UIChecker开关"
                            android:textColor="@color/color_333333"
                            android:textSize="16sp" />

                        <com.commsource.beautyplus.setting.widget.SwitchView
                            android:id="@+id/vSwitchUIChecker"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="15dp" />

                    </RelativeLayout>

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <TextView
                        android:id="@+id/maxDebugger"
                        style="@style/settings_button_style"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="80dp"
                        android:gravity="start|center_vertical"
                        android:paddingStart="15dp"
                        android:text="开启Max广告Debugger"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="是否启用防截屏"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/screenShotSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--ABTest数据-->
                <TextView
                    android:id="@+id/vTvBugKiller"
                    style="@style/settings_button_style"
                    android:layout_width="fill_parent"
                    android:layout_height="55dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp"
                    android:text="BugKiller"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--ABTest数据-->
                <TextView
                    android:id="@+id/vTvABTest"
                    style="@style/settings_button_style"
                    android:layout_width="fill_parent"
                    android:layout_height="55dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp"
                    android:text="ABTest数据"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--AR一键下载-->
                <RelativeLayout
                    android:id="@+id/llInputDeeplink"
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="手动输入协议"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="15dp"
                        android:text="拦截订阅"
                        android:textColor="@color/color_333333" />

                    <com.commsource.camera.xcamera.widget.CameraSwitchButton
                        android:id="@+id/sw_proxy"
                        android:layout_width="40dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="15dp"
                        android:text="是否订阅"
                        android:textColor="@color/color_333333" />

                    <com.commsource.camera.xcamera.widget.CameraSwitchButton
                        android:id="@+id/sw_fake"
                        android:layout_width="40dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp" />

                </LinearLayout>

                <!--UI核对开关-->

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--Firebase打点数据-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="Firebase打点写入文件"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchFirebaseEventOutput"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--显示首页停留时间开关-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="自拍效果核对"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />


                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchCameraEffeckCheck"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--显示首页停留时间开关-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="补光强制命中低端机"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />


                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchGlowEffeckCheck"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--显示首页停留时间开关-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="测试包关闭广告"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />


                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchDisableAds"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--显示首页停留时间开关-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="首页停留时间"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchFormalHomeDuration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--素材切换正式环境-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="素材切换为测试环境"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchProductCenterDebug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--素材切换正式环境-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="解锁所有单购"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchAllPurchase"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="Webview调试功能"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchWebViewDebug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <TextView
                        android:id="@+id/apm_btn"
                        style="@style/settings_button_style"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="80dp"
                        android:gravity="start|center_vertical"
                        android:paddingStart="15dp"
                        android:text="B+ 在线APM"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/forceDump"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="20dp"
                        android:gravity="center"
                        android:text="强制Dump上报"
                        android:textColor="@color/color_fb5986"
                        android:textSize="14dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8"
                    android:visibility="gone" />

                <!--灵问环境切换-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp"
                    android:visibility="gone">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="灵问强制使用正式环境"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchLingWen"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--灵问环境切换-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        android:id="@+id/btnLingWen"
                        style="@style/settings_button_style"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="80dp"
                        android:gravity="start|center_vertical"
                        android:paddingStart="15dp"
                        android:text="灵问测试页面"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--素材切换正式环境-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="遍历AR内存性能"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/svScanArPerformance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--AR一键下载-->
                <RelativeLayout
                    android:id="@+id/ll_store"
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="沙盒存储"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <!--AR一键下载-->
                <RelativeLayout
                    android:id="@+id/ll_dialog"
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="Dialog测试页面"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <!--AR一键下载-->
                <RelativeLayout
                    android:id="@+id/ll_thumb"
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="Thumbnail测试页面"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <!--AR一键下载-->
                <RelativeLayout
                    android:id="@+id/ll_http_cache"
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="HttpCache测试页面"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/ll_user_portrait"
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="手动输入用户画像分组，“,”分隔"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--AR一键下载-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="ar一键下载"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchArDownload"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--打印人脸点的开关-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="打印人脸信息"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchTestPrintFaceInfos"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--效果调试工具-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="效果调试工具"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchDebugEffectTool"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--闪退-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        android:id="@+id/vTvCrashDebug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="闪退弹窗（再点文字5次以开启/关闭）"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/crashDebug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--闪退-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        android:id="@+id/vTvCppCrashDebug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="底层闪退测试（再点文字5次崩溃）"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--闪退-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        android:id="@+id/vTvVivoCrashDebug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="VIVO 闪退弹窗（再点文字5次以开启/关闭）"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/VivoCrashDebug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--闪退-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        android:id="@+id/vTvSoMissDebug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="SO 丢失（再点文字5次以开启/关闭）"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/soMissDebug"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--消耗-->
                <LinearLayout
                    android:id="@+id/vLlConsume"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/vTvConsume"
                        android:layout_width="match_parent"
                        android:layout_height="55dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="消耗"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>

                <!--用户已购商品-->
                <LinearLayout
                    android:id="@+id/vLlProduct"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/vTvProduct"
                        android:layout_width="match_parent"
                        android:layout_height="55dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="已购商品"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>


                <!--新手订阅优惠时间-->
                <LinearLayout
                    android:id="@+id/vLlDiscountTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/vTvDiscountTime"
                            android:layout_width="match_parent"
                            android:layout_height="55dp"
                            android:background="?attr/selectableItemBackground"
                            android:padding="15dp"
                            android:text="新手订阅优惠时间: "
                            android:textColor="@color/color_333333"
                            android:textSize="15sp" />

                        <EditText
                            android:id="@+id/et_input"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="20dp"
                            android:inputType="number"
                            android:textColor="@color/color_333333"
                            android:textSize="15dp" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>

                <!--订阅用户类型-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="55dp"
                        android:layout_marginEnd="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="订阅用户类型"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vTvUserType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="15dp" />

                    <Spinner
                        android:id="@+id/v_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:dropDownVerticalOffset="40dp"
                        android:spinnerMode="dropdown" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--购买消除笔-->
                <LinearLayout
                    android:id="@+id/vLlPurchase"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/vTvPurchase"
                        android:layout_width="match_parent"
                        android:layout_height="55dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="购买消除笔"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>

                <!--购买面部打光-->
                <LinearLayout
                    android:id="@+id/vLlPurchaseRelight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/vTvPurchaseRelight"
                        android:layout_width="match_parent"
                        android:layout_height="55dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="购买面部打光"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>

                <!--购买AI增强-->
                <LinearLayout
                    android:id="@+id/vLlPurchaseEnhance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/vTvPurchaseEnhance"
                        android:layout_width="match_parent"
                        android:layout_height="55dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="购买AI增强"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>

                <!--购买AI增强-->
                <LinearLayout
                    android:id="@+id/vLlPurchaseAiBeauty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/vTvPurchaseAiBeauty"
                        android:layout_width="match_parent"
                        android:layout_height="55dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="购买AI美颜"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>

                <!--清除数据-->
                <LinearLayout
                    android:id="@+id/vLlClear"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/vTvClear"
                        android:layout_width="match_parent"
                        android:layout_height="55dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="清除数据"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>

                <!--协议跳转-->
                <LinearLayout
                    android:id="@+id/llUrlJump"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvUrlJump"
                        android:layout_width="match_parent"
                        android:layout_height="55dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="H5 协议"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>

                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="GL泄漏检测"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchGLMemoryDetecor"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--Firebase打点数据-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="笔刷核对工具"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/brushDebugSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <TextView
                        android:id="@+id/upload_device"
                        style="@style/settings_button_style"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="80dp"
                        android:gravity="start|center_vertical"
                        android:paddingStart="15dp"
                        android:text="上报机型参数"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="15dp"
                        android:text="创意者"
                        android:textColor="@color/color_333333" />

                    <com.commsource.camera.xcamera.widget.CameraSwitchButton
                        android:id="@+id/sw_create"
                        android:layout_width="40dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--协议跳转-->
                <LinearLayout
                    android:id="@+id/llOneThingInTheEnd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvOneThingInTheEnd"
                        android:layout_width="match_parent"
                        android:layout_height="55dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="15dp"
                        android:text="一货到底到付费滤镜"
                        android:textColor="@color/color_333333"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@color/color_e6e5e8" />

                </LinearLayout>

                <!--模拟拉取订阅折扣信息-->
                <RelativeLayout
                    android:id="@+id/ll_subs_config"
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="订阅配置"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="滤镜数据源读取SD卡"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchFilterDataSource"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        android:id="@+id/createDump"
                        style="@style/settings_button_style"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:gravity="start|center_vertical"
                        android:paddingStart="15dp"
                        android:text="拉取Dump 文件 (耗时)"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="15dp"
                        android:text="拦截是否新用户"
                        android:textColor="@color/color_333333" />

                    <com.commsource.camera.xcamera.widget.CameraSwitchButton
                        android:id="@+id/sw_new_user_proxy"
                        android:layout_width="40dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="15dp"
                        android:text="是否新用户"
                        android:textColor="@color/color_333333" />

                    <com.commsource.camera.xcamera.widget.CameraSwitchButton
                        android:id="@+id/sw_fake_new_user"
                        android:layout_width="40dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:background="@color/color_e6e5e8" />

                <!--启动展示onboarding页面-->
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="55dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:paddingStart="15dp"
                        android:text="启动展示onboarding"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vOnBoardingSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="15dp" />

                </RelativeLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/userType"
                            style="@style/settings_button_style"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:paddingStart="15dp"
                            android:text="获取用户类型"
                            android:textColor="@color/color_333333"
                            android:textSize="16sp" />

                    </RelativeLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:orientation="horizontal"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="HW Gid拦截"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchGid"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="15dp" />

                    <EditText
                        android:id="@+id/et_gid"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="20dp"
                        android:hint="输入gid"
                        android:imeOptions="actionDone"
                        android:inputType="number"
                        android:minWidth="30dp"
                        android:padding="12dp"
                        android:textColor="@color/color_333333"
                        android:textColorHint="@color/Gray_C"
                        android:textSize="15dp"
                        android:visibility="visible" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:orientation="horizontal"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp">

                    <TextView
                        style="@style/settings_button_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="拦截续订挽留弹窗展示时间"
                        android:textColor="@color/color_333333"
                        android:textSize="16sp" />

                    <com.commsource.beautyplus.setting.widget.SwitchView
                        android:id="@+id/vSwitchRenew"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="15dp" />

                    <EditText
                        android:id="@+id/et_renew_time"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="20dp"
                        android:hint="输入续订挽留弹窗时间单位毫秒"
                        android:imeOptions="actionDone"
                        android:inputType="number"
                        android:minWidth="30dp"
                        android:padding="12dp"
                        android:textColor="@color/color_333333"
                        android:textColorHint="@color/Gray_C"
                        android:textSize="15dp"
                        android:visibility="visible" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvFirstIntall"
                    style="@style/settings_button_style"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="15dp"
                    android:text="重置首次安装状态"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvWeek"
                    style="@style/settings_button_style"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="15dp"
                    android:text="周订阅条件"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvNewUser"
                    style="@style/settings_button_style"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="start|center_vertical"
                    android:text="设置需要添加水印"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvHairDye"
                    style="@style/settings_button_style"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="15dp"
                    android:text="获取染发素材"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/subscribeDialog"
                    style="@style/settings_button_style"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="start|center_vertical"
                    android:text="订阅权益弹窗"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvAdTestActivity"
                    style="@style/settings_button_style"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="15dp"
                    android:text="广告相关"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvDeleteLimitFreeData"
                    style="@style/settings_button_style"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="15dp"
                    android:text="删除限免数据"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvDefaultDeeplink"
                    style="@style/settings_button_style"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="start|center_vertical"
                    android:paddingStart="15dp"
                    android:text="预置协议跳转"
                    android:textColor="@color/color_333333"
                    android:textSize="16sp" />
            </LinearLayout>

        </ScrollView>

        <FrameLayout
            android:id="@+id/apm_page"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/White"
            android:visibility="gone" />

        <!--搜索Fragment包裹布局-->
        <FrameLayout
            android:id="@+id/subSearchContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}" />

    </RelativeLayout>

</layout>
