<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="false"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            />

        <FrameLayout
            android:id="@+id/fl_empty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <com.commsource.widget.IconFrontView
                android:layout_width="63dp"
                android:layout_height="63dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="22.5dp"
                android:gravity="center"
                android:text="@string/all_img_warning_no_content"
                android:textColor="@color/Gray_B"
                android:textSize="63dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:layout_marginTop="94.5dp"
                android:text="@string/t_text_favorite_tips"
                android:textColor="@color/color_ffb7b7b7"
                android:textSize="12dp" />

        </FrameLayout>

    </FrameLayout>

</layout>