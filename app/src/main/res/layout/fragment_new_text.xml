<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />

        <import type="com.meitu.library.util.device.DeviceUtils" />

        <variable
            name="fragment"
            type="androidx.fragment.app.Fragment" />
    </data>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/btnTextStyle"
            radius="@{15}"
            solid="@{@color/white}"
            strokeColor="@{@color/Gray_Dashline}"
            strokeWidth="@{0.5f}"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_above="@+id/fl_bottom"
            android:layout_gravity="bottom"
            android:layout_marginStart="109dp"
            android:layout_marginBottom="15dp"
            android:alpha="0"
            android:drawablePadding="5dp"
            android:gravity="center"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:text="@string/t_text_edit"
            android:textColor="@color/Gray_A"
            android:textSize="14dp"
            android:visibility="invisible" />

        <com.commsource.studio.component.UndoRedoComponent
            android:id="@+id/undoRedo"
            bindFragment="@{fragment}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/fl_bottom"
            android:layout_margin="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="16dp"
            android:alpha="0"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/fl_bottom"
            android:layout_width="match_parent"
            android:layout_height="@{StudioLayoutConstants.INSTANCE.DECORATE_PANEL_HEIGHT - DeviceUtils.dip2px(50f),default = match_parent}"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:layout_marginBottom="50dp"
            android:clickable="true">

            <RelativeLayout
                android:id="@+id/templatePanel"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/templateCategory"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginEnd="46dp"/>

                <com.commsource.widget.IconFrontView
                    android:id="@+id/function_search"
                    solid="@{@color/white}"
                    radiusRightTop="@{10}"
                    android:layout_width="46dp"
                    android:layout_height="44dp"
                    android:layout_marginStart="15dp"
                    android:gravity="center"
                    android:layout_alignParentEnd="true"
                    android:text="@string/if_filter_search"
                    android:textColor="@color/Gray_B"
                    android:textSize="30dp"
                    app:view_text_size="11dp" />

                <!-- 属性的Bar -->
                <FrameLayout
                    android:id="@+id/template_line_divider"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_below="@id/templateCategory"
                    android:layout_marginEnd="46dp"
                    android:layout_gravity="bottom"
                    android:background="@color/white">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:layout_gravity="bottom"
                        android:background="@color/Gray_Dashline" />

                    <com.commsource.widget.LineSelectView
                        android:id="@+id/templateLineSelect"
                        bindRecyclerView="@{templateCategory}"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="bottom" />

                </FrameLayout>

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/vpTextTemplate"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@id/template_line_divider" />

            </RelativeLayout>

            <com.commsource.widget.mask.MaskContainer
                android:id="@+id/maskContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                />

        </FrameLayout>

    </RelativeLayout>

</layout>
