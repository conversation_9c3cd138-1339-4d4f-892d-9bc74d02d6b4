<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/fl_multi_face_bar"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:alpha="0"
            android:background="@color/edit_tab_background_80">

            <com.commsource.widget.IconFrontView
                android:id="@+id/ifv_close_multi_face"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="start|center_vertical"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/video_icon_font"
                android:text="@string/vf_video_edit_icon_multi_faces_on"
                android:textColor="@color/Primary_A"
                android:textSize="24dp" />

            <com.commsource.camera.xcamera.cover.bottomFunction.effect.filter.category.FadingEdgeRecyclerView
                android:id="@+id/rv_face"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="44dp"
                android:layout_marginEnd="44dp"
                android:clipChildren="false"
                android:clipToPadding="false" />

            <com.commsource.widget.RotateLoadingView
                android:id="@+id/loading"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical|end"
                android:layout_marginEnd="12dp"
                android:background="@drawable/icon_loading"
                android:visibility="gone" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_face_bar"
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <FrameLayout
                android:id="@+id/fl_open_multi_face"
                radius="@{18}"
                solid="@{@color/black50}"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_gravity="center"
                android:visibility="gone">

                <com.commsource.widget.IconFrontView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="start|center_vertical"
                    android:layout_marginStart="12dp"
                    android:fontFamily="@font/video_icon_font"
                    android:text="@string/vf_video_edit_icon_multi_faces_off"
                    android:textColor="@color/white"
                    android:textSize="24dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="44dp"
                    android:layout_marginEnd="12dp"
                    android:text="@string/v_beauty_click_distinguish"
                    android:textColor="@color/white"
                    android:textSize="14dp" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_multi_face_loading"
                radius="@{18}"
                solid="@{@color/black50}"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_gravity="center">

                <com.commsource.widget.RotateLoadingView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="start|center_vertical"
                    android:layout_marginStart="12dp"
                    android:background="@drawable/icon_loading" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="36dp"
                    android:layout_marginEnd="12dp"
                    android:text="@string/v_face_detecting"
                    android:textColor="@color/white"
                    android:textSize="14dp" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_body_detect_loading"
                radius="@{18}"
                solid="@{@color/black50}"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_gravity="center"
                android:visibility="gone">

                <com.commsource.widget.CircleDownloadProgressView
                    android:id="@+id/body_detect_loading"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="start|center_vertical"
                    android:layout_marginStart="12dp"
                    app:cdpvCircleColor="@color/white"
                    app:cdpvCircleRadius="16dp"
                    app:cdpvCircleStrokeWidth="2dp"
                    app:cdpvMaskCircleColor="@color/white50"
                    app:cdpvMaskColor="@color/transparent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="44dp"
                    android:layout_marginEnd="12dp"
                    android:text="@string/v_reshape_detecting_tips"
                    android:textColor="@color/white"
                    android:textSize="14dp" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_detect_body_tips"
                radius="@{18}"
                solid="@{@color/black50}"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_gravity="center"
                android:visibility="gone">

                <com.commsource.widget.IconFrontView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="start|center_vertical"
                    android:layout_marginStart="12dp"
                    android:fontFamily="@font/icon_font"
                    android:text="@string/edit_icon_beauty_unidentified"
                    android:textColor="@color/white"
                    android:textSize="24dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="44dp"
                    android:layout_marginEnd="12dp"
                    android:text="@string/v_reshape_detected_no_body_tips"
                    android:textColor="@color/white"
                    android:textSize="14dp" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_detect_multi_body_tips"
                radius="@{18}"
                solid="@{@color/black50}"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginStart="50dp"
                android:layout_marginEnd="50dp"
                android:layout_gravity="center"
                android:visibility="gone">

                <com.commsource.widget.IconFrontView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="start|center_vertical"
                    android:layout_marginStart="12dp"
                    android:fontFamily="@font/icon_font"
                    android:text="@string/edit_icon_beauty_onlyone"
                    android:textColor="@color/white"
                    android:textSize="24dp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/fl_detect_multi_body_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="44dp"
                    android:layout_marginEnd="12dp"
                    app:autoSizeMaxTextSize="14dp"
                    app:autoSizeMinTextSize="3dp"
                    app:autoSizeTextType="uniform"
                    android:gravity="start"
                    android:maxLines="2"
                    android:text="@string/v_beauty_support_one_person_tips"
                    android:textColor="@color/white"
                    android:textSize="14dp" />
            </FrameLayout>

        </FrameLayout>

        <FrameLayout
            android:id="@+id/flAdjust"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_gravity="bottom"
            android:layout_marginBottom="124dp"
            android:alpha="0"
            android:background="@color/Gray_Background_3"
            android:clickable="true"
            android:translationY="60dp"
            android:visibility="gone"
            tools:visibility="visible">

            <!-- 新Seekbar -->
            <com.commsource.widget.XSeekBar
                android:id="@+id/xsb_auto"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_gravity="top"
                android:layout_marginStart="52dp"
                android:layout_marginEnd="52dp"
                app:isEnableStroke="false"
                app:xBackgroundColor="@color/Gray_Background_4"
                app:xProgressColor="@color/Gray_label_1" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/flAdjustCheckBox"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_gravity="bottom"
            android:layout_marginBottom="124dp"
            android:alpha="0"
            android:background="@color/Gray_Background_3"
            android:clickable="true"
            android:paddingBottom="16dp"
            android:translationY="60dp"
            android:visibility="gone"
            tools:visibility="visible">

            <com.commsource.beautyplus.miniapp.XSegmentButtonGroup
                android:id="@+id/xsbg"
                android:layout_width="130dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:layoutDirection="ltr"
                app:seg_background_color="@color/gray_background_placeholder"
                app:seg_background_corner="16dp"
                app:seg_indicator_color="@color/white"
                app:seg_indicator_corner="16dp"
                app:seg_indicator_shadow_enable="false"
                app:seg_padding="3dp">

                <TextView
                    android:layout_width="65dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="ON"
                    android:textColor="@color/text_select_black"
                    android:textSize="14dp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="65dp"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="OFF"
                    android:textColor="@color/text_select_black"
                    android:textSize="14dp"
                    android:textStyle="bold" />

            </com.commsource.beautyplus.miniapp.XSegmentButtonGroup>

        </FrameLayout>

        <com.commsource.widget.BottomSheetLayout
            android:id="@+id/fl_bottom"
            radiusLeftTop="@{16}"
            radiusRightTop="@{16}"
            solid="@{@color/edit_tab_background}"
            android:layout_width="match_parent"
            android:layout_height="140dp"
            android:layout_gravity="bottom"
            android:clickable="true">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvBeauty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp" />

            </FrameLayout>

        </com.commsource.widget.BottomSheetLayout>

        <FrameLayout
            android:id="@+id/flTabBottom"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_gravity="bottom"
            android:background="@color/edit_tab_background">

            <com.commsource.widget.IconFrontView
                android:id="@+id/ivClose"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_gravity="start|bottom"
                android:layout_marginStart="11dp"
                android:layout_marginBottom="5dp"
                android:fontFamily="@font/video_icon_font"
                android:gravity="center"
                android:text="@string/vf_all_icon_collapse"
                android:textColor="@color/Gray_label_1"
                android:textSize="24dp"
                app:auto_mirror="true" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:layout_marginBottom="12.5dp"
                android:text="@string/v_facial_beauty"
                android:textColor="@color/Gray_label_1"
                android:textSize="16dp" />

        </FrameLayout>

        <!--  气泡  -->
        <com.commsource.widget.part.XSeekBubbleView
            android:id="@+id/xbv"
            bindXSeek="@{xsbAuto}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|bottom"
            android:layout_marginStart="52dp"
            android:layout_marginBottom="184dp" />

    </FrameLayout>

</layout>