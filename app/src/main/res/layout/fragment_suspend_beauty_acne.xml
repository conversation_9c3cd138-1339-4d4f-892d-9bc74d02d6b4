<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_gravity="bottom">

        <com.commsource.beautyplus.miniapp.XSegmentButtonGroup
            android:id="@+id/xsbg"
            android:layout_width="112dp"
            android:layout_height="32dp"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="16dp"
            app:seg_background_color="@color/color_80000000"
            app:seg_background_corner="16dp"
            app:seg_indicator_color="@color/white"
            app:seg_indicator_corner="16dp"
            app:seg_indicator_shadow_enable="false"
            app:seg_padding="2dp">

            <TextView
                android:layout_width="56dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="OFF"
                android:textColor="@color/text_select_black"
                android:textSize="13dp" />

            <TextView
                android:layout_width="56dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="ON"
                android:textColor="@color/text_select"
                android:textSize="13dp" />

        </com.commsource.beautyplus.miniapp.XSegmentButtonGroup>

    </FrameLayout>
</layout>