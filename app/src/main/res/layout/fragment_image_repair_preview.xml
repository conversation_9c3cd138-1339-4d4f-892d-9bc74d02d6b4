<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_f5f5f5"
        android:clickable="true"
        android:focusable="true">

        <com.commsource.widget.IconFrontView
            android:id="@+id/ifvBack"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:text="@string/common_icon_navbar_back"
            android:textColor="@color/color_333333"
            android:textSize="20dp"
            app:auto_mirror="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="52dp"
            android:layout_marginEnd="52dp"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/v77170_D_1"
            android:textColor="@color/color_333333"
            android:textSize="16dp"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="16dp"
            app:autoSizeMinTextSize="8dp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="@id/ifvBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/ifvBack" />

        <com.commsource.airepair.imagequality.view.ImageRepairContractView
            android:id="@+id/ivContract"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="44dp"
            app:layout_constraintBottom_toTopOf="@id/flBackground"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/flBackground"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="-12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="@id/llBefore" />

        <LinearLayout
            android:id="@+id/llBefore"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@id/clSave"
            app:layout_constraintEnd_toStartOf="@id/llContrast"
            app:layout_constraintStart_toStartOf="parent">

            <com.commsource.widget.IconFrontView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:fontFamily="@font/icon_font"
                android:text="@string/repair_icon_original"
                android:textColor="@color/color_333333"
                android:textSize="28dp" />

            <com.commsource.widget.AutoFitTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:maxLines="1"
                android:text="@string/v77170_D_17"
                android:textColor="@color/color_333333"
                android:textSize="12dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llContrast"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintEnd_toStartOf="@id/llAfter"
            app:layout_constraintStart_toEndOf="@id/llBefore"
            app:layout_constraintTop_toTopOf="@id/llBefore">

            <com.commsource.widget.IconFrontView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:fontFamily="@font/icon_font"
                android:text="@string/home_repair_ic_contrast"
                android:textColor="@color/color_333333"
                android:textSize="28dp" />

            <com.commsource.widget.AutoFitTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:maxLines="1"
                android:text="@string/v77170_D_18"
                android:textColor="@color/color_333333"
                android:textSize="12dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llAfter"
            android:layout_width="0dp"
            android:layout_height="72dp"
            android:layout_marginEnd="16dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/llContrast"
            app:layout_constraintTop_toTopOf="@id/llBefore">

            <com.commsource.widget.IconFrontView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:fontFamily="@font/icon_font"
                android:text="@string/a_videorepair_adjust_ic_hd"
                android:textColor="@color/color_333333"
                android:textSize="28dp" />

            <com.commsource.widget.AutoFitTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:maxLines="1"
                android:text="@string/v77170_D_19"
                android:textColor="@color/color_333333"
                android:textSize="12dp" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clSave"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:id="@+id/ivPro"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:scaleType="fitCenter"
                android:src="@drawable/common_corner_premium_black_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tvSave"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.commsource.widget.PressTextView
                android:id="@+id/tvSave"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:gravity="center"
                android:maxLines="1"
                android:text="@string/confirm_save"
                android:textColor="@color/white"
                android:textSize="16dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivPro"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.commsource.widget.mask.MaskContainer
            android:id="@+id/loadContainer"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="@id/ivContract" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>