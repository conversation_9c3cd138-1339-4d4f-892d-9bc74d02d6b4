<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.commsource.widget.corner.CornerContainer
            android:id="@+id/cc_container"
            cc_corner="@{16f}"
            cc_strokeColor="@{@color/Gray_Dashline}"
            cc_strokeEnable="@{true}"
            cc_strokeWidth="@{0.5f}"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white">

            <ImageView
                android:id="@+id/iv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY" />

            <!--点击水波纹控件-->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/ripple_radius_6" />

        </com.commsource.widget.corner.CornerContainer>

    </FrameLayout>


</layout>