<?xml version="1.0" encoding="utf-8"?>
<layout>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/edit_tab_background">

        <com.commsource.widget.IconFrontView
            android:id="@+id/ivClose"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="start|center_vertical"
            android:layout_marginStart="16dp"
            android:gravity="center"
            android:text="@string/all_icon_indicator_down_16"
            android:textColor="@color/Gray_label_1"
            android:textSize="24dp" />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textColor="@color/Gray_label_1"
            android:textSize="16dp" />

    </FrameLayout>
</layout>