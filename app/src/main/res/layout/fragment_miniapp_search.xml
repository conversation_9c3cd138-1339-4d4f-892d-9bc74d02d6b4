<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.commsource.homev2.ui.search.miniapp.MiniAppSearchViewModel" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="16dp"
        android:background="@color/white"
        android:clickable="true"
        android:focusable="true"
        app:onClickCommand="@{viewModel.blankClick}">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!--            最近搜索-->
                <include
                    layout="@layout/layout_search_history"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    app:viewModel="@{viewModel.searchHistoryViewModel}" />

                <!--            热词-->
                <include
                    layout="@layout/layout_search_hot"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:viewModel="@{viewModel.searchHotViewModel}" />

            </androidx.appcompat.widget.LinearLayoutCompat>


            <com.commsource.widget.mask.MaskContainer
                android:id="@+id/mask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:visibility="@{viewModel.netStatusVisibility}"
                app:searchStatus="@{viewModel.maskStatus}"
                app:searchWord="@{viewModel.searchWord}">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:overScrollMode="never"
                    app:currentView="@{viewModel.getRecyclerView}"
                    app:setRVAdapter="@{viewModel.searchAdapter}"
                    app:trackRecyclerView="@{viewModel.tracker}" />

            </com.commsource.widget.mask.MaskContainer>

        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>