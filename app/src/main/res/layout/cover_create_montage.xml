<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 蒙太奇顶部 -->
        <RelativeLayout
            android:id="@+id/rl_top_bar"
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:visibility="visible">

            <com.commsource.widget.IconFrontView
                android:id="@+id/ifv_back"
                android:layout_width="35dp"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:text="@string/selfie_top_icon_back"
                android:textColor="@color/black"
                android:textSize="35dp"
                app:auto_mirror="true"
                app:show_stroke="false"
                app:stroke_color="@color/color_4d000000"
                app:usePressState="true" />

            <!--切换摄像头,右边-->
            <com.commsource.widget.IconFrontView
                android:id="@+id/ifv_switch"
                android:layout_width="35dp"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:text="@string/selfie_top_icon_cameraswitch"
                android:textColor="@color/black"
                android:textSize="35dp"
                app:show_stroke="false"
                app:stroke_color="@color/color_4d000000"
                app:usePressState="true" />

        </RelativeLayout>

        <com.meitu.ratiorelativelayout.RatioRelativeLayout
            android:id="@+id/rl_montage_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"
            app:layout_aspectRatio="0.75"
            app:layout_widthSpec="375">

            <ImageView
                android:id="@+id/iv_montage_face"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                app:layout_ratioHeight="281"
                app:layout_ratioMarginTop="82"
                app:layout_ratioWidth="236" />

            <com.commsource.comic.widget.StrokeTextView
                android:id="@+id/tv_montage_no_face_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/iv_montage_face"
                android:layout_centerHorizontal="true"
                android:text="@string/montage_create_text_tip"
                android:textColor="@color/white"
                android:textSize="18dp"
                app:layout_ratioMarginTop="20" />

        </com.meitu.ratiorelativelayout.RatioRelativeLayout>

    </FrameLayout>
</layout>