<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />

        <variable
            name="fragment"
            type="androidx.fragment.app.Fragment" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false">

        <com.commsource.studio.component.SeekComponent
            android:id="@+id/sc"
            radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            solid="@{@color/Gray_E}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/fl_bottom"
            android:clipChildren="false"
            android:clipToPadding="false" />

        <RelativeLayout
            android:id="@+id/fl_seek_container"
            android:layout_width="match_parent"
            android:layout_height="170dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_above="@+id/fl_bottom">

            <com.commsource.studio.component.BannerContainer
                android:id="@+id/flBottomBanner"
                android:layout_width="match_parent"
                android:alpha="0"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="-12dp"
                android:clipToPadding="false"
                android:clipChildren="false"
                android:layout_height="12dp">

                <!-- 付费Banner-->
                <com.commsource.studio.component.ProBottomBannerComponent
                    android:id="@+id/proBottomBanner"
                    android:layout_width="match_parent"
                    bindFragment="@{fragment}"
                    android:layout_height="wrap_content"/>

            </com.commsource.studio.component.BannerContainer>

            <!-- 预览 -->
            <com.commsource.studio.component.PreviewComponent
                android:id="@+id/preview"
                bindFragment="@{fragment}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@id/flBottomBanner"
                android:layout_toStartOf="@id/contrast"
                android:paddingStart="14dp"
                android:paddingTop="14dp"
                android:paddingBottom="14dp" />

            <!--对比-->
            <com.commsource.studio.component.ContrastComponent
                android:id="@+id/contrast"
                bindFragment="@{fragment}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_above="@id/flBottomBanner"
                android:padding="14dp" />



        </RelativeLayout>

        <com.meitu.ratiorelativelayout.RatioRelativeLayout
            android:id="@+id/fl_bottom"
            radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            solid="@{@color/white}"
            android:layout_width="match_parent"
            android:layout_height="@{StudioLayoutConstants.INSTANCE.HIGH_PANEL_HEIGHT,default = match_parent}"
            android:layout_alignParentBottom="true"
            android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
            android:orientation="vertical">

            <!-- 美妆分组 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_group"
                android:layout_width="match_parent"
                android:layout_height="40dp" />

            <!--  子美妆数据 -->
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/rv_group"
                android:layout_marginBottom="50dp" />

            <com.commsource.widget.mask.MaskContainer
                android:id="@+id/maskContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="50dp" />

            <!-- 确认取消按钮-->
            <com.commsource.studio.component.ConfirmCancelComponent
                bindFragment="@{fragment}"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_alignParentBottom="true"
                android:background="@color/white" />

        </com.meitu.ratiorelativelayout.RatioRelativeLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="@{StudioLayoutConstants.INSTANCE.HIGH_PANEL_HEIGHT}">

            <!-- 侧边栏 -->
            <FrameLayout
                android:id="@+id/fl_right"
                android:layout_width="68dp"
                android:layout_height="211dp"
                android:layout_gravity="end|center_vertical" />

            <RelativeLayout
                android:id="@+id/ll_makeup_scroll_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="40dp"
                android:layout_marginEnd="55dp"
                android:alpha="0">

                <View
                    android:id="@+id/rightDot"
                    radius="@{4}"
                    solid="@{0xffFB5986}"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true" />

                <View
                    android:id="@+id/rightLine"
                    android:layout_width="23dp"
                    android:layout_height="2dp"
                    android:layout_centerVertical="true"
                    android:layout_toStartOf="@id/rightDot"
                    android:background="#FB5986" />

                <com.commsource.widget.round.RoundTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toStartOf="@id/rightLine"
                    android:gravity="center"
                    android:paddingStart="12dp"
                    android:paddingTop="9dp"
                    android:paddingEnd="12dp"
                    android:paddingBottom="9dp"
                    android:text="@string/makeup_slide_guide"
                    android:textColor="@color/white"
                    app:rv_backgroundColor="#FB5986"
                    app:rv_isRadiusHalfHeight="true" />

            </RelativeLayout>

        </FrameLayout>

        <!-- 悬浮的textview 显示不同function -->
        <RelativeLayout
            android:id="@+id/rl_floating"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:alpha="0">

            <com.commsource.widget.round.RoundFrameLayout
                android:id="@+id/fl_color"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_centerVertical="true"
                android:layout_marginEnd="2dp"
                android:layout_toStartOf="@+id/tv_floating"
                android:visibility="gone"
                app:rv_isRadiusHalfHeight="true"
                app:rv_isRippleEnable="false"
                app:rv_strokeColor="@color/white"
                app:rv_strokeWidth="1dp" />

            <com.commsource.comic.widget.StrokeTextView
                android:id="@+id/tv_floating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:textColor="@color/white"
                android:textSize="22dp" />

        </RelativeLayout>

    </RelativeLayout>
</layout>