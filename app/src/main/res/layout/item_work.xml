<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <com.commsource.widget.ThumbnailContainer
        android:id="@+id/tc"
        tc_corner="@{8}"
        tc_shadowEnable="@{true}"
        tc_strokeWidthEnable="@{true}"
        android:layout_width="match_parent"
        android:layout_height="148dp">

        <TextView
            android:id="@+id/work_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|bottom"
            android:layout_marginStart="8dp"
            android:layout_marginBottom="8dp"
            android:textColor="@color/white"
            android:textSize="12dp" />
        
        <ImageView
            android:id="@+id/iv_selected"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="end|top"
            android:layout_marginEnd="8dp"
            android:layout_marginTop="8dp"
            />

    </com.commsource.widget.ThumbnailContainer>
</layout>