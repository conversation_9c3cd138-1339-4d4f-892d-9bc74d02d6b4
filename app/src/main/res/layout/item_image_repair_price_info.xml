<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        tools:background="@color/color_121214">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDuration"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textSize="13dp"
            android:textColor="@color/white"
            tools:text="0–10 seconds"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="13dp"
            android:textColor="@color/white"
            tools:text="$ 9.9 "
            />

    </LinearLayout>

</layout>