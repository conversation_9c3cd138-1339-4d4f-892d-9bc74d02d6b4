<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_gravity="bottom">

        <com.commsource.widget.XSeekBar
            android:id="@+id/xsb"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_centerVertical="true"
            android:layout_gravity="bottom"
            android:layout_marginStart="36dp"
            android:layout_marginEnd="66dp"
            android:layout_marginBottom="14dp"
            android:layout_toStartOf="@+id/iv_contract"
            android:visibility="visible"
            app:isEnableStroke="true" />

        <com.commsource.widget.part.XSeekCameraTextView
            bindXSeek="@{xsb}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|bottom"
            android:layout_marginStart="36dp"
            android:layout_marginBottom="41dp" />

        <com.commsource.camera.widget.CameraContractImageView
            android:id="@+id/iv_contract"
            shape="@{1}"
            solid="@{@color/color_b3333333}"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:textSize="30dp"
            android:text="@string/if_studio_compare"
            android:textColor="@color/white"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="16.5dp"
            app:auto_mirror="true" />

    </FrameLayout>
</layout>