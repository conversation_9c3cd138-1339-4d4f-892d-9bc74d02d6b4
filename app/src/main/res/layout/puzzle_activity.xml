<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />

        <import type="com.meitu.library.util.device.DeviceUtils" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:splitMotionEvents="false">

        <com.commsource.puzzle.patchedworld.PatchedWorldView
            android:id="@+id/pwv_puzzle"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layoutDirection="ltr"
            app:layout_constraintBottom_toTopOf="@+id/bottomView"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintTop_toBottomOf="@+id/iv_back"
            app:layout_constraintTop_toTopOf="parent" />


        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="44dp"
            android:visibility="gone" />


        <com.commsource.widget.PressImageView
            android:id="@+id/iv_back"
            shape="@{1}"
            strokeColor="@{@color/Gray_Dashline}"
            strokeWidth="@{0.5f}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            android:padding="0.5dp"
            android:src="@drawable/arrow_back_icon_circle_black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.commsource.widget.AutoFitTextView
            android:id="@+id/iv_save"
            radius="@{15}"
            solid="@{@color/color_fb5986}"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_centerInParent="true"
            android:layout_centerVertical="true"
            android:layout_margin="15dp"
            android:drawableStart="@drawable/selfie_save_icon"
            android:drawablePadding="3dp"
            android:gravity="center"
            android:maxWidth="120dp"
            android:maxLines="1"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/confirm_save"
            android:textColor="@color/white"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottomView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.commsource.studio.text.TextTabView
                android:id="@+id/tabBar"
                radius="@{16}"
                solid="@{@color/color_f5f5f5}"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginTop="12dp"
                app:icon_left="@string/ic_collage_16"
                app:icon_right="@string/edit_collage_icon_collage1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:text_left="@string/puzzle"
                app:text_right="@string/t_puzzle_picstrip" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/puzzle_rv"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tabBar" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_proportion"
                android:layout_width="match_parent"
                android:layout_height="67dp"
                android:layout_marginTop="3dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/puzzle_rv">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_ratio"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/ratio_line"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/ratio_line"
                    android:layout_width="1dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="12dp"
                    android:background="@color/Gray_Dashline"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/sizeBackground"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/sizeBackground"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="12dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.commsource.widget.PressAutoFitTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/t_puzzle_size"
                        android:textColor="@color/Primary_A"
                        android:textSize="14dp" />

                    <com.commsource.widget.IconFrontView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_marginStart="4dp"
                        android:gravity="center"
                        android:text="@string/all_icon_indicator_forward_16"
                        android:textColor="@color/Primary_A"
                        android:textSize="14dp"
                        app:auto_mirror="true" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>


            <LinearLayout
                android:id="@+id/tipsPuzzleBubbleView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:layout_marginBottom="-16dp"
                android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/cl_proportion"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    android:id="@+id/switch_size_tv"
                    radius="@{20f}"
                    solid="@{@color/color_fa64b0}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingStart="12dp"
                    android:paddingTop="8dp"
                    android:paddingEnd="12dp"
                    android:paddingBottom="8dp"
                    android:text="@string/t_tips_switch_size"
                    android:textColor="@color/white"
                    android:textSize="13dp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginEnd="21dp"
                    android:src="@drawable/editor_bubble_indictor_down" />

            </LinearLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_size"
                android:layout_width="match_parent"
                android:layout_height="67dp"
                android:layout_marginTop="3dp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/puzzle_rv">

                <LinearLayout
                    android:id="@+id/proportionBackground"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="16dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.commsource.widget.IconFrontView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:gravity="center"
                        android:text="@string/edit_collage_ic_arrow_12"
                        android:textColor="@color/Primary_A"
                        android:textSize="14dp"
                        app:auto_mirror="true" />

                    <com.commsource.widget.PressAutoFitTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/t_puzzle_ratio"
                        android:textColor="@color/Primary_A"
                        android:textSize="14dp" />

                </LinearLayout>

                <View
                    android:id="@+id/size_line"
                    android:layout_width="1dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="12dp"
                    android:background="@color/Gray_Dashline"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/rv_size"
                    app:layout_constraintStart_toEndOf="@+id/proportionBackground"
                    app:layout_constraintTop_toTopOf="parent" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_size"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/size_line"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <View
                android:id="@+id/line2"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="3dp"
                android:background="@color/color_1a000000"
                app:layout_constraintTop_toBottomOf="@+id/cl_proportion" />

            <TextView
                android:id="@+id/spacing"
                android:layout_width="60dp"
                android:layout_height="50dp"
                android:layout_marginStart="15dp"
                android:gravity="center_vertical"
                android:text="@string/t_puzzle_space"
                android:textColor="@color/color_333333"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/puzzle_board_sb"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/line2" />


            <com.commsource.widget.XSeekBar
                android:id="@+id/puzzle_board_sb"
                android:layout_width="0dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="15dp"
                app:isEnableStroke="false"
                app:layout_constraintBottom_toBottomOf="@+id/spacing"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toEndOf="@+id/spacing"
                app:layout_constraintTop_toTopOf="@+id/spacing"
                app:xBackgroundColor="@color/color_f3f3f3"
                app:xProgress="50"
                app:xProgressColor="@color/black" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/tipsBubbleMask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
            android:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>