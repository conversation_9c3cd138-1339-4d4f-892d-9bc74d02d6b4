<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <View
            android:id="@+id/vPaddingStart"
            android:layout_width="16dp"
            android:layout_height="match_parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="71dp"
            android:layout_height="wrap_content"
            android:paddingEnd="16dp">

            <FrameLayout
                android:id="@+id/flRoot"
                android:layout_width="55dp"
                android:layout_height="52dp"
                android:background="@drawable/radius_16_bg_graye"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">


                <com.commsource.widget.IconFrontView
                    android:id="@+id/iconFront"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_gravity="center"
                    android:textColor="@color/Gray_A"
                    android:textSize="28dp"
                    android:visibility="gone" />

                <com.commsource.widget.CircleImageView
                    android:id="@+id/iconMiniApp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:scaleType="center"
                    app:round_radius="16dp"
                    android:visibility="gone" />

            </FrameLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clBottom"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:background="@drawable/common_home_search_type"
                app:layout_constraintBottom_toBottomOf="@id/flRoot"
                app:layout_constraintEnd_toEndOf="@id/flRoot">

                <ImageView
                    android:id="@+id/ivType"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:layout_marginEnd="1dp"
                    android:layout_marginBottom="1dp"
                    android:src="@drawable/search_icon_photo_tag"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tvFunName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_horizontal"
                android:textColor="@color/color_333333"
                android:textSize="11sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/flRoot" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>

