<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />

    </data>

    <RelativeLayout
        android:id="@+id/clRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="false">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_child"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_alignParentBottom="true"
            android:background="@color/white"
            android:paddingTop="10dp" />


        <LinearLayout
            android:id="@+id/tipsBubbleView"
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:layout_above="@id/rv_child"
            android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="invisible">

            <TextView
                android:id="@+id/tip_content"
                radius="@{20f}"
                solid="@{@color/color_fa64b0}"
                android:layout_width="wrap_content"
                android:layout_height="33dp"
                android:background="@color/Primary_A"
                android:gravity="center"
                android:paddingStart="12dp"
                android:paddingTop="8dp"
                android:paddingEnd="12dp"
                android:paddingBottom="8dp"
                android:text="@string/t_hair_manaul_guide_tips"
                android:textColor="@color/white"
                android:textSize="13dp" />

            <ImageView
                android:id="@+id/iv_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/editor_bubble_indictor_down" />
        </LinearLayout>

        <com.commsource.widget.mask.MaskContainer
            android:id="@+id/maskContainer"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_alignParentBottom="true" />

    </RelativeLayout>
</layout>