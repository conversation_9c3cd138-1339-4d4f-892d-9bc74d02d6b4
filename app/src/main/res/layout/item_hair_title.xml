<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:id="@+id/flContent"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/t_hairstyle"
                android:textColor="@color/black"
                android:textSize="13dp" />

            <ImageView
                android:id="@+id/iv_right_corner"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginStart="2dp"
                android:src="@drawable/common_corner_premium_white_shadow"
                android:visibility="visible" />

            <!-- 限时免费Icon-->
            <ImageView
                android:id="@+id/ivLimitFreeTime"
                android:layout_width="32dp"
                android:layout_height="17dp"
                android:layout_marginStart="2dp"
                android:src="@drawable/free_tag"
                android:visibility="gone" />

            <!-- 限次免费Icon-->
            <ImageView
                android:id="@+id/ivLimitFreeCount"
                android:layout_width="32dp"
                android:layout_height="17dp"
                android:layout_marginStart="2dp"
                android:src="@drawable/ic_limit_free_count"
                android:visibility="gone" />

        </LinearLayout>

    </FrameLayout>

</layout>

