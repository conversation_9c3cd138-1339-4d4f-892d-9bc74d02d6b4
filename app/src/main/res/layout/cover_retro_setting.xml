<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/settingContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <View
                android:id="@+id/clickBarrier"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutSettings"
                radius="@{10}"
                solid="@{@color/black80}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_marginStart="25dp"
                android:layout_marginEnd="25dp"
                android:clickable="true"
                android:padding="10dp">

                <FrameLayout
                    android:id="@+id/timerContainer"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/gridContainer"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.commsource.widget.IconFrontView
                        android:id="@+id/ifvTimer"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_gravity="top|center_horizontal"
                        android:gravity="center"
                        android:text="@string/selfie_setting_icon_time_off"
                        android:textColor="@color/white"
                        android:textSize="35dp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvTimer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top|center_horizontal"
                        android:layout_marginTop="38dp"
                        android:gravity="center"
                        android:maxLines="1"
                        android:text="@string/v79000_A_4"
                        android:textColor="@color/white"
                        android:textSize="11dp"
                        app:autoSizeMaxTextSize="11dp"
                        app:autoSizeMinTextSize="3dp"
                        app:autoSizeTextType="uniform" />

                </FrameLayout>

                <FrameLayout
                    android:id="@+id/gridContainer"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@id/timerContainer"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.commsource.widget.IconFrontView
                        android:id="@+id/ifvGrid"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_gravity="top|center_horizontal"
                        android:gravity="center"
                        android:text="@string/iphone_cam_icon_more_gird"
                        android:textColor="@color/white"
                        android:textSize="35dp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvGrid"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top|center_horizontal"
                        android:layout_marginTop="38dp"
                        android:gravity="center"
                        android:maxLines="1"
                        android:text="@string/v79000_A_6"
                        android:textColor="@color/white"
                        android:textSize="11dp"
                        app:autoSizeMaxTextSize="11dp"
                        app:autoSizeMinTextSize="3dp"
                        app:autoSizeTextType="uniform" />

                </FrameLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>

    </FrameLayout>

</layout>