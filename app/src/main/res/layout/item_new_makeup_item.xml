<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="60dp"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:id="@+id/rl_root"
            android:layout_width="60dp"
            android:layout_height="wrap_content">

            <com.commsource.widget.ThumbnailContainer
                android:id="@+id/tc"
                tc_corner="@{4}"
                android:layout_width="54dp"
                android:layout_height="54dp"
                android:layout_centerHorizontal="true">

                <com.commsource.widget.round.RoundIconfontTextView
                    android:id="@+id/fl_select_mask"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="@string/if_seek_bar_select"
                    android:textColor="@color/white"
                    android:textSize="28dp"
                    android:visibility="gone"
                    app:rv_backgroundColor="@color/color_primary50"
                    app:rv_isRippleEnable="false" />

                <!-- 下载进度 -->
                <com.commsource.widget.RotateLoadingView
                    android:id="@+id/cdpv_progress"
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_gravity="bottom|end"
                    android:background="@drawable/common_loading_white_rotate_icon"
                    android:visibility="gone" />

                <com.commsource.widget.IconFrontView
                    android:id="@+id/ifv_download"
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_gravity="end|bottom"
                    android:text="@string/if_look_download"
                    android:textColor="@color/white"
                    android:textSize="18dp"
                    android:visibility="gone"
                    app:show_stroke="true"
                    app:stroke_color="@color/color_4d000000" />

                <ImageView
                    android:id="@+id/iv_right_corner"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="end|top"
                    android:layout_marginTop="1dp"
                    android:layout_marginEnd="1dp"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/iv_left_corner"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start|top"
                    android:visibility="gone" />

            </com.commsource.widget.ThumbnailContainer>

            <com.commsource.widget.IconFrontView
                android:id="@+id/ifv_preset"
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:layout_alignTop="@+id/tv_name"
                android:layout_alignBottom="@+id/tv_name"
                android:layout_toStartOf="@+id/tv_name"
                android:gravity="center"
                android:text="@string/if_preset"
                android:textSize="6dp"
                android:visibility="gone" />

            <com.commsource.widget.AutoFitTextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="14dp"
                android:layout_below="@+id/tc"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="4dp"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="10dp" />

        </RelativeLayout>

    </FrameLayout>
</layout>

