<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_b3000000"
        android:clickable="true"
        android:focusable="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.airbnb.lottie.LottieAnimationView
                android:saveEnabled="false"
                android:id="@+id/ai_progress"
                android:layout_width="80dp"
                android:layout_height="80dp"
                app:lottie_imageAssetsFolder="lottie" />

            <TextView
                android:id="@+id/ai_prompt_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="提升肌肤光泽..."
                android:textColor="@color/white"
                android:textSize="15dp" />

            <TextView
                android:id="@+id/ai_beauty_loading_cancel_old"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:padding="15dp"
                android:text="@string/cancel"
                android:textColor="@color/color_999999"
                android:textSize="12dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_ai_subscribe_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/ai_beauty_title_top"
                android:text="@string/ai_beauty_subscribe_title"
                android:textColor="@color/white"
                android:layout_marginStart="37dp"
                android:layout_marginEnd="37dp"
                android:textSize="15dp" />

            <TextView
                android:id="@+id/tv_ai_subscribe_feature"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/ai_beauty_feature_top"
                android:drawableStart="@drawable/common_tick_icon_pink"
                android:drawablePadding="0dp"
                android:gravity="center"
                android:text="@string/ai_beauty_subscribe_feature"
                android:textColor="@color/white"
                android:layout_marginStart="37dp"
                android:layout_marginEnd="37dp"
                android:textSize="15dp" />

            <TextView
                android:id="@+id/tv_ai_subscribe_feature_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/ai_beauty_feature_top"
                android:drawableStart="@drawable/common_tick_icon_pink"
                android:drawablePadding="0dp"
                android:gravity="center"
                android:text="@string/ai_beauty_subscribe_feature_1"
                android:textColor="@color/white"
                android:layout_marginStart="37dp"
                android:layout_marginEnd="37dp"
                android:textSize="15dp" />

            <TextView
                android:id="@+id/tv_ai_subscribe_feature_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/ai_beauty_feature_top"
                android:drawableStart="@drawable/common_tick_icon_pink"
                android:drawablePadding="0dp"
                android:gravity="center"
                android:text="@string/ai_beauty_subscribe_feature_2"
                android:textColor="@color/white"
                android:layout_marginStart="37dp"
                android:layout_marginEnd="37dp"
                android:textSize="15dp" />

            <com.commsource.widget.PressTextView
                android:id="@+id/tv_ai_beauty_subscribe"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginStart="77dp"
                android:layout_marginTop="@dimen/ai_beauty_subscribe_top"
                android:layout_marginEnd="77dp"
                android:gravity="center"
                android:text="@string/ai_beauty_subscribe_join_now"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:textSize="15dp" />

        </LinearLayout>

        <com.commsource.beautymain.widget.AlphaImageView
            android:id="@+id/ai_beauty_loading_cancel"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_gravity="end"
            android:layout_margin="10dp"
            android:layout_marginTop="13dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:src="@drawable/common_close_icon_white" />

    </FrameLayout>

</layout>