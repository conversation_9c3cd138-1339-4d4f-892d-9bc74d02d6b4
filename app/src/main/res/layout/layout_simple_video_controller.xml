<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.commsource.widget.IconFrontView
            android:id="@+id/video_state_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="12dp"
            android:fontFamily="@font/icon_font"
            android:gravity="center"
            android:text="@string/album_icon_play"
            android:textColor="@color/Gray_A"
            android:textSize="24dp" />

        <com.commsource.widget.XSeekBar
            android:id="@+id/video_progress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            app:xBackgroundColor="@color/Gray_Dashline"
            app:xProgress="0"
            app:xProgressColor="@color/Gray_A" />

        <TextView
            android:id="@+id/video_duration"
            android:layout_width="90dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:text="00:00/00:00"
            android:textColor="@color/Gray_A"
            android:textSize="12dp" />

        <com.commsource.widget.IconFrontView
            android:id="@+id/video_volume_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="12dp"
            android:fontFamily="@font/icon_font"
            android:gravity="center"
            android:text="@string/album_icon_sound_off"
            android:textColor="@color/Gray_A"
            android:textSize="24dp" />

    </LinearLayout>
</layout>