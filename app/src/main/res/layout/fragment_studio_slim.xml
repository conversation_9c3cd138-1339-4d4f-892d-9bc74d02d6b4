<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />

        <import type="com.meitu.library.util.device.DeviceUtils" />

        <variable
            name="fragment"
            type="androidx.fragment.app.Fragment" />
    </data>

    <com.meitu.ratiorelativelayout.RatioRelativeLayout
        android:id="@+id/rlContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!--放大镜-->
        <com.commsource.studio.MagnifyComponent
            android:id="@+id/manify"
            bindFragment="@{fragment}"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.commsource.studio.component.BannerContainer
            android:id="@+id/flBottomBanner"
            android:layout_width="match_parent"
            android:layout_above="@+id/fl_bottom"
            android:layout_marginTop="14dp"
            android:alpha="0"
            android:layout_marginBottom="-26dp"
            android:clipToPadding="false"
            android:clipChildren="false"
            android:layout_height="12dp">

            <!-- 付费Banner-->
            <com.commsource.studio.component.ProBottomBannerComponent
                android:id="@+id/proBottomBanner"
                android:layout_width="match_parent"
                bindFragment="@{fragment}"
                android:layout_height="wrap_content"/>

        </com.commsource.studio.component.BannerContainer>

        <FrameLayout
            android:id="@+id/fl_bottom"
            android:layout_width="match_parent"
            android:layout_height="@{StudioLayoutConstants.INSTANCE.SLIM_PANEL_HEIGHT, default = match_parent}"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:clickable="true"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingTop="14dp">

            <!--手动面板-->
            <com.commsource.studio.component.AutoManualPanelComponent
                bindFragment="@{fragment}"
                isManual="@{true}"
                radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                solid="@{@color/white}"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="50dp"
                android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}">

                <com.meitu.ratiorelativelayout.RatioRelativeLayout
                    android:id="@+id/rl_manual"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.meitu.ratiorelativelayout.RatioRelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="32dp"
                        android:layout_marginStart="15dp"
                        android:layout_alignParentStart="true"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="15dp">

                        <com.commsource.widget.AutoFitTextView
                            android:id="@+id/tvBgProtect"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_alignParentStart="true"
                            android:text="@string/t_background_lock"
                            android:textColor="@color/Gray_A"
                            android:textSize="12dp" />

                        <ImageView
                            android:id="@+id/ivPremium"
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="2dp"
                            android:layout_toEndOf="@id/tvBgProtect"
                            android:src="@drawable/common_corner_premium_white_shadow" />

                        <com.commsource.camera.xcamera.widget.CameraSwitchButton
                            android:id="@+id/bgSwitch"
                            android:layout_width="40dp"
                            android:layout_height="24dp"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="16dp"
                            android:layout_toEndOf="@id/ivPremium" />

                        <LinearLayout
                            android:id="@+id/btnSetBackground"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:visibility="invisible">

                            <com.commsource.widget.PressAutoFitTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:text="@string/t_liquify_bg_lock_edit_guide"
                                android:textColor="@color/Primary_A"
                                android:textSize="12dp" />

                            <com.commsource.widget.IconFrontView
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:layout_marginStart="4dp"
                                android:gravity="center"
                                android:text="@string/all_icon_indicator_forward_16"
                                android:textColor="@color/Primary_A"
                                android:textSize="12dp"
                                app:auto_mirror="true" />

                        </LinearLayout>

                    </com.meitu.ratiorelativelayout.RatioRelativeLayout>

                    <!--手动模式画笔选择-->
                    <com.commsource.studio.component.PaintEraserComponent
                        android:id="@+id/paintEraser"
                        bindFragment="@{fragment}"
                        android:layout_width="match_parent"
                        android:layout_height="35dp"
                        android:layout_alignParentBottom="true"
                        android:layout_marginStart="30dp"
                        android:layout_marginEnd="30dp"
                        android:layout_marginBottom="15dp" />

                </com.meitu.ratiorelativelayout.RatioRelativeLayout>

            </com.commsource.studio.component.AutoManualPanelComponent>

            <!-- 自动面板-->
            <com.commsource.studio.component.AutoManualPanelComponent
                bindFragment="@{fragment}"
                isManual="@{false}"
                radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                solid="@{@color/white}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="50dp"
                android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}">

                <com.meitu.ratiorelativelayout.RatioRelativeLayout
                    android:id="@+id/rl_auto"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!-- 新Seekbar -->
                    <com.commsource.widget.XSeekBar
                        android:id="@+id/xsb_auto"
                        android:layout_width="match_parent"
                        android:layout_height="35dp"
                        android:layout_centerInParent="true"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="35dp"
                        android:layout_marginEnd="35dp"
                        android:visibility="visible"
                        app:isEnableStroke="false"
                        app:xBackgroundColor="@color/color_e5e5e5"
                        app:xProgressColor="@color/black" />

                </com.meitu.ratiorelativelayout.RatioRelativeLayout>

            </com.commsource.studio.component.AutoManualPanelComponent>

            <!-- 确认取消按钮-->
            <com.commsource.studio.component.ConfirmCancelComponent
                android:id="@+id/confirm_cancel"
                bindFragment="@{fragment}"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_gravity="bottom"
                android:background="@color/white"
                android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
                android:outlineProvider="none" />

        </FrameLayout>

        <RelativeLayout
            android:id="@+id/flCanvas"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_width="match_parent"
            android:layout_above="@id/flBottomBanner"
            android:layout_height="match_parent">
            <!--撤销重做-->
            <com.commsource.studio.component.UndoRedoComponent
                android:id="@+id/undoRedo"
                bindFragment="@{fragment}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_alignParentStart="true"
                android:layout_marginBottom="-14dp"
                android:clickable="true"
                android:focusable="true"
                android:padding="14dp"
                android:visibility="gone" />

            <!-- 自动手动-->
            <com.commsource.studio.component.AutoManualComponent
                android:id="@+id/autoManual"
                bindFragment="@{fragment}"
                android:layout_width="74dp"
                android:layout_height="30dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true" />

            <!-- 预览 -->
            <com.commsource.studio.component.PreviewComponent
                android:id="@+id/preview"
                bindFragment="@{fragment}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="-14dp"
                android:layout_toStartOf="@id/contrast"
                android:paddingStart="14dp"
                android:paddingTop="14dp"
                android:paddingBottom="14dp" />

            <!--对比-->
            <com.commsource.studio.component.ContrastComponent
                android:id="@+id/contrast"
                bindFragment="@{fragment}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_alignParentEnd="true"
                android:layout_marginBottom="-14dp"
                android:clickable="true"
                android:focusable="true"
                android:padding="14dp" />
        </RelativeLayout>



        <!--  气泡  -->
        <com.commsource.widget.part.XSeekBubbleView
            android:id="@+id/xbv"
            bindXSeek="@{xsbAuto}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/fl_bottom"
            android:layout_alignParentStart="true"
            android:layout_marginStart="35dp"
            android:layout_marginBottom="-14dp" />

        <FrameLayout
            android:id="@+id/flBackgroundProtect"
            radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            solid="@{@color/white}"
            android:layout_width="match_parent"
            android:layout_height="@{StudioLayoutConstants.INSTANCE.SLIM_PANEL_HEIGHT, default = match_parent}"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingTop="14dp"
            android:visibility="invisible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- 新Seekbar -->
                <com.commsource.studio.component.PaintSelectComponent
                    android:id="@+id/xsb_paint"
                    bindFragment="@{fragment}"
                    android:layout_width="match_parent"
                    android:layout_height="35dp"
                    android:layout_marginStart="75dp"
                    android:layout_marginEnd="75dp"
                    app:isEnableStroke="false"
                    app:xBackgroundColor="@color/color_e5e5e5"
                    app:xProgressColor="@color/black" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPaintMode"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:layout_gravity="center_horizontal|bottom"
                android:layout_marginBottom="50dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:scrollbars="none" />

            <com.commsource.widget.BoldTextView
                android:id="@+id/tvBackground"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_gravity="bottom|center_horizontal"
                android:gravity="center"
                android:text="@string/t_background_lock"
                android:textColor="@color/black"
                android:textSize="13dp" />

            <com.commsource.widget.IconFrontView
                android:id="@+id/ivClose"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_gravity="end|bottom"
                android:layout_marginEnd="11dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:text="@string/all_icon_indicator_down_16"
                android:textColor="@color/Gray_A"
                android:textSize="24dp" />

        </FrameLayout>

    </com.meitu.ratiorelativelayout.RatioRelativeLayout>

</layout>
