<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <com.meitu.ratiorelativelayout.RatioRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/mRlRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_heightSpec="667"
        app:layout_widthSpec="375"
        app:adaptType="fitY"
        android:background="@color/white">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="17sp"
            android:text="@string/ar_loc_cancel"
            android:gravity="center"
            android:textColor="@color/color_333333"
            android:layout_alignParentEnd="true"
            app:layout_ratioMarginTop="15"
            app:layout_ratioMarginBottom="15"
            app:layout_ratioMarginRight="15"
            app:layout_ratioMarginLeft="15"
            app:layout_ratioHeight="35"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_alignParentStart="true"
            android:layout_toStartOf="@id/tv_cancel"
            android:layout_alignTop="@id/tv_cancel"
            app:layout_ratioMarginLeft="15"
            android:gravity="center_vertical"
            android:background="@drawable/location_search_input_bg"
            android:layout_alignBottom="@id/tv_cancel">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="14dp"
                android:layout_marginStart="14dp"
                android:src="@drawable/setting_search_icon"/>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                android:layout_marginEnd="14dp"
                android:gravity="start"
                android:focusable="true"
                android:singleLine="true"
                android:maxLines="1"
                android:imeOptions="actionSearch"
                android:hint="Enter Location"
                android:focusableInTouchMode="true"
                android:textColor="@color/black"
                android:textCursorDrawable="@drawable/edit_text_cursor_shape"
                android:textSize="13sp"
                android:saveEnabled="false"/>

        </LinearLayout>

        <View
            android:id="@+id/divide_line"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_ratioHeight="0.5"
            android:background="@color/color_cccccc"
            android:layout_below="@id/tv_cancel"/>

        <TextView
            android:id="@+id/tv_empty_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/divide_line"
            android:textColor="@color/color_666666"
            android:textSize="13sp"
            app:layout_ratioMarginTop="20"
            app:layout_ratioMarginLeft="15"
            android:visibility="gone"
            android:text="@string/ar_loc_could_not_obtain_location"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_nearby_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/divide_line">

        </androidx.recyclerview.widget.RecyclerView>



    </com.meitu.ratiorelativelayout.RatioRelativeLayout>
</layout>