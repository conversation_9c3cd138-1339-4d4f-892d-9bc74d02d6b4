<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cc_root"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintTop_toTopOf="@+id/cc_top"
                app:layout_constraintBottom_toBottomOf="@+id/cc_top"
                solid="@{@color/color_262626}"
                radiusLeftTop="@{24}"
                radiusRightTop="@{24}"
                radiusLeftBottom="@{12}"
                radiusRightBottom="@{12}"/>

            <com.commsource.widget.CornerImageView
                android:id="@+id/iv_bg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:scaleType="fitXY"
                android:src="@drawable/img_premium_gradientbg1"
                android:visibility="visible"
                cornerTopLeft="@{24}"
                cornerTopRight="@{24}"
                cornerBottomLeft="@{12}"
                cornerBottomRight="@{12}"
                app:layout_constraintTop_toTopOf="@+id/cc_top"
                app:layout_constraintBottom_toBottomOf="@+id/cc_top"/>

            <com.commsource.widget.corner.CornerContainer
                android:id="@+id/cc_top"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/cc_bottom"
                app:layout_constraintVertical_chainStyle="packed"
                cc_cornerTL="@{24}"
                cc_cornerTR="@{24}"
                cc_cornerBL="@{12}"
                cc_cornerBR="@{12}">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:layout_marginBottom="24dp"
                    android:orientation="vertical">

                    <com.commsource.widget.BoldTextView
                        android:id="@+id/tv_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="24dp"
                        android:layout_marginEnd="24dp"
                        android:text="Don't miss the new user offer!"
                        android:textAlignment="viewStart"
                        android:textColor="@color/white"
                        android:textSize="20dp"
                        app:boldTextWidth="1" />


                    <TextView
                        android:id="@+id/tv_tip"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="16dp"
                        android:layout_marginStart="24dp"
                        android:layout_marginEnd="24dp"
                        android:textSize="14dp"
                        android:textColor="@color/white"
                        android:text="Missed also need to subscribe, will be restored to $33.3/year"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv"
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:paddingStart="24dp"
                        android:clipToPadding="false"/>

                </LinearLayout>

                <com.commsource.widget.IconFrontView
                    android:id="@+id/ifvClose"
                    radius="@{12}"
                    solid="@{0x4DE5E5E5}"
                    strokeColor="@{@color/Gray_Dashline}"
                    strokeWidth="@{0.5f}"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="12dp"
                    android:layout_gravity="end"
                    android:gravity="center"
                    android:text="@string/common_icon_navbar_close"
                    android:textColor="@color/black"
                    android:textSize="10dp" />

            </com.commsource.widget.corner.CornerContainer>


            <com.commsource.widget.corner.CornerContainer
                android:id="@+id/cc_bottom"
                android:layout_width="match_parent"
                android:layout_height="121dp"
                android:background="@color/color_262626"
                app:layout_constraintTop_toBottomOf="@+id/cc_top"
                app:layout_constraintBottom_toBottomOf="parent"
                cc_cornerTL="@{12}"
                cc_cornerTR="@{12}"
                cc_cornerBL="@{24}"
                cc_cornerBR="@{24}">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/img_premium_gradientbg2"
                    android:scaleType="fitXY"/>

                <com.commsource.widget.BoldTextView
                    android:id="@+id/tv_price"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginTop="20dp"
                    android:textColor="@color/white"
                    android:textSize="16dp"/>

                <com.commsource.widget.PressTextView
                    android:id="@+id/tv_subscribe"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginStart="24dp"
                    android:layout_marginEnd="24dp"
                    android:layout_marginTop="51dp"
                    android:text="Get 12% OFF NOW"
                    android:textSize="16dp"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textStyle="bold"/>

            </com.commsource.widget.corner.CornerContainer>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>



</layout>