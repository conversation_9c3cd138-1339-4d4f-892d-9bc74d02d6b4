<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="fragment"
            type="androidx.fragment.app.Fragment" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.commsource.widget.round.RoundFrameLayout
            android:id="@+id/fl_effect_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:layout_marginTop="20dp"
            android:clickable="true"
            android:elevation="8dp"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius_TL="16dp"
            app:rv_cornerRadius_TR="16dp"
            app:rv_isRippleEnable="false">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/lookContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.commsource.widget.IconFrontView
                    android:id="@+id/ic_none"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="20dp"
                    android:textColor="@color/color_333333"
                    android:layout_marginStart="18dp"
                    android:text="@string/edit_icon_default"
                    app:layout_constraintBottom_toBottomOf="@id/rv_look_cat"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/rv_look_cat" />

                <View
                    android:id="@+id/v_div"
                    android:layout_width="0.5dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="16dp"
                    android:background="@color/color_89d8d8d8"
                    app:layout_constraintStart_toEndOf="@id/ic_none"
                    app:layout_constraintBottom_toBottomOf="@id/rv_look_cat"
                    app:layout_constraintTop_toTopOf="@id/rv_look_cat" />

                <View
                    android:id="@+id/v_none"
                    android:layout_width="0dp"
                    android:layout_height="44dp"
                    app:layout_constraintTop_toTopOf="@id/rv_look_cat"
                    app:layout_constraintStart_toStartOf="@id/ic_none"
                    app:layout_constraintEnd_toStartOf="@id/v_div" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_look_cat"
                    android:layout_width="0dp"
                    android:layout_height="44dp"
                    android:overScrollMode="never"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/v_div"/>

                <View
                    android:id="@+id/dashLine"
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/Gray_Dashline"
                    app:layout_constraintBottom_toBottomOf="@id/rv_look_cat" />

                <com.commsource.widget.LineSelectView
                    android:id="@+id/lineSelect"
                    bindRecyclerView="@{rvLookCat}"
                    android:layout_width="0dp"
                    android:layout_height="2dp"
                    android:layout_gravity="bottom"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/v_div"
                    app:layout_constraintBottom_toBottomOf="@+id/rv_look_cat" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_look"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never"
                    app:layout_constraintTop_toBottomOf="@id/rv_look_cat" />

                <FrameLayout
                    android:id="@+id/fl_mask"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clickable="true"
                    android:focusable="true"
                    android:layout_marginTop="44dp"
                    android:visibility="gone">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/cl_mask_content"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <TextView
                            android:id="@+id/tv_tips"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/look_ar_use_tips"
                            android:textColor="@color/color_333333"
                            android:textSize="15dp"
                            app:layout_constraintBottom_toTopOf="@id/tv_remove_ar"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="packed" />

                        <TextView
                            android:id="@+id/tv_remove_ar"
                            style="@style/selectable_item"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="5dp"
                            android:gravity="center"
                            android:paddingStart="10dp"
                            android:paddingTop="3dp"
                            android:paddingEnd="10dp"
                            android:paddingBottom="3dp"
                            android:text="@string/tap_to_cancel_ar"
                            android:textColor="@color/Primary_A"
                            android:textSize="15dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="@id/tv_tips"
                            app:layout_constraintEnd_toEndOf="@id/tv_tips"
                            app:layout_constraintTop_toBottomOf="@id/tv_tips" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </FrameLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.commsource.widget.mask.MaskContainer
                android:id="@+id/maskContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="90dp" />

        </com.commsource.widget.round.RoundFrameLayout>

    </FrameLayout>

</layout>