<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/card"
        radius="@{16}"
        solid="@{@color/white}"
        strokeColor="@{@color/color_0D000000}"
        strokeWidth="@{1f}"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/thumbnailContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <com.commsource.widget.ThumbnailContainer
                    android:id="@+id/group_image"
                    tc_corner="@{16}"
                    tc_strokeColor="@{@color/black5}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <ImageView
                    android:id="@+id/premiumIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="top|end"
                    android:layout_margin="2dp"
                    android:src="@drawable/common_corner_premium_white_shadow"
                    android:visibility="gone" />

            </FrameLayout>


            <LinearLayout
                android:id="@+id/llTools"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.commsource.widget.IconFrontView
                    android:id="@+id/iFFavorite"
                    radius="@{25}"
                    solid="@{@color/white}"
                    strokeColor="@{@color/color_0D000000}"
                    strokeWidth="@{1f}"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginStart="12dp"
                    android:background="@drawable/radius_25_white"
                    android:fontFamily="@font/video_icon_font"
                    android:gravity="center"
                    android:text="@string/vf_video_icon_favorite_off"
                    android:textColor="@color/Gray_A"
                    android:textSize="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:id="@+id/tvApply"
                    radius="@{25}"
                    solid="@{@color/white}"
                    strokeColor="@{@color/color_0D000000}"
                    strokeWidth="@{1f}"
                    android:layout_width="0dp"
                    android:layout_height="28dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="12dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/t_apply_template"
                    android:textColor="@color/Gray_A"
                    android:textSize="11sp"
                    android:textStyle="bold" />


            </LinearLayout>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.cardview.widget.CardView>

</layout>