<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.commsource.studio.function.composition.QuadrangleAdjustView
            android:id="@+id/adjustView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="200dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:orientation="vertical">

            <com.commsource.widget.PxVernierView
                android:id="@+id/vernierX"
                android:layout_width="match_parent"
                android:layout_height="40dp" />

            <com.commsource.widget.PxVernierView
                android:id="@+id/vernierY"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="10dp" />

        </LinearLayout>

    </FrameLayout>
</layout>