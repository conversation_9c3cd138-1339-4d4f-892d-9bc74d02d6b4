<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_marginStart="8dp"
        android:layout_width="56dp"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/filter_thumail"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:gravity="start|center_vertical"
            android:padding="0.75dp"
            android:src="@drawable/max_default_icon"
            android:visibility="visible"
            app:shapeAppearance="@style/RoundShapeImageStyle_6"
            app:strokeColor="@color/white"
            app:strokeWidth="1.5dp" />

        <com.commsource.widget.AutoFitTextView
            android:id="@+id/filter_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textSize="11dp" />

    </LinearLayout>
</layout>