<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.commsource.duffle.sticker.search.sticker.StickerSearchListViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical">

        <com.commsource.widget.title.XTitleBar
            android:id="@+id/titleBar"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            app:xtb_divide_enable="false" />

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            app:currentView="@{viewModel.getRefreshLayout}"
            app:srlEnableLoadMore="true"
            app:srlEnableRefresh="false">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="16dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:descendantFocusability="beforeDescendants"
                android:overScrollMode="never"
                app:currentView="@{viewModel.getRecyclerView}" />

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    </LinearLayout>

</layout>