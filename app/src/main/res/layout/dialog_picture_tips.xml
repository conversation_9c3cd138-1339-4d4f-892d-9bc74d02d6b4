<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:elevation="30dp"
            app:cardBackgroundColor="@color/transparent"
            app:cardCornerRadius="20dp"
            app:cardElevation="0dp">

            <ImageView
                android:id="@+id/iv_pro"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="-80dp"
                android:layout_gravity="end" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.commsource.widget.video.VideoContainer
                    android:id="@+id/video_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <com.commsource.homev2.PagPlayerContainer
                    android:id="@+id/pagPlayer"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone" />

                <ImageView
                    android:id="@+id/iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="centerCrop"
                    android:visibility="gone" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <com.commsource.widget.BoldTextView
                        android:id="@+id/tv_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="start"
                        android:lineHeight="28dp"
                        android:textAlignment="gravity"
                        android:textColor="@color/Gray_A"
                        android:textSize="16dp"
                        android:visibility="gone"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/tv_subtitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:lineHeight="22dp"
                        android:textColor="@color/Gray_D"
                        android:textSize="16dp"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/tv_title"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/tv_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:lineHeight="22dp"
                        android:textColor="@color/Gray_D"
                        android:textSize="16dp"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:id="@+id/llBottom"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="vertical"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/tv_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent">

                        <View
                            android:id="@+id/vTop"
                            android:layout_height="0.5dp"
                            android:layout_width="match_parent"
                            android:background="@color/color_1a000000"/>

                    <LinearLayout
                        android:id="@+id/ll_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"/>

                    </androidx.appcompat.widget.LinearLayoutCompat>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

            <com.commsource.widget.PressImageView
                android:id="@+id/iv_close"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="end|top"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/bg_solid_99e5e5e5"
                android:scaleType="centerInside"
                android:src="@drawable/common_close_icon_black2"
                android:visibility="gone" />

        </androidx.cardview.widget.CardView>

    </FrameLayout>
</layout>