<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />

        <variable
            name="fragment"
            type="androidx.fragment.app.Fragment" />
    </data>


    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false">

        <FrameLayout
            android:id="@+id/transitionContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/color_f4f4f4"
            android:visibility="gone">

            <ImageView
                android:id="@+id/temp_translate"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="matrix" />
        </FrameLayout>

        <!--放大镜-->
        <com.commsource.studio.MagnifyComponent
            android:id="@+id/manify"
            bindFragment="@{fragment}"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible" />

        <RelativeLayout
            android:id="@+id/flCanvas"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_width="match_parent"
            android:layout_above="@id/fl_bottom"
            android:layout_height="match_parent">

            <com.commsource.studio.component.BannerContainer
                android:id="@+id/flBottomBanner"
                android:layout_width="match_parent"
                android:layout_alignParentBottom="true"
                android:alpha="0"
                android:layout_marginBottom="-12dp"
                android:clipToPadding="false"
                android:clipChildren="false"
                android:layout_height="12dp">

                <!-- 付费Banner-->
                <com.commsource.studio.component.ProBottomBannerComponent
                    android:id="@+id/proBottomBanner"
                    android:layout_width="match_parent"
                    bindFragment="@{fragment}"
                    android:layout_height="wrap_content"/>

            </com.commsource.studio.component.BannerContainer>

            <com.commsource.studio.component.ContrastComponent
                android:id="@+id/contrast"
                bindFragment="@{fragment}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@id/flBottomBanner"
                android:layout_alignParentEnd="true"
                android:padding="14dp" />

            <!-- 预览 -->
            <com.commsource.studio.component.PreviewComponent
                android:id="@+id/preview"
                bindFragment="@{fragment}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@id/flBottomBanner"
                android:layout_toStartOf="@id/contrast"
                android:paddingStart="14dp"
                android:paddingTop="14dp"
                android:paddingBottom="14dp" />
        </RelativeLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/paintLayout"
            radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            solid="@{@color/Gray_E}"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:layout_alignTop="@id/fl_bottom"
            android:orientation="horizontal">

            <com.commsource.studio.component.PaintEraserComponent
                android:id="@+id/paintEraser"
                bindFragment="@{fragment}"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="6dp"
                app:component_bg="@color/White"
                app:icon_text_size="32dp"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/applyIcon"
                app:layout_constraintTop_toTopOf="parent"
                app:paint_Progress="50"
                app:progress_bg="@color/Gray_Background" />

            <TextView
                android:id="@+id/applyIcon"
                radius="@{20f}"
                solid="@{@color/white}"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="13dp"
                android:enabled="false"
                android:gravity="center"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:text="@string/moving_average_apply"
                android:textColor="@color/black"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@id/paintEraser"
                app:layout_constraintStart_toEndOf="@id/paintEraser"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/paintEraser" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <View
            android:id="@+id/seekBarBg"
            radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            solid="@{@color/Gray_E}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/fl_bottom" />

        <com.commsource.studio.component.SeekComponent
            android:id="@+id/sc"
            radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/fl_bottom"
            android:layout_toStartOf="@id/paintIcon"
            android:clipChildren="false"
            android:clipToPadding="false" />

        <com.commsource.widget.IconFrontView
            android:id="@+id/paintIcon"
            radius="@{18f}"
            solid="@{@color/white}"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignTop="@id/fl_bottom"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="26dp"
            android:layout_marginEnd="15dp"
            android:gravity="center"
            android:text="@string/edit_icon_removebg_selection_add"
            android:textColor="@color/black"
            android:textSize="20dp" />

        <FrameLayout
            android:id="@+id/fl_animation"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/fl_bottom"
            android:background="@color/Gray_Stroke"
            android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical">

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/lav_prism"
                    android:layout_width="160dp"
                    android:layout_height="160dp"
                    android:layout_gravity="center_horizontal" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:text="@string/t_style_prism_tips"
                    android:textColor="@color/white"
                    android:textSize="14dp" />

            </LinearLayout>

        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_bottom"
            radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
            solid="@{@color/white}"
            android:layout_width="match_parent"
            android:layout_height="@{StudioLayoutConstants.INSTANCE.STUDIO_STYLE_HEIGHT,default = match_parent}"
            android:layout_alignParentBottom="true"
            android:clickable="true"
            android:clipToPadding="false"
            android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/fl_head"
                android:layout_width="match_parent"
                android:layout_height="44dp">

                <com.commsource.widget.IconFrontView
                    android:id="@+id/ic_none"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/edit_icon_default"
                    android:textColor="@color/color_333333"
                    android:textSize="20dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <View
                    android:id="@+id/divider"
                    android:layout_width="0.5dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="16dp"
                    app:layout_constraintStart_toEndOf="@id/ic_none"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:background="@color/color_89d8d8d8" />

                <View
                    android:id="@+id/v_none"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    app:layout_constraintStart_toStartOf="@id/ic_none"
                    app:layout_constraintEnd_toEndOf="@id/divider"
                    app:layout_constraintTop_toTopOf="parent"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_group"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    app:layout_constraintStart_toEndOf="@id/divider"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_gravity="bottom"
                    android:background="@color/Gray_Dashline"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <com.commsource.widget.LineSelectView
                    android:id="@+id/lineSelect"
                    bindRecyclerView="@{rvGroup}"
                    android:layout_width="0dp"
                    android:layout_height="2dp"
                    app:layout_constraintStart_toEndOf="@id/divider"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_child"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:layout_marginTop="44dp"
                android:layout_marginBottom="50dp"
                android:orientation="horizontal" />

            <com.commsource.widget.mask.MaskContainer
                android:id="@+id/maskContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="50dp" />

            <com.commsource.studio.component.ConfirmCancelComponent
                android:id="@+id/confirm_cancel"
                bindFragment="@{fragment}"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_gravity="bottom"
                android:background="@color/white" />

        </FrameLayout>
    </RelativeLayout>
</layout>