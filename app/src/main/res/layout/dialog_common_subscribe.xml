<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View"/>
        <variable
            name="data"
            type="com.commsource.widget.dialog.common.SubscribeADialog.Builder" />

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:id="@+id/rl_common_dialog_subscribe_root"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:id="@+id/rl_common_dialog_subscribe_content"
            android:layout_centerInParent="true"
            solid="@{@color/white}"
            radius="@{10}"
            android:layout_marginStart="40dp"
            android:paddingTop="8dp"
            android:layout_marginEnd="40dp"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_common_dialog_subscribe_content"
                android:layout_width="0dp"
                android:textColor="@color/black"
                android:textSize="16dp"
                android:gravity="center"
                android:text="@{data.content}"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginStart="24dp"
                android:layout_marginTop="34dp"
                android:layout_marginEnd="24dp"
                android:layout_height="wrap_content" />

            <com.commsource.widget.PressTextView
                android:layout_width="wrap_content"
                android:minWidth="180dp"
                android:textColor="@color/white"
                app:layout_constraintTop_toBottomOf="@id/tv_common_dialog_subscribe_content"
                android:layout_marginTop="34dp"
                android:padding="5dp"
                android:gravity="center"
                android:textSize="13dp"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:layout_marginStart="27dp"
                android:layout_marginEnd="27dp"
                android:id="@+id/tv_common_dialog_subscribe_positive"
                app:layout_constraintStart_toStartOf="parent"
                solid="@{@color/color_fb5986}"
                radius="@{20}"
                app:layout_constraintEnd_toEndOf="parent"
                android:text="@{data.positiveText}"
                android:layout_height="40dp" />

            <com.commsource.widget.PressTextView
                android:layout_width="wrap_content"
                android:minWidth="180dp"
                android:textColor="@color/color_fb5986"
                android:textSize="13dp"
                android:text="@{data.negativeText}"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginTop="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginBottom="24dp"
                android:paddingStart="24dp"
                android:paddingEnd="24dp"
                android:layout_marginStart="27dp"
                android:layout_marginEnd="27dp"
                app:layout_constraintEnd_toEndOf="parent"
                solid="@{@color/white}"
                android:gravity="center"
                radius="@{20}"
                strokeWidth="@{0.5f}"
                strokeColor="@{@color/color_fb5986}"
                android:id="@+id/tv_common_dialog_subscribe_negative"
                android:padding="5dp"
                app:layout_constraintTop_toBottomOf="@id/tv_common_dialog_subscribe_positive"
                android:layout_height="40dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </RelativeLayout>
</layout>