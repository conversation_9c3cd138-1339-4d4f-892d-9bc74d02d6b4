<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.commsource.studio.StudioLayoutConstants" />

        <variable
            name="fragment"
            type="androidx.fragment.app.Fragment" />
    </data>

    <com.commsource.widget.mask.MaskContainer
        android:id="@+id/outMaskContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/rlContent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false">

            <!--放大镜-->
            <com.commsource.studio.MagnifyComponent
                android:id="@+id/manify"
                bindFragment="@{fragment}"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <!--        顶部滑竿-->
            <com.commsource.studio.component.SeekComponent
                android:id="@+id/sc"
                radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                solid="@{@color/Gray_E}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/fl_bottom"
                android:clipChildren="false"
                android:clipToPadding="false" />

            <!--        预览，对比和撤销重做-->
            <RelativeLayout
                android:id="@+id/flTools"
                android:layout_width="match_parent"
                android:layout_height="180dp"
                android:layout_above="@+id/fl_bottom">

                <com.commsource.studio.component.BannerContainer
                    android:id="@+id/flBottomBanner"
                    android:layout_width="match_parent"
                    android:layout_height="12dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginTop="14dp"
                    android:layout_marginBottom="-12dp"
                    android:alpha="0"
                    android:clipChildren="false"
                    android:clipToPadding="false">

                    <!-- 限免Banner-->
                    <include
                        android:id="@+id/limit_free"
                        layout="@layout/layout_limit_free"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <!-- 付费Banner-->
                    <com.commsource.studio.component.ProBottomBannerComponent
                        android:id="@+id/proBottomBanner"
                        bindFragment="@{fragment}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </com.commsource.studio.component.BannerContainer>

                <!-- 预览 -->
                <com.commsource.studio.component.PreviewComponent
                    android:id="@+id/preview"
                    bindFragment="@{fragment}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@id/flBottomBanner"
                    android:layout_toStartOf="@id/styleManual"
                    android:paddingStart="14dp"
                    android:paddingTop="14dp"
                    android:paddingEnd="14dp" />

                <!--发型  橡皮擦-->
                <com.commsource.widget.IconFrontView
                    android:id="@+id/styleManual"
                    shape="@{1}"
                    solid="@{@color/white}"
                    strokeColor="@{@color/Gray_Dashline}"
                    strokeWidth="@{0.5f}"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_above="@id/flBottomBanner"
                    android:layout_marginEnd="14dp"
                    android:layout_toStartOf="@id/contrast"
                    android:gravity="center"
                    android:text="@string/edit_icon_portrait_ai_muscle_delete30"
                    android:textColor="@color/Gray_A"
                    android:textSize="30dp"
                    android:visibility="gone"
                    app:auto_mirror="true" />

                <!--对比-->
                <com.commsource.studio.component.ContrastComponent
                    android:id="@+id/contrast"
                    bindFragment="@{fragment}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@id/flBottomBanner"
                    android:layout_alignParentEnd="true"
                    android:paddingEnd="14dp" />

                <!--撤销重做-->
                <com.commsource.studio.component.UndoRedoComponent
                    android:id="@+id/undoRedo"
                    bindFragment="@{fragment}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@id/flBottomBanner"
                    android:layout_alignParentStart="true"
                    android:paddingStart="14dp"
                    android:visibility="invisible" />

            </RelativeLayout>


            <!--        tab和底部确认栏-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/fl_bottom"
                radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                solid="@{@color/white}"
                android:layout_width="match_parent"
                android:layout_height="@{StudioLayoutConstants.INSTANCE.RELIGHT_PANEL_HEIGHT,default = match_parent}"
                android:layout_alignParentBottom="true"
                android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
                android:orientation="vertical">


                <!-- 美发分组 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_group"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginBottom="90dp"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    app:layout_constraintBottom_toTopOf="@id/ccc" />


                <FrameLayout
                    android:id="@+id/divider"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    app:layout_constraintBottom_toBottomOf="@id/rv_group">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:layout_gravity="bottom"
                        android:background="@color/Gray_Dashline" />

                    <com.commsource.widget.LineSelectView
                        android:id="@+id/lineSelect"
                        bindRecyclerView="@{rvGroup}"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="16dp" />
                </FrameLayout>

                <!-- 确认取消按钮-->
                <com.commsource.studio.component.ConfirmCancelComponent
                    android:id="@+id/ccc"
                    bindFragment="@{fragment}"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/white"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <!--  补发，发际线等需要显示XSeekBubbleView，这里不得不match_parent-->
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="50dp"
                android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}" />

            <!-- 悬浮的textview 显示不同tips -->
            <RelativeLayout
                android:id="@+id/rl_floating"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="50dp"
                android:alpha="0">

                <com.commsource.widget.round.RoundFrameLayout
                    android:id="@+id/fl_color"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="2dp"
                    android:layout_toStartOf="@+id/tv_floating"
                    android:visibility="gone"
                    app:rv_isRadiusHalfHeight="true"
                    app:rv_isRippleEnable="false"
                    app:rv_strokeColor="@color/white"
                    app:rv_strokeWidth="1dp" />

                <com.commsource.comic.widget.StrokeTextView
                    android:id="@+id/tv_floating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:textColor="@color/white"
                    android:textSize="22dp" />

            </RelativeLayout>

            <!--            <com.commsource.widget.mask.MaskContainer-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="match_parent" />-->

            <FrameLayout
                android:id="@+id/unlockMask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="179dp"
                android:background="@color/black60"
                android:clickable="true"
                android:visibility="invisible">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="65dp"
                    android:fitsSystemWindows="true">


                    <androidx.cardview.widget.CardView
                        android:id="@+id/cvVideo"
                        android:layout_width="30dp"
                        android:layout_height="40dp"
                        android:layout_marginBottom="40dp"
                        app:cardBackgroundColor="@color/transparent"
                        app:cardElevation="0dp"
                        app:layout_constraintBottom_toTopOf="@id/tvUnlock"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent">

                        <com.commsource.widget.video.VideoContainer
                            android:id="@+id/videoContainer"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />

                    </androidx.cardview.widget.CardView>

                    <com.commsource.widget.corner.CornerContainer
                        android:id="@+id/tvUnlock"
                        cc_corner="@{25f}"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_marginStart="40dp"
                        android:layout_marginEnd="40dp"
                        android:layout_marginBottom="65dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent">

                        <com.commsource.widget.BoldTextView
                            pro_gradient_background="@{true}"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:text="@string/v77170_G_1"
                            android:textColor="@color/black"
                            android:textSize="16dp" />

                    </com.commsource.widget.corner.CornerContainer>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>

            <!--            手动操作面板-->
            <com.meitu.ratiorelativelayout.RatioRelativeLayout
                android:id="@+id/rr_manaul"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clickable="false"
                android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
                android:focusable="false"
                android:visibility="invisible">


                <LinearLayout
                    android:id="@+id/ll_bottom"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_gravity="bottom"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:orientation="vertical"
                    android:paddingTop="14dp">

                    <!--                滑竿布局-->
                    <LinearLayout
                        android:id="@+id/rl_auto"
                        radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                        radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                        solid="@{@color/color_f7f8fa}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clickable="true"
                        android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
                        android:focusable="true"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="32dp"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="16dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="60dp"
                                android:layout_height="wrap_content"
                                android:text="@string/beauty_main_aiming_size"
                                android:textColor="@color/color_333333"
                                android:textSize="12sp" />

                            <!-- 新Seekbar -->
                            <com.commsource.widget.XSeekBar
                                android:id="@+id/xsb_size"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_centerInParent="true"
                                android:layout_gravity="bottom"
                                android:layout_marginStart="10dp"
                                app:isEnableStroke="false"
                                app:xBackgroundColor="@color/color_e5e5e5"
                                app:xProgress="35"
                                app:xProgressColor="@color/black"
                                app:xSeekbarHeight="2dp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="32dp"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="16dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="60dp"
                                android:layout_height="wrap_content"
                                android:text="@string/t_edit_brush_hardness"
                                android:textColor="@color/color_333333"
                                android:textSize="12sp" />

                            <com.commsource.widget.XSeekBar
                                android:id="@+id/xsb_hardness"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_centerInParent="true"
                                android:layout_gravity="bottom"
                                android:layout_marginStart="10dp"
                                app:isEnableStroke="false"
                                app:xBackgroundColor="@color/color_e5e5e5"
                                app:xProgress="20"
                                app:xProgressColor="@color/black"
                                app:xSeekbarHeight="2dp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="32dp"
                            android:layout_marginStart="16dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="16dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="60dp"
                                android:layout_height="wrap_content"
                                android:text="@string/t_opacity"
                                android:textColor="@color/color_333333"
                                android:textSize="12sp" />

                            <com.commsource.widget.XSeekBar
                                android:id="@+id/xsb_opacity"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_centerInParent="true"
                                android:layout_gravity="bottom"
                                android:layout_marginStart="10dp"
                                app:isEnableStroke="false"
                                app:xBackgroundColor="@color/color_e5e5e5"
                                app:xProgress="100"
                                app:xProgressColor="@color/black"
                                app:xSeekbarHeight="2dp" />
                        </LinearLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                            radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                            solid="@{@color/white}"
                            android:layout_width="match_parent"
                            android:layout_height="84dp"
                            android:layout_marginTop="10dp">

                            <LinearLayout
                                android:id="@+id/ll_paint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="vertical"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@id/ll_eraser"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <com.commsource.widget.IconFrontView
                                    android:id="@+id/ic_paint"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/edit_icon_removebg_selection_add"
                                    android:textColor="@color/Primary_A"
                                    android:textSize="35dp" />

                                <TextView
                                    android:id="@+id/tv_paint"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/brush"
                                    android:textColor="@color/Primary_A"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/ll_eraser"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="46dp"
                                android:gravity="center"
                                android:orientation="vertical"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/ll_paint"
                                app:layout_constraintTop_toTopOf="parent">

                                <com.commsource.widget.IconFrontView
                                    android:id="@+id/ic_eraser"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/edit_icon_removebg_selection_delete"
                                    android:textColor="@color/color_333333"
                                    android:textSize="35dp" />

                                <TextView
                                    android:id="@+id/tv_eraser"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/beauty_main_submodule_eraser"
                                    android:textColor="@color/color_333333"
                                    android:textSize="12sp" />
                            </LinearLayout>
                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </LinearLayout>

                    <!-- 确认取消按钮-->
                    <com.commsource.studio.component.ConfirmCancelComponent
                        android:id="@+id/confirm_cancel"
                        bindFragment="@{fragment}"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:background="@color/white"
                        android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
                        android:outlineProvider="none" />

                </LinearLayout>

                <!--  大小气泡  -->
                <com.commsource.widget.part.XSeekBubbleView
                    bindXSeek="@{xsbSize}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/ll_bottom"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="86dp"
                    android:layout_marginBottom="-23dp" />

                <!--  硬度气泡  -->
                <com.commsource.widget.part.XSeekBubbleView
                    bindXSeek="@{xsbHardness}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/ll_bottom"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="86dp"
                    android:layout_marginBottom="-66dp" />

                <!--  透明度气泡  -->
                <com.commsource.widget.part.XSeekBubbleView
                    bindXSeek="@{xsbOpacity}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/ll_bottom"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="86dp"
                    android:layout_marginBottom="-108dp" />

            </com.meitu.ratiorelativelayout.RatioRelativeLayout>

            <!--        气泡提醒的mask-->
            <FrameLayout
                android:id="@+id/tipsBubbleMask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
                android:visibility="gone" />

            <!--        点击x退出的时候显示，避免gl环境退出后应用效果导致崩溃-->
            <FrameLayout
                android:id="@+id/flMask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:elevation="@{StudioLayoutConstants.INSTANCE.SHADOW_HEIGHT}"
                android:visibility="invisible" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clStyleManual"
                    radiusLeftTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                    radiusRightTop="@{StudioLayoutConstants.INSTANCE.subCorner}"
                    solid="@{@color/white}"
                    android:layout_width="match_parent"
                    android:layout_height="156dp"
                    app:layout_constraintTop_toBottomOf="parent">


                    <!--                滑竿布局-->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="112dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toTopOf="@id/clEnsure">

                        <com.commsource.studio.component.PaintEraserComponent
                            android:id="@+id/paintEraser"
                            bindFragment="@{fragment}"
                            android:layout_width="match_parent"
                            android:layout_height="42dp"
                            android:layout_marginStart="30dp"
                            android:layout_marginTop="35dp"
                            android:layout_marginEnd="30dp"
                            app:icon_left_pen="@string/if_studio_eraser"
                            app:icon_right_pen="@string/if_history_paint"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </LinearLayout>


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/clEnsure"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        app:layout_constraintBottom_toBottomOf="parent">

                        <!-- 取消 -->
                        <com.commsource.widget.PressImageView
                            android:id="@+id/pivCancel"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:background="@null"
                            android:paddingStart="10dp"
                            android:src="@drawable/common_close_icon_black"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <com.commsource.widget.BoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_centerInParent="true"
                            android:gravity="center"
                            android:text="@string/v_edit"
                            android:textColor="@color/black"
                            android:textSize="13dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <!-- 确认 -->
                        <com.commsource.widget.PressImageView
                            android:id="@+id/pivApply"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:paddingEnd="10dp"
                            android:src="@drawable/common_tick_icon_black"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </RelativeLayout>
    </com.commsource.widget.mask.MaskContainer>

</layout>