<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/bg_solid1affffff_radius12"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/tv_task_status"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_ad_task" />

        <TextView
            android:id="@+id/tvContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:text="watch 1 Video AD"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:autoSizeMaxTextSize="20dp"
            app:autoSizeMinTextSize="3dp"
            app:autoSizeTextType="uniform" />

        <LinearLayout
            android:id="@+id/llWatchAd"
            android:layout_width="67dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:gravity="center"
            android:paddingStart="12dp"
            android:paddingEnd="12dp">

            <com.commsource.widget.PressTextView
                android:id="@+id/tvWatchAd"
                android:layout_width="match_parent"
                android:layout_height="28dp"
                android:gravity="center"
                android:maxLines="1"
                android:text="@string/v77153_A_14"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:autoSizeMaxTextSize="16dp"
                app:autoSizeMinTextSize="3dp"
                app:autoSizeTextType="uniform" />

        </LinearLayout>
    </LinearLayout>
</layout>