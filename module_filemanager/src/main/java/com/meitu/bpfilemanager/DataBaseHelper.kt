package com.meitu.bpfilemanager

import android.content.Context
import androidx.room.Room

object DataBaseHelper {

    private const val DB_NAME = "FILE_MANAGE_DB"

    private var dataBase: FileManageDB? = null

    fun prepare(context: Context, onOpen: (() -> Unit)? = null) {
        dataBase = Room.databaseBuilder(context.applicationContext, FileManageDB::class.java, DB_NAME).build()
        onOpen?.invoke()
    }

    fun getRelationDao(): RelationDao? {
        return dataBase?.getRelationDao()
    }

    fun getRecordDao(): RecordDao? {
        return dataBase?.getRecordDao()
    }
}