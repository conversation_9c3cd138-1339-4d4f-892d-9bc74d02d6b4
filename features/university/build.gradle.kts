plugins {
    id("com.android.library")
    id("kotlin-kapt")
    id("com.pixocial.library")
}

android {
    namespace = "com.pixocial.features.university"
}

dependencies {
    // Jar包依赖
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

    kapt(libs.the.router.apt)
    implementation(libs.the.router)

    // 测试
    routerApi(project(":businesses:societies"))

    implementation(libs.androidx.appcompat)
    implementation(libs.google.material)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}