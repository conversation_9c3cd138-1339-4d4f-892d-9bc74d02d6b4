package com.pixocial.business.duffle.repo.film

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.google.gson.annotations.SerializedName
import com.meitu.common.utils.FileUtil
import com.pixocial.business.duffle.core.DuffleFile
import com.pixocial.business.duffle.core.DuffleTypeConverter
import com.pixocial.business.duffle.core.local.BindSubMaterial
import com.pixocial.business.duffle.core.local.DuffleMaterial
import com.pixocial.business.duffle.core.remote.FileModel
import kotlin.random.Random

@Entity(tableName = "Duffle_Film")
@TypeConverters(DuffleTypeConverter::class)
class FilmMaterial : DuffleMaterial<FilmMaterial>, DuffleFile, BindSubMaterial {

    @PrimaryKey
    @ColumnInfo(name = "material_id")
    @SerializedName("g_id")
    override var materialId: String = ""

    @ColumnInfo(name = "name")
    @SerializedName("name")
    override var materialName: String? = null

    @ColumnInfo(name = "internal_state")
    override var internalState: Int = 0

    @ColumnInfo(name = "parent_ids")
    override var parentIds: List<String>? = null

    @ColumnInfo(name = "sub_material_ids")
    override var subMaterialIds: HashMap<Int, List<String>>? = null

    @ColumnInfo(name = "icon")
    @SerializedName("icon")
    var icon: String? = null

    @ColumnInfo(name = "bind_types")
    @SerializedName("bind_types")
    var bindTypes: List<Int>? = null

    @ColumnInfo(name = "file")
    @SerializedName("file")
    override var file: FileModel? = null

    @ColumnInfo(name = "download_state")
    override var downloadState: Int = 0

    // 0-3:4, 1-2:3，默认值=0
    @ColumnInfo(name = "painting_size", defaultValue = "0")
    @SerializedName("painting_size")
    var paintingSize: Int = 0

    // 0-后置，1-前置，默认值=0
    @ColumnInfo(name = "default_lens", defaultValue = "0")
    @SerializedName("default_lens")
    var defaultLens: Int = 0

    // 0-通用，1-柔光，默认值=0
    @ColumnInfo(name = "beauty", defaultValue = "0")
    @SerializedName("beauty")
    var beauty: Int = 0

    @ColumnInfo(name = "shouldBeauty", defaultValue = "1")
    var shouldBeauty: Boolean = true

    // 0-数码，1-CCD，2-胶片 默认值=0
    @ColumnInfo(name = "watermark", defaultValue = "0")
    @SerializedName("watermark")
    var watermark: Int = 0

    @ColumnInfo(name = "shouldWatermark", defaultValue = "1")
    var shouldWatermark: Boolean = true


    // 0-无，1-双爆，2-半格，默认值=0x
    //这里的双爆面相的是ui显示，判断需不需要上双爆效果，请用shouldDoubleExport判断
    @ColumnInfo(name = "special_features", defaultValue = "0")
    @SerializedName("special_features")
    var specialFeatures: Int = 0

    @ColumnInfo(name = "shouldDoubleExport", defaultValue = "0")
    var shouldDoubleExport: Boolean = false

    // 前置闪光灯开关，0-关闭，1-开启，默认值=0
    @ColumnInfo(name = "rfront_flash", defaultValue = "0")
    @SerializedName("rfront_flash")
    var rfrontFlash: Int = 0

    // 后置闪光灯开关，0-关闭，1-开启，默认值=0
    @ColumnInfo(name = "rear_flash", defaultValue = "0")
    @SerializedName("rear_flash")
    var rearFlash: Int = 0

    // 多地区配置字段，可以配置多张图像URL
    @ColumnInfo(name = "sample_config")
    @SerializedName("sample_config")
    var sampleConfig: List<FilmSampleImageEntity>? = null

    //0 false
    @ColumnInfo(name = "is_hot", defaultValue = "0")
    @SerializedName("is_hot")
    var hot: Int = 0

    //0 false
    @ColumnInfo(name = "is_new", defaultValue = "0")
    @SerializedName("is_new")
    var new: Int = 0

    @ColumnInfo(name = "paid_type", defaultValue = "0")
    @SerializedName("paid_type")
    var paidType: Int? = null

    //是否需要显示小红点
    @ColumnInfo(name = "is_new_click")
    var newClick: Int? = null

    @Ignore
    override var downloadProgress: Int = 0

    @ColumnInfo(name = "ar_plist_path")
    var arPlistPath: String? = null

    @ColumnInfo(name = "filter_plist_path")
    var filterPlistPath: String? = null


    @ColumnInfo(name = "video_plist_path")
    var videoArPaths: List<String>? = null

    @ColumnInfo(name = "photo_plist_path")
    var photoArPaths: List<String>? = null

    //分d s文件夹。且需要随机的
    @Ignore
    var isSpacialCamera = false

    @ColumnInfo(name = "Frame_Rotate")
    var frameRotate: Float = 0f

    @ColumnInfo(name = "Template_Width")
    var templateWidth: Float = 1080f

    @ColumnInfo(name = "Template_Height")
    var templateHeight: Float = 1440f

    @ColumnInfo("Frame_CenterX")
    var centerPosX: Float = 540f

    @ColumnInfo("Frame_CenterY")
    var centerPosY: Float = 653f

    /// 边框的宽度
    @ColumnInfo("Frame_Width")
    var frameWidth: Float = 763f

    @ColumnInfo("Frame_Height")
    /// 边框的高度
    var frameHeight: Float = 1028f

    @Ignore
    var downloadMaterials = mutableListOf<FilmMaterial>()

    @Ignore
    var needDownloadMaterials = mutableListOf<FilmMaterial>()

    @ColumnInfo(name = "defaultPolaroid")
    var defaultPolaroid: String? = null

    @ColumnInfo(name = "defaultFilter")
    var defaultFilter: String? = null

    @ColumnInfo(name = "defaultSpecial")
    var defaultSpecial: String? = null

    override fun getRootPath(): String {
        return FileUtil.createFileDirInternal("Film_Material") ?: ""
    }

    fun getSpecialVideoPath(): String? {
        if (videoArPaths.isNullOrEmpty()) {
            return null
        }

        val random = if (videoArPaths!!.size == 1) {
            0
        } else {
            Random.nextInt(0, videoArPaths!!.size - 1)
        }
        return videoArPaths?.get(random)
    }

    fun getSpecialPhotoPath(): String? {
        if (photoArPaths.isNullOrEmpty()) {
            return null
        }

        val random = if (photoArPaths!!.size == 1) {
            0
        } else {
            Random.nextInt(0, photoArPaths!!.size - 1)
        }
        return photoArPaths?.get(random)
    }

    override fun onCompareLocal(localEntity: FilmMaterial): Boolean {
        val same = isSame(localEntity)

        newClick = localEntity.newClick
        downloadState = localEntity.downloadState
        arPlistPath = localEntity.arPlistPath
        filterPlistPath = localEntity.filterPlistPath
        photoArPaths = localEntity.photoArPaths
        videoArPaths = localEntity.videoArPaths

        defaultPolaroid = localEntity.defaultPolaroid
        defaultFilter = localEntity.defaultFilter
        defaultSpecial = localEntity.defaultSpecial


        shouldWatermark = localEntity.shouldWatermark
        shouldBeauty = localEntity.shouldBeauty
        shouldDoubleExport = localEntity.shouldDoubleExport
        rearFlash = localEntity.rearFlash
        rfrontFlash = localEntity.rfrontFlash
        defaultLens = localEntity.defaultLens

        return same
    }

    override fun equals(other: Any?): Boolean {
        return other is FilmMaterial && this.materialId == other.materialId
    }

    fun isSame(other: FilmMaterial?): Boolean {
        other ?: return false

        var same = this.materialId == other.materialId
        same = same and (this.materialName == other.materialName)
        same =
            same and (this.parentIds?.joinToString { it } == other.parentIds?.joinToString { it })
        same = same and (this.sampleConfig == other.sampleConfig)
        return same
    }

    override fun toString(): String {
        return "FilmMaterial(materialId='$materialId', materialName=$materialName, " +
                "相机类型=${
                    if (specialFeatures == 1) {
                        "双重曝光"
                    } else if (specialFeatures == 2) {
                        "半格"
                    } else {
                        "普通"
                    }
                }, " +
                "下载状态=${
                    if (downloadState == 0) {
                        "未下载"
                    } else {
                        "已下载"
                    }
                }, internalState=$internalState" +
                ", 比率=${
                    if (paintingSize == 0) {
                        "3:4"
                    } else {
                        "2:3"
                    }
                }, 摄像头=${
                    if (defaultLens == 0) {
                        "后置"
                    } else {
                        "前置"
                    }
                }, " +
                "前置闪光灯是否打开=${rfrontFlash == 1}, 后置闪光灯是否打开=${rearFlash == 1},beauty=${
                    if (beauty == 0) {
                        "通用"
                    } else {
                        "柔光"
                    }
                }， parentIds=$parentIds, subMaterialIds=$subMaterialIds, icon=$icon, bindTypes=$bindTypes, file=$file, , watermark=$watermark, sampleConfig=$sampleConfig, hot=$hot, new=$new, paidType=$paidType, newClick=$newClick, downloadProgress=$downloadProgress, arPlistPath=$arPlistPath, filterPlistPath=$filterPlistPath, videoArPaths=$videoArPaths, photoArPaths=$photoArPaths, isSpacialCamera=$isSpacialCamera)"

    }

    fun getWaterMarkPath() =
//        "film/CCD/ar/configuration.plist"
        when (watermark) {
            1 -> {
                "film/CCD/ar/configuration.plist"
            }

            2 -> {
                "film/Film/ar/configuration.plist"
            }

            3 -> "film/Dv/ar/configuration.plist"

            else -> {
                "film/Digital/ar/configuration.plist"
            }
        }


    fun getRadioRect() = if (paintingSize == 0) {
        Pair(3f, 4f)
    } else {//PICTURE_RATIO_3_2
        Pair(2f, 3f)
    }

    companion object {
        const val NONE = "0000000"
        const val RANDOM = "RANDOM"
    }
}