package com.pixocial.business.duffle.core

import com.pixocial.business.duffle.core.local.DuffleCategory
import com.pixocial.business.duffle.core.local.DuffleMaterial
import com.pixocial.business.duffle.core.remote.CategorySortType
import com.pixocial.business.duffle.core.remote.DataWrapper
import com.pixocial.business.duffle.core.remote.DeliveryType
import com.pixocial.business.duffle.core.remote.MaterialModel
import com.pixocial.business.duffle.core.remote.MaterialRequestType
import com.pixocial.business.duffle.core.remote.MaterialSortType
import kotlinx.coroutines.flow.Flow

fun <M> List<M>.toTypedArray(clazz: Class<*>): Array<M>? {
    val arr = try {
        java.lang.reflect.Array.newInstance(clazz, this.size) as Array<M>
    } catch (e: Exception) {
        return null
    }
    this.forEachIndexed { i, e ->
        arr[i] = e
    }
    return arr
}

fun <C : DuffleCategory<C>> BaseDuffleRepo<*, C>.fetchCategory(
    typeId: Int,
    @CategorySortType sortBy: Int = CategorySortType.FEATURED,
    @DeliveryType delivery: Int = DeliveryType.DEFAULT,
    isLoadMore: Boolean = false,
    controller: PagingController = PagingController()
): Flow<DataWrapper<C>?> {
    val categoryKey = buildCategoryKey(typeId, sortBy)
    if (!isLoadMore) {
        controller.refreshPage(categoryKey)
    }
    controller.getConfig(categoryKey).apply {
        this.isLoadMore = isLoadMore
    }

    return loadCategory(
        typeId = typeId,
        sortBy = sortBy,
        categoryKey = categoryKey,
        delivery = delivery,
        controller = controller
    )
}

/**
 * 获取素材数据
 *
 * 普通获取方式，根据运营排序返回
 */
fun <M : DuffleMaterial<M>, C : DuffleCategory<C>> BaseDuffleRepo<M, C>.fetchMaterial(
    typeId: Int,
    @MaterialSortType sortBy: Int = MaterialSortType.DEFAULT,
    @DeliveryType delivery: Int = DeliveryType.DEFAULT,
    isLoadMore: Boolean = false,
    controller: PagingController = PagingController(),
    isLoadLocal: Boolean = true,
    isLoadNet: Boolean = true
): Flow<DataWrapper<M>?> {
    val materialKey = buildMaterialKey(typeId, sortBy = sortBy, delivery = delivery)
    if (!isLoadMore) {
        controller.refreshPage(materialKey)
    }
    controller.getConfig(materialKey).apply {
        this.isLoadMore = isLoadMore
        this.isLoadLocal = isLoadLocal
        this.isLoadNet = isLoadNet
    }

    return loadMaterial(
        typeId = typeId,
        materialModel = MaterialModel(
            key = materialKey,
            requestType = MaterialRequestType.MATERIAL_LIST_BY_SORT,
            sortBy = sortBy
        ),
        delivery = delivery,
        controller = controller
    )
}

/**
 * 获取素材数据
 *
 * 根据分类获取素材
 */
fun <M : DuffleMaterial<M>, C : DuffleCategory<C>> BaseDuffleRepo<M, C>.fetchMaterialsByCategory(
    typeId: Int,
    category: C,
    @DeliveryType delivery: Int = DeliveryType.DEFAULT,
    isLoadMore: Boolean = false,
    controller: PagingController = PagingController()
): Flow<DataWrapper<M>?> {
    val materialKey = buildMaterialKey(typeId, id = category.categoryId, delivery = delivery)
    if (!isLoadMore) {
        controller.refreshPage(materialKey)
    }
    controller.getConfig(materialKey).apply {
        this.isLoadMore = isLoadMore
    }

    return loadMaterial(
        typeId = typeId,
        materialModel = MaterialModel(
            key = materialKey,
            requestType = MaterialRequestType.MATERIAL_LIST_BY_CATEGORY,
            category = category,
        ),
        delivery = delivery,
        controller = controller
    )
}

/**
 * 获取素材数据
 *
 * 根据绑定素材获取素材
 */
fun <M : DuffleMaterial<M>, C : DuffleCategory<C>> BaseDuffleRepo<M, C>.fetchMaterialsByMaterial(
    typeId: Int,
    material: M,
    @DeliveryType delivery: Int = DeliveryType.DEFAULT,
    isLoadMore: Boolean = false,
    controller: PagingController = PagingController()
): Flow<DataWrapper<M>?> {
    val materialKey = buildMaterialKey(typeId, id = material.materialId, delivery = delivery)
    if (!isLoadMore) {
        controller.refreshPage(materialKey)
    }
    controller.getConfig(materialKey).apply {
        this.isLoadMore = isLoadMore
    }

    return loadMaterial(
        typeId = typeId,
        materialModel = MaterialModel(
            key = materialKey,
            requestType = MaterialRequestType.MATERIAL_LIST_BY_MATERIAL,
            material = material,
        ),
        delivery = delivery,
        controller = controller
    )
}