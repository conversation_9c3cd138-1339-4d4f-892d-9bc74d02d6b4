package com.pixocial.bussiness.limit

import com.commsource.util.common.SPConfig
import com.meitu.common.AppContext

object FreeLimitCache : SPConfig(AppContext.context, "free_limit_cache"){

    /**
     * 按频率更新时间
     */
    private const val UPDATE_TIME_FREQUENCY = "UPDATE_TIME_FREQUENCY"

    fun setUpdateTimeFrequency(time: Long) = putValue(UPDATE_TIME_FREQUENCY, time)

    fun getUpdateTimeFrequency() = getLong(UPDATE_TIME_FREQUENCY, 0L)

}