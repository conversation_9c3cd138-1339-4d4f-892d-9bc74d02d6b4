package com.pixocial.bussiness.limit.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update

@Dao
interface FreeLimitStrategyDao {
    /**
     * 插入策略
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(usage: FreeLimitStrategy)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(usageList: List<FreeLimitStrategy>)

    /**
     * 更新策略
     */
    @Update
    fun update(usage: FreeLimitStrategy)

    @Update
    fun updateAll(usageList: List<FreeLimitStrategy>)

    /**
     * 删除策略
     */
    @Delete
    fun delete(usage: FreeLimitStrategy)

    /**
     * 查询所有策略
     */
    @Query("select * from free_limit_strategy")
    fun queryAll(): List<FreeLimitStrategy>

    @Query("select * from free_limit_strategy where id = :id")
    fun queryById(id: String): FreeLimitStrategy?

    @Query("delete from free_limit_strategy")
    fun deleteAll()
}