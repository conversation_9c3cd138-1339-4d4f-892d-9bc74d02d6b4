plugins {
    id("com.android.library")
    id("kotlin-kapt")
    id("com.pixocial.library")
}

android {
    namespace = "com.pixocial.business.ar"

    dataBinding {
        isEnabled = true
    }
}

dependencies {
    // Jar包依赖
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

    // 基础库
    implementation(projects.moduleCommon)

    // 开源库
    implementation(projects.moduleOpensource)

    // 控件库
    implementation(projects.frameworks.widget)

    // duffle 先简单依赖下、后面用 api 模式
    implementation(projects.businesses.duffle)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}