package com.pixocial.business.ar.container

import android.app.Activity
import android.content.Context
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout.LayoutParams
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.commsource.util.dp
import com.commsource.util.gone
import com.commsource.util.resColor
import com.commsource.util.setMarginCompat
import com.commsource.util.setSize
import com.commsource.util.visible
import com.commsource.widget.PressImageView
import com.commsource.widget.RotateLoadingView
import com.commsource.widget.recyclerview.BaseItem
import com.commsource.widget.recyclerview.BaseViewHolder
import com.commsource.widget.round.RoundFrameLayout
import com.pixocial.business.ar.R
import com.pixocial.business.ar.inject.ARProvider
import com.pixocial.business.ar.theme.ARThemeType
import com.pixocial.business.ar.theme.MaterialTheme
import com.pixocial.business.ar.widget.MaterialItemView
import com.pixocial.business.duffle.repo.ar.ARMaterial
import com.therouter.TheRouter

class ARMaterialViewHolder(context: Context, parent: ViewGroup?) :
    BaseViewHolder<ARMaterial>(context, parent, MaterialItemView(context)) {

    companion object {
        private val AR_IV_PADDING = 7.dp()
    }

    private val rlContent: RoundFrameLayout = itemView.findViewById(MaterialItemView.idRlContent)
    private val arMaterialItemIv: PressImageView =
        itemView.findViewById(MaterialItemView.idArMaterialItemIv)
    private val selectIv: View = itemView.findViewById(MaterialItemView.idSelectIv)
    private val ivRed: View = itemView.findViewById(MaterialItemView.idIvRed)
    private val progress: RotateLoadingView = itemView.findViewById(MaterialItemView.idProgress)
    private val ivRight: ImageView = itemView.findViewById(MaterialItemView.idIvRight)
    private val ivLeft: ImageView = itemView.findViewById(MaterialItemView.idIvLeft)
    private val ivRightTop: ImageView = itemView.findViewById(MaterialItemView.idIvRightTop)
    private val ivLeftTop: ImageView = itemView.findViewById(MaterialItemView.idIv)

    private val studioTheme = MaterialTheme.getStudioTheme()
    private val cameraTheme = MaterialTheme.getCameraTheme()

    private val themeType: Int by lazy(LazyThreadSafetyMode.NONE) {
        adapter.getTag(ARThemeType.KEY_THEME_TYPE) as? Int ?: ARThemeType.STUDIO_THEME
    }

    private fun getCurrentTheme(): MaterialTheme {
        return if (themeType == ARThemeType.STUDIO_THEME) studioTheme else cameraTheme
    }

    private val webpTransform = TheRouter.get(ARProvider::class.java)?.webpDecoder()
    private val needHideVipIcon = TheRouter.get(ARProvider::class.java)?.isNeedHideVipIcon(true)

    override fun onBindViewHolder(
        position: Int,
        item: BaseItem<ARMaterial>?,
        payloads: MutableList<Any>?
    ) {
        super.onBindViewHolder(position, item, payloads)
        if (item?.entity == null) {
            return
        }
        val theme = getCurrentTheme()

        rlContent.tag = item.entity.materialId
        val entity = item.entity
        entity?.let {
            if (payloads.isNullOrEmpty() || it.isMontageAr()) {
                rlContent.tag = entity.materialId
                rlContent.delegate.apply {
                    cornerRadius = theme.cornerRadius
                    backgroundColor = theme.backgroundColor.resColor()
                }
                ivRight.apply {
                    layoutParams = LayoutParams(theme.downloadSize, theme.downloadSize).apply {
                        gravity = Gravity.END or Gravity.BOTTOM
                        rightMargin = 3.dp
                        bottomMargin = 3.dp
                    }
                }

                arMaterialItemIv.isZoomEnable = true
                arMaterialItemIv.isPressEnable = true

                val request = Glide.with(mContext as Activity)
                    .load(entity.icon)
                    .transform(RoundedCorners(10.dp()))
                    .placeholder(theme.placeRes)
                // 如果是 webp 格式的图片，使用 webpTransform 进行转换
                if (entity.icon?.endsWith(".webp") == true && webpTransform != null) {
                    request.apply(webpTransform).into(arMaterialItemIv)
                } else {
                    request.into(arMaterialItemIv)
                }

                if (entity.isMontageAr()) {
                    arMaterialItemIv.setPadding(
                        AR_IV_PADDING,
                        AR_IV_PADDING,
                        AR_IV_PADDING,
                        AR_IV_PADDING
                    )
                } else {
                    arMaterialItemIv.setPadding(0, 0, 0, 0)
                }
                refreshDynamicState()
            } else {
                refreshDynamicState()
            }
        }
    }

    /**
     * 刷新状态 部分状态动态改变 需要触发
     */
    private fun refreshDynamicState() {
        val entity = item.entity ?: return
        // 相机情况才有音乐图标，高级美颜不显示
        if (themeType == ARThemeType.CAMERA_THEME && entity.musicEffect == 1) {
            ivLeft.setImageResource(R.drawable.selfie_ar_icon_music)
            ivLeft.visible()
        } else if (entity.isNeedShare() && !entity.isShared()) {
            ivLeft.setImageResource(R.drawable.selfie_ar_icon_share)
            ivLeft.visible()
        } else {
            ivLeft.gone()
        }

        // 通过下载状态显示是否下载
        // 重定向AR
        // 右图标显示
        if (entity.isNeedRedirect()) {
            ivRight.setImageResource(R.drawable.selfie_ar_icon_jump)
            ivRight.visible()
            progress.gone()
        } else if (entity.isDownloaded()) {
            progress.gone()
            ivRight.gone()
        } else if (entity.isDownloading()) {
            progress.visible()
            ivRight.gone()
        } else {
            ivRight.setImageResource(getCurrentTheme().downloadRes)
            ivRight.visible()
            progress.gone()
        }

        //下载中 部分布局色差
        if (entity.isDownloading()) {
            rlContent.alpha = 0.5f
        } else {
            rlContent.alpha = 1f
        }

        // 如果套用了Ar 需要重置Ar内部
        if (item.isSelect) {
            selectIv.setBackgroundResource(getCurrentTheme().selectedRes)
            selectIv.visible()
        } else {
            selectIv.gone()
        }

        if (needHideVipIcon == true) {
            ivRightTop.gone()
        } else {
            // 是否显示IP 订阅
            // 右上图标显示
            if (entity.isNeedPaid()) {
                ivRightTop.setSize(18.dp(), 18.dp())
                ivRightTop.setMarginCompat(0, 2.dp(), 2.dp(), 0)
                ivRightTop.setImageResource(R.drawable.common_corner_premium_white_shadow)
                ivRightTop.visible()
            } else {
                ivRightTop.gone()
            }
        }

        //左上的图标显示
        if (entity.isSwitchable()) {
            ivLeftTop.setImageResource(R.drawable.selfie_ar_icon_switch)
            ivLeftTop.visible()
        } else {
            ivLeftTop.gone()
        }
        // 是否显示小红点
        if (entity.isRed()) {
            ivRed.setBackgroundResource(R.drawable.camera_ar_material_red)
            ivRed.visible()
        } else {
            ivRed.gone()
        }
    }
}