package com.pixocial.business.ar.widget

import android.content.Context
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import com.commsource.util.dp
import com.commsource.util.resColor
import com.commsource.widget.IconFrontView
import com.commsource.widget.PressImageView
import com.commsource.widget.round.RoundFrameLayout
import com.meitu.library.util.device.DeviceUtils
import com.pixocial.business.ar.R

class FunctionView(context: Context) : FrameLayout(context) {

    companion object {
        val idRlContent = View.generateViewId()
        val iv = View.generateViewId()
        val idIvRight = View.generateViewId()
        val idIvRightTop = View.generateViewId()
        val idIfv = View.generateViewId()
    }

    private val size = (DeviceUtils.getScreenWidth() * 63f / 375f).toInt()

    init {
        val fl = RoundFrameLayout(context).apply {
            id = idRlContent

            addView(
                PressImageView(context).apply {
                    id = iv
                },
                LayoutParams(size, size).apply {
                    gravity = Gravity.CENTER
                }
            )

            addView(
                IconFrontView(context).apply {
                    id = idIfv
                    gravity = Gravity.CENTER
                    setTextColor(R.color.Gray_A.resColor())
                    setTextSize(30f)
                },
                LayoutParams(-1, -1).apply {
                    gravity = Gravity.CENTER
                }
            )

            addView(
                ImageView(context).apply {
                    id = idIvRight
                    visibility = View.GONE
                },
                LayoutParams(16f.dp, 16f.dp).apply {
                    gravity = Gravity.END or Gravity.BOTTOM
                    rightMargin = 3.dp
                    bottomMargin = 3.dp
                }
            )

            addView(
                ImageView(context).apply {
                    id = idIvRightTop
                    visibility = View.GONE
                },
                LayoutParams(16.dp, 16.dp).apply {
                    gravity = Gravity.END or Gravity.TOP
                    topMargin = 3.dp
                    rightMargin = 3.dp
                }
            )
        }

        addView(fl, size, size)
    }
}

