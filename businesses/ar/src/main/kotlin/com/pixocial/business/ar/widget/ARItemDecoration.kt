package com.pixocial.business.ar.widget

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.commsource.util.setRTL
import com.meitu.library.util.device.DeviceUtils
import kotlin.math.ceil

class ARItemDecoration() : RecyclerView.ItemDecoration() {

    companion object {
        val space = (DeviceUtils.getScreenWidth() * 5f / 375f).toInt()

        const val SPAN_COUNT = 5
    }

    private var extraOffset = 0

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        val position = parent.getChildAdapterPosition(view)
        val itemCount = parent.adapter?.itemCount ?: 0

        val rowCount = ceil(itemCount / SPAN_COUNT.toDouble()).toInt()
        val currentRow = (position / SPAN_COUNT) + 1
        val isLastRow = currentRow == rowCount

        if (isLastRow) {
            outRect.setRTL(space, space, space, space + extraOffset)
        } else {
            outRect.setRTL(space, space, space, space)
        }
    }

    fun updateExtraOffset(offset: Int) {
        extraOffset = offset
    }
}