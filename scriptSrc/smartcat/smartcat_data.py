import os
import re
import sys
import xml.etree.ElementTree as ET
import shutil
from natsort import natsorted
from typing import OrderedDict

RESOURCE_TEMPLATE = '<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">\n'

HISTORY_DIRS = [
    "resource/common/{}/strings.xml",
    "resource/video/{}/strings.xml",
    "resource/newtranslate/{}/strings.xml",
]

TARGET_DIR = "resource/newtranslate/{}/strings.xml"

LANGUAGE_MAP = {
    "en": "values",
    "ar": "values-ar",
    "de": "values-de",
    "es-MX": "values-es",
    "fr": "values-fr",
    "id": "values-in",
    "it": "values-it",
    "ja": "values-ja",
    "ko": "values-ko",
    "pt-BR": "values-pt",
    "ru": "values-ru",
    "th": "values-th",
    "tr": "values-tr",
    "vi": "values-vi",
    "zh-Hans": "values-zh-rCN",
    "zh-HK": "values-zh-rHK",
    "zh-Hant-TW": "values-zh-rTW",
}


def get_string_template(format, name, value):
    return (
        ("    " if format else "")
        + f'<string name="{name}">{value}</string>'
        + ("\n" if format else "")
    )


def application_path():
    if getattr(sys, "frozen", False):
        application_path = os.path.dirname(sys.executable)
    elif __file__:
        application_path = os.path.dirname(__file__)
    return application_path


# 处理特殊字符
def fix_special_characters(process_value):
    if not process_value:
        return process_value

    process_value.rstrip()
    # &替换
    process_value = process_value.replace("&", "&amp;")
    # 最后一个字符是%的情况下需要改成两个转义符。

    # 占位符替换
    re_str = r"\{[^{}]*\}"
    result = re.findall(re_str, process_value, re.M)

    # 有占位符的话 %需要替换&#37
    if len(result) > 0:
        process_value = process_value.replace("%", "%%")

    result = re.findall(re_str, process_value, re.M)
    if len(result) == 1:
        process_value = process_value.replace(result[0], f"%1$s")
    else:
        # 提取#之前的数字
        num_re_str = r"\d+@"
        num_re_str2 = r"@\d+"
        for pos in range(len(result)):
            # 判断是否以数字+#的方式开头
            num_result = re.search(num_re_str, result[pos], re.M)
            num_result2 = re.search(num_re_str2, result[pos], re.M)
            if num_result:
                num = num_result.group().replace("@", "")
                if num.isdigit():
                    process_value = process_value.replace(result[pos], f"%{num}$s")
            elif num_result2:
                num2 = num_result2.group().replace("@", "")
                if num2.isdigit():
                    process_value = process_value.replace(result[pos], f"%{num2}$s")
            else:
                print(f"占位符格式不对>>{result[pos]},联系产品修正!!!")

    # 去除首尾的空格
    process_value = process_value.strip(" ")
    # 换行符转换
    process_value = process_value.replace("\n", "\\n")
    # 单引号加转义，去除末尾的换行
    for i in range(1, len(process_value)):
        if process_value[i] == "'" and process_value[i - 1] != "\\":
            process_value = process_value[:i] + "\\" + process_value[i:]
        if process_value[i] == "\n" and i == len(process_value) - 1:
            process_value = process_value[:i]
    return process_value


# 替换特殊字符
def sanitize_key(key):
    return key.replace("(", "").replace(")", "").replace("-", "_")


# 替换原有可能变更的字符串
def replace_old_strings(strings, output_file):
    with open(output_file, "r", encoding="utf-8") as f:
        content = f.read()
        for name, value in strings.items():
            start = content.find(f'<string name="{name}">')
            if start != -1:
                end = content.find("</string>", start)
                if end != -1:
                    old_string = content[start : end + len("</string>")]
                    new_string = get_string_template(
                        format=False, name=name, value=value
                    )
                    content = content.replace(old_string, new_string)

    with open(output_file, "w", encoding="utf-8") as f:
        f.write(content)


# 新增字符串
def merge_strings_to_file(strings, output_file):
    strings = OrderedDict(natsorted(strings.items()))
    if not os.path.exists(output_file):
        with open(output_file, "w", encoding="utf-8") as f:
            f.write('<?xml version="1.0" encoding="utf-8"?>\n')
            f.write(RESOURCE_TEMPLATE)
            for name, value in strings.items():
                f.write(get_string_template(format=True, name=name, value=value))
            f.write("</resources>\n")
    else:
        with open(output_file, "r+", encoding="utf-8") as f:
            content = f.read()
            f.seek(0)
            f.truncate()
            end_index = content.rfind("</resources>")
            if end_index != -1:
                new_content = content[:end_index]
                for name, value in strings.items():
                    if f'<string name="{name}">' not in content:
                        new_content += get_string_template(
                            format=True, name=name, value=value
                        )
                new_content += "</resources>"
                f.write(new_content)


# 增加默认语言处理，en 格式未翻译使用 zh-Hans 的翻译
def process_default_language(base_dir, project_dir, prefix):
    output_file = TARGET_DIR.format(LANGUAGE_MAP["en"])
    output_file = os.path.join(project_dir, output_file)
    if not os.path.exists(output_file):
        return False

    default_file = os.path.join(base_dir, "values-zh-Hans.xml")
    en_file = os.path.join(base_dir, "values-en.xml")
    if not os.path.exists(default_file) or not os.path.exists(en_file):
        return False

    default_element = ET.parse(default_file).getroot()
    en_element = ET.parse(en_file).getroot()

    en_strings = {
        prefix + sanitize_key(string.get("name")): string.text
        for string in en_element.findall("string")
    }

    strings = {}
    for string in default_element.findall("string"):
        name = prefix + sanitize_key(string.get("name"))
        default_value = fix_special_characters(string.text)
        real_value = fix_special_characters(en_strings.get(name))
        if not default_value:
            continue
        strings[name] = real_value if real_value else default_value

    if not strings:
        return False

    replace_old_strings(strings, output_file)
    merge_strings_to_file(strings, output_file)
    return True


# 加载并合并字符串
def load_and_merge_strings(base_dir, prefix):
    # 脚本放在 /BeautyPlusAndroid/script_smartcatTranslate 中
    # 翻译完的 xml 文件放在 /BeautyPlusAndroid/resource/newtranslate 中
    # 获取项目的根目录
    project_dir = os.path.abspath(os.path.join(base_dir, os.pardir, os.pardir))

    result = process_default_language(base_dir, project_dir, prefix)
    for root, _, files in os.walk(base_dir):
        for file in files:
            if not file.endswith(".xml"):
                continue

            file_path = os.path.join(root, file)
            tree = ET.parse(file_path)
            root_element = tree.getroot()

            if root_element.tag != "resources":
                continue

            values_file = os.path.basename(file)
            language_code = values_file.split(".")[0].replace("values-", "")
            if language_code not in LANGUAGE_MAP or language_code == "en":
                continue

            output_file = TARGET_DIR.format(LANGUAGE_MAP[language_code])
            output_file = os.path.join(project_dir, output_file)
            if not os.path.exists(output_file):
                continue

            strings = {}
            for string in root_element.findall("string"):
                name = prefix + sanitize_key(string.get("name"))
                value = fix_special_characters(string.text)
                if not value:
                    continue
                strings[name] = value

            if not strings:
                result = True
                continue

            # Smartcat 上无香港繁体，需要复用台湾繁体
            if language_code == "zh-Hant-TW":
                hk_file = TARGET_DIR.format(LANGUAGE_MAP["zh-HK"])
                hk_file = os.path.join(project_dir, hk_file)
                if os.path.exists(hk_file):
                    replace_old_strings(strings, hk_file)
                    merge_strings_to_file(strings, hk_file)

            replace_old_strings(strings, output_file)
            merge_strings_to_file(strings, output_file)
            result = True

    # 删除 base_dir 目录
    shutil.rmtree(base_dir)
    return result


# 替换历史字符串
def replace_history_strings(base_dir):
    project_dir = os.path.abspath(os.path.join(base_dir, os.pardir, os.pardir))
    reuslt = False

    for root, _, files in os.walk(base_dir):
        for file in files:
            if not file.endswith(".xml"):
                continue

            file_path = os.path.join(root, file)
            file_path = os.path.join(root, file)
            tree = ET.parse(file_path)
            root_element = tree.getroot()

            if root_element.tag != "resources":
                continue

            values_file = os.path.basename(file)
            language_code = values_file.split(".")[0].replace("values-", "")
            if language_code not in LANGUAGE_MAP:
                continue

            for history_dir in HISTORY_DIRS:
                history_file = os.path.join(
                    project_dir, history_dir.format(LANGUAGE_MAP[language_code])
                )
                if not os.path.exists(history_file):
                    continue

                strings = {}
                for string in root_element.findall("string"):
                    name = sanitize_key(string.get("name"))
                    value = fix_special_characters(string.text)
                    if not value:
                        continue
                    strings[name] = value

                if not strings:
                    reuslt = True
                    continue

                replace_old_strings(strings, history_file)
                reuslt = True

    # 删除 base_dir 目录
    shutil.rmtree(base_dir)
    return reuslt
