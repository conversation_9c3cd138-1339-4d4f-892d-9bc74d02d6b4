package com.pixocial.plugin.router.bridge

import org.gradle.api.Plugin
import org.gradle.api.initialization.ProjectDescriptor
import org.gradle.api.initialization.Settings
import java.io.File
import javax.xml.parsers.DocumentBuilderFactory

/**
 * @Description:
 *
 * @Author: vinvince, @Time: 2024/7/23 19:47
 */
class RouterBridgeSettingsPlugin : Plugin<Settings> {

    override fun apply(settings: Settings) {
        println("----> RouterBridge setting plugin apply. <----")

        settings.extensions.create(
            "routerBridgeSettings",
            RouterBridgeSettingsExtension::class.java
        )

        settings.gradle.settingsEvaluated {
            val projectDirs = HashSet<String>()
            traverseProjectDescriptors(projectDirs, it.rootProject)

            projectDirs.forEach { projectPath ->
                // 通过查找api目录下的文件有没有源码，判断是否要生成api模块
                val routerApiSourceDir =
                    projectPath + File.separator + CompilerUtil.ROUTER_API_SOURCE
                val sourceFile = File(routerApiSourceDir).walk().filter { it.isFile }
                    .filter { it.extension.equals("kt", true) or it.extension.equals("java", true) }
                    .toList()

                if (sourceFile.isNotEmpty()) {
                    println("----> RouterBridge find api source dir: $routerApiSourceDir, sourceFile size: ${sourceFile.size}. <----")

                    val projectRelativePath =
                        projectPath.removePrefix(settings.rootProject.projectDir.absolutePath)
                    val buildTreePath = projectRelativePath.replace("/", ":")

                    generateApiModuleFiles(settings, buildTreePath)

                    val routerApiModuleName = CompilerUtil.getRouterApiProject(buildTreePath)
                    settings.include(routerApiModuleName)
                    println("----> RouterBridge include module name: $routerApiModuleName. <----")
                }
            }
        }
    }

    // 递归方法遍历 ProjectDescriptor 对象及其所有子节点
    private fun traverseProjectDescriptors(
        projectDirs: HashSet<String>, descriptor: ProjectDescriptor
    ) {
        projectDirs.add(descriptor.projectDir.absolutePath)

        // 遍历当前 ProjectDescriptor 的所有直接子项目
        for (child in descriptor.children) {
            // 对每个子项目调用此方法（递归）
            traverseProjectDescriptors(projectDirs, child)
        }
    }

    private fun generateApiModuleFiles(settings: Settings, buildTreePath: String) {
        val routeApiModuleName = CompilerUtil.getRouterApiProject(buildTreePath)
        val rootProjectPath = settings.rootDir.absolutePath
        val routerApiModuleDir =
            File(rootProjectPath + File.separator + routeApiModuleName.replace(":", "/"))

        println("----> RouterBridge start generate router api module files: $routerApiModuleDir <----")
        if (!routerApiModuleDir.exists()) {
            if (!routerApiModuleDir.mkdirs()) {
                return
            }
        }

        val srcMainFile = File(routerApiModuleDir, "src${File.separator}main")
        if (!srcMainFile.exists()) {
            srcMainFile.mkdirs()
        }

        val androidManifestFile = File(srcMainFile, "AndroidManifest.xml")
        androidManifestFile.deleteOnExit()
        androidManifestFile.createNewFile()
        androidManifestFile.writeText("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" + "<manifest/>")

        // 查找包名，先通过AndroidManifest文件查找，没有再查找build.gradle文件中的namespace
        var packageName = findPackageNameFromManifest(settings, buildTreePath)
        if (packageName.isNullOrEmpty()) {
            packageName = findNameSpaceFromBuildFile(settings, buildTreePath)
        }
        if (packageName?.isNotEmpty() == true) {
            packageName += ".api"
        } else {
            throw IllegalArgumentException("Project $buildTreePath has no package name.")
        }

        val extension = settings.extensions.findByType(RouterBridgeSettingsExtension::class.java)
        val compileSdk = extension?.compileSdk ?: 35
        val minSdk = extension?.minSdk ?: 21
        val javaVersion = extension?.javaVersion ?: 17
        val kotlinStdLibVersion = extension?.kotlinStdLibVersion ?: "2.0.20"
        val annotationVersion = extension?.annotationVersion ?: "1.8.2"

        val buildGradleFile = File(routerApiModuleDir, "build.gradle.kts")
        buildGradleFile.deleteOnExit()
        buildGradleFile.createNewFile()
        buildGradleFile.writeText(
            "import org.jetbrains.kotlin.gradle.dsl.JvmTarget\n" +
                    "\n" +
                    "/********************* RouterBridge自动生成，不要修改这个文件 *********************/" +
                    "\n" +
                    "plugins {\n" +
                    "    id(\"com.android.library\")\n" +
                    "    id(\"org.jetbrains.kotlin.android\")\n" +
                    "}\n" +
                    "\n" +
                    "android {\n" +
                    "    compileSdk = ${compileSdk}\n" +
                    "    namespace = \"${packageName}\"\n" +
                    "\n" +
                    "    defaultConfig {\n" +
                    "        minSdk = ${minSdk}\n" +
                    "    }\n" +
                    "\n" +
                    "    compileOptions {\n" +
                    "        sourceCompatibility = JavaVersion.toVersion(${javaVersion})\n" +
                    "        targetCompatibility = JavaVersion.toVersion(${javaVersion})\n" +
                    "    }\n" +
                    "\n" +
                    "    kotlinOptions {\n" +
                    "        jvmTarget = JvmTarget.fromTarget(\"${javaVersion}\").target\n" +
                    "    }\n" +
                    "}\n" +
                    "\n" +
                    "dependencies {\n" +
                    "    implementation(\"org.jetbrains.kotlin:kotlin-stdlib:${kotlinStdLibVersion}\")\n" +
                    "    implementation(\"androidx.annotation:annotation:${annotationVersion}\")\n" +
                    "}\n"
        )
    }

    private fun findNameSpaceFromBuildFile(settings: Settings, buildTreePath: String): String? {
        val rootProjectPath = settings.rootDir.absolutePath
        val subModuleDir =
            rootProjectPath + File.separator + buildTreePath.replace(":", "/")
        var nameSpace: String? = null
        var buildFile = File(subModuleDir, "build.gradle")
        if (!buildFile.exists()) {
            buildFile = File(subModuleDir, "build.gradle.kts")
        }
        if (buildFile.exists()) {
            val fileContent = buildFile.readText()
            val namespaceRegex = """namespace\s*=\s*"(.+?)"""".toRegex()
            val matchResult = namespaceRegex.find(fileContent)
            nameSpace = matchResult?.groups?.get(1)?.value

            if (nameSpace?.isNotEmpty() == true) {
                println("----> RouterBridge ${buildFile.absolutePath} package is: $nameSpace <----")
            } else {
                println("----> RouterBridge package attribute not found in ${buildFile.absolutePath} <----")
            }
        } else {
            println("----> RouterBridge no build.gradle file found for ${buildFile.absolutePath} <----")
        }
        return nameSpace
    }

    private fun findPackageNameFromManifest(settings: Settings, buildTreePath: String): String? {
        val rootProjectPath = settings.rootDir.absolutePath
        val subModuleDir =
            rootProjectPath + File.separator + buildTreePath.replace(":", "/")

        var packageName: String? = null
        val manifestFile = File(subModuleDir, "/src/main/AndroidManifest.xml")
        if (manifestFile.exists()) {
            val documentBuilderFactory = DocumentBuilderFactory.newInstance()
            val documentBuilder = documentBuilderFactory.newDocumentBuilder()
            val document = documentBuilder.parse(manifestFile)

            packageName = document.documentElement.getAttribute("package")
            if (packageName?.isNotEmpty() == true) {
                println("----> RouterBridge ${manifestFile.absolutePath} package is: $packageName <----")
            } else {
                println("----> RouterBridge package attribute not found in ${manifestFile.absolutePath} <----")
            }
        } else {
            println("----> RouterBridge no AndroidManifest.xml file found for ${manifestFile.absolutePath} <----")
        }
        return packageName
    }
}