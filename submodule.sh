#! /bin/bash

MODULE=$1

# 仓库的数量。

REPOSITORIES_NUM=$(cat "submodule.json" | jq '. | length')

function clone_to_project() {
    selectIndex=$1
    for((index=0;index<REPOSITORIES_NUM;index++))
    do
        local_module=$(cat "submodule.json" | jq -r .[$index].LOCAL_MODULE_NAME)
        module_name=$(cat "submodule.json" | jq -r .[$index].MODULE_NAME)
        if [ ! $selectIndex = "" ]; then
            if [ ! "${selectIndex}" = "${local_module}" ]; then
                #如果不是all，跳出
                if [ ! "${selectIndex}" = "all" ]; then
                    echo "需要的是${selectIndex}，当前是${local_module}"
                    continue
                fi
            fi
        else
            echo "不加任何参数为更新没有的"
            #判断当前是否有这个文件夹
            if [ -d ${local_module} ]; then
                continue
            fi
        fi
        # 先删除
        rm -rf $local_module

        GITPath=$(cat submodule_local_path.json | jq -r .[$index])
        count=$(cat ${GITPath}/$module_name/$module_name".json" | jq '. | length')

        for((i=0;i<count;i++))
        do
            src=$(cat ${GITPath}/$module_name/$module_name".json" | jq -r .[$i].src)
            target=$(cat ${GITPath}/$module_name/$module_name".json" | jq -r .[$i].target)
            
            exlclude_count=$(cat ${GITPath}/$module_name/$module_name".json" | jq -r .[$i].exlclude | jq '. | length')
            exlcludeArr=""
            arr=("")
            for((j=0;j<exlclude_count;j++))
            do
                arr[$j]=$(cat ${GITPath}/$module_name/$module_name".json" | jq -r .[$i].exlclude | jq -r .[$j])
                exlcludeArr=$exlcludeArr" --exclude "${arr[$j]}
            done
            copySrcPath=${GITPath}/$module_name/$src
            if [ -d $copySrcPath ];then
                # 路径
                echo "源="$copySrcPath
                echo "目标="$target
                if [ ! -d $target ];then
                    # 作保护，自动创建目标文件夹
                    echo "目标文件夹不存在，需要创建！"$target
                    mkdir -p $target
                fi
                rsync -av $copySrcPath/ $target
                for((j=0;j<exlclude_count;j++))
                do
                    rmpath=${arr[$j]}
                    rmpathLength=${#rmpath}
                    srcLength=${#src}
                    rmpath=${rmpath:$srcLength:$rmpathLength-$srcLength}
                    rmpath=$target$rmpath
                    if [ -L $rmpath ];then
                        echo "删除链接文件="$rmpath
                        rm -f $rmpath
                    elif [ -d $rmpath ]; then
                        echo "删除文件夹="$rmpath
                        rm -rf $rmpath
                    fi
                    if [ -f  $rmpath ];then
                        rm -f $rmpath
                    elif [ ! -d $rmpath ];then
                        # 创建文件夹
                        mkdir $rmpath
                    fi
                done
            elif [ -f $copySrcPath ];then
                # 文件
                cp $copySrcPath $target
            fi
            
        done
        add_commit_log $GITPath $module_name $local_module
        settings_gradle=$(cat settings.gradle)
        if [[ $settings_gradle =~ $local_module ]];then
            echo "包含，不需要做任何操作！"
        else
            echo "追加子模块"
            echo "include ':$local_module'" >> settings.gradle
        fi

    done
}

# 采用SSH协议克隆仓库到本地。
function update_with_ssh() {
    current_dir=`pwd`/`dirname $0`
    echo $current_dir
    
    selectIndex=$1
	for((i=0; i<$REPOSITORIES_NUM; i++))
	do
        local_module=$(cat "submodule.json" | jq -r .[$i].LOCAL_MODULE_NAME)
        echo "yjz="${local_module}
        module_name=$(cat "submodule.json" | jq -r .[$i].MODULE_NAME)
        if [ ! $selectIndex = "" ]; then
            if [ ! "${selectIndex}" = "${local_module}" ]; then
                #如果不是all，跳出
                if [ ! "${selectIndex}" = "all" ]; then
                    echo "需要的是${selectIndex}，当前是${local_module}"
                    continue
                fi
            fi
        else
            echo "不加任何参数为更新没有的---"${local_module}
            #判断当前是否有这个文件夹
            if [ -d ${local_module} ]; then
                echo "继续"
                continue
            fi
        fi
        
        GITPath=$(cat submodule_local_path.json | jq -r .[$i])
        #判断当前是否有这个文件夹
        TARGET_BRANCHES=$(cat "submodule.json" | jq -r .[$i].TARGET_BRANCHES)
        echo "yjz"$TARGET_BRANCHES
        if [ ! -d ${GITPath}/${module_name} ]; then
            SSH_REPOSITORIES=$(cat "submodule.json" | jq -r .[$i].SSH_REPOSITORIES)
            git clone $SSH_REPOSITORIES ${GITPath}/${module_name}
            echo "install ${module_name} $TARGET_BRANCHES"
        fi
        pushd $current_dir
        
		cd ${GITPath}/${module_name}
		if [ -d ".git" ]; then
			echo -e "\033[32m""<-----------------------Start update  "${module_name}"----------------------->""\033[0m"
			git checkout $TARGET_BRANCHES
			git pull
			echo -e "\033[32m""<-----------------------Finish update "${module_name}"----------------------->""\033[0m"
        else
            echo "${module_name}模块不存在于${GITPath}下，请检查路径或模块名是否正确！！！"
            continue
		fi
        
		popd
        clone_to_project $selectIndex
	done

	echo `dirname $0`
}

function add_commit_log() {
    GITPath=$1
    module_name=$2
    local_module=$3
    FILENAME="commit.json"

    pushd $current_dir
        cd $GITPath/$module_name
        GITCOMMITLOG=$(git show -s)
        #GITCOMMITLOG=$(git show -s --format=%H)
    popd
    
    pushd $current_dir
        cd $local_module
        echo $GITCOMMITLOG > commit.txt
    popd
        
#    echo $STR
#    target=$(cat ${FILENAME} | jq -r .$module_name)
##    if [[ $target = "null" ]]; then
 ##       JSONSTR=$(cat ${FILENAME})
  #      JSONSTR=${JSONSTR:0:${#JSONSTR}-1}",\""$module_name"\": \""$GITCOMMITLOG"\"}"
  #      echo $JSONSTR
  #      echo $JSONSTR > ${FILENAME}
  #      cat ${FILENAME} | jq . > ${FILENAME}
  #  else
  #      cat $FILENAME |
  #      jq 'to_entries |
  #           map(if .key == "FilterOnline_GL"
  #              then . + {"value": $STR}
  #              else .
  #              end
  #             ) |
  #          from_entries'
  #  fi
}

update_with_ssh $MODULE
