<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">

<plist>
  <array>
    <dict>
      <key>CustomParamDict</key>
      <dict>
        <key>EyelidRealtimeModelType</key>
        <integer>-1</integer>
      </dict>
      <key>SpecialFacelift</key>
      <integer>1</integer>
      <key>FacePart</key>
      <array>
        <dict>
          <key>Type</key>
          <string>FaceliftV2</string>
          <key>SwapPoint</key>
          <string>1</string>
          <key>MaskPath</key>
          <array>
            <!--脸型  Start-->
            <!--瘦脸  Start-->
            <dict>
              <key>Path</key>
              <string>face/shoulian/shoulianz.png</string>
              <key>Rectangle</key>
              <string>13,85,254,172;</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/shoulian/shoulianf.png</string>
              <key>Rectangle</key>
              <string>15,72,252,184;</string>
            </dict>
            <!--瘦脸  End  -->
            <!--脸宽 正向 窄  Start-->
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z1/l.png</string>
              <key>Rectangle</key>
              <string>22,78,120,176;</string>
              <key>ChildSlider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z1/r.png</string>
              <key>Rectangle</key>
              <string>142,78,119,176;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z2/l.png</string>
              <key>Rectangle</key>
              <string>22,81,120,71;</string>
              <key>ChildSlider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z2/r.png</string>
              <key>Rectangle</key>
              <string>142,81,119,71;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--脸宽 正向 窄  End  -->
            <!--脸宽 反向 宽  Start-->
            <dict>
              <key>Path</key>
              <string>face/liankuan/F/l.png</string>
              <key>Rectangle</key>
              <string>22,73,120,181;</string>
              <key>ChildSlider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/F/r.png</string>
              <key>Rectangle</key>
              <string>142,73,119,181;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--脸宽 反向 宽  End  -->
            <!--颌骨 正向 小 Start-->
            <dict>
              <key>Path</key>
              <string>face/hegu/Z/l.png</string>
              <key>Rectangle</key>
              <string>48,154,93,63;</string>
              <key>ChildSlider</key>
              <string>113</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/hegu/Z/r.png</string>
              <key>Rectangle</key>
              <string>141,154,93,63;</string>
              <key>ChildSlider</key>
              <string>114</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--颌骨 正向 小 End  -->
            <!--颌骨 反向 大 Start-->
            <dict>
              <key>Path</key>
              <string>face/hegu/F/l.png</string>
              <key>Rectangle</key>
              <string>45,144,93,68;</string>
              <key>ChildSlider</key>
              <string>113</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/hegu/F/r.png</string>
              <key>Rectangle</key>
              <string>148,144,94,68;</string>
              <key>ChildSlider</key>
              <string>114</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--颌骨 反向 大 End  -->
            <!--颧骨 正向 窄 Start-->
            <dict>
              <key>Path</key>
              <string>face/quangu/Z/l.png</string>
              <key>Rectangle</key>
              <string>47,115,92,55;</string>
              <key>ChildSlider</key>
              <string>111</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/quangu/Z/r.png</string>
              <key>Rectangle</key>
              <string>139,115,92,55;</string>
              <key>ChildSlider</key>
              <string>112</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--颧骨 正向 窄 End-->
            <!--颧骨 负向 宽 Start-->
            <dict>
              <key>Path</key>
              <string>face/quangu/F1/l.png</string>
              <key>Rectangle</key>
              <string>53,116,86,53;</string>
              <key>ChildSlider</key>
              <string>111</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/quangu/F1/r.png</string>
              <key>Rectangle</key>
              <string>139,116,87,53;</string>
              <key>ChildSlider</key>
              <string>112</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/quangu/F2/l.png</string>
              <key>Rectangle</key>
              <string>19,113,119,63;</string>
              <key>ChildSlider</key>
              <string>111</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/quangu/F2/r.png</string>
              <key>Rectangle</key>
              <string>138,113,120,63;</string>
              <key>ChildSlider</key>
              <string>112</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--颧骨 负向 宽 End-->
            <!--下巴 正向  短 Start-->
            <dict>
              <key>Path</key>
              <string>face/xiaba/0_1_140659887590832.png</string>
              <key>Rectangle</key>
              <string>111,188,64,52;</string>
            </dict>
            <!--下巴 正向  短 End  -->
            <!--下巴 反向  长 Start-->
            <dict>
              <key>Path</key>
              <string>face/xiaba/0_1_140274698754504.png</string>
              <key>Rectangle</key>
              <string>74,180,136,78;</string>
            </dict>
            <!--发际线 正向  短 Start-->
            <dict>
              <key>Path</key>
              <string>face/fajixian/0_1_140721847956912.png</string>
              <key>Rectangle</key>
              <string>16,25,245,117;</string>
              <key>GenerateType</key>
              <integer>0</integer>
            </dict>
            <!--发际线 正向  短 End  -->
            <!--发际线 反向  长  Start-->
            <dict>
              <key>Path</key>
              <string>face/fajixian/0_1_140721857374640.png</string>
              <key>Rectangle</key>
              <string>16,24,245,118;</string>
              <key>GenerateType</key>
              <integer>0</integer>
            </dict>
            <!--发际线 反向  长  End  -->
            <!--脸型  End-->
          </array>
          <key>Configure</key>
          <array>
            <!--脸型  Start-->
            <!--瘦脸 正向  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>0</integer>
              <key>LiftControlType</key>
              <integer>1</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--瘦脸 正向  End  -->
            <!--瘦脸 负向  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>1</integer>
              <key>LiftControlType</key>
              <integer>1</integer>
              <key>ControlRange</key>
              <string>-1.0,0.00</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--瘦脸 负向 End  -->
            <!--脸宽 正向 窄  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>2</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>3</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>4</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>5</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--脸宽 正向 窄  End  -->
            <!--脸宽 反向 宽  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>6</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1,0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>7</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1,0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--脸宽 反向 宽  End  -->
            <!--颌骨 正向 小 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>8</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>9</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--颌骨 正向 小 End  -->
            <!--颌骨 反向 大 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>10</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>11</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--颌骨 反向 大 End  -->
            <!--颧骨 正向 窄 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>12</integer>
              <key>LiftControlType</key>
              <integer>55</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>13</integer>
              <key>LiftControlType</key>
              <integer>55</integer>
              <key>ControlRange</key>
              <string>0.00,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--颧骨 正向 窄 End-->
            <!--颧骨 负向 宽 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>14</integer>
              <key>LiftControlType</key>
              <integer>55</integer>
              <key>ControlRange</key>
              <string>-1.0,0.00</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>15</integer>
              <key>LiftControlType</key>
              <integer>55</integer>
              <key>ControlRange</key>
              <string>-1.0,0.00</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>16</integer>
              <key>LiftControlType</key>
              <integer>55</integer>
              <key>ControlRange</key>
              <string>-1.00,0.00</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>17</integer>
              <key>LiftControlType</key>
              <integer>55</integer>
              <key>ControlRange</key>
              <string>-1.00,0.00</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--颧骨 负向 宽 End-->
            <!--下巴 正向  短 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>18</integer>
              <key>LiftControlType</key>
              <integer>2</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--下巴 正向  短 End  -->
            <!--下巴 反向  长 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>19</integer>
              <key>LiftControlType</key>
              <integer>2</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--下巴 反向  长 End  -->
            <!--发际线 正向  短 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>20</integer>
              <key>LiftControlType</key>
              <integer>13</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--发际线 正向  短 End  -->
            <!--发际线 反向  长  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>21</integer>
              <key>LiftControlType</key>
              <integer>13</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--发际线 反向  长  End  -->
            <!--脸型  End-->
          </array>
        </dict>
      </array>
    </dict>
  </array>
</plist>
