// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        maven("https://maven.meitu.city/")
        maven("https://storage.googleapis.com/r8-releases/raw")
        maven("https://artifact.bytedance.com/repository/AwemeOpenSDK")
        google()
        mavenCentral()
        maven("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea")
        maven("https://artifact.bytedance.com/repository/pangle")
        maven("https://oss.sonatype.org/content/repositories/public")
    }

    dependencies {
        classpath(PluginsPath.AGP)
        classpath(PluginsPath.KGP)

        // CI 打包插件
        classpath("com.meitu.gradle:easymaven:1.3.3")

        // Meitu 基础Gradle插件.
        classpath("com.meitu.gradle:android-base:1.2.0")

        // 渠道打包发布插件
        classpath("com.meitu.gradle:eva:2.0.1")

        // Lint静态代码检测。
        classpath("com.meitu.gradle:quality:0.7.2")

        // gms插件
        classpath("com.google.gms:google-services:4.3.15")

        // Matrix插件
        classpath("com.tencent.matrix:matrix-gradle-plugin:2.1.0") {
            isChanging = true
        }
        // bundle包的bundletools，添加完整性监测需要。
        classpath("com.android.tools.build:bundletool:0.10.3")

        // 精准测试插件
        //classpath ("com.meitu.autotest.kotrace:plugin:0.3.4")

        // 切面注入
        classpath("com.meitu.library.mtajx:plugin:1.0.9-dev-9")

        //TheRouter
        classpath(libs.the.router.plugin)

        classpath("com.didiglobal.booster:booster-gradle-plugin:4.16.3")
//        classpath("com.didiglobal.booster:booster-transform-r-inline:4.16.3")

        //razor
        classpath("com.meitu.razor:gradle-plugin:0.7.8")

        // app cia
        classpath(libs.appcia.plugin)
        classpath(libs.appcia.omnibus.plugin)
    }
}

tasks.register("clean", Delete::class) {
    delete(rootProject.buildDir)
}

plugins {
    id("com.meitu.quality.lint-all") apply false
    id("com.google.firebase.crashlytics") version "2.9.9" apply false

    // RouterBridge插件
    id("com.pixocial.router.bridge")
}

gradle.taskGraph.whenReady {
    tasks.find { it.name.contains("meituMergeLintReports") }?.let {
        it.enabled = false
        println("关闭Lint检测，不然错误太多，插件Bug")
    }
}

configurations.all {
    // check for updates every build
    resolutionStrategy.cacheDynamicVersionsFor(4, "hours")
    resolutionStrategy.cacheChangingModulesFor(1, "minutes")
}

/* 项目名称，用于生产对应输出目录，简明固定不变，例如：BeautyPlus。*/
extra["PROJECT_NAME"] = "BeautyPlus"
/* 项目应用版本，例如：1.0.0。*/
extra["VERSION_NAME"] = AppCoordinates.VERSION_NAME
/* 项目应用版本Code，例如：100。*/
extra["VERSION_CODE"] = AppCoordinates.VERSION_CODE