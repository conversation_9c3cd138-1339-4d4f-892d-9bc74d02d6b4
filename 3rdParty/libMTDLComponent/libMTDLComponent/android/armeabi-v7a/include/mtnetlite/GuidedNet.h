#pragma once

#include "BaseNet.h"

namespace mtcvlite
{
	class GuidedNet
	{
	public:
		GuidedNet();
		~GuidedNet();

		std::shared_ptr<BaseNet> GetSharpNet() { return m_spSharpNet; }
		std::shared_ptr<BaseNet> GetGuidedNet() { return m_spGuidedNet; }
		void ClearIOData();


		//输入输出格式均为BGRA
		bool Forward(
			const Mat& matImage640,
			const Mat& matSharp640, 
			const Mat& matImage, 
			Mat& matBeauty, 
			const int nBeautyWidth = 0, const int nBeautyHeight = 0,
			const float fMinNorm = -1.0f, const float fMaxNorm = 1.0f);

		bool Forwards(const std::vector<Mat>& vecMatImage640,
			const std::vector<Mat>& vecMatSharp640, 
			const std::vector<Mat>& vecMatImage,
			std::vector<Mat>& vecMatBeauty, 
			const int nBeautyWidth = 0, const int nBeautyHeight = 0,
			const float fMinNorm = -1.0f, const float fMaxNorm = 1.0f);

		std::shared_ptr<BaseNet> m_spSharpNet;
		std::shared_ptr<BaseNet> m_spGuidedNet;
	};

} // end namespace ICL