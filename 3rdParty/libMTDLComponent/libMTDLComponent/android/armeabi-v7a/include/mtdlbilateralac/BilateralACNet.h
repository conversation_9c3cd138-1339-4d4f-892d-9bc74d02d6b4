#pragma once
#include <memory>
#include "mtnetlite/BaseNet.h"
namespace mtcvlite
{
	class BaseNet;
	
	/*
	
	*/

	enum BilaADCMode{
		MT_ADC_LOW_FAST_SP,   //灰度方向插值时提前均分，减少draw的插值次数
		/*MT_ADC_LOW_FAST, 弃用*/
		MT_ADC_LOW_MEDIUM, 
		MT_ADC_HIGH
		//MT_ADC_FLOAT //Todo: 采用glcaffe跑前向，采用浮点纹理，提高插值效率
	};

	class BilateralACNet
	{
	public:
		BilateralACNet(const int nComputeMode = MT_ADC_HIGH);
		virtual ~BilateralACNet();

		//void Init();

		void GetNetInputSize(int& nInputHeight, int& nInputWidth);

		/*
		* @param [pImage]         input: 
		* @param [nWidth]         input: 
		* @param [nHeight]        input: 
		*/

		void Run(unsigned char* pImage, const int nWidth, const int nHeight);


		inline std::shared_ptr<BaseNet>& GetNet() { return m_spADC; }

	protected:
		std::shared_ptr<BaseNet> m_spADC;
		int m_nDepth;
		int m_nComputeMode;

	};

} // end namespace mtcvlite
