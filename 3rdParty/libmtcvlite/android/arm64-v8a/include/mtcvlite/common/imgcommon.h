
#ifndef _MT_CV_LITE_IMG_COMMON_H_
#define _MT_CV_LITE_IMG_COMMON_H_

// #ifdef _MSC_VER
// #	ifdef __cplusplus
// #		ifdef MT_STATIC_LIB
// #			define MTCVLITE_SDK_API  extern "C"
// #		else
// #			ifndef SDK_SRC
// #				ifdef SDK_EXPORTS
// #					define MTCVLITE_SDK_API extern "C" __declspec(dllexport)
// #				else
// #					define MTCVLITE_SDK_API extern "C" __declspec(dllimport)
// #				endif
// #			else
// #				define MTCVLITE_SDK_API
// #           endif
// #		endif
// #	else
// #		ifdef MT_STATIC_LIB
// #			define MTCVLITE_SDK_API
// #		else
// #			ifdef SDK_EXPORTS
// #				define MTCVLITE_SDK_API __declspec(dllexport)
// #			else
// #				define MTCVLITE_SDK_API __declspec(dllimport)
// #			endif
// #		endif
// #	endif
// #else /* _MSC_VER */
// #	ifdef __cplusplus
// #		ifdef SDK_EXPORTS
// #			define MTCVLITE_SDK_API extern "C" __attribute__((visibility ("default")))
// #		else
// #			define MTCVLITE_SDK_API extern "C"
// #		endif
// #	else
// #		ifdef SDK_EXPORTS
// #			define MTCVLITE_SDK_API __attribute__((visibility ("default")))
// #		else
// #			define MTCVLITE_SDK_API
// #		endif
// #	endif
// #endif

#define MTCVLITE_SDK_API

namespace mtcvlite
{
#ifndef uint8_t
	typedef unsigned char uint8_t;
#endif

#define MT_PIX_CN_SHIFT   4
#define MT_PIX_CN_MASK          ((8 - 1) << MT_PIX_CN_SHIFT)

#define MT_PIX_FMT_COLOR_SHIFT 14
#define MT_PIX_FMT_COLOR (1<<MT_PIX_FMT_COLOR_SHIFT)

#define MT_PIX_CN_COLOR_SHIFT 10
//#define MT_PIX_CN_COLOR (1<<MT_PIX_FMT_COLOR_SHIFT)

#define MT_PIX_TYPE(fmt_color, cn_color, cn) ((fmt_color << MT_PIX_FMT_COLOR_SHIFT) + (cn_color << MT_PIX_CN_COLOR_SHIFT) + (((cn) - 1) << MT_PIX_CN_SHIFT))
#define MT_PIX_CN(flags)        ((((flags) & MT_PIX_CN_MASK) >> MT_PIX_CN_SHIFT) + 1)

#define MT_PIX_8U   0
#define MT_PIX_8S   1
#define MT_PIX_16U  2
#define MT_PIX_16S  3
#define MT_PIX_32S  4
#define MT_PIX_32F  5
#define MT_PIX_64F  6
#define MT_PIX_USRTYPE1 7

#define MT_PIX_RGBA 0
#define MT_PIX_RGB 1
#define MT_PIX_BGRA 2
#define MT_PIX_BGR 3

typedef enum {
    PIX_FMT_GRAY8U,     ///< Y    1        8bpp ( 单通道8bit灰度像素 )
    PIX_FMT_YUV420P,    ///< YUV  4:2:0   12bpp ( 3通道, 一个亮度通道, 另两个为U分量和V分量通道, 所有通道都是连续的 )
    PIX_FMT_NV12,       ///< YUV  4:2:0   12bpp ( 2通道, 一个通道是连续的亮度通道, 另一通道为UV分量交错 )
    PIX_FMT_NV21,       ///< YUV  4:2:0   12bpp ( 2通道, 一个通道是连续的亮度通道, 另一通道为VU分量交错 )
	PIX_FMT_COLOR = MT_PIX_FMT_COLOR,
	PIX_FMT_BGRA8U = MT_PIX_TYPE(1, MT_PIX_BGRA, 4) + MT_PIX_8U,     ///< BGRA 8:8:8:8 32bpp ( 4通道32bit BGRA 像素 )
	PIX_FMT_RGBA8U = MT_PIX_TYPE(1, MT_PIX_RGBA, 4) + MT_PIX_8U,     ///< BGRA 8:8:8:8 32bpp ( 4通道32bit RGBA 像素 )
	PIX_FMT_BGR8U = MT_PIX_TYPE(1, MT_PIX_BGR, 3) + MT_PIX_8U,      ///< BGR  8:8:8   24bpp ( 3通道24bit BGR 像素 ) 不建议使用
	PIX_FMT_RGB8U = MT_PIX_TYPE(1, MT_PIX_RGB, 3) + MT_PIX_8U,      ///< RGB  8:8:8   24bpp ( 3通道24bit RGB 像素 ) 不建议使用
	PIX_FMT_RGBA16U = MT_PIX_TYPE(1, MT_PIX_RGBA, 4) + MT_PIX_16U,
	PIX_FMT_BGRA16U = MT_PIX_TYPE(1, MT_PIX_BGRA, 4) + MT_PIX_16U
}pixel_format;

typedef struct image_t
{
    union{
        unsigned char *y;        //y通道数据、RGB类型数据、GRAY
        unsigned char* data;
    };
    unsigned char *u;            //u通道数据
    unsigned char *v;            //v通道数据
    union{
        int ystride;             //y通道stride
        int stride;
    };
    int ustride;                 //u通道的stride
    int vstride;                 //v通道的stride
    
    int width;                   //图片宽度
    int height;                  //图片高度
    int orientation;             //exif方向
    pixel_format format;
    int* refcount;
}image_t;

MTCVLITE_SDK_API void
init_image(image_t& img);

MTCVLITE_SDK_API image_t
create_image();

MTCVLITE_SDK_API image_t
create_image(int _format, int _width, int _height, uint8_t* _data, int _orientation = 1);

MTCVLITE_SDK_API image_t
create_image_from_gray(int _width, int _height, uint8_t* _data, int _orientation = 1, int _stride = 0);

MTCVLITE_SDK_API image_t
create_image_from_bgra(int _width, int _height, uint8_t* _data, int _orientation = 1, int _stride = 0);

MTCVLITE_SDK_API image_t
create_image_from_bgr(int _width, int _height, uint8_t* _data, int _orientation = 1, int _stride = 0);

MTCVLITE_SDK_API image_t
create_image_from_rgba(int _width, int _height, uint8_t* _data, int _orientation = 1, int _stride = 0);

MTCVLITE_SDK_API image_t
create_image_from_rgb(int _width, int _height, uint8_t* _data, int _orientation = 1, int _stride = 0);

MTCVLITE_SDK_API image_t
create_image_from_nv12(int _width, int _height, uint8_t* _py, uint8_t* _puv, int _orientation = 1, int _ystride = 0, int _uvstride = 0);

MTCVLITE_SDK_API image_t
create_image_from_nv21(int _width, int _height, uint8_t* _py, uint8_t* _pvu, int _orientation = 1, int _ystride = 0, int _vustride = 0);

MTCVLITE_SDK_API image_t
create_image_from_i420(int _width, int _height, uint8_t* _py, uint8_t* _pu, uint8_t* _pv, int _orientation = 1, int _ystride = 0, int _ustride = 0, int _vstride = 0);


}

#endif