#ifndef _MT_CV_LITE_PsNaturalSaturationUtil_h_
#define _MT_CV_LITE_PsNaturalSaturationUtil_h_


// #if defined(_WIN32) || defined(_WIN32_) || defined(WIN32) || defined(_WIN64_) || defined(WIN64) || defined(_WIN64)
// #ifdef MTAPILIB
// #define MTCORE_EXPORT _declspec(dllexport)
// #else
// #define MTCORE_EXPORT  _declspec(dllimport)
// #endif
// #else
// #define MTCORE_EXPORT
// #endif

namespace mtcvlite
{

	class /*MTCORE_EXPORT */PSNaturalSaturation
	{
	public:
		PSNaturalSaturation();
		~PSNaturalSaturation();
		bool  ChangeNaturalSaturation(unsigned char* pImage, int nWidth, int nHeight, int dwParam);
	private:
		void NaturalSaturation(float &vmin, float &vmax, float vhue, float newSat);
		void HueValueRange2Rgb(float vmin, float vmax, float vhue, unsigned char *ptrImage, /*PBYTE pRecoverR*/float *recover);
		void Rgb2HueValueRange(float r, float g, float b, float &vmin, float &vmax, float &vhue);

	};

}
#endif