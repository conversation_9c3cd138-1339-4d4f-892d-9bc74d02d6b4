/********************************************************************************************************************************
* FleckCleanFill 消除人皮肤上的斑痘
* Copyright (c) 2015-2020年 MEITU. All rights reserved.
*
* @version: 1.1
*
* @author:  lym
*
* @date: 2015-09-07
*
* @note:
*
* @change:  dqplum  2020-09-09
         整理跟mtcvlite兼容的接口，便于兼容非脸部皮肤的斑痘祛除，允许传斑痘框或其他矩形框进行消除
         dqplum  2020-09-11
         允许中间结果的输出
*********************************************************************************************************************************/

#pragma once

#include "FleckCleanDefine.h"
#include "mtcvlite/mtcvdef.h"

#include "mtcvlite/base/Mat/mat.h"

namespace mtcvlite
{
namespace fc
{

//根据指定的框修复对应的区域
MT_CV_EXPORT bool FillFleckRegionByRect(
                      Mat& matImage,                    //原图，4通道rgba格式，
                      const Mat& matSkin,               //皮肤mask，单通道
                      const Rect* pFleckRect,           //斑痘矩形框
                      const int nFleckRect,
                      FleckRectInfo* pRealValidFRect,   //返回斑痘实际的矩形框和对应的个数
                      int& nRealValidFleckRect,         //返回斑痘对应的个数，当初始为0，表示需重新校正斑痘矩形框
                      const int nDetectPreWidth = -1,   //采用小图处理的尺寸
                      const int nDetectPreHeight = -1,
                      const float* pfFacePoints = 0,
                      const int nFace = 0, const int nFacePoints = 0,
                      Mat* pmatTagSkin = 0
                      );

MT_CV_EXPORT bool FillFleckRegion(Mat& matImage, const Mat& matSkin,
                                  const Mat& matTagSkin, const Mat& matTagFleckID,
                                  const Rect* pTagFleckRect, const int nTagFleckRect,
                                  FleckRectInfo* pRealValidFRects);

/////////五官区域保护并排除人脸区域之外的区域
MT_CV_EXPORT bool ProtectFaceComponentRegion(
                                             Mat& matMask,
                                             const float* pfFacePoints, const int nFacePoints);

} //end namespace fc

} //end namespace mtvlite
