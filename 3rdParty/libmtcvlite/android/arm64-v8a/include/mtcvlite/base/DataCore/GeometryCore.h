/*****************************************************************************
 * GeometryCore.h
 ***************************************************************************** 
 *
 * 模块: iCloudLibrary/DataCore
 * 作者: Qidong Li       
 * 描述: 定义N维数据类型，包括2D，3D，ND
 *
 * (c) Copyright by iCloudLibrary Development Group 
 *                 
 * 
 *****************************************************************************
 *
 *参考文献: 
 *
 *
 *****************************************************************************
 *
 * $Revision:  $
 * $Date: 
 *
 */
#pragma once

#include "TypeBase.h"
#include <math.h>

#define ICL_GEOMETRY_CORE_UNIT

namespace mtcvlite
{

namespace ICL
{
/***************************************************************** 
** 数据结构: template <typename Tp, int N> class Point_nD
** 使用说明: n维坐标点，相应扩展到2维、3维
** 版本说明: V1.0	    
** 修改说明:	
******************************************************************/
template <typename Tp, int N>
class Point_nD;

template <typename Tp>
class Point_nD<Tp, 3>;

template <typename Tp>
class Point_nD<Tp, 2>;

typedef Point_nD<dtype_t, 4> Point4D;
typedef Point_nD<double, 4> Point4Dd;
typedef Point_nD<float, 4> Point4Df;

/***************************************************************** 
** 数据结构: template <typename Tp, int N> class Vector_nD
** 使用说明: n维向量，相应扩展到2维、3维
** 版本说明: V1.0	    
** 修改说明:	
******************************************************************/
template <typename Tp, int N>
class Vector_nD;

template <typename Tp>
class Vector_nD<Tp, 3>;

template <typename Tp>
class Vector_nD<Tp, 2>;

typedef Vector_nD<dtype_t, 4> Vector4D;
typedef Vector_nD<double, 4> Vector4Dd;
typedef Vector_nD<float, 4> Vector4Df;

/***************************************************************** 
** 数据结构: template <typename Tp, int N> class UnVec_nD
** 使用说明: n维单位向量，相应扩展到2维、3维
** 版本说明: V1.0	    
** 修改说明:	
******************************************************************/
template <typename Tp, int N>
class UnVec_nD;

template <typename Tp>
class UnVec_nD<Tp, 3>;

template <typename Tp>
class UnVec_nD<Tp, 2>;

/***************************************************************** 
** 数据结构: template <typename Tp, int N> class PointNor_nD
** 使用说明: 带n维单位向量或法矢的n维坐标点，相应扩展到2维、3维
** 版本说明: V1.0	    
** 修改说明:	
******************************************************************/
template <typename Tp, int N>
class PointNor_nD;

template <typename Tp>
class PointNor_nD<Tp, 3>;

template <typename Tp>
class PointNor_nD<Tp, 2>;


/***************************************3D 几何坐标操作***************************************/
//! Geometry2D Operator
//! 坐标点相加，可用于求和、点集的中心等
template <typename Tp>
Point_nD<Tp, 3> operator + (const Point_nD<Tp, 3> &pt3d0, const Point_nD<Tp, 3> &pt3d1); 
//! 表示坐标点平移变换(变换以向量表示)
template <typename Tp>
Point_nD<Tp, 3> operator + (const Point_nD<Tp, 3> &pt3d, const Vector_nD<Tp, 3> &vec3d);
//! 表示坐标点正向平移一个单位向量
template <typename Tp>
Point_nD<Tp, 3> operator + (const Point_nD<Tp, 3> &pt3d, const UnVec_nD<Tp, 3> &uv3d); 
//! 2个坐标点相减
template <typename Tp>
Point_nD<Tp, 3> operator + (const Point_nD<Tp, 3> &pt3d, const Tp pc);
//! 表示坐标点同时加上一个常数
template <typename Tp>
Point_nD<Tp, 3> operator - (const Point_nD<Tp, 3> &pt3d0, const Point_nD<Tp, 3> &pt3d1);
//! 表示坐标点平移变换(变换以向量表示)
template <typename Tp>
Point_nD<Tp, 3> operator - (const Point_nD<Tp, 3> &pt3d, const Vector_nD<Tp, 3> &vec3d);
//! 表示坐标点负向平移一个单位向量
template <typename Tp>
Point_nD<Tp, 3> operator - (const Point_nD<Tp, 3> &pt3d, const UnVec_nD<Tp, 3> &uv3d); 
//! 该点缩放后得到的新点，不推荐使用，尽量用*
template <typename Tp>
Point_nD<Tp, 3> operator / (const Point_nD<Tp, 3> &pt3d, const Tp dc);
//! 该点缩放后得到的新点，推荐使用，尽量不用/
template <typename Tp>
Point_nD<Tp, 3> operator * (const Point_nD<Tp, 3> &pt3d, const Tp sc);
//! 原点到该点之间的向量往某个方向（以单位向量表示）的投影值
template <typename Tp>
Tp  operator * (const Point_nD<Tp, 3> &pt3d, const UnVec_nD<Tp, 3> &uv3d);
//! 判断两个点是否为同一个点
template <typename Tp>
bool    operator == (const Point_nD<Tp, 3> &pt3d0, const Point_nD<Tp, 3> &pt3d1); 

template <typename Tp>
void operator += (Point_nD<Tp, 3> &self_pt3d, const Point_nD<Tp, 3> &pt3d);

template <typename Tp>
void operator += (Point_nD<Tp, 3> &self_pt3d, const Vector_nD<Tp, 3> &vec3d);
//! 表示坐标点自身正向平移一个单位向量
template <typename Tp>
void operator += (Point_nD<Tp, 3> &self_pt3d, const UnVec_nD<Tp, 3> &uv3d); 
//! 表示坐标点自身的三个坐标值同时加上一个常数
template <typename Tp>
void operator += (Point_nD<Tp, 3> &self_pt3d, const Tp pc); 

template <typename Tp>
void operator -= (Point_nD<Tp, 3> &self_pt3d, const Point_nD<Tp, 3> &pt3d);

template <typename Tp>
void operator -= (Point_nD<Tp, 3> &self_pt3d, const Vector_nD<Tp, 3> &vec3d);
//! 表示坐标点自身负向平移一个单位向量
template <typename Tp>
void operator -= (Point_nD<Tp, 3> &self_pt3d, const UnVec_nD<Tp, 3> &uv3d); 

template <typename Tp>
void operator /= (Point_nD<Tp, 3> &self_pt3d, const Tp sc);	

template <typename Tp>
void operator *= (Point_nD<Tp, 3> &self_pt3d, const Tp sc);

template <typename Tp>
Vector_nD<Tp, 3>  operator+(const Vector_nD<Tp, 3>& vec3d0, const Vector_nD<Tp, 3>& vec3d1);

template <typename Tp>
Vector_nD<Tp, 3>  operator+(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d);

template <typename Tp>
Vector_nD<Tp, 3>  operator+(const Vector_nD<Tp, 3>& vec3d, const Tp& pc);

template <typename Tp>
Vector_nD<Tp, 3>  operator-(const Vector_nD<Tp, 3>& vec3d0, const Vector_nD<Tp, 3>& vec3d1);

template <typename Tp>
Vector_nD<Tp, 3>  operator-(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d);
//! 叉乘
template <typename Tp>
Vector_nD<Tp, 3>  operator^(const Vector_nD<Tp, 3>& vec3d0, const Vector_nD<Tp, 3>& vec3d1);
//! 与单位向量的叉乘
template <typename Tp>
Vector_nD<Tp, 3>  operator^(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d);
//! 与一个三维向量的点乘，内积
template <typename Tp>
Tp    operator*(const Vector_nD<Tp, 3>& vec3d0, const Vector_nD<Tp, 3>& vec3d1); 
//! 与一个单位向量的点乘，内积
template <typename Tp>
Tp    operator*(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d); 

template <typename Tp>
Vector_nD<Tp, 3>  operator*(const Vector_nD<Tp, 3>& vec3d, const Tp& sc);

template <typename Tp>
Vector_nD<Tp, 3>  operator / (const Vector_nD<Tp, 3>& vec3d, const Tp& sc);
//! 2个3维单位向量求和
template <typename Tp>
Vector_nD<Tp, 3>   operator + (const UnVec_nD<Tp, 3> &uv3d0, const UnVec_nD<Tp, 3>& uv3d1 );

template <typename Tp>
Vector_nD<Tp, 3>   operator + (const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d );

template <typename Tp>
Vector_nD<Tp, 3>   operator - (const UnVec_nD<Tp, 3> &uv3d0, const UnVec_nD<Tp, 3>& uv3d1);

template <typename Tp>
Vector_nD<Tp, 3>   operator - (const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d );
//! 与一个单位向量的叉乘
template <typename Tp>
Vector_nD<Tp, 3>   operator ^ (const UnVec_nD<Tp, 3> &uv3d0, const UnVec_nD<Tp, 3>& uv3d1);
//! 与一个三维向量的叉乘
template <typename Tp>
Vector_nD<Tp, 3>   operator ^ (const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d);
//! 与一个单位向量的点乘，内积
template <typename Tp>
Tp     operator * (const UnVec_nD<Tp, 3> &uv3d0, const UnVec_nD<Tp, 3>& uv3d1);
//! 与一个三维向量的点乘，内积
template <typename Tp>
Tp     operator * (const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d); 

template <typename Tp>
Vector_nD<Tp, 3>   operator * (const UnVec_nD<Tp, 3> &uv3d, const Tp& sc );

template <typename Tp>
Vector_nD<Tp, 3>   operator / (const UnVec_nD<Tp, 3> &uv3d, const Tp& sc );

/***************************************2D 几何坐标操作***************************************/
//! Geometry2D Operator
//! 2个点相加
template <typename Tp>
Point_nD<Tp, 2> operator + (const Point_nD<Tp, 2>& lp, const Point_nD<Tp, 2>& rp);
//! 2个点相减
template <typename Tp>
Point_nD<Tp, 2> operator - (const Point_nD<Tp, 2>& lp, const Point_nD<Tp, 2>& rp);
//! 点乘以一个常数
template <typename Tp>
Point_nD<Tp, 2> operator * (const Point_nD<Tp, 2>& lp, const Tp multip);
//! 点自加一个点
template <typename Tp>
void operator += (Point_nD<Tp, 2>& thispoint, const Point_nD<Tp, 2>& newpoint);
//！点自减一个点
template <typename Tp>
void operator -= (Point_nD<Tp, 2>& thispoint, const Point_nD<Tp, 2>& newpoint);
//! 点自乘一个常数
template <typename Tp>
void operator *= (Point_nD<Tp, 2>& thispoint, const Tp& multip);
//! 表示坐标点平移变换(变换以向量表示)
template <typename Tp>
Point_nD<Tp, 2> operator + (const Point_nD<Tp, 2> &pt2d, const Vector_nD<Tp, 2> &vec2d);
//! 表示坐标点平移变换(变换以向量表示)
template <typename Tp>
Point_nD<Tp, 2> operator - (const Point_nD<Tp, 2> &pt2d, const Vector_nD<Tp, 2> &vec2d);
//! 2个2维向量相减
template <typename Tp>
Vector_nD<Tp, 2> operator - (const Vector_nD<Tp, 2>& vec2d, const Vector_nD<Tp, 2>& vec2d1);
//! 2个2维向量相减
template <typename Tp>
Vector_nD<Tp, 2> operator + (const Vector_nD<Tp, 2>& vec2d, const Vector_nD<Tp, 2>& vec2d1); 
//! 与一个二维向量的点乘，内积
template <typename Tp>
Tp operator * (const Vector_nD<Tp, 2>& vec2d, const Vector_nD<Tp, 2>& vec2d1); 
//! 与一个单位向量的点乘，内积
template <typename Tp>
Tp operator * (const Vector_nD<Tp, 2>& vec2d, const UnVec_nD<Tp, 2>& uv2d);
//! 
template <typename Tp>
Vector_nD<Tp, 2> operator * (const UnVec_nD<Tp, 2>& vec2d, const Tp sc);
/***************************************ND 几何坐标操作***************************************/
//! GeometryND Operator
//! 
template <typename Tp, int N>
Point_nD<Tp, N> operator + (const Point_nD<Tp, N>& lp, const Point_nD<Tp, N>& rp);

}

} // namespace mtcvlite

#include "GeometryCoreND.h"

