/*****************************************************************
* PS USM锐化
*
*
* @version: 1.2
*
* @author:  pxdev(7go)
*
* @date: 2013-04-28
*
* @note: 实现PS版本的USM锐化，高斯模糊模块使用的是stackblur, 可能导致结果有偏差
*
* @change: ver1.1  2013-06-21  7go 增加单通道支持
* @change: ver1.2  2013-12-10  7go 优化，加入查询表计算

*
******************************************************************/


#ifndef MTCV_LITE_UNSHARP_MASK_h
#define MTCV_LITE_UNSHARP_MASK_h

#pragma once

namespace mtcvlite
{
	
// USM锐化
class CUnsharpMask
{
public:
	CUnsharpMask(void);
	~CUnsharpMask(void);

	/*
	@param amount 数量 [1, 500]
	@param radius 半径
	@param threshold 阈值
	@note: 算法内部模糊用stackblur，stackblur同高斯的数值关系在内部进行半径转换，这里接口传入的radius为photoshop的半径
	*/
	bool Run(unsigned char* pImage, int nWidth, int nHeight, int nStride, int amount, float radius, unsigned char threshold);
};

}

#endif // MTDSP_UNSHARP_MASK_h