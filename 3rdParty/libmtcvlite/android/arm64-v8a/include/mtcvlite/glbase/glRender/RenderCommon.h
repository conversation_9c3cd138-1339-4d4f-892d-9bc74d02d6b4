#pragma once

#include <vector>
#include <memory>
#include "RenderDefine.h"

namespace mtcvlite
{

//#define GLERRCHECK() {GLint err = glGetError();if(err!=GL_NO_ERROR){printf("glerror in %s(%d) %d\n",__FILE__,__LINE__,err);}}

void GLERRCHECK();

bool GLNoErrorStatus(const char* module_name = 0);

void GLBindBuffer(const GLuint gVbo = 0);

GLuint GLGetLastVbo();

void GLInitZero();

bool IsSupportGLFloatTexture(int* pnSupportBite = 0);
int GetMaxFragmentUniformVec4s();

void InitTextureParam(TextureParam& texture_param);
void CreateTexture(TextureParam& texture_param, int width, int height,
	GLenum format, GLenum type, GLint interp, void* data, const int float_flag = 32);
void CreateByteTexture(TextureParam& texture_param, int width, int height, GLint interp, void* data = 0);
void CreateByteTexture2(TextureParam& texture_param, int width, int height, bool is_linear = true, void* data = 0);

void CreateFloatTexture(TextureParam& texture_param, int width, int height, GLint interp, 
	void* data = 0, const int tex_bit = 32);

void DeleteTexture(std::shared_ptr<TextureParam>& sp_texture_param);

void DeleteTexture(TextureParam& texture_param);
void DeleteTexture(std::vector<TextureParam>& vec_textures);

bool StoreImage(TextureParam& texture_param, const unsigned char* input);

void DeleteShader(GLuint& shader_id);
void DeleteFramebuffer(GLuint& frame_buffer);
void DeleteBuffer(GLuint& buffer);

bool SameTextureID(const TextureParam& texture_param0, const TextureParam& texture_param1);
bool SameTextureSize(const TextureParam& texture_param0, const TextureParam& texture_param1);

}
