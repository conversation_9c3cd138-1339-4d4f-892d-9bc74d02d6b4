/*****************************************************************
* OptFlowUtilGL, gl版的光流接口
* Copyright (c) 2017 MEITU. All rights reserved.
*
* @version: 1.1
*
* @author:  dqplum
*
* @email:  <EMAIL>
*
* @date: 2017-04-06
*
* @note:
*
* @usage:
*
* @change: 2017-04-14 去除无用的脚本初始化，节省初始化时间
*
******************************************************************/
#ifndef __MT_OPTFLOW_UTIL_GL_H__
#define __MT_OPTFLOW_UTIL_GL_H__

#include <memory>
#include <vector>

#include "OptFlowUtil.h"
#include "mtcvlite/glbase/glRender/RenderDefine.h"

namespace mtcvlite
{
//class Mat;
class RenderBase;

namespace optflow
{
	class DISOpticalFlow;
}

class MT_CV_EXPORT OptFlowUtilGL : public OptFlowUtil
{
public:
	//! nFlowWidth和nFlowHeight，等比例缩放，且控制在320x320范围内，
	//! 目前验证320这个尺度，可减少抖动
	//! 且保证nFlowWidth和nFlowHeight能够整除32，便于neon加速
	/*
	*/
	OptFlowUtilGL(const int nFlowWidth, const int nFlowHeight, const int nFlowMode = PRESET_FAST);
	~OptFlowUtilGL();

	void ResetFlowSizeAndTexture(const int nFlowWidth, const int nFlowHeight);

	//所需执行的线程初始化
	void InitGL();
	//GL初始化的线程里退出
	void ExitGL();

	int Run(
		unsigned char* pPrevYUV,
		unsigned char* pCurYUV,
		const int nUVStart,
		const GLuint gMaskTexture,
		const int nMaskWidth, const int nMaskHeight,
		const float* pfMaskTexCoordTrans,
		const float* pfMaskTexCoordInvTrans,
		const float fThreshold = 4.0f,
		const float fAlpha = 1.0f);

	void CopyTexture(const GLuint gInputTexture,
		const GLuint gOutputTexture,
		const int nOutputWidth,
		const int nOutputHeight,
		const float fAlpha = 1.0f);

	void InterpMask(const GLuint gMaskTexture,
		const int nMaskWidth, const int nMaskHeight,
		const float* pfMaskTexCoordTrans,
		const float* pfMaskTexCoordInvTrans,
		const float fAlpha = 1.0f);

	void InterpMask_s(const GLuint gMaskTexture,
		const int nMaskWidth, const int nMaskHeight,
		const float* pfMaskTexCoordTrans,
		const float* pfMaskTexCoordInvTrans,
		const float fAlpha = 1.0f);

	void BindFrameBuffer(const GLuint gMaskTexture,
		const int nMaskWidth, const int nMaskHeight);

protected:
	OptFlowUtilGL();

	void ConvertFlow();
	void ConvertFlow_s();
	void FormTexture();
protected:
	std::shared_ptr<RenderBase> m_RenderCopy;
	std::shared_ptr<RenderBase> m_RenderErodeMask;
	std::shared_ptr<RenderBase> m_RenderDilateMask;
	std::shared_ptr<RenderBase> m_RenderRemapMask;

	float m_fFlowMinX, m_fFlowMaxX, m_fFlowMinY, m_fFlowMaxY;
	Mat m_matFlow;

	GLuint m_gOffScreenFramebuffer;
	TextureParam m_gMask1, m_gMask2, m_gFlowMask, m_gFinestFlowMask;

	GLuint m_gCopyShader, m_gRemapShader, m_gErodeShader, m_gDilateShader;
};

} // end of namespace mtcvlite

#endif