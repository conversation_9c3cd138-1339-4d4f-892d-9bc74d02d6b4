#pragma once

#include <memory>

namespace mtcvlite
{
	class Mat;
	class FaceFuse;

	class FaceTextureBlend
	{
	public:
		FaceTextureBlend();
		~FaceTextureBlend();
		//外部先传106点，里面再转171点
		//数据含义: matUserPoints(points_num, 1, MT_32FC2), float* pUsrPoints = (float *)matUsrPoints.data;
		void Run(Mat& matUserImage, const Mat& matUserPoints, const Mat& matRefTextureImage, const Mat& matRefSmoothImage,
			const Mat& matRefPoints, const float fTextureBlendAlpha = 0.6f);

		void Run(Mat& matUserImage, const Mat& matUserPoints, const Mat& matRefResImage,
			const Mat& matRefPoints, const float fTextureBlendAlpha = 0.6f);

		void ComputeResImage(const Mat& matUserImage, Mat& matUserResImage, const Mat& matUserPoints, const Mat& matRefResImage,
			const Mat& matRefPoints);

		void ComputeWarpRef2User(
			const Mat& matUserPoints, const int nUserWidth, const int nUserHeight,
			const Mat& matRefPoints, const int nRefWidth, const int nRefHeight,
			Mat& matRemap);

	protected:
	private:
		std::shared_ptr<FaceFuse> m_spFaceFuse;
	};

}

