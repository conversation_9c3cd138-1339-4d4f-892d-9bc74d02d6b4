/*****************************************************************************
 * GeometryCore3D.h
 ***************************************************************************** 
 *
 * 模块: iCloudLibrary/DataCore
 * 作者: Qidong Li       
 * 描述: 定义三维数据类型
 *
 * (c) Copyright by iCloudLibrary Development Group 
 *                 
 * 
 *****************************************************************************
 *
 *参考文献: 
 *
 *
 *****************************************************************************
 *
 * $Revision:  $
 * $Date: 
 *
 */
#pragma once

#include "GeometryCoreND.h"

namespace mtcvlite
{

namespace ICL
{
#ifdef ICL_GEOMETRY_CORE_UNIT

typedef Point_nD<dtype_t, 3> Point3D;
typedef Point_nD<double, 3> Point3Dd;
typedef Point_nD<float, 3> Point3Df;

typedef Vector_nD<dtype_t, 3> Vector3D;
typedef Vector_nD<double, 3> Vector3Dd;
typedef Vector_nD<float, 3> Vector3Df;

// template class ICL_EXPORT_TEMPLATE UnVec3D_<dtype_t>;
// template class ICL_EXPORT_TEMPLATE UnVec3D_<double>;
typedef UnVec_nD<dtype_t, 3> UnVec3D;
typedef UnVec_nD<float, 3> UnVec3Df;
typedef UnVec_nD<double, 3> UnVec3Dd;

// template class ICL_EXPORT_TEMPLATE Point3DNor_<dtype_t>;
// template class ICL_EXPORT_TEMPLATE Point3DNor_<double>;
typedef PointNor_nD<dtype_t, 3> Point3DNor;
typedef PointNor_nD<double, 3> Point3DNord;
typedef PointNor_nD<float, 3> Point3DNorf;

template <typename Tp, typename TpColor>
class Point3DNorColor_;

typedef Point3DNorColor_<dtype_t, float> Point3DNorColor;
typedef Point3DNorColor_<double, float> Point3DdNorColorf;

#endif

template <typename Tp>
class ICL_EXPORT_TEMPLATE Point_nD<Tp, 3>
{
public:
	inline Point_nD( const Tp* xyz_c3 )
	{ coord[0]=xyz_c3[0]; coord[1]=xyz_c3[1]; coord[2]=xyz_c3[2]; }
	inline Point_nD( Tp xa = 0, Tp yb = 0, Tp zc = 0)
	{ 
		coord[0]=xa; coord[1]=yb; coord[2]=zc; 
	}

	~Point_nD() {}

	Point_nD<Tp, 3>&  operator = (const Point_nD<Tp, 3> &pt3d);
	// 	Point_nD<Tp, 3>& operator += (const Point_nD<Tp, 3> &pt3d);
	// 	Point_nD<Tp, 3>& operator += (const Vector_nD<Tp, 3> &vec3d);
	// 	Point_nD<Tp, 3>& operator += (const UnVec_nD<Tp, 3> &uv3d); //表示坐标点自身正向平移一个单位向量
	// 	Point_nD<Tp, 3>& operator += ( const Tp &pc ); //表示坐标点自身的三个坐标值同时加上一个常数
	// 	Point_nD<Tp, 3>& operator -= (const Point_nD<Tp, 3> &pt3d);
	// 	Point_nD<Tp, 3>& operator -= (const Vector_nD<Tp, 3> &vec3d);
	// 	Point_nD<Tp, 3>& operator -= (const UnVec_nD<Tp, 3> &uv3d); //表示坐标点自身负向平移一个单位向量
	// 	Point_nD<Tp, 3>& operator /= (const Tp &sc);	
	// 	Point_nD<Tp, 3>& operator *= (const Tp &sc);

	friend void operator += <Tp>(Point_nD<Tp, 3> &self_pt3d, const Point_nD<Tp, 3> &pt3d);
	friend void operator += <Tp>(Point_nD<Tp, 3> &self_pt3d, const Vector_nD<Tp, 3> &vec3d);
	friend void operator += <Tp>(Point_nD<Tp, 3> &self_pt3d, const UnVec_nD<Tp, 3> &uv3d); //表示坐标点自身正向平移一个单位向量
	friend void operator += <Tp>(Point_nD<Tp, 3> &self_pt3d, const Tp pc); //表示坐标点自身的三个坐标值同时加上一个常数
	friend void operator -= <Tp>(Point_nD<Tp, 3> &self_pt3d, const Point_nD<Tp, 3> &pt3d);
	friend void operator -= <Tp>(Point_nD<Tp, 3> &self_pt3d, const Vector_nD<Tp, 3> &vec3d);
	friend void operator -= <Tp>(Point_nD<Tp, 3> &self_pt3d, const UnVec_nD<Tp, 3> &uv3d); //表示坐标点自身负向平移一个单位向量
	friend void operator /= <Tp>(Point_nD<Tp, 3> &self_pt3d, const Tp sc);	

	//template <typename Tp2>
	friend void operator *= <Tp>(Point_nD<Tp, 3> &self_pt3d, const Tp sc);

	friend Point_nD<Tp, 3> operator + <Tp>(const Point_nD<Tp, 3> &pt3d0, const Point_nD<Tp, 3> &pt3d1); //坐标点相加，可用于求和、点集的中心等
	friend Point_nD<Tp, 3> operator + <Tp>(const Point_nD<Tp, 3> &pt3d, const Vector_nD<Tp, 3> &vec3d); //表示坐标点平移变换(变换以向量表示)
	friend Point_nD<Tp, 3> operator + <Tp>(const Point_nD<Tp, 3> &pt3d, const UnVec_nD<Tp, 3> &uv3d); //表示坐标点正向平移一个单位向量
	friend Point_nD<Tp, 3> operator + <Tp>(const Point_nD<Tp, 3> &pt3d, const Tp pc); //表示坐标点同时加上一个常数
	friend Point_nD<Tp, 3> operator - <Tp>(const Point_nD<Tp, 3> &pt3d0, const Point_nD<Tp, 3> &pt3d1);
	friend Point_nD<Tp, 3> operator - <Tp>(const Point_nD<Tp, 3> &pt3d, const Vector_nD<Tp, 3> &vec3d); //表示坐标点平移变换(变换以向量表示)
	friend Point_nD<Tp, 3> operator - <Tp>(const Point_nD<Tp, 3> &pt3d, const UnVec_nD<Tp, 3> &uv3d); //表示坐标点负向平移一个单位向量
	friend Point_nD<Tp, 3> operator / <Tp>(const Point_nD<Tp, 3> &pt3d, const Tp dc);  //该点缩放后得到的新点，不推荐使用，尽量用*
	friend Point_nD<Tp, 3> operator * <Tp>(const Point_nD<Tp, 3> &pt3d, const Tp sc);       //该点缩放后得到的新点，推荐使用，尽量不用/

	friend Tp  operator * <Tp>(const Point_nD<Tp, 3> &pt3d, const UnVec_nD<Tp, 3> &uv3d); //原点到该点之间的向量往某个方向（以单位向量表示）的投影值
	friend bool    operator == <Tp>(const Point_nD<Tp, 3> &pt3d0, const Point_nD<Tp, 3> &pt3d1); //判断两个点是否为同一个点
public:
	inline void setx( const Tp newc ) { coord[0]=newc; }
	inline void sety( const Tp newc ) { coord[1]=newc; }
	inline void setz( const Tp newc ) { coord[2]=newc; }

	inline void setcoord( const int seq, const Tp newc ) { coord[seq]=newc; }
	inline void setcoord(const Tp _x, const Tp _y, const Tp _z) 
	{ coord[0] = _x; coord[1] = _y; coord[2] = _z; }

	inline Tp& x() { return coord[0]; }
	inline Tp& y() { return coord[1]; }
	inline Tp& z() { return coord[2]; }

	inline Tp x() const { return coord[0]; }
	inline Tp y() const { return coord[1]; }
	inline Tp z() const { return coord[2]; }

	Tp* pcoord() { return coord; }
	const Tp* pcoord() const { return coord; }

	inline Tp &operator[](int idx) { return coord[idx]; }            
	inline const Tp &operator[](int idx) const { return coord[idx]; }
	Tp getcoord(const int seq) const { return coord[seq]; }

	//! 该点到原点距离的平方
	Tp dis0_sq() const;  
	//! 该点到原点的距离
	double dis0() const;    
	//! 在精度方范围内，判断两个点是否相等
	bool equal_to(const Point_nD<Tp, 3> &pt3d, const Tp eps = (Tp)(1.0e-3)) const;
protected:
	Tp coord[3];
};

template <typename Tp>
class ICL_EXPORT_TEMPLATE Vector_nD<Tp, 3>
{
public:
	inline Vector_nD( Tp xi=0.0, Tp yj=0.0, Tp zk=0.0 )
	{ vec[0]=xi; vec[1]=yj; vec[2]=zk; }

	inline Vector_nD( const Tp *vec3d3 )
	{ vec[0] = vec3d3[0];vec[1] = vec3d3[1];vec[2] = vec3d3[2]; }

	inline Vector_nD( const Point_nD<Tp, 3>& pt3d )
	{ vec[0]=pt3d.x(); vec[1]=pt3d.y(); vec[2]=pt3d.z(); }

	inline Vector_nD( const UnVec_nD<Tp, 3>& uv3d )
	{ vec[0]=uv3d.ux(); vec[1]=uv3d.uy(); vec[2]=uv3d.uz(); }

	~Vector_nD() {}

	inline void setvec( const int seq, const Tp uv ) { vec[seq]=uv; }
	inline void setvx( const Tp uv ) { vec[0]=uv; }
	inline void setvy( const Tp uv ) { vec[1]=uv; }
	inline void setvz( const Tp uv ) { vec[2]=uv; }

	inline Tp& vx() { return vec[0]; }
	inline Tp& vy() { return vec[1]; }
	inline Tp& vz() { return vec[2]; }

	void reverse() { vec[0]=-vec[0]; vec[1]=-vec[1]; vec[2]=-vec[2]; } //将当前向量换为相反的方向

	inline Tp vx() const { return vec[0]; }
	inline Tp vy() const { return vec[1]; }
	inline Tp vz() const { return vec[2]; }
	inline Tp getvec( const int seq ) const { return vec[seq]; }

	UnVec_nD<Tp, 3> normalize() const; //将向量单位化得到相应的单位向量
	void opposite(); //将向量变为相反的方向
	Tp len_sq() const { return vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2]; } //返回向量长度的平方
	Tp len() const { return (Tp)sqrt((double)(vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2])); } //返回向量的长度

	//Vector_nD  operator overloading  = + - * / ^  ||
	Vector_nD<Tp, 3>& operator=( const Vector_nD<Tp, 3>& vec3d );
	Vector_nD<Tp, 3>&  operator+=( const Vector_nD<Tp, 3>& vec3d );//自身加上一个三维向量
	Vector_nD<Tp, 3>&  operator+=( const UnVec_nD<Tp, 3>& uv3d );//自身加上一个单位向量
	Vector_nD<Tp, 3>&  operator+=( const Tp& pc );//自身三个坐标值同时加上一个常数
	Vector_nD<Tp, 3>&  operator-=( const Vector_nD<Tp, 3>& vec3d );//自身减去一个三维向量
	Vector_nD<Tp, 3>&  operator-=( const UnVec_nD<Tp, 3>& uv3d );//自身减去一个单位向量
	Vector_nD<Tp, 3>&  operator^=( const Vector_nD<Tp, 3>& vec3d );//自身与一个三维向量叉乘
	Vector_nD<Tp, 3>&  operator^=( const UnVec_nD<Tp, 3>& uv3d );//自身与一个单位向量叉乘
	Vector_nD<Tp, 3>&  operator*=( const Tp& sc );
	Vector_nD<Tp, 3>&  operator/=( const Tp& sc );

	friend Point_nD<Tp, 3> operator + <Tp>(const Point_nD<Tp, 3> &pt3d, const Vector_nD<Tp, 3> &vec3d); //表示坐标点平移变换(变换以向量表示)
	friend Point_nD<Tp, 3> operator - <Tp>(const Point_nD<Tp, 3> &pt3d, const Vector_nD<Tp, 3> &vec3d); //表示坐标点平移变换(变换以向量表示)

	friend Vector_nD<Tp, 3>  operator+<Tp>(const Vector_nD<Tp, 3>& vec3d0, const Vector_nD<Tp, 3>& vec3d1);
	friend Vector_nD<Tp, 3>  operator+<Tp>(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d);
	friend Vector_nD<Tp, 3>  operator+<Tp>(const Vector_nD<Tp, 3>& vec3d, const Tp& pc);
	friend Vector_nD<Tp, 3>  operator-<Tp>(const Vector_nD<Tp, 3>& vec3d0, const Vector_nD<Tp, 3>& vec3d1);
	friend Vector_nD<Tp, 3>  operator-<Tp>(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d);
	friend Vector_nD<Tp, 3>  operator^<Tp>(const Vector_nD<Tp, 3>& vec3d0, const Vector_nD<Tp, 3>& vec3d1);//叉乘
	friend Vector_nD<Tp, 3>  operator^<Tp>(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d);//与单位向量的叉乘
	friend Tp    operator*<Tp>(const Vector_nD<Tp, 3>& vec3d0, const Vector_nD<Tp, 3>& vec3d1); //与一个三维向量的点乘，内积
	friend Tp    operator*<Tp>(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d); //与一个单位向量的点乘，内积
	friend Vector_nD<Tp, 3>  operator*<Tp>(const Vector_nD<Tp, 3>& vec3d, const Tp& sc);
	friend Vector_nD<Tp, 3>  operator/<Tp>(const Vector_nD<Tp, 3>& vec3d, const Tp& sc); 

	friend Vector_nD<Tp, 3>   operator + <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d );
	friend Vector_nD<Tp, 3>   operator - <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d );
	//! 与一个三维向量的叉乘
	friend Vector_nD<Tp, 3>   operator ^ <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d);
	//! 与一个三维向量的点乘，内积
	friend Tp     operator * <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d); 
protected:
	Tp vec[3];
};

template <typename Tp>
class ICL_EXPORT_TEMPLATE UnVec_nD<Tp, 3>
{
public:
	inline UnVec_nD() : is_zero( true ) //默认初始化为零向量
	{ 
		vec[0] = 0; vec[1] = 0; vec[2] = 0; 
	}

	inline UnVec_nD( Tp xi, Tp yj, Tp zk=(Tp)0 ) //给定三个实数并单位化
	{
		vec[0]=xi; vec[1]=yj; vec[2]=zk;
		Tp dlen=sqrt( xi*xi+yj*yj+zk*zk );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp)(1) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }  //若还没单位化，则单位化
	}

	inline UnVec_nD( const Tp *vec3d3, bool sym_zero ) : is_zero(sym_zero)//给定三个单位化的实数或零向量，则不需要进行单位化
	{ 
		vec[0]=vec3d3[0]; vec[1]=vec3d3[1]; vec[2]=vec3d3[2]; 
	}

	inline UnVec_nD(Tp xi, Tp yj, Tp zk, bool sym_zero) : is_zero(sym_zero)//给定三个单位化的实数或零向量，则不需要进行单位化
	{ 
		vec[0]=xi; vec[1]=yj; vec[2]=zk; 
	}

	inline UnVec_nD( const Tp *vec3d3 ) //给定三个实数并单位化，需要判断是否为零向量
	{ 
		vec[0]=vec3d3[0]; vec[1]=vec3d3[1]; vec[2]=vec3d3[2];
		Tp dlen=sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp(1)) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }
	}

	inline UnVec_nD( const Vector_nD<Tp, 3>& vec3d ) //将三维向量单位化
	{
		vec[0]=vec3d.vx(); vec[1]=vec3d.vy(); vec[2]=vec3d.vz();
		Tp dlen=sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] );
		if( !( is_zero=( dlen<ICLZeroRange ) ) )//判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp(1)) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }
	}

	~UnVec_nD(){}

	//修改单位向量的坐标值，采用以下几个函数
	inline void setuv(const Vector_nD<Tp, 3>& vec3d)
	{
		vec[0]=vec3d.vx(); vec[1]=vec3d.vy(); vec[2]=vec3d.vz();
		Tp dlen=sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp(1)) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }
	}

	inline void setuv( const Tp *vec3d3 ) 
	{
		vec[0] = vec3d3[0]; vec[1] = vec3d3[1]; vec[2] = vec3d3[2];
		Tp dlen=sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp(1)) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }
	}
	inline void setuv( Tp xi, Tp yj, Tp zk ) 
	{
		vec[0]=xi; vec[1]=yj; vec[2]=zk;
		Tp dlen=sqrt( xi*xi+yj*yj+zk*zk );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp(1)) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }  //若还没单位化，则单位化
	}

	//! 使用该函数，需要保证向量已单位化，内部不再做判断，减少不必要的计算
	inline void setuv( Tp xi, Tp yj, Tp zk, bool zero_flag ) 
	{
		vec[0]=xi; vec[1]=yj; vec[2]=zk; ;is_zero = zero_flag;
	}

	inline Tp ux() const { return vec[0]; }
	inline Tp uy() const { return vec[1]; }
	inline Tp uz() const { return vec[2]; }
	inline Tp uxyz( const int seq ) const { return vec[seq]; }

	inline bool IsZero() const { return is_zero; } //判断该向量是否为0向量

	inline void opposite()
	{ vec[0] = -vec[0]; vec[1] = -vec[1]; vec[2] = -vec[2]; }//将单位向量变为相反的方向

	//UnVec3D_  operator overloading  = + - * / ^  ||
	UnVec_nD<Tp, 3>& operator = ( const UnVec_nD<Tp, 3>& uv3d );

	friend Point_nD<Tp, 3> operator + <Tp>(const Point_nD<Tp, 3> &pt3d, const UnVec_nD<Tp, 3> &uv3d); //表示坐标点正向平移一个单位向量
	friend Point_nD<Tp, 3> operator - <Tp>(const Point_nD<Tp, 3> &pt3d, const UnVec_nD<Tp, 3> &uv3d); //表示坐标点负向平移一个单位向量
	friend Tp operator * <Tp>(const Point_nD<Tp, 3> &pt3d, const UnVec_nD<Tp, 3> &uv3d); //原点到该点之间的向量往某个方向（以单位向量表示）的投影值

	friend Vector_nD<Tp, 3>   operator + <Tp>(const UnVec_nD<Tp, 3> &uv3d0, const UnVec_nD<Tp, 3>& uv3d1 );
	friend Vector_nD<Tp, 3>   operator + <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d );
	friend Vector_nD<Tp, 3>   operator - <Tp>(const UnVec_nD<Tp, 3> &uv3d0, const UnVec_nD<Tp, 3>& uv3d1);
	friend Vector_nD<Tp, 3>   operator - <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d );
	//! 与一个单位向量的叉乘
	friend Vector_nD<Tp, 3>   operator ^ <Tp>(const UnVec_nD<Tp, 3> &uv3d0, const UnVec_nD<Tp, 3>& uv3d1);
	//! 与一个三维向量的叉乘
	friend Vector_nD<Tp, 3>   operator ^ <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d);
	//! 与一个单位向量的点乘，内积
	friend Tp     operator * <Tp>(const UnVec_nD<Tp, 3> &uv3d0, const UnVec_nD<Tp, 3>& uv3d1);
	//! 与一个三维向量的点乘，内积
	friend Tp     operator * <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Vector_nD<Tp, 3>& vec3d); 
	friend Vector_nD<Tp, 3>   operator * <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Tp& sc );
	friend Vector_nD<Tp, 3>   operator / <Tp>(const UnVec_nD<Tp, 3> &uv3d, const Tp& sc ); 

	friend Vector_nD<Tp, 3>  operator+<Tp>(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d);
	friend Vector_nD<Tp, 3>  operator-<Tp>(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d);
	friend Vector_nD<Tp, 3>  operator^<Tp>(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d);//与单位向量的叉乘
	friend Tp    operator*<Tp>(const Vector_nD<Tp, 3>& vec3d, const UnVec_nD<Tp, 3>& uv3d); //与一个单位向量的点乘，内积

protected:
	Tp vec[3];
	bool   is_zero;
};

template <typename Tp>
class ICL_EXPORT_TEMPLATE PointNor_nD<Tp, 3> : public Point_nD<Tp, 3>, public UnVec_nD<Tp, 3>
{
public:
	inline PointNor_nD( Tp xa=0.0, Tp yb=0.0, Tp zc=0.0,
		Tp xi=0.0, Tp yj=0.0, Tp zk=0.0 ) //由6个实数定义带单位法矢的三维点
		: is_selected( false ), surfvar( 0.0 ), Point_nD<Tp, 3>( xa, yb, zc ), UnVec_nD<Tp, 3>( xi, yj, zk ) {}

	inline PointNor_nD( const Tp *pt3d3, const Tp *nor3d3, const bool sym_zero ) //分别定义三维点和对应的单位法矢
		: is_selected( false ), surfvar( 0.0 ), Point_nD<Tp, 3>( pt3d3 ), UnVec_nD<Tp, 3>( nor3d3, sym_zero ) {}

	inline PointNor_nD( const Tp *pt3d3, const Tp *nor3d3 ) //分别定义三维点和对应的单位法矢
		: is_selected( false ), surfvar( 0.0 ), Point_nD<Tp, 3>( pt3d3 ), UnVec_nD<Tp, 3>( nor3d3 ) {}

	inline PointNor_nD( const Tp *ptnor3d6 ) //由6个实数定义带单位法矢的三维点
		: is_selected( false ), surfvar( 0.0 ), Point_nD<Tp, 3>( ptnor3d6 ), UnVec_nD<Tp, 3>( ptnor3d6 + 3 ) {}

	inline PointNor_nD( const Point_nD<Tp, 3>& pt3d ) //定义三维点，默认其初始法矢为零向量，待求
		: is_selected( false ), surfvar( 0.0 ), Point_nD<Tp, 3>( pt3d ) {}

	inline PointNor_nD( const Point_nD<Tp, 3>& pt3d, const UnVec_nD<Tp, 3>& nor3d ) //通过三维点和单位向量定义带法矢的三维点
		: is_selected( false ), surfvar( 0.0 ), Point_nD<Tp, 3>( pt3d ), UnVec_nD<Tp, 3>( nor3d ) {}

	~PointNor_nD<Tp, 3>() {}

	inline bool IsSelected() const { return is_selected; }
	inline void SetSelected( const bool sym_sel ) { is_selected=sym_sel; } 
	inline void SetSurfVar( const Tp surf_var ) { surfvar=surf_var; } 
	inline Tp SurfVar() const { return surfvar; }

	PointNor_nD<Tp, 3>& operator = (const PointNor_nD<Tp, 3> &vec3d);

private:
	Tp surfvar; //曲面变化量
	bool is_selected;
};

#ifdef ICL_GEOMETRY_CORE_UNIT

//带颜色信息的点
template <typename Tp, typename TpColor>
class ICL_EXPORT_TEMPLATE Point3DNorColor_: public PointNor_nD<Tp, 3>
{
public:
	Point3DNorColor_() : r((TpColor)0), g((TpColor)0), b((TpColor)0){}
	~Point3DNorColor_() {}

	Point3DNorColor_( const PointNor_nD<Tp, 3>& t_PtN ) : 
	PointNor_nD<Tp, 3>(t_PtN), r((TpColor)0), g((TpColor)0), b((TpColor)0){}
	Point3DNorColor_( const PointNor_nD<Tp, 3>& t_PtN, const TpColor t_Clr[3] ) : 
	PointNor_nD<Tp, 3>(t_PtN), r(t_Clr[0]), g(t_Clr[1]), b(t_Clr[2]){}

	void SetColor( const TpColor* iaRGB ) { r = iaRGB[0]; g = iaRGB[1]; b = iaRGB[2]; }
	TpColor clrr() const { return r; }
	TpColor clrg() const { return g; }
	TpColor clrb() const { return b; }
public:
	TpColor r;
	TpColor g;
	TpColor b;
};

#endif

#ifdef ICL_GEOMETRY_CORE_UNIT

#else
template <typename Tp>
class Point3D_;

// template class ICL_EXPORT_TEMPLATE ICL::Point3D_<dtype_t>;
// template class ICL_EXPORT_TEMPLATE Point3D_<double>;

typedef Point3D_<dtype_t> Point3D;
typedef Point3D_<double> Point3Dd;
typedef Point3D_<float> Point3Df;

template <typename Tp>
class Vector3D_;

// template class ICL_EXPORT_TEMPLATE Vector3D_<dtype_t>;
// template class ICL_EXPORT_TEMPLATE Vector3D_<double>;

typedef Vector3D_<dtype_t> Vector3D;
typedef Vector3D_<double> Vector3Dd;
typedef Vector3D_<float> Vector3Df;

template <typename Tp>
class UnVec3D_;

// template class ICL_EXPORT_TEMPLATE UnVec3D_<dtype_t>;
// template class ICL_EXPORT_TEMPLATE UnVec3D_<double>;
typedef UnVec3D_<dtype_t> UnVec3D;
typedef UnVec3D_<double> UnVec3Dd;

template <typename Tp>
class Point3DNor_;

// template class ICL_EXPORT_TEMPLATE Point3DNor_<dtype_t>;
// template class ICL_EXPORT_TEMPLATE Point3DNor_<double>;
typedef Point3DNor_<dtype_t> Point3DNor;
typedef Point3DNor_<double> Point3DNord;
typedef Point3DNor_<float> Point3DNorf;

template <typename Tp, typename TpColor>
class Point3DNorColor_;
typedef Point3DNorColor_<dtype_t, float> Point3DNorColor;
typedef Point3DNorColor_<double, float> Point3DdNorColorf;


template <typename Tp>
class ICL_EXPORT_TEMPLATE Point3D_
{
public:
	//Point3D_( const Point3D_& pt );
	inline Point3D_( const Tp xyz_c[3] )
	{ coord[0]=xyz_c[0]; coord[1]=xyz_c[1]; coord[2]=xyz_c[2]; }
// 	inline Point3D_( const float xyz_c[3] )
// 	{ coord[0]=(Tp)xyz_c[0]; coord[1]=(Tp)xyz_c[1]; coord[2]=(Tp)xyz_c[2]; }
	inline Point3D_( Tp xa = (Tp)0, Tp yb = (Tp)0, Tp zc = (Tp)0)
	{ coord[0]=xa; coord[1]=yb; coord[2]=zc; }

	~Point3D_() {}

	Point3D_<Tp>  operator = (const Point3D_<Tp> &pt3d);
	Point3D_<Tp> operator + (const Point3D_<Tp> &pt3d) const; //坐标点相加，可用于求和、点集的中心等
	Point3D_<Tp> operator + (const Vector3D_<Tp> &vec3d) const; //表示坐标点平移变换(变换以向量表示)
	Point3D_<Tp> operator + (const UnVec3D_<Tp> &uv3d) const; //表示坐标点正向平移一个单位向量
	Point3D_<Tp> operator + ( const Tp &pc ) const; //表示坐标点同时加上一个常数
	Point3D_<Tp> operator - (const Point3D_<Tp> &pt3d) const;
	Point3D_<Tp> operator - (const Vector3D_<Tp> &vec3d) const; //表示坐标点平移变换(变换以向量表示)
	Point3D_<Tp> operator - (const UnVec3D_<Tp> &uv3d) const; //表示坐标点负向平移一个单位向量
	Point3D_<Tp> operator / (const Tp &dc) const;  //该点缩放后得到的新点，不推荐使用，尽量用*
	Tp  operator * (const UnVec3D_<Tp> &uv3d) const; //原点到该点之间的向量往某个方向（以单位向量表示）的投影值
	Point3D_<Tp> operator * (const Tp &sc) const;       //该点缩放后得到的新点，推荐使用，尽量不用/
	bool    operator == ( const Point3D_ &pt3d ) const; //判断两个点是否为同一个点
	Point3D_<Tp> operator += (const Point3D_ &pt3d);
	Point3D_<Tp> operator += (const Vector3D_<Tp> &vec3d);
	Point3D_<Tp> operator += (const UnVec3D_<Tp> &uv3d); //表示坐标点自身正向平移一个单位向量
	Point3D_<Tp> operator += ( const Tp &pc ); //表示坐标点自身的三个坐标值同时加上一个常数
	Point3D_<Tp> operator -= (const Point3D_ &pt3d);
	Point3D_<Tp> operator -= (const Vector3D_<Tp> &vec3d);
	Point3D_<Tp> operator -= (const UnVec3D_<Tp> &uv3d); //表示坐标点自身负向平移一个单位向量
	Point3D_<Tp> operator /= (const Tp &sc);	
	Point3D_<Tp> operator *= (const Tp &sc);
	//Point3D_ operator *= (const TDS3DRotMat &rotmat);   //将坐标点自旋转变换(旋转变换矩阵)，更新当前坐标点
	//Point3D_ operator %= (const TDS4DHomoMat &homomat); //将坐标点进行自齐次变换(齐次矩阵)，更新当前坐标点

public:
	inline void setxyz( const int seq, const Tp newc ) { coord[seq]=newc; }
	inline void setx( const Tp newc ) { coord[0]=newc; }
	inline void sety( const Tp newc ) { coord[1]=newc; }
	inline void setz( const Tp newc ) { coord[2]=newc; }

	inline void setcoord(const Tp _x, const Tp _y, const Tp _z) 
	{ coord[0] = _x; coord[1] = _y; coord[2] = _z; }

	inline Tp& x() { return coord[0]; }
	inline Tp& y() { return coord[1]; }
	inline Tp& z() { return coord[2]; }

	inline Tp x() const { return coord[0]; }
	inline Tp y() const { return coord[1]; }
	inline Tp z() const { return coord[2]; }

	inline Tp &operator[](int idx) { return coord[idx]; }            
	inline const Tp &operator[](int idx) const { return coord[idx]; }

	//inline GLfloat fx() const { return (GLfloat)coord[0]; }
	//inline GLfloat fy() const { return (GLfloat)coord[1]; }
	//inline GLfloat fz() const { return (GLfloat)coord[2]; }

	Tp getcoord( const int seq ) const { return coord[seq]; }

	Tp dis0_sq() const; //该点到原点距离的平方
	Tp dis0() const;    //该点到原点的距离

	bool equal_to(const Point3D_ &pt3d, const Tp eps = (Tp)(1.0e-3)) const;

	//Point3D_ RotP( const TDS3DRotMat& RotM ); //将坐标点进行旋转变换为新的点
	//Point3D_ HomoP( const TDS4DHomoMat& HomoM ); //将坐标点进行齐次变换为新的点

	//void SelfRotP( const TDS3DRotMat& RotM ); //将坐标点自旋转变换，更新当前坐标点
	//void SelfHomoP( const TDS4DHomoMat& HomoM ); //将坐标点进行自齐次变换，更新当前坐标点

protected:
	Tp coord[3];
};

template <typename Tp>
class ICL_EXPORT_TEMPLATE Vector3D_
{
public:
	inline Vector3D_( Tp xi=0.0, Tp yj=0.0, Tp zk=0.0 )
	{ vec[0]=xi; vec[1]=yj; vec[2]=zk; }

	inline Vector3D_( const Tp vec3d[3] )
	{ vec[0]=vec3d[0];vec[1]=vec3d[1];vec[2]=vec3d[2]; }

	inline Vector3D_( const Point3D_<Tp>& pt3d )
	{ vec[0]=pt3d.x(); vec[1]=pt3d.y(); vec[2]=pt3d.z(); }

	inline Vector3D_( const UnVec3D_<Tp>& uv3d )
	{ vec[0]=uv3d.ux(); vec[1]=uv3d.uy(); vec[2]=uv3d.uz(); }

	~Vector3D_() {}

	inline void setvxyz( const int seq, const Tp uv ) { vec[seq]=uv; }
	inline void setvx( const Tp uv ) { vec[0]=uv; }
	inline void setvy( const Tp uv ) { vec[1]=uv; }
	inline void setvz( const Tp uv ) { vec[2]=uv; }

	inline Tp& vx() { return vec[0]; }
	inline Tp& vy() { return vec[1]; }
	inline Tp& vz() { return vec[2]; }

	void reverse() { vec[0]=-vec[0]; vec[1]=-vec[1]; vec[2]=-vec[2]; } //将当前向量换为相反的方向

	inline Tp vx() const { return vec[0]; }
	inline Tp vy() const { return vec[1]; }
	inline Tp vz() const { return vec[2]; }
	inline Tp vxyz( const int seq ) const { return vec[seq]; }

	//Vector3D_  operator overloading  = + - * / ^  ||
	Vector3D_<Tp>  operator=( const Vector3D_<Tp>& vec3d );
	Vector3D_<Tp>  operator+( const Vector3D_<Tp>& vec3d ) const;
	Vector3D_<Tp>  operator+( const UnVec3D_<Tp>& uv3d ) const;
	Vector3D_<Tp>  operator+( const Tp& pc ) const;
	Vector3D_<Tp>  operator-( const Vector3D_<Tp>& vec3d ) const;
	Vector3D_<Tp>  operator-( const UnVec3D_<Tp>& uv3d ) const;
	//Vector3D_<Tp>  operator-() const;
	Vector3D_<Tp>  operator^( const Vector3D_<Tp>& vec3d )  const;//叉乘
	Vector3D_<Tp>  operator^( const UnVec3D_<Tp>& uv3d ) const;//与单位向量的叉乘
	Vector3D_<Tp>  operator+=( const Vector3D_<Tp>& vec3d );//自身加上一个三维向量
	Vector3D_<Tp>  operator+=( const UnVec3D_<Tp>& uv3d );//自身加上一个单位向量
	Vector3D_<Tp>  operator+=( const Tp& pc );//自身三个坐标值同时加上一个常数
	Vector3D_<Tp>  operator-=( const Vector3D_<Tp>& vec3d );//自身减去一个三维向量
	Vector3D_<Tp>  operator-=( const UnVec3D_<Tp>& uv3d );//自身减去一个单位向量
	Vector3D_<Tp>  operator^=( const Vector3D_<Tp>& vec3d );//自身与一个三维向量叉乘
	Vector3D_<Tp>  operator^=( const UnVec3D_<Tp>& uv3d );//自身与一个单位向量叉乘
	Tp    operator*( const Vector3D_<Tp>& vec3d ) const; //与一个三维向量的点乘，内积
	Tp    operator*( const UnVec3D_<Tp>& uv3d ) const; //与一个单位向量的点乘，内积
	//Vector3D_  operator*( const TDSCVMat& rhmat ); //rhmat可以使3X3的旋转变换矩阵也可以是4X4的齐次矩阵
	Vector3D_<Tp>  operator*( const Tp& sc ) const;
	Vector3D_<Tp>  operator/( const Tp& sc ) const; 
	//Vector3D_  operator*=( const TDSCVMat& rhmat );
	Vector3D_<Tp>  operator*=( const Tp& sc );
	Vector3D_<Tp>  operator/=( const Tp& sc ); 
	UnVec3D_<Tp> normalize() const; //将向量单位化得到相应的单位向量
	void opposite(); //将向量变为相反的方向
	Tp len_sq() const { return vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2]; } //返回向量长度的平方
	Tp len() const { return sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] ); } //返回向量的长度
private:
	Tp vec[3];
};

template <typename Tp>
class ICL_EXPORT_TEMPLATE UnVec3D_
{
public:
	inline UnVec3D_() : is_zero( true ) //默认初始化为零向量
	{ vec[0]=(Tp)0; vec[1]=(Tp)0; vec[2]=(Tp)0; }

	inline UnVec3D_( Tp xi, Tp yj, Tp zk=(Tp)0 ) //给定三个实数并单位化
	{
		vec[0]=xi; vec[1]=yj; vec[2]=zk;
		Tp dlen=sqrt( xi*xi+yj*yj+zk*zk );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp)(1) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }  //若还没单位化，则单位化
	}

	inline UnVec3D_( const Tp vec3d[3], bool sym_zero ) : is_zero(sym_zero)//给定三个单位化的实数或零向量，则不需要进行单位化
	{ 
		vec[0]=vec3d[0]; vec[1]=vec3d[1]; vec[2]=vec3d[2]; 
	}

	inline UnVec3D_(Tp xi, Tp yj, Tp zk, bool sym_zero) : is_zero(sym_zero)//给定三个单位化的实数或零向量，则不需要进行单位化
	{ 
		vec[0]=xi; vec[1]=yj; vec[2]=zk; 
	}

// 	inline UnVec3D_( const Tp vec3d[3] ) //给定三个实数并单位化，需要判断是否为零向量
// 	{ 
// 		vec[0]=vec3d[0]; vec[1]=vec3d[1]; vec[2]=vec3d[2];
// 		Tp dlen=sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] );
// 		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
// 		{ vec[0]/=dlen; vec[1]/=dlen; vec[2]/=dlen; }
// 	}

	inline UnVec3D_( const Tp vec3d[3] ) //给定三个实数并单位化，需要判断是否为零向量
	{ 
		vec[0]=vec3d[0]; vec[1]=vec3d[1]; vec[2]=vec3d[2];
		Tp dlen=sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp)(1) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }
	}

	inline UnVec3D_( const Vector3D_<Tp>& vec3d ) //将三维向量单位化
	{
		vec[0]=vec3d.vx(); vec[1]=vec3d.vy(); vec[2]=vec3d.vz();
		Tp dlen=sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] );
		if( !( is_zero=( dlen<ICLZeroRange ) ) )//判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp)(1) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }
	}

	~UnVec3D_(){}

	//修改单位向量的坐标值，采用以下几个函数
	inline void setuv(const Vector3D& vec3d)
	{
		vec[0]=vec3d.vx(); vec[1]=vec3d.vy(); vec[2]=vec3d.vz();
		Tp dlen=sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp)(1) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }
	}

	inline void setuv( const Tp vec3d[3] ) 
	{
		vec[0]=vec3d[0]; vec[1]=vec3d[1]; vec[2]=vec3d[2];
		Tp dlen=sqrt( vec[0]*vec[0]+vec[1]*vec[1]+vec[2]*vec[2] );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp)(1) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }
	}
	inline void setuv( Tp xi, Tp yj, Tp zk ) 
	{
		vec[0]=xi; vec[1]=yj; vec[2]=zk;
		Tp dlen=sqrt( xi*xi+yj*yj+zk*zk );
		if( !( is_zero=( dlen<ICLZeroRange ) ) ) //判断是否为零向量，若为非零向量，则单位化
		{ dlen = (Tp)(1) / dlen; vec[0]*=dlen; vec[1]*=dlen; vec[2]*=dlen; }  //若还没单位化，则单位化
	}

	inline void setuv( Tp xi, Tp yj, Tp zk, bool zero_flag ) 
	{
		vec[0]=xi; vec[1]=yj; vec[2]=zk; ;is_zero = zero_flag;
	}

	inline Tp ux() const { return vec[0]; }
	inline Tp uy() const { return vec[1]; }
	inline Tp uz() const { return vec[2]; }
	inline Tp uxyz( const int seq ) const { return vec[seq]; }

	inline bool IsZero() const { return is_zero; } //判断该向量是否为0向量

	//UnVec3D_  operator overloading  = + - * / ^  ||
	UnVec3D_<Tp>   operator = ( const UnVec3D_<Tp>& uv3d );
	Vector3D_<Tp>   operator + ( const UnVec3D_<Tp>& uv3d ) const;
	Vector3D_<Tp>   operator + ( const Vector3D_<Tp>& vec3d ) const;
	Vector3D_<Tp>   operator - ( const UnVec3D_<Tp>& uv3d ) const;
	Vector3D_<Tp>   operator - ( const Vector3D_<Tp>& vec3d ) const;
	//UnVec3D_ operator - ();
	Vector3D_<Tp>   operator ^ ( const UnVec3D_<Tp>& uv3d ) const;//与一个单位向量的叉乘
	Vector3D_<Tp>   operator ^ ( const Vector3D_<Tp>& vec3d ) const;//与一个三维向量的叉乘
	Tp     operator * ( const UnVec3D_<Tp>& uv3d ) const; //与一个单位向量的点乘，内积
	Tp     operator * ( const Vector3D_<Tp>& vec3d ) const; //与一个三维向量的点乘，内积
	Vector3D_<Tp>   operator * ( const Tp& sc ) const;
	Vector3D_<Tp>   operator / ( const Tp& sc ) const; 

	inline void opposite()
	{ vec[0] = -vec[0]; vec[1] = -vec[1]; vec[2] = -vec[2]; }//将单位向量变为相反的方向

protected:
	Tp vec[3];
	bool   is_zero;
};


template <typename Tp>
class ICL_EXPORT_TEMPLATE Point3DNor_ : public Point3D_<Tp>, public UnVec3D_<Tp>
{
public:
	inline Point3DNor_( Tp xa=0.0, Tp yb=0.0, Tp zc=0.0,
		Tp xi=0.0, Tp yj=0.0, Tp zk=0.0 ) //由6个实数定义带单位法矢的三维点
		: is_selected( false ), surfvar( 0.0 ), Point3D_<Tp>( xa, yb, zc ), UnVec3D_<Tp>( xi, yj, zk ) {}

	inline Point3DNor_( const Tp pt3d[3], const Tp nor3d[3], const bool sym_zero ) //分别定义三维点和对应的单位法矢
		: is_selected( false ), surfvar( 0.0 ), Point3D_<Tp>( pt3d ), UnVec3D_<Tp>( nor3d, sym_zero ) {}

	inline Point3DNor_( const Tp pt3d[3], const Tp nor3d[3] ) //分别定义三维点和对应的单位法矢
		: is_selected( false ), surfvar( 0.0 ), Point3D_<Tp>( pt3d ), UnVec3D_<Tp>( nor3d ) {}

	inline Point3DNor_( const Tp ptnor3d[6] ) //由6个实数定义带单位法矢的三维点
		: is_selected( false ), surfvar( 0.0 ), Point3D_<Tp>( ptnor3d ), UnVec3D_<Tp>( ptnor3d+3 ) {}

	inline Point3DNor_( const Point3D_<Tp>& pt3d ) //定义三维点，默认其初始法矢为零向量，待求
		: is_selected( false ), surfvar( 0.0 ), Point3D_<Tp>( pt3d ) {}

	inline Point3DNor_( const Point3D_<Tp>& pt3d, const UnVec3D_<Tp>& nor3d ) //通过三维点和单位向量定义带法矢的三维点
		: is_selected( false ), surfvar( 0.0 ), Point3D_<Tp>( pt3d ), UnVec3D_<Tp>( nor3d ) {}

	~Point3DNor_() {}

	inline bool IsSelected() const { return is_selected; }
	inline void SetSelected( const bool sym_sel ) { is_selected=sym_sel; } 
	inline void SetSurfVar( const Tp surf_var ) { surfvar=surf_var; } 
	inline Tp SurfVar() const { return surfvar; }

	Point3DNor_<Tp> operator = (const Point3DNor_<Tp> &vec3d);
	//带法矢的点的坐标变换，平移时坐标点平移，法矢方向不变，旋转变换(齐次变换)时坐标变化，法矢方向也发生改变
	Point3DNor_<Tp> operator + ( const Vector3D_<Tp> &vec3d ) const; //带法矢的坐标点平移，法矢不变
	Point3DNor_<Tp> operator + ( const UnVec3D_<Tp> &uv3d ) const; //带法矢的坐标点正向平移一个单位向量，坐标点平移，法矢不变
	Point3DNor_<Tp> operator += ( const Vector3D_<Tp> &vec3d );//自身坐标点正向平移，法矢不变
	Point3DNor_<Tp> operator += ( const UnVec3D_<Tp> &uv3d ); //自身坐标点正向平移一个单位向量，单位法矢不变
	Point3DNor_<Tp> operator - ( const Vector3D_<Tp> &vec3d ) const; //带法矢的坐标点平移，坐标点平移，法矢不变
	Point3DNor_<Tp> operator - ( const UnVec3D_<Tp> &uv3d ) const; //带法矢的坐标点负向平移一个单位向量，坐标点平移，法矢不变
	Point3DNor_<Tp> operator -= ( const Vector3D_<Tp> &vec3d );//自身坐标点负向平移一个单位向量，法矢不变
	Point3DNor_<Tp> operator -= ( const UnVec3D_<Tp> &uv3d ); //带法矢的坐标点平移一个单位向量，坐标点平移，法矢不变

private:
	Tp surfvar; //曲面变化量
	bool is_selected;
};

//带颜色信息的点
template <typename Tp, typename TpColor>
class ICL_EXPORT_TEMPLATE Point3DNorColor_: public Point3DNor_<Tp>
{
public:
	Point3DNorColor_() : r((TpColor)0), g((TpColor)0), b((TpColor)0){}
	~Point3DNorColor_() {}

	Point3DNorColor_( const Point3DNor_<Tp>& t_PtN ) : 
		Point3DNor_<Tp>(t_PtN), r((TpColor)0), g((TpColor)0), b((TpColor)0){}
	Point3DNorColor_( const Point3DNor_<Tp>& t_PtN, const TpColor t_Clr[3] ) : 
		Point3DNor_<Tp>(t_PtN), r(t_Clr[0]), g(t_Clr[1]), b(t_Clr[2]){}

	void SetColor( const TpColor* iaRGB ) { r = iaRGB[0]; g = iaRGB[1]; b = iaRGB[2]; }
	TpColor clrr() const { return r; }
	TpColor clrg() const { return g; }
	TpColor clrb() const { return b; }
public:
	TpColor r;
	TpColor g;
	TpColor b;
};

#endif

}
    
} // namespace mtcvlite

#include "GeometryCore3D.hpp"
