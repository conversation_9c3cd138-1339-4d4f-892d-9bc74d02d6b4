#pragma once

#include "ImageCore.h"

namespace mtcvlite
{

	void spatialGradient(const Mat&  _src, Mat& _dx, Mat& _dy,
		int ksize = 3, int borderType = MT_BORDER_DEFAULT);

	void GradientSobel3(const unsigned char* src, 
		short* dx, short* dy, 
		const int w, const int h,
		short* dx1 = 0, short* dy1 = 0);

	void GradientSobel3(const float* src,
		float* dx, float* dy,
		const int w, const int h,
		float* dx1 = 0, float* dy1 = 0);
}