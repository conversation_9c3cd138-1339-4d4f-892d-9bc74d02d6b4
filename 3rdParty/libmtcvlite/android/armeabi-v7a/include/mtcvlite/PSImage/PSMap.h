#pragma once
#ifndef _MT_CV_LITE_PS_MAP_H_H_
#define _MT_CV_LITE_PS_MAP_H_H_


#include "mtcvlite/mtcvdef.h"

namespace mtcvlite
{
    //pImage， pRefMap均为4通道，都是rgba格式
	//64x64基准图
	MT_CV_EXPORT void Lut3D_16(unsigned char* pImage, unsigned char* pRefMap, int nWidth, int nHeight);

	//512x512基准图
	MT_CV_EXPORT void Lut3D_64(unsigned char* pImage, unsigned char* pRefMap, int nWidth, int nHeight, const int nStep = 0);

}//end namespace mtcvlite

#endif /* _MT_CV_LITE_PS_MAP_H_H_ */
