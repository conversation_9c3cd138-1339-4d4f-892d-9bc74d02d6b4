#pragma once

#include <mtcvlite/base/DataCore/DataCore.h>

namespace mtcvlite
{

namespace ICL
{

class ICL_EXPORT RotatedRect
{

public:
	//! various constructors
	RotatedRect() : center(0, 0), size(0, 0), angle(0) {}
	//RotatedRect(const Point2f& center, const Size2f& size, float angle);
	//RotatedRect(const CvBox2D& box);

	//! returns 4 vertices of the rectangle
	void points(Point2Df pts[]) const;
	//! returns the minimal up-right rectangle containing the rotated rectangle
	//Rect boundingRect() const;
	//! conversion to the old-style CvBox2D structure
	//operator CvBox2D() const;

	Point2Df center; //< the rectangle mass center
	Size2f size;    //< width and height of the rectangle
	float angle;    //< the rotation angle. When the angle is 0, 90, 180, 270 etc., the rectangle becomes an up-right rectangle.
};

 //                    ((CvPoint2D32f*)out)[0] - corner
 //                    ((CvPoint2D32f*)out)[1] - vector1
 //                    ((CvPoint2D32f*)out)[0] - vector2
 //
 //                      ^
 //                      |
 //              vector2 |
 //                      |
 //                      |____________\
 //                    corner         /
 //                               vector1

void minAreaRect(const TVecPt2D& _points, Point2Df out[3]);

RotatedRect minAreaRect(const TVecPt2D& _points);

} // end namespace ICL

} // namespace mtcvlite
