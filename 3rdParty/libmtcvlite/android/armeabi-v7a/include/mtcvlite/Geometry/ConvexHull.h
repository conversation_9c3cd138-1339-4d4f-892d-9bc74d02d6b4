#pragma once

#include <mtcvlite/base/DataCore/DataCore.h>

namespace mtcvlite
{

namespace ICL
{

/******************************************************
**结 构：class ICL_Line_
**功 能：定义由两个点组成的线段及其操作
**创 建：djq 2015.11.04
**版 本：v1.0
******************************************************/
template <typename Tp>  
class ICL_Line_
{
public:

	ICL_Line_(){}
	~ICL_Line_(){}
	inline ICL_Line_(Tp _a, Tp _b)  //Tp一般是点（也可以用int，即点的索引来构造）
	{
		a = _a;
		b = _b;
	}

	inline Tp p1() const {return a;}
	inline Tp p2() const {return b;}

private:
	Tp a;  //线段的一个端点a（或者其索引值）
	Tp b;  //线段的另一个端点b
};  //end ICL_Line_


typedef ICL_Line_<int> LineIndex;  //Line的两个端点存放的是索引值
typedef ICL_Line_<Point2Df> Line2Df;
typedef ICL_Line_<Point3D> Line3D;
typedef ICL_Line_<Point3DNor> Line3DNor;


/******************************************************
**类 名：ConvexHull
**功 能：定义平面点集上的凸包
**创 建：djq 2015.11.04
**版 本：v1.0
******************************************************/
class ICL_EXPORT ConvexHull
{
public:
	ConvexHull(const TVecPt2Df& iPSet);  //输入：平面点集
	~ ConvexHull();

	//! 交换两个整数
	//void Swap(int& a, int& b);

	//! 求数组中最小元素在数组中的位置
	void MinElemArr(dtype_t* array, int iElemNum, int& oPos);

	//! 对点集按照逆时针进行排序
	void SortPtIdxs(TVecInt& ioPSetIdxs);

	//! 取点集的索引集合
	TVecInt GetPSetID();

	//! 取点集
	TVecPt2Df GetPSet2D();

	//**************************递归分治搜索凸包法*****************************
	//! 判断点pt3在向量pt1->pt2的左侧还是右侧
	double Area(int iPtIdx1, int iPtIdx2, int iPtIdx3);   //输入参数为三个点的索引

	//! 找出平面点集最左上和最右下的两个端点
	void FindEndPt(int& oMinPtIdx, int& oMaxPtIdx);

	//! 搜索点集的凸包顶点，两两组成线段，将线段存储起来（如果不需要对点排序，直接计算凸包，可以用这个函数）
	void QuickHull(int iMinPtIdx, int iMaxPtIdx,   //输入：两个端点的索引值
		TVecInt& iPSetIdxs,                              //输入：平面点集的索引
		std::vector<LineIndex>& oLineIdxVec);              //输出：保存凸包顶点（顶点用其索引值表示）连线的容器

	//! 搜索点集的凸包顶点，保存顶点的索引
	void QuickHull(int iMinPtIdx, int iMaxPtIdx,   //输入：两个端点的索引值
		TVecInt& iPSetIdxs,                          //输入：平面点集的索引
		TVecInt& oVecHullIdxs);              //输出：保存凸包顶点的容器

	/////////该函数弃用，存在错误！！！！！！！！！！！
	//! 运行QuickHull函数求凸包，按逆时针顺序返回凸包顶点的索引
	void RunQuickHull(TVecInt& oPSetIdxs);

	//**************************Graham搜索凸包法*****************************
	//! 求两点p1和p2的距离
	double Dis_sq(int iPtIdx1, int iPtIdx2);

	//! 求向量p0->p1和p0->p2的叉积
	double CrossProduct(int iPtIdx1, int iPtIdx2, int iPtIdx0);

	//! 求点集中最左下的点
	void LeftBottomPt(const TVecInt& iPSetIdxs, int& oPos);  //输出：最左下点的索引

	//! 使用Graham扫描法求点集的凸包，按逆时针顺序返回凸包顶点的索引
	void GrahamHull(TVecInt& oPSetIdxs);  //输入输出：按逆时针顺序存放凸包顶点的容器

	//**************************Jarvis步进法*****************************
	//! 使用Jarvis步进法求点集的凸包，按逆时针顺序返回凸包顶点的索引
	void JarvisHull(TVecInt& oPSetIdxs);


private:
	ConvexHull();        //默认构造函数设置为private，强制用户使用带参数的构造函数
	TVecPt2Df m_pSet2D;  //二维点集
	TVecInt m_pSetID;    //点集m_pSet2D中点的索引
	int m_ptNum;         //点数
};  //class ConvexHull



} //end namespace ICL

} // namespace mtcvlite
