#pragma once

namespace mtcvlite
{
	typedef struct FaceCompMaskParam
	{
		int left_eye = 3, right_eye = 3;
		int left_brow = 3, right_brow = 3;
		int mouth = 3;
		int nose = 3;

	}FaceCompMaskParam;
	void FormFaceCompMask(
		unsigned char* pFaceMask, 
		const int nWidth, const int nHeight,
		const float* pfFacePoints, 
		const FaceCompMaskParam& sParam);

	void FormFaceMask(unsigned char* pMask,
		const int nMaskWidth, const int nMaskHeight,
		const float* pfFacePoints, 
		const int nOriginWidth, const int nOriginHeight,
		const unsigned char nFillColor = 255U);

	void FormLineMask(unsigned char* pMask,
		const int nMaskWidth, const int nMaskHeight, 
		const float* pfPoints, const int nPoints,
		const int nDilateRadius = 0);

	void FormContourInnerMask(unsigned char* pMask,
		const int nMaskWidth, const int nMaskHeight,
		const float* pfPoints, const int nPoints,
		const int nDilateRadius = 0,
		const unsigned char nFillColor = 255U);

// 	void FormFaceContourMask(unsigned char* pMask,
// 		const int nMaskWidth, const int nMaskHeight,
// 		const float* pfFacePoints,
// 		const int nOriginWidth, const int nOriginHeight);

}