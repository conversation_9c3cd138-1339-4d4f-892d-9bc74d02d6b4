#ifndef _BITMAP2BYTE_H_
#define _BITMAP2BYTE_H_

#include "mtdefine.h"

#include <android/bitmap.h>

//����Bitmap���ݸ�NDK��ARGB��˳��
int BitmapARGBCheck(JNIEnv* env,jobject bitmap);

BYTE* Bitmap2BYTE(JNIEnv* env,jobject bitmap,int &nWidth,int &nHeight);

int BYTE2Bitmap(JNIEnv* env,jobject bitmap,BYTE* pImageData,int nWidth,int nHeight);

// ֻ����BGR, ������Alapha����
int BYTE2BitmapBGRX(JNIEnv* env,jobject bitmap,BYTE* pImageData);

//����Bitmap
jobject BitmapCreate(JNIEnv* env,int nWidth,int nHeight);


//��ȡBitmap �ߴ�
void getBitmapSize(JNIEnv* env,jobject bitmap,int& width,int& height);
#endif
