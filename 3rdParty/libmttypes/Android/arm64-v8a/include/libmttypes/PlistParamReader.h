//
//  用于Jni层读取 符合要求的PList配置文件里面的数据，方便进行效果调试及配置读取
//  在各自so的JNI_Onload里面需要调用一次 静态方法registerJVM
//  由于jvm变量是一个静态变量，多个so同时调用的时候，不确定是否会有问题，目前在单个so上调用没有问题
//  PlistParamReader.hpp
//  myxj_xcode_project
//
//  Created by <PERSON> on 16/6/28.
//  Copyright © 2016年 Ryan. All rights reserved.
//

#ifndef PlistParamReader_hpp
#define PlistParamReader_hpp
#include <jni.h>
#include <stdio.h>
class CPlistParamReader {
private:
    // 获取class 等
    void registerClass(JNIEnv* env);
public:
    // 必需要在对应的JNI_OnLoad 里面进行注册
    static void registerJVM(JavaVM *jvm);
public:
    
    CPlistParamReader();
    
    /**
     *  是否为效果测试环境，判断的依据是对应的目录有对应的效果配置文件
     *
     *  @return
     */
    bool isDebug();
    
    /**
     *  获取用于批处理的图片的存放目录
     *
     *  @return
     */
    char* getDirOfImageForBatch();
    
    /**
     *  获取用于保存批处理效果图片的目录
     *
     *  @return
     */
    char* getDirOfProcessedImageForBatch();
    
    /**
     *  清除效果图片保存目录(不删除目录)
     */
    void clearResultDir();
    
    /**
     *  获取用于批处理的图片的数量
     *
     *  @return
     */
    int getCountOfImagesForBatch();
    
    /**
     *  获取某个批处理图片在磁盘中的绝对路径
     *
     *  @return
     */
    char* getPathOfImageWithIndex(int index);
    
    /**
     *  获取 float 类型键值
     *
     *  @param featureKey 一级键值
     *  @param valueKey   二级键值
     *
     *  @return 默认返回.0
     */
    float floatValueForKey(const char* featureKey,const char* valueKey);
    
    /**
     *  获取 boolean 类型键值
     *
     *  @param featureKey 一级键值
     *  @param valueKey 二级键值
     *
     *  @return 默认返回 false
     */
    bool booleanValueForKey(const char* featureKey,const char* valueKey);
    
    /**
     *  获取 int 类型键值
     *
     *  @param featureKey 一级键值
     *  @param valueKey 二级键值
     *
     *  @return 默认返回 0
     */
    int intValueForKey(const char* featureKey,const char* valueKey);
    
    /**
     *  获取 字符串 类型键值
     *
     *  @param featureKey 一级键值
     *  @param valueKey 二级键值
     *
     *  @return 默认返回null
     */
    char* stringValueForKey(const char* featureKey,const char* valueKey);
    
    /**
     * 获取文件名的起始字符在一个路径字符串中的位置
     *  @param filePath 文件路径
     *
     *  @return 文件名的开头在路径字符串中位置
     */
    static int lastIndexOfFileName(const char* filePath);
    
private:
    jclass cls_clazz;
    jmethodID mid_isDebug;
    jmethodID mid_getCountOfImagesForBatch;
    jmethodID mid_getDirOfImageForBatch;
    jmethodID mid_getDirOfProcessedImageForBatch;
    jmethodID mid_getPathOfImageWithIndex;
    jmethodID mid_floatValueForKey;
    jmethodID mid_booleanValueForKey;
    jmethodID mid_intValueForKey;
    jmethodID mid_stringValueForKey;
    jmethodID mid_clearResult;
    JNIEnv* g_env;
};

#endif /* PlistParamReader_hpp */
