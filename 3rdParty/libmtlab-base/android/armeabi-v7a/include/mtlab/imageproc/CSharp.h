/*
 *  CCUSMSharp.h
 *  Hello
 *
 *  Created by fu songlin on 10-10-9.
 *  Copyright 2010 ?ͼ?. All rights reserved.
 *
 */

#ifndef _MTLAB__CCUSMSHARP_H_
#define _MTLAB__CCUSMSHARP_H_

#include "mtlab/common/MeituDefine.h"
#include <math.h>

namespace mtlab {
    
#define m_MaxDim 51
    
    
    
    enum Flags
    {
        PlaneBlue = 0x01,
        PlaneGreen = 0x02,
        PlaneRed = 0x04,
        PlaneAll = PlaneBlue | PlaneGreen | PlaneRed
    };
    
#define Sharp_Int(a)  ((int)(a+0.5f))
    
    class MTLAB_EXPORT CSharp{
        unsigned int m_width4;
        unsigned int m_Height;
        unsigned int m_Width;
        int m_FilterVector[255];
        int m_Denominator;
        unsigned int m_Dim;
        unsigned int m_Flags;
        float m_Radius;
        float m_Depth;
        BYTE* m_pSharpDst;
        
    public:
        CSharp();
        ~CSharp();
        
        //preview??񻯲??
        int SharpProcess(BYTE*pSrc,BYTE* pBlurData,int width,int height,float depth);
        int GetBlurInit(BYTE * pSrc,int width,int height, int radius, unsigned int flags, bool bCopy);
        
        bool SetRadius(int radius);
        
        int useEffect(BYTE* pSrc,int width,int height,int Radius,float Depth);
        
        int ConvoluteDimension(BYTE * pSrc,BYTE* pDst, unsigned int flags,bool bHorizontal, bool bCopy);
        
        int GetBlur(BYTE * pSrc,BYTE *pDst, int radius, unsigned int flags, bool bCopy);
        
        int GetUnsharpMask(BYTE * pSrc, BYTE * pDst,int radius, float depth, unsigned int flags);
        
        int useEffectPreview(BYTE* pSrc,int width,int height,int Radius,float Depth);
        
        int GetUnsharpMaskPreview(BYTE * pSrc,BYTE *pDst, int radius, float depth, unsigned int flags,int size);
        
    };
    
}
#endif

