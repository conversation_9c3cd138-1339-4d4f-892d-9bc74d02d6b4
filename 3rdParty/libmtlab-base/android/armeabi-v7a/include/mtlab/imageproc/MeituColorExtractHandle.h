//
// Created by jck on 2019/8/26.
//

#pragma once
#include "mtlab/common/MeituDefine.h"

namespace mtlab {
    /**
     * ColorParam 设置取色的参数
     */
    typedef struct ColorParam
    {
        int nColorMode; // 输入图像的颜色模型的类型，0表示RGBA，1表示BGRA
        int nSign;      //等于0是局部颜色提取；1表示图像全局提色
        int nSimple;    //是否简化算法，0表示不简化，1表示简化（算法简化后，效果会有些损失）
        int nSortMode;  //排序权重的类型，0表示饱和度，1表示数量，2表示饱和度和数量
    }ColorParam_t;
    
    class MeituColorExtract;
    class MTLAB_EXPORT MeituColorExtractWithNeon;
    
    class MeituColorExtractHandle
    {
    public:
        MeituColorExtractHandle();
        ~MeituColorExtractHandle();
        
    public:
        
        /**
         * 初始化内存，需要先初始化
         * @param nColorNum [输入，需要提取的颜色数量]
         * @return          [返回值，true 成功，false 失败]
         */
        bool initColorExtract();
        
        /**
         * 运行程序
         * @param pImage    [输入，RGBA或者BGRA四通道图像]
         * @param nWidth    [输入，图像的宽度]
         * @param nHeight   [输入，图像的高度]
         * @param nStride   [输入，图像的stride值，如果若没有stride，则设置为宽的四倍width*4]
         * @param nColorNum [输入，需要提取的颜色数量]
         * @param param     [输入，取色的参数设置]
         * @param pColor    [输出，取色结果的颜色数组]
         * @return          [返回值，true 成功，false 失败]
         */
        bool runColorExtract(const unsigned char* pImage, int nWidth, int nHeight, int nStride, int nColorNum, ColorParam_t param, unsigned char* pColor);
        
    private:
        MeituColorExtract* colorExtractHandle;
        MeituColorExtractWithNeon* colorExtractWithNeonHandle;
    };
}


