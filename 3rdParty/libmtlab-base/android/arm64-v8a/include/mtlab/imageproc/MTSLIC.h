// MTSLIC.h: interface for the MTSLIC class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(_MTLAB_AFX_MTSLIC_H__EC17850F_FA7E_4ABF_A54D_FAB5EE073371__INCLUDED_)
#define _MTLAB_AFX_MTSLIC_H__EC17850F_FA7E_4ABF_A54D_FAB5EE073371__INCLUDED_
#include "mtlab/common/MeituDefine.h"

namespace mtlab {
    
    class MTLAB_EXPORT MTSLIC
    {
    public:
        MTSLIC();
        virtual ~MTSLIC();
        
        //WPF: pDataRGB is the rgb image, pData can be an rgb or lab image
        /*
         @brief annotation added by zhangen<PERSON><PERSON>
         @brief 线性迭代聚类超像素分割算法
         @param pDataRGB:输入图像数据，rgba格式
         @param pData:pData can be an rgb or lab image
         @param nWidth: 输入图像宽
         @param nHeight:输入图像高
         @param nSuperpixelsize:超像素大小，包含的像素数，通常为方形，即长=宽
         @param fCompactness:k-means聚类过程中空间距离对分类的影响系数
         @param flag:分割结果，为一副label图（取值0...n-1）
         @param flagSize:superpixels分割结果的块数n
         @param flagValues:superpixels分割得到每个块的平均rgb值(按照0...n-1的顺序)
         */
        void Run(const BYTE *pDataRGB, const BYTE* pData,int nWidth,int nHeight,int    nSuperpixelsize, float fCompactness, int** flag, int &flagSize, int** flagValues);
    private:
        void RGB2XYZ(BYTE sR,BYTE sG,BYTE sB,float&    X,float& Y,float& Z);
        void RGB2LAB(BYTE sR, BYTE sG, BYTE sB, float& lval, float& aval, float& bval);
        void DoRGBtoLABConversion(const BYTE* pDataRGB,int nWidth,int nHeight);
        //绘制
        void DrawContoursAroundSegments(const BYTE* pData,int* labels,int width,int height, int numlabels, int** flagValues);
        //===========================================================================
        ///    EnforceLabelConnectivity
        ///
        ///        1. finding an adjacent label for each new component at the start
        ///        2. if a certain component is too small, assigning the previously found
        ///            adjacent label to this component, and not incrementing the label.
        //===========================================================================

        //labels:input labels that need to be corrected to remove stray labels
        //nlabels:new labels
        //numlabels:the number of labels changes in the end if segments are removed
        //K:the number of superpixels desired by the user
        //去除孤立点，根据4邻域或者8邻域连通的连通算法，判断这个连通分量的面积，如果面积过小，则将连通分量的分类
        //分给最近的类别
        void EnforceLabelConnectivity(int* labels,int width,int height,int*& nlabels,int& numlabels,int K);
        //===========================================================================
        ///    PerformSuperpixelSLIC
        ///
        ///    Performs k mean segmentation. It is fast because it looks locally, not
        /// over the entire image.
        /// klabels: 生成的超像素标记
        //===========================================================================
        void PerformSuperpixelSLIC(float* pKseedsl,float* pKseedsa,float* pKseedsb,int* pKseedsx,int* pKseedsy,int*& klabels,int nWidth,int nHeight,int nStep,float M,int numk);
        void PerturbSeeds(float* pKseedsl,float* pKseedsa,float* pKseedsb,int* pKseedsx,int* pKseedsy,float* pEdges,int nWidth,int nHeight,int numseeds);
        //计算聚类中心，五维(l,a,b,x,y)
        void GetLABXYSeeds_ForGivenStepSize(float* pKseedsl,float* pKseedsa,float* pKseedsb,int* pKseedsx,int* pKseedsy,int nWidth,int nHeight,int nStep);
        void DetectLabEdges(float* lvec,float* avec,float* bvec,int width,int height,float* edges);
        
        float* m_lvec;
        float* m_avec;
        float* m_bvec;
    };
}
#endif // !defined(AFX_MTSLIC_H__EC17850F_FA7E_4ABF_A54D_FAB5EE073371__INCLUDED_)

