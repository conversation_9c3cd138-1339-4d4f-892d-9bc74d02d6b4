// BitmapTool.h: interface for the BitmapTool class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(_MTLAB_AFX_BITMAPTOOL__46FCBD61_EE07_4EEC_AC54_57C6B05A3DB0__INCLUDED_)
#define _MTLAB_AFX_BITMAPTOOL__46FCBD61_EE07_4EEC_AC54_57C6B05A3DB0__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
#include "mtlab/common/MeituDefine.h"

namespace mtlab {
    class MTLAB_EXPORT CImageRotate{
    public:
        
        /**
         任意角度旋转
         
         @param src        原图数据
         @param srcW       原尺寸
         @param srcH
         @param dstW       结果图尺寸
         @param dstH
         @param stride
         @param rotate     旋转角度
         @param bagColor   旋转完后背景颜色
         @return
         */
        static  unsigned char* BitmapRotateAny(unsigned char* src,int srcW,int srcH,int& dstW,int& dstH,int rotate,const unsigned char* bagColor);
        
    };
    
}


#endif // !defined(AFX_BITMAPTOOL_H__46FCBD61_EE07_4EEC_AC54_57C6B05A3DB0__INCLUDED_)
