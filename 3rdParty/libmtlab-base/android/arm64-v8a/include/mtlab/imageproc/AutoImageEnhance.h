/*****************************************************************
* 图片自动增强类
*
*
* @version: 1.0
*
* @author:  pxdev
*
* @date: 2013-09-23
*
* @change:
*
* @note: 
*
******************************************************************/
#ifndef _MTLAB__MT_ALGO_AUTO_IMAGE_ENHACE_H_
#define _MTLAB__MT_ALGO_AUTO_IMAGE_ENHACE_H_

#include "mtlab/common/MeituDefine.h"

namespace mtlab {
    
    class MTLAB_EXPORT CAutoImageEnhance
    {
    public:
        CAutoImageEnhance(void);
        ~CAutoImageEnhance(void);
        
    public:
        /*
         @annotation added by z<PERSON><PERSON><PERSON><PERSON>
         @brief 自动色调->自动对比度->自然饱和度->USM锐化->微提亮
         @param nStride: 行步长
         */
        void Run(BYTE* pImage, int nWidth, int nHeight, int nStride);
    };
}
#endif // _MT_ALGO_AUTO_IMAGE_ENHACE_H_
