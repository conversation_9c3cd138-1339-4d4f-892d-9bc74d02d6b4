/*****************************************************************
 * PsLevelsUtil, PS色阶调整工具
 * Copyright (c) 2017年 MEITU. All rights reserved.
 *
 * @version: 1.0
 *
 * @author:  7go
 *
 * @date: 2017-10-14
 *
 * @note:
 *
 * @usage：
 *
 ******************************************************************/

#ifndef _MTLAB_MT_PS_LEVELS_UTIL_2n02mm20y_
#define _MTLAB_MT_PS_LEVELS_UTIL_2n02mm20y_

#include "mtlab/common/MeituDefine.h"

namespace mtlab {
    // 色阶调整参数，同PS
    struct LEVELS_PARAM
    {
        //输入色阶参数1
        int nInputShadow;
        //输入色阶参数2
        int nGamma;
        //输入色阶参数3
        int nInputHighlight;
        
        //输出色阶参数1
        int nOutputShadow;
        //输出色阶参数2
        int nOutputHighlight;
        
        LEVELS_PARAM()
        {
            nOutputShadow=0;
            nOutputHighlight=255;
            
            nInputShadow = 0;
            nGamma = 100;
            nInputHighlight = 255;
        }
        
        // 判断是否是默认参，是否有必要做接下去的色阶调整
        bool IsDefaultValue()
        {
            if(nInputShadow != 0 || nInputHighlight != 255 || nGamma != 100 || nOutputShadow != 0 || nOutputHighlight != 255)
            {
                return false;
            }
            
            return true;
        }
        
    };
    
    
    class MTLAB_EXPORT PsLevelsUtil
    {
    public:
        PsLevelsUtil(void);
        ~PsLevelsUtil(void);
        /*
         @brief 输入到输出的映射
         if input>nInputHighlight
            output=nOutputHighlight
         else if input<nInputShadow
            output =nOutputShadow
         else
            output = nOutputShadow+(input-nInputShadow)/(nInputHighlight-nInputShadow)*(nOutputHighlight-nOutputShadow)
         @Param paramLevels: 传入参结构体
         @Param pMap： 传入的256映射表格
         @return：输出新的映射表，存在pMap内，生成的映射表直接对数据做映射
         */
        bool CreateMapTable(LEVELS_PARAM& paramLevels, unsigned char* pMap);        //pMap为长256的字节表
    private:
        void GetTableWithoutParam2(unsigned char *pMap, int nInput1, int nInput3, int nOutput1, int nOutput2);
        void GetTableWithParam2(unsigned char *pMap, int nInput1, int nInput3, int nOutput1, int nOutput2, float lfInput2);
        void GammaAdjust(unsigned char *pMap, float lfInput2);
    };
}

#endif  // MT_PS_LEVELS_UTIL_2n02mm20y_
