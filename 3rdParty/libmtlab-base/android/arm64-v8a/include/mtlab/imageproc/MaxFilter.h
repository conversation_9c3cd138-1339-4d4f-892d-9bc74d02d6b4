//
//  MaxFilter.h
//  mtlabbase
//  最大值算法 ，膨涨算法
//  Created by aidy on 2018/1/17.
//  Copyright © 2018年 Meitu.com. All rights reserved.
//

#ifndef _MTLAB_MTDSP_MAX_Filter_h
#define _MTLAB_MTDSP_MAX_Filter_h

#include "mtlab/common/MeituDefine.h"

namespace mtlab {
    class MTLAB_EXPORT CMaxFilter
    {
    public:
        CMaxFilter(void);
        ~CMaxFilter(void);
        /**
         *  膨涨算法
         *  @brief 最大值膨胀，膨胀半径内取最大值作为输出值
         *  @param pImageSteam
         *  @param nWidth
         *  @param nHeight
         *  @param nRowStride  行步长
         *  @param nLen       膨胀半径 [0,max]
         
         *  @return
         */
        bool Run(BYTE* pImageSteam, int nWidth, int nHeight, int nRowStride, int nLen);
        
    private:
        //经过优化
        void ErodeFilterRGB(BYTE* pImageSteam, BYTE* pDataImage, int nWidth, int nHeight, int nRowStride, int nLen);
        void ErodeFilterGray(BYTE* pImageSteam, BYTE* pDataImage, int nWidth, int nHeight, int nRowStride, int nLen);
        
        //原始版本
        void ErodeFilterRGBOriginal(BYTE* pImageSteam, BYTE* pDataImage, int nWidth, int nHeight, int nRowStride, int nLen);
        void ErodeFilterGrayOriginal(BYTE* pImageSteam, BYTE* pDataImage, int nWidth, int nHeight, int nRowStride, int nLen);
        
    };
}
#endif

