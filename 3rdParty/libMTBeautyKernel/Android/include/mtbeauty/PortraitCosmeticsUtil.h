/*****************************************************************
 * 美颜磨皮通用计算模块
 *
 *
 * @version: 1.0
 *
 * @author:  7go(pxdev)
 *
 * @date: 2015-04-23
 *
 * @note:
 *
 * @change:
 *
 ******************************************************************/
#ifndef _MTLAB__MT_ALGO_PORTRAIT_COSMETICS_3_UTIL_H_
#define _MTLAB__MT_ALGO_PORTRAIT_COSMETICS_3_UTIL_H_

#include "mtlab/common/MeituDefine.h"
#include "mtlab/common/MeituTypes.h"

#include "PortraitCosmeticsDefine.h"

namespace mtbeauty {
    class MTLAB_EXPORT PortraitCosmeticsUtil
    {
    public:
        /*
         获取脸部皮肤区域均色，如果nFaceCount为0，计算全图皮肤蒙版数值>220的均色
         @param pYChannel: 采样的明度图
         @param nWidth: 图宽
         @param nHeight: 图高
         @param nFaceCount: 人脸个数
         @param pFaceInfo: 脸部定位信息
         @param pSkinMask: 皮肤识别蒙版
         @remark: 如果有脸部识别，只去第一个位置的脸部索引信息分析
         */
        static int GetFaceSkinLuminance(BYTE* pYChannel, int nWidth, int nHeight, int nFaceCount, mtlab::MT_FaceInfo* pFaceInfo, BYTE* pSkinMask);
        
        
        /*
         基础磨皮, 通道提亮
         @param pChannel: 提亮通道
         @param nWidth: 图宽
         @param nHeight: 图高
         @param pHighpassMask: 高反差蒙版
         @param pSkinMask: 皮肤蒙版
         @param cFaceAvgY: 肤色均值，用来影响提亮曲线的自动选择
         */
        static void LighterChannel(BYTE* pChannel, int nWidth, int nHeight, BYTE* pHighpassMask, BYTE* pSkinMask, BYTE cFaceAvgY, int nLevel=100);
        
        
        static void ScaleFaceInfo(mtlab::MT_FaceInfo* pSrcFaceInfo, mtlab::MT_FaceInfo* pTagFaceInfo, int nFaceCount, float fScale);
        
        
        // 脸部立体高光
        static void HighlightFace(BYTE* pSrcImage, int nSrcWidth, int nSrcHeight, int nTagWidth, int nTagHeight, int nFaceCount, mtlab::MT_FaceInfo* pSrcFaceInfo, BYTE* pSrcSkinMask, int nFaceAvgY);
    };
}
#endif // _MT_ALGO_PORTRAIT_COSMETICS_3_UTIL_H_

