//
//  PurryEyeManualCleaner.hpp
//  MTXX
//
//  Created by dyz on 2018/11/16.
//  Copyright © 2018年 Meitu. All rights reserved.
//

#ifndef _MTLAB_PurryEyeManualCleaner_hpp
#define _MTLAB_PurryEyeManualCleaner_hpp

#include <mtlab/common/MeituDefine.h>
#include "mtlab/common/Vectors.h"

namespace mtbeauty
{
    class Vector2;
    class CPurryEyeManualCleaner
    {
        public :
        CPurryEyeManualCleaner(void);
        ~CPurryEyeManualCleaner(void);
        
        /*
         /// <summary>
         /// 实现仿天天p图黑眼圈处理效果，Vertion 0.1
         /// </summary>
         /// <param name="pImage">输入的图像数据(RGB)。</param>
         /// <param name="nWidth">输入图像的宽度。</param>
         /// <param name="nHeight">输入图像的高度。</param>
         /// <param name="nStride">输入图像的行步长。</param>
         /// <param name="pMask">输入手动区域Mask。尺寸同pImage大小，黑色，涂抹区域为255</param>
         *
         *  @return 成功返回true，失败返回false
         */
		bool Run(BYTE *pImage, int nWidth, int nHeight, int nStride, BYTE *pMask);
        
    private:
        //天天p图祛眼袋的算法
        int RemovePurryEyeFromTTPi(BYTE *pImage, int nWidth, int nHeight,
                                   int nEyeX, int nEyeY, int nEyeW, int nEyeH, int nEyeDist,
                                   BYTE **pResImg);
        
    };
}

#endif /* PurryEyeManualCleaner_hpp */
