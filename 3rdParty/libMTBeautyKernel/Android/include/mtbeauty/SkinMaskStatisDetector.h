/*****************************************************************
 * SkinMaskStatisDetector 皮肤识别类, 获取皮肤蒙版
 * Copyright (c) 2014年 MEITU. All rights reserved.
 *
 * @version: 1.4
 *
 * @author:  7go
 *
 * @date: 2014-12-24
 *
 * @note: 支持无人脸识别情况
 *
 * @change: ver1.1 2015-01-20 7go 增加收敛条件，收缩皮肤区域，适用于美白这个的局部操作
 * @change: ver1.2 2015-03-04 7go 修改基准肤色计算，增加UV区域的蒙版限制
 * @change: ver1.3 2015-04-13 7go 增加yuv接口
 * @change: ver1.4 2016-06-09 7go 修正RGB接口无人脸识别的蒙版为空bug
 
 ******************************************************************/


#ifndef __MTLAB_SKIN_MASK_GENERATOR_928v2i290nv____
#define __MTLAB_SKIN_MASK_GENERATOR_928v2i290nv____

#include "mtlab/common/MeituDefine.h"
#include "mtlab/common/MeituTypes.h"

namespace mtbeauty {
    
    // 皮肤识别类, 获取皮肤蒙版
    class SkinMaskStatisDetector
    {
    public:
        SkinMaskStatisDetector(void);
        ~SkinMaskStatisDetector(void);
        
        /*
         @param pImage：彩色图
         @param nWidth: 图宽
         @param nHeight: 图高
         @param pSkinMask: 传入的接收蒙版指针，内存由外部分配
         @param nFaceCount: 检测到的人脸个数
         @param pFaceInfo: 检测到的人脸信息
         @param pFaceY: 获取脸部区域大概率是皮肤的主色调Y
         @param pFaceCb: 获取脸部区域大概率是皮肤的主色调Cb
         @param pFaceCr: 获取脸部区域大概率是皮肤的主色调Cr
         @param bConstraint: 皮肤识别是否做一下收敛，适合肤色调整，避免很多背景区域被微调到，皮肤蒙版用来做美白的时候，建议开启true
         */
        void Run(BYTE* pImage, int nWidth, int nHeight, BYTE* pSkinMask, int nFaceCount, mtlab::MT_FaceInfo* pFaceInfo, BYTE* pFaceY, BYTE* pFaceCb, BYTE* pFaceCr, bool bConstraint=false);
        
        /*
         @param pYChannel：Y通道数据流
         @param pUChannel：U通道数据流
         @param pVChannel：V通道数据流
         @param nWidth: 图宽
         @param nHeight: 图高
         @param pSkinMask: 传入的接收蒙版指针，内存由外部分配
         @param nFaceCount: 检测到的人脸个数
         @param pFaceInfo: 检测到的人脸信息
         @param pFaceY: 获取脸部区域大概率是皮肤的主色调Y
         @param pFaceCb: 获取脸部区域大概率是皮肤的主色调Cb
         @param pFaceCr: 获取脸部区域大概率是皮肤的主色调Cr
         @param bConstraint: 皮肤识别是否做一下收敛，适合肤色调整，避免很多背景区域被微调到，皮肤蒙版用来做美白的时候，建议开启true
         */
        void RunYuv(BYTE* pYChannel, BYTE* pCbChannel, BYTE* pCrChannel, int nWidth, int nHeight, BYTE* pSkinMask, int nFaceCount, mtlab::MT_FaceInfo* pFaceInfo, BYTE* pFaceY, BYTE* pFaceCb, BYTE* pFaceCr, bool bConstraint=false);
        
    private:
        void GetFaceColor(BYTE* pImage, int nWidth, int nHeight, mtlab::MT_FaceInfo& faceInfo, BYTE* pAvgY, BYTE* pAvgCb, BYTE* pAvgCr, BYTE* pClipShadow, BYTE* pClipHighlight);
        void GetFaceColorYuv(BYTE* pYChannel, BYTE* pCbChannel, BYTE* pCrChannel, int nWidth, int nHeight, mtlab::MT_FaceInfo& faceInfo, BYTE* pAvgY, BYTE* pAvgCb, BYTE* pAvgCr, BYTE* pClipShadow, BYTE* pClipHighlight);
    };
}

#endif // __SKIN_MASK_GENERATOR_928v2i290nv____

