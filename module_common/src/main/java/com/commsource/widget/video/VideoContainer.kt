package com.commsource.widget.video

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ImageView.ScaleType
import androidx.appcompat.widget.AppCompatImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.commsource.util.gone
import com.commsource.util.visible
import com.commsource.widget.image.ImageLoader
import com.commsource.widget.image.RequestListener
import com.pixocial.videokit.AspectRatio
import com.pixocial.videokit.PlaySource
import com.pixocial.videokit.VideoPlayerState
import com.pixocial.videokit.VideoUIPackage
import com.pixocial.videokit.XPlayerTools
import com.pixocial.videokit.decoder.OnVideoEventListener
import com.pixocial.videokit.decoder.PlayerEvent
import com.pixocial.videokit.view.XVideoContainer
import com.pixocial.videokit.view.XVideoRenderView
import java.io.File
import java.io.FileOutputStream


/**
 * @Desc : 单播放器
 * <AUTHOR> meitu - 2021/8/31
 */
class VideoContainer @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    /**
     * 渲染层
     */
    val renderContainer: XVideoContainer = XVideoContainer(context).apply {
        onVideoEventListener = object : OnVideoEventListener {
            override fun onPlayerEventChange(state: Int) {
                when (state) {
                    PlayerEvent.playChange -> {
                    }

                    PlayerEvent.startRender -> {
                        videoCover.gone()
                        <EMAIL> = 1f
                    }

                    PlayerEvent.playerError -> {
                        videoCover.visible()
                    }
                }
                extraVideoListener?.onPlayerEventChange(state)
            }
        }
    }

    var extraVideoListener: OnVideoEventListener? = null

    /**
     * 封面
     */
    val videoCover: AppCompatImageView =
        AppCompatImageView(context).apply { scaleType = ImageView.ScaleType.CENTER_CROP }

    init {
        //视频播放层
        addView(renderContainer, LayoutParams(-1, -1, Gravity.CENTER))
        //视频封面
        addView(videoCover, LayoutParams(-1, -1, Gravity.CENTER))
    }

    fun isAttachVideo(): Boolean = renderContainer.isAttachRenderView()

    /**
     * 准备视频封面
     */
    fun prepareVideoCover(
        coverPath: String? = null,
        coverUri: Uri? = null,
        coverDrawable: Drawable? = null,
        coverBitmap: Bitmap? = null,
        isAssets: Boolean = false,
        overrideHeight: Int = -1,
        overrideWidth: Int = -1,
        placeHolder: Drawable = ColorDrawable(Color.parseColor("#F1F1F1")),
        scaleType: ScaleType? = null,
        requestListener: RequestListener? = null,
        fadeDisplay: Boolean = false,
    ) {
        videoCover.setImageDrawable(null)
        videoCover.visible()
        scaleType?.let {
            videoCover.scaleType = scaleType
        }
        ImageLoader.with(context)
            .apply {
                if (coverDrawable != null) {
                    load(coverDrawable)
                } else if (coverPath != null) {
                    load(path = coverPath, isAssets = isAssets)
                } else if (coverBitmap != null) {
                    load(coverBitmap)
                } else {
                    load(coverUri)
                }
            }
            .immediatePriority()
            .error(ImageLoader.with(context).load(placeHolder))
            .frameAt(0L)
            .listener(requestListener)
            .overrideSize(overrideWidth, overrideHeight)
            .placeHolderDrawable(placeHolder)
            .into(videoCover)
    }

    /**
     * 激活Video播放
     * @param isLazy 是否检测懒加载的情况
     * 简单激活 这种之后都是业务中可自定义编辑和添加的部分
     */
    private var currentAspectRatio: Int = AspectRatio.AspectRatio_Center_Crop
    fun play(
        playSource: PlaySource,
        isLazy: Boolean = false,
        isLoop: Boolean = true,
        volume: Float = 0f,
        singleMode: Boolean = true,
        aspectRatio: Int = AspectRatio.AspectRatio_Center_Crop,
        videoUIPackage: VideoUIPackage<XVideoStateHolder> = VideoPackages.getDefaultPackage()
    ) {
        if (isLazy && renderContainer.isAttachRenderView()) {
            return
        }
        currentAspectRatio = aspectRatio
        XPlayerTools.detach(renderContainer)
        XPlayerTools.attach(
            renderContainer,
            playSource,
            videoUIPackage,
            originPosition = 0,
            volume,
            isLoop = isLoop,
            singleMode = singleMode,
            displayAspectRatio = aspectRatio
        )
        renderContainer.alpha = 0f
    }

    /**
     * 用于在同一个播放容器中切换播放源的场景
     * 不用play方法的原因：多次调用存在视频切换闪烁问题
     */
    fun switchPlaySource(playSource: PlaySource, isNeedStop: Boolean = false) {
        renderContainer.requestPause()
        if (isNeedStop) {
            renderContainer.renderView?.player?.stop()
        }
        renderContainer.renderView?.player?.setPlaySource(playSource)
        renderContainer.requestStart()
    }


    /**
     * 请求开启
     */
    fun requestStart() {
        renderContainer.requestStart()
    }

    fun seekTo(position: Long) {
        renderContainer.requestSeekTo(position)
    }

    /**
     * 请求暂停
     */
    fun requestPause() {
        renderContainer.requestPause()
    }

    /**
     * 直接释放对应的VideoView
     */
    fun detach() {
        if (renderContainer.isAttachRenderView()) {

            XPlayerTools.detach(renderContainer)
            videoCover.visible()
        }
    }

    /**
     * 安全
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        detach()
    }

    /**
     * 在windowVisible变化
     */
    var restorePlayingInWindowVisibleChange = true

    /**
     * 在页面间跳动处于不可见状态下的视频组件 做内部暂停/播放
     */
    private var isPlayingInWindowInvisible = false
    override fun onWindowVisibilityChanged(visibility: Int) {
        super.onWindowVisibilityChanged(visibility)
        if (isAttachVideo()) {
            if (visibility == View.GONE) {
                isPlayingInWindowInvisible =
                    renderContainer.renderView?.player?.isPlaying() ?: false
                requestPause()
            } else {
                if (isPlayingInWindowInvisible && restorePlayingInWindowVisibleChange) {
                    requestStart()
                }
            }
        }
    }

}