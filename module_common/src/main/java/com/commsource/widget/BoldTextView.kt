package com.commsource.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import com.commsource.util.resColor
import com.meitu.lib_common.R

/**
 *  独立设置文字粗细控件
 * Created on 2020/6/22
 * <AUTHOR>
 */
open class BoldTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : AutoFitTextView(context, attrs) {

    companion object {
        const val BOLD = 0.8f // medium

        const val TAB_BOLD = 1.2f // semibold
    }

    var strokeWidth: Float = 0f
        set(value) {
            field = value
            invalidate()
        }

    init {
        val array = context.obtainStyledAttributes(attrs, R.styleable.BoldTextView)
        strokeWidth = array.getFloat(R.styleable.BoldTextView_boldTextWidth, 0.8f)
        array.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        paint.style = Paint.Style.FILL_AND_STROKE
        paint.strokeWidth = strokeWidth
        super.onDraw(canvas)
    }

    /**
     *  @param isSelect 是否选中
     *  @param selectColor = 选中颜色
     *  @param normalColor = 常规颜色
     */
    fun setTabSelect(
        isSelect: Boolean,
        selectColor: Int = R.color.Gray_A.resColor(),
        normalColor: Int = R.color.Gray_B.resColor()
    ) {
        if (isSelect) {
            strokeWidth = TAB_BOLD
            setTextColor(selectColor)
        } else {
            strokeWidth = 0f
            setTextColor(normalColor)
        }
    }
}