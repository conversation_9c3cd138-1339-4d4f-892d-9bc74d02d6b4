package com.commsource.widget;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.content.res.Resources;
import android.os.Build;
import android.text.TextUtils;

import com.meitu.common.AppContext;
import com.meitu.library.util.device.DeviceUtils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

/**
 * 屏幕刘海检测适配
 *
 * <AUTHOR> Email: <EMAIL>
 * @version 1.0.0
 * Create at: 2018/9/5
 */
public class DisplayExtension {
    private final static int CUTOUT_MODE_INVALID = 0;
    private final static int CUTOUT_MODE_NONE = 1;
    private final static int CUTOUT_MODE_EXIST = 2;
    private static int CUTOUT_MODE = CUTOUT_MODE_INVALID;

    /**
     * 判断手机是否是刘海屏
     */
    public static boolean hasCutout() {
        Application application = AppContext.getApplication();
        if (application != null) {
            if (CUTOUT_MODE == CUTOUT_MODE_INVALID) {
                final int[] values = new int[]{CUTOUT_MODE_NONE};
                if (checkOPPO(application, values)
                        || checkVIVO(application, values)
                        || checkEMUI(application, values)
                        || checkMeitu(application, values)
                        || checkXiaomi(application, values)
                        || checkSamsung(application, values)) {
                    CUTOUT_MODE = values[0];
                } else {
                    CUTOUT_MODE = CUTOUT_MODE_NONE;
                }
            }
        }
        return CUTOUT_MODE == CUTOUT_MODE_EXIST || DeviceUtils.isFullScreenDevice();
//        return DeviceUtils.isFullScreenDevice();
    }

    @SuppressLint("MethodNameLowerCameCase")
    private static boolean checkOPPO(Application application, int[] values) {
        boolean ret = application.getPackageManager().hasSystemFeature("com.oppo.feature.screen.heteromorphism");
        if (ret) {
            values[0] = CUTOUT_MODE_EXIST;
            return true;
        }
        return false;
    }

    @SuppressLint("MethodNameLowerCameCase")
    private static boolean checkVIVO(Application application, int[] values) {
        final int NOTCH_IN_SCREEN_VOIO = 0x00000020;//是否有凹槽
        // final int ROUNDED_IN_SCREEN_VOIO = 0x00000008;//是否有圆角
        try {
            ClassLoader cl = application.getClassLoader();
            @SuppressLint("PrivateApi")
            Class aClass = cl.loadClass("android.util.FtFeature");
            @SuppressWarnings("unchecked")
            Method get = aClass.getMethod("isFeatureSupport", int.class);
            if ((boolean) get.invoke(aClass, NOTCH_IN_SCREEN_VOIO)) {
                values[0] = CUTOUT_MODE_EXIST;
            }
            return true;
        } catch (ClassNotFoundException ignored) {
        } catch (NoSuchMethodException ignored) {
        } catch (Exception ignored) {
        }
        return false;
    }

    // 华为手机
    @SuppressLint("MethodNameLowerCameCase")
    private static boolean checkEMUI(Application application, int[] values) {
        try {
            ClassLoader cl = application.getClassLoader();
            Class aClass = cl.loadClass("com.huawei.android.util.HwNotchSizeUtil");
            //noinspection unchecked
            Method get = aClass.getMethod("hasNotchInScreen");
            if ((boolean) get.invoke(aClass)) {
                values[0] = CUTOUT_MODE_EXIST;
            }
            return true;
        } catch (ClassNotFoundException ignored) {
        } catch (NoSuchMethodException ignored) {
        } catch (Exception ignored) {
        }
        return false;
    }

    // 美图手机
    private static boolean checkMeitu(Application application, int[] values) {
        try {
            ClassLoader cl = application.getClassLoader();
            Class aClass = cl.loadClass("com.meitu.mobile.cutout.CutOutUtils");
            //noinspection unchecked
            Constructor constructor = aClass.getDeclaredConstructor(Context.class);
            constructor.setAccessible(true);
            Object instance = constructor.newInstance(application);
            //noinspection unchecked
            Method get = aClass.getMethod("isCutOutScreen");
            if ((boolean) get.invoke(instance)) {
                values[0] = CUTOUT_MODE_EXIST;
            }
            return true;
        } catch (ClassNotFoundException ignored) {
        } catch (NoSuchMethodException ignored) {
        } catch (Exception ignored) {
        }
        return false;
    }

    // 小米手机
    private static boolean checkXiaomi(Application application, int[] values) {
        String manufacturer = Build.MANUFACTURER;
        if (TextUtils.isEmpty(manufacturer)) {
            return false;
        }
        if (manufacturer.toLowerCase().contains("Xiaomi".toLowerCase())) {
            try {
                ClassLoader cl = application.getClassLoader();
                @SuppressLint("PrivateApi")
                Class aClass = cl.loadClass("android.os.SystemProperties");
                //noinspection unchecked
                Method get = aClass.getMethod("getInt", String.class, int.class);
                if (((int) get.invoke(aClass, "ro.miui.notch", 0)) == 1) {
                    values[0] = CUTOUT_MODE_EXIST;
                }
                return true;
            } catch (ClassNotFoundException ignored) {
            } catch (NoSuchMethodException ignored) {
            } catch (Exception ignored) {
            }
        }
        return false;
    }

    // 三星手机
    private static boolean checkSamsung(Application application, int[] values) {
        try {
            final Resources res = application.getResources();
            final int resId = res.getIdentifier("config_mainBuiltInDisplayCutout", "string", "android");
            final String spec = resId > 0 ? res.getString(resId) : null;
            if (spec != null && !TextUtils.isEmpty(spec)) {
                values[0] = CUTOUT_MODE_EXIST;
                return true;
            }
        } catch (Exception e) {
        }
        return false;
    }
}
