package com.commsource.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateInterpolator
import androidx.annotation.FloatRange
import com.commsource.camera.util.XAnimator
import com.commsource.camera.util.XAnimatorCalculateValuer
import com.commsource.util.RTLTool
import com.commsource.util.ResourcesUtils
import com.commsource.util.dpf
import com.commsource.util.hapticFeedBack
import com.commsource.widget.part.XSeekBackgroundPart
import com.commsource.widget.part.XSeekCenterExpandBackgroundPart
import com.commsource.widget.part.XSeekCenterPositionPart
import com.commsource.widget.part.XSeekDefaultPositionPart
import com.commsource.widget.part.XSeekProgressPart
import com.commsource.widget.part.XSeekThumbIndicatorPart
import com.commsource.widget.part.XSeekThumbPart
import com.meitu.lib_common.R
import com.meitu.library.util.device.DeviceUtils

/**
 * @Desc : 一个内容可编辑的Seekbar 比较简易添加自动滚动和中心可移动模式
 *
 * [defaultPosition] SeekBar默认位置
 * [centerPointPercent] SeekBar进度中心位置
 * [isEnableExpandMode] 是否支持滑杆一种渐进变化
 * [isEnableAutoAdsorbPosition] 是否支持自动吸附
 * [isEnableStroke] 是否支持描边
 * [isEnableCenterPoint] 是否需要中心点
 * [isEnableThumbIndicator] 是否需要thumb中心指示器
 *
 * TODO 因为修改为kotlin 部分参数未收起 之后可提供修改
 *
 * <AUTHOR> csxiong - 2020-01-14
 */
class XSeekBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    /**
     * 部分整合在一个风格的参数
     */
    companion object {

        /**
         * 调整气泡的距离
         */
        val BUBBLE_FIX_DISTANCE = 8.dpf()

        /**
         * 气泡残余停留时间
         */
        const val BUBBLE_DURATION: Long = 300

        /**
         * 气泡展示时间
         */
        const val BUBBLE_SHOW_DURATION: Long = 200

        /**
         * 自动吸附范围值
         */
        const val AUTO_ADSORB_FIXED_VALUE = 2
    }

    /**
     * 是否允许描边
     */
    var isEnableStroke = true
        set(value) {
            field = value
            invalidate()
        }

    /**
     * 是否需要中心点
     */
    var isEnableCenterPoint = false

    /**
     * 是否支持图钉中心指示器
     */
    var isEnableThumbIndicator = false

    /**
     * 是否支持自动吸附
     */
    var isEnableAutoAdsorbPosition = true

    /**
     * 是否支持扩张模式
     */
    var isEnableExpandMode = false
        set(value) {
            field = value
            expand(value)
            updateDrawPart(false)
            invalidate()
        }

    /**
     * 背景颜色
     */
    var thumbBackgroundColor = ResourcesUtils.getColor(R.color.color_80f2f2f2)
        set(value) {
            field = value
            thumbBackgroundPaint.color = thumbBackgroundColor
            invalidate()
        }

    /**
     * 背景笔
     */
    var thumbBackgroundPaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)

    /**
     * 描边颜色
     */
    var strokeColor = ResourcesUtils.getColor(R.color.color_4d000000)

    /**
     * 描边笔
     */
    var strokePaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)

    /**
     * 进度颜色
     */
    var progressColor = -0x1
        set(value) {
            field = value
            progressPaint.color = progressColor
            invalidate()
        }

    /**
     * 普通颜色笔
     */
    var progressPaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)

    /**
     * 图钉中心指示器颜色
     */
    var thumbIndicatorColor = Color.WHITE
        set(value) {
            field = value
            thumbIndicatorPaint.color = thumbIndicatorColor
            invalidate()
        }

    /**
     * 图钉指示器画笔
     */
    var thumbIndicatorPaint: Paint = Paint(Paint.ANTI_ALIAS_FLAG)

    /**
     * 图钉半径
     */
    var thumbRadius = DeviceUtils.dip2px(9.5f)

    /**
     * 中心点宽度
     */
    var centerPointWidth = DeviceUtils.dip2px(3f)

    /**
     * 中心点point高度
     */
    var centerPointHeight = DeviceUtils.dip2px(7f)

    /**
     * bar高度
     */
    var seekBarHeight = DeviceUtils.dip2fpx(2f)

    /**
     * bar内容高度 可变
     */
    var contentHeight = seekBarHeight

    /**
     * 扩展的高度
     */
    var expandHeight = DeviceUtils.dip2fpx(14f)

    /**
     * 进度坐标X
     */
    var progressX = 0f

    /**
     * 进度浮点 为了UI渲染在progress范围小时 UI显得卡顿
     */
    var progress = 0f

    /**
     * 整型进度 回调进度
     */
    var intProgress = 0

    /**
     * 中心点位于滑杆位置
     */
    var centerPointPercent = 0f
        set(value) {
            field = value
            centerPointPositionX = calculateProgressX((maxProgress + minProgress) / 2f)
            updateDrawPart(false)
            invalidate()
        }

    /**
     * 中心点X坐标
     */
    var centerPointPositionX = 0f

    /**
     * 描边宽度
     */
    val strokeWidth = DeviceUtils.dip2fpx(.5f)

    /**
     * 最小进度
     */
    var minProgress = 0

    /**
     * 最大进度
     */
    var maxProgress = 100

    /**
     * View宽度
     */
    var viewWidth = 0

    /**
     * View高度
     */
    var viewHeight = 0

    /**
     * bar宽度 -> 0-100 真实的bar宽度 背景因为兼容滑杆非圆心对称 不是真实progress的宽度
     */
    var barWidth = 0f
    private var progressStartX = 0f
    private var progressEndX = 0f

    /**
     * 默认位置的半径
     */
    val defaultRadius = DeviceUtils.dip2px(2.5f)

    /**
     * 默认位置X
     */
    var defaultPositionX = 0f

    /**
     * 默认位置
     */
    @FloatRange(from = 0.0, to = 1.0)
    var defaultPosition = 0.0f
        set(value) {
            field = value
            defaultProgress = (defaultPosition * (maxProgress - minProgress)).toInt()
            defaultPositionX = calculateProgressX(defaultProgress.toFloat())
            updateDrawPart(false)
            invalidate()
        }

    /**
     * 默认进度
     */
    var defaultProgress = 0

    /**
     * 期望进度
     */
    var forwardProgress = 0f

    /**
     * 是否可用
     */
    var isSeekEnable = true

    /**
     * 进度计算
     */
    val progressValuer = XAnimatorCalculateValuer()

    /**
     * 绘制组件
     */
    private val drawParts = ArrayList<XSeekDrawPart>()
        .apply {
            add(XSeekBackgroundPart(this@XSeekBar))
            add(XSeekCenterExpandBackgroundPart(this@XSeekBar))
            add(XSeekProgressPart(this@XSeekBar))
            add(XSeekCenterPositionPart(this@XSeekBar))
            add(XSeekDefaultPositionPart(this@XSeekBar))
            add(XSeekThumbPart(this@XSeekBar))
            add(XSeekThumbIndicatorPart(this@XSeekBar))
        }

    /**
     * onProgressChanges 进度监听组件
     */
    private val onProgressChanges = ArrayList<OnProgressChangeListener>()

    /**
     * Progress执行动画
     */
    private val progressAnimator by lazy {
        XAnimator.ofFloat(0f, 1f)
            .duration(300)
            .interpolator(AccelerateInterpolator())
            .setAnimationListener(object : XAnimator.XAnimationListener {
                override fun onAnimationUpdate(fraction: Float, value: Float) {
                    val curProgress = progressValuer.calculateValue(fraction)
                    progressX = calculateProgressX(curProgress)
                    setProgressInner(curProgress, false)
                }

                override fun onAnimationStart(animation: XAnimator) {
                    progressValuer.mark(progress, forwardProgress)
                }

                override fun onAnimationEnd(animation: XAnimator) {
                    for (listener in onProgressChanges) {
                        listener.onProgressChange(intProgress, progressX, false)
                    }
                }

                override fun onAnimationCancel(animation: XAnimator) {
                    for (listener in onProgressChanges) {
                        listener.onProgressChange(intProgress, progressX, false)
                    }
                }
            })
    }

    /**
     * Expand高度改变值
     */
    private var contentHeightValuer = XAnimatorCalculateValuer()

    var onVisibilityChangeAction: ((isVisible: Boolean) -> Unit)? = null

    init {
        initAttrs(context, attrs)
        thumbBackgroundPaint.color = thumbBackgroundColor
        thumbBackgroundPaint.style = Paint.Style.FILL_AND_STROKE
        strokePaint.color = strokeColor
        strokePaint.strokeWidth = strokeWidth
        strokePaint.style = Paint.Style.STROKE
        progressPaint.color = progressColor
        progressPaint.style = Paint.Style.FILL_AND_STROKE
        thumbIndicatorPaint.color = thumbIndicatorColor
        thumbIndicatorPaint.style = Paint.Style.FILL_AND_STROKE
    }

    /**
     * 基础参数获取
     */
    private fun initAttrs(context: Context?, attrs: AttributeSet?) {
        if (context != null && attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.XSeekBar)
            isEnableStroke = typedArray.getBoolean(R.styleable.XSeekBar_isEnableStroke, false)
            isEnableCenterPoint =
                typedArray.getBoolean(R.styleable.XSeekBar_isEnableCenterPoint, false)
            isEnableThumbIndicator =
                typedArray.getBoolean(R.styleable.XSeekBar_isEnableThumbIndicator, false)
            thumbIndicatorColor =
                typedArray.getColor(R.styleable.XSeekBar_xThumbIndicatorColor, -0x4a67a)
            thumbBackgroundColor =
                typedArray.getColor(R.styleable.XSeekBar_xBackgroundColor, -0x7f0d0d0e)
            strokeColor = typedArray.getColor(
                R.styleable.XSeekBar_xStrokeColor, ResourcesUtils.getColor(R.color.color_4d000000)
            )
            progressColor = typedArray.getColor(R.styleable.XSeekBar_xProgressColor, -0x1)
            thumbRadius = typedArray.getDimensionPixelSize(
                R.styleable.XSeekBar_xThumbRadius, DeviceUtils.dip2px(9.5f)
            )
            centerPointWidth = typedArray.getDimensionPixelSize(
                R.styleable.XSeekBar_xCenterPointWidth, DeviceUtils.dip2px(3f)
            )
            centerPointHeight = typedArray.getDimensionPixelSize(
                R.styleable.XSeekBar_xCenterPointHeight, DeviceUtils.dip2px(7f)
            )
            seekBarHeight = typedArray.getDimensionPixelSize(
                R.styleable.XSeekBar_xSeekbarHeight, DeviceUtils.dip2px(2.5f)
            ).toFloat()
            contentHeight = seekBarHeight
            centerPointPercent = typedArray.getFloat(R.styleable.XSeekBar_xCenterPointPercent, 0f)
            maxProgress = typedArray.getInteger(R.styleable.XSeekBar_xMaxProgress, 100)
            minProgress = typedArray.getInteger(R.styleable.XSeekBar_xMinProgress, 0)
            progress = typedArray.getInteger(R.styleable.XSeekBar_xProgress, 0).toFloat()
            typedArray.recycle()
        }
    }

    /**
     * 绘制
     */
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        for (drawPart in drawParts) {
            drawPart.onDraw(canvas)
        }
    }

    /**
     * 设置进度
     *
     * @param progress
     */
    fun setProgress(progress: Int) {
        setProgress(progress, false)
    }

    /**
     * 设置进度
     *
     * @param progress
     * @param withAnimation
     */
    fun setProgress(progress: Int, withAnimation: Boolean) {
        if (withAnimation) {
            forwardProgress = progress.toFloat()
            progressAnimator.cancel()
            progressAnimator.start()
        } else {
            //外部设置ProgressX需要计算这个
            progressX = calculateProgressX(progress.toFloat())
            setProgressInner(progress.toFloat(), false)
        }
    }

    /**
     * 内部设置进度
     *
     * @param progress 期望进度
     */
    private fun setProgressInner(progress: Float, fromUser: Boolean) {
        //安全范围
        when {
            progress < minProgress -> this.progress = minProgress.toFloat()
            progress > maxProgress -> this.progress = maxProgress.toFloat()
            else -> this.progress = progress
        }
        //校准Progress
        var newIntProgress =
            if (this.progress < 0) (this.progress - .1f).toInt() else (this.progress + .1f).toInt()
        if (intProgress != newIntProgress) {
            intProgress = newIntProgress
            for (listener in onProgressChanges) {
                listener.onProgressChange(intProgress, progressX, fromUser)
            }
        }
        //基础的回调
        for (listener in onProgressChanges) {
            listener.onPositionChange(intProgress, progressX, fromUser)
        }
        //内部回调
        updateDrawPart(fromUser)
        //刷新
        invalidate()
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        initSize(right - left, bottom - top)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        initSize(w, h)
    }

    var adsorbFixedValue = AUTO_ADSORB_FIXED_VALUE

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isEnabled || !isSeekEnable) {
            return super.onTouchEvent(event)
        }
        //计算手势触控进度百分比
        val action = event.action
        var tempX = event.x
        //限制手势范围
        if (RTLTool.isLayoutRtl()) {
            when {
                tempX > progressStartX -> tempX = progressStartX
                tempX < progressEndX -> tempX = progressEndX
            }
        } else {
            when {
                tempX < progressStartX -> tempX = progressStartX
                tempX > progressEndX -> tempX = progressEndX
            }
        }
        // 计算新进度
        var newProgress = if (RTLTool.isLayoutRtl()) {
            (viewWidth - limitStart - tempX) * (maxProgress - minProgress) / barWidth + minProgress
        } else {
            (tempX - limitStart) * (maxProgress - minProgress) / barWidth + minProgress
        }
        var newIntProgress: Int = newProgress.toInt()
        // 计算对应Progress 下的progressX
        progressX = calculateProgressX(newProgress)
        //检测中心吸附
        val centerProgress = (maxProgress + minProgress) / 2f
        if (isEnableCenterPoint && isEnableAutoAdsorbPosition) {
            if (newIntProgress >= (centerProgress - adsorbFixedValue) && newIntProgress <= (centerProgress + adsorbFixedValue)) {
                progressX = calculateProgressX(centerProgress)
                newProgress = centerProgress
                newIntProgress = centerProgress.toInt()
            }
        }
        //检测默认值吸附
        if (defaultProgress != 0 && isEnableAutoAdsorbPosition) {
            if (newIntProgress >= defaultProgress - adsorbFixedValue && newIntProgress <= defaultProgress + adsorbFixedValue) {
                progressX = calculateProgressX(defaultProgress.toFloat())
                newProgress = defaultProgress.toFloat()
                newIntProgress = defaultProgress
            }
        }
        //判断Int级别的数值是否改变
        //浮点数progress只是为了滑动顺畅使用 无极调整
        var isChange = newIntProgress != intProgress
        //如果自动吸附 需要自动吸附 + 震动
        if (isEnableAutoAdsorbPosition && isChange
            && ((defaultProgress != 0 && newIntProgress == defaultProgress)
                    || (isEnableCenterPoint && newIntProgress == centerProgress.toInt()))
        ) {
            hapticFeedBack()
        }
        setProgressInner(newProgress, true)
        //对应发生变化
        if (action == MotionEvent.ACTION_DOWN) {
            for (listener in onProgressChanges) {
                listener.onStartTracking(intProgress, progressX)
            }
            inTracking = true
            parent?.requestDisallowInterceptTouchEvent(true)
            return true
        } else if (action == MotionEvent.ACTION_MOVE) {
            return true
        } else if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL) {
            for (listener in onProgressChanges) {
                listener.onProgressChange(intProgress, progressX, true)
                listener.onStopTracking(intProgress, progressX, true)
            }
            inTracking = false
            parent?.requestDisallowInterceptTouchEvent(false)
        }
        return super.onTouchEvent(event)
    }

    var inTracking: Boolean = false

    /**
     * 适配ViewPadding
     *
     * @return
     */
    private val limitStart: Float
        get() = paddingStart + thumbRadius + strokeWidth * 2

    /**
     * 适配ViewPadding
     *
     * @return
     */
    private val limitEnd: Float
        get() = paddingEnd + thumbRadius + strokeWidth * 2

    /**
     * 初始化size
     */
    private fun initSize(width: Int, height: Int) {
        viewWidth = width
        viewHeight = height
        barWidth = width - limitStart - limitEnd

        // RTL适配
        if (RTLTool.isLayoutRtl()) {
            progressStartX = width - limitStart
            progressEndX = limitEnd
        } else {
            progressStartX = limitStart
            progressEndX = width - limitEnd
        }
        progressX = calculateProgressX(progress)
        centerPointPositionX = calculateProgressX((maxProgress + minProgress) / 2f)

        setProgressInner(progress, false)
        updateDrawPart(false)
    }

    fun addOnProgressChangeListener(onProgressChangeListener: OnProgressChangeListener?) {
        onProgressChangeListener?.takeIf { !onProgressChanges.contains(it) }
            ?.let {
                onProgressChanges.add(it)
            }
    }

    /**
     * 移除监听
     */
    fun removeOnProgressChangeListener(onProgressChangeListener: OnProgressChangeListener?) {
        onProgressChangeListener?.takeIf { onProgressChanges.contains(it) }
            ?.let {
                onProgressChanges.remove(it)
            }
    }

    /**
     * 进度改变监听
     */
    interface OnProgressChangeListener {

        /**
         * 开始拖动
         *
         * @param progress
         */
        fun onStartTracking(progress: Int, leftDx: Float) {}

        /**
         * 进度改变
         *
         * @param progress
         * @param fromUser
         */
        fun onProgressChange(progress: Int, leftDx: Float, fromUser: Boolean) {}

        /**
         * 位置改变监听
         *
         * @param leftDx
         */
        fun onPositionChange(progress: Int, leftDx: Float, fromUser: Boolean) {}

        /**
         * 停止拖动
         *
         * @param progress
         */
        fun onStopTracking(progress: Int, leftDx: Float, fromUser: Boolean) {}
    }

    /**
     * 计算获得正确的ProgressX
     */
    fun calculateProgressX(progress: Float): Float {
        if (RTLTool.isLayoutRtl()) {
            var curProgressX =
                viewWidth - (progress - minProgress) * barWidth / (maxProgress - minProgress) - limitStart
            when {
                curProgressX > progressStartX -> curProgressX = progressStartX
                curProgressX < progressEndX -> curProgressX = progressEndX
            }
            return curProgressX
        } else {
            var curProgressX =
                (progress - minProgress) * barWidth / (maxProgress - minProgress) + limitStart
            when {
                curProgressX < progressStartX -> curProgressX = progressStartX
                curProgressX > progressEndX -> curProgressX = progressEndX
            }
            return curProgressX
        }
    }

    /**
     * 通过进度计算百分比
     */
    fun calculateProgressPercent(progress: Float): Float {
        return (progress - minProgress) / (maxProgress - minProgress)
    }

    /**
     * 设置Seekbar是否可用
     */
    fun setSeekEnable(seekEnable: Boolean, isDeep: Boolean = false) {
        isSeekEnable = seekEnable
        alpha = if (isSeekEnable) {
            1.0f
        } else {
            if (isDeep) 0.15f else 0.5f
        }
    }

    /**
     * 更新组件
     */
    private fun updateDrawPart(fromUser: Boolean) {
        for (drawPart in drawParts) {
            drawPart.calculateDrawValue(fromUser)
        }
    }

    fun expand(isExpand: Boolean) {
        contentHeight = if (isExpand) expandHeight else seekBarHeight
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (changedView == this) {
            onVisibilityChangeAction?.invoke(visibility == VISIBLE)
        }
    }
}