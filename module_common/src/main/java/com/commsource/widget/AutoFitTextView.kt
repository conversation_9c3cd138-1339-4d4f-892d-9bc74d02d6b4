package com.commsource.widget

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.util.TypedValue
import androidx.appcompat.widget.AppCompatTextView
import com.meitu.lib_common.R

/**
 * Created by luohaoyang on 2018/3/20.
 */
open class AutoFitTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {
    private var mMinTextSize = 0
    private var mMaxLines = 0
    private var mInitTextSize: Float
    private var strokeWidth = 0f
    private val mBorderPaint = Paint()
    private val mRoundBorderRect = RectF()
    private var mBorderWidth = 0
    private var mRoundRadius = 0

    init {
        if (attrs != null) {
            val a = context.obtainStyledAttributes(attrs, R.styleable.AutoFitTextView)
            mMinTextSize = a.getDimensionPixelSize(R.styleable.AutoFitTextView_minTextSize, 0)
            mMaxLines = a.getInt(R.styleable.AutoFitTextView_expectMaxLines, 0)
            strokeWidth = a.getFloat(R.styleable.AutoFitTextView_strokeTextSize, 0f)
            mBorderWidth = a.getDimensionPixelSize(R.styleable.AutoFitTextView_border_width, 0)
            mRoundRadius = a.getDimensionPixelSize(R.styleable.AutoFitTextView_border_radius, 0)
            val borderColor = a.getColor(R.styleable.AutoFitTextView_border_color, Color.BLACK)
            mBorderPaint.style = Paint.Style.STROKE
            mBorderPaint.isAntiAlias = true
            mBorderPaint.color = borderColor
            mBorderPaint.strokeWidth = mBorderWidth.toFloat()
            a.recycle()
        }
        if (strokeWidth != 0f) {
            val textPaint = paint
            textPaint.strokeWidth = strokeWidth
            textPaint.style = Paint.Style.FILL_AND_STROKE
        }
        mInitTextSize = textSize
    }

    override fun setTextSize(unit: Int, size: Float) {
        super.setTextSize(unit, size)
        mInitTextSize = textSize
    }

    override fun setText(text: CharSequence?, type: BufferType) {
        super.setText(text, type)
        if (mInitTextSize == 0f) {
            mInitTextSize = textSize
        }
        // 设置了文案，需要重新测量。
        setTextSize(TypedValue.COMPLEX_UNIT_PX, mInitTextSize)
        requestLayout()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        if (mInitTextSize == 0f) {
            mInitTextSize = textSize
        }
        if (widthMode != MeasureSpec.UNSPECIFIED) {
            if (mMinTextSize != 0) {
                autoFit(textSize, widthSize, mMaxLines, 0.1f)
            } else {
                autoFit(textSize, widthSize, maxLines, 0.1f)
            }
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mRoundBorderRect[0f, 0f, w.toFloat()] = h.toFloat()
        mRoundBorderRect.inset(mBorderWidth / 2f, mBorderWidth / 2f)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (mBorderWidth != 0) {
            canvas.drawRoundRect(
                mRoundBorderRect,
                mRoundRadius.toFloat(),
                mRoundRadius.toFloat(),
                mBorderPaint
            )
        }
    }

    /**
     * Re-sizes the textSize of the TextView so that the text fits within the bounds of the View.
     */
    private fun autoFit(maxTextSize: Float, width: Int, maxLines: Int, precision: Float) {
        if (maxLines <= 0 || maxLines == Int.MAX_VALUE) {
            // Don't auto-size since there's no limit on lines.
            return
        }
        val targetWidth = width - paddingStart - paddingEnd
        if (targetWidth <= 0) {
            return
        }
        var text = text
        val method = transformationMethod
        if (method != null) {
            text = method.getTransformation(text, this)
        }
        val context = context
        var r = Resources.getSystem()
        val displayMetrics: DisplayMetrics
        var size = maxTextSize
        val high = size
        val low = mMinTextSize.toFloat()
        if (context != null) {
            r = context.resources
        }
        displayMetrics = r.displayMetrics
        val paint = TextPaint()
        paint.set(getPaint())
        paint.textSize = size
        if (maxLines == 1 && paint.measureText(text, 0, text.length) > targetWidth
            || getLineCount(text, paint, size, targetWidth.toFloat(), displayMetrics) > maxLines
        ) {
            size = getAutoFitTextSize(
                text,
                paint,
                targetWidth.toFloat(),
                maxLines,
                low,
                high,
                precision,
                displayMetrics
            )
            paint.textSize = size
            if (mMinTextSize != 0 && getLineCount(
                    text,
                    paint,
                    size,
                    targetWidth.toFloat(),
                    displayMetrics
                ) > maxLines
            ) {
                paint.textSize = maxTextSize
                size = getAutoFitTextSize(
                    text,
                    paint,
                    targetWidth.toFloat(),
                    getMaxLines(),
                    0f,
                    high,
                    precision,
                    displayMetrics
                )
            }
        }
        setTextSize(TypedValue.COMPLEX_UNIT_PX, size)
    }

    companion object {
        private fun getLineCount(
            text: CharSequence, paint: TextPaint, size: Float, width: Float,
            displayMetrics: DisplayMetrics
        ): Int {
            paint.textSize = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_PX,
                size,
                displayMetrics
            )
            val layout = StaticLayout(
                text,
                paint,
                width.toInt(),
                Layout.Alignment.ALIGN_NORMAL,
                1.0f,
                0.0f,
                true
            )
            return layout.lineCount
        }

        /**
         * Recursive binary search to find the best size for the text.
         */
        private fun getAutoFitTextSize(
            text: CharSequence, paint: TextPaint, targetWidth: Float, maxLines: Int,
            low: Float, high: Float, precision: Float, displayMetrics: DisplayMetrics
        ): Float {
            val mid = (low + high) / 2.0f
            var lineCount = 1
            var layout: StaticLayout? = null
            paint.textSize = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_PX,
                mid,
                displayMetrics
            )
            if (maxLines != 1) {
                layout = StaticLayout(
                    text,
                    paint,
                    targetWidth.toInt(),
                    Layout.Alignment.ALIGN_NORMAL,
                    1.0f,
                    0.0f,
                    true
                )
                lineCount = layout.lineCount
            }
            return if (lineCount > maxLines) {
                // For the case that `text` has more newline characters than `maxLines`.
                if (high - low < precision) {
                    low
                } else getAutoFitTextSize(
                    text,
                    paint,
                    targetWidth,
                    maxLines,
                    low,
                    mid,
                    precision,
                    displayMetrics
                )
            } else if (lineCount < maxLines) {
                getAutoFitTextSize(
                    text,
                    paint,
                    targetWidth,
                    maxLines,
                    mid,
                    high,
                    precision,
                    displayMetrics
                )
            } else {
                if (maxLines == 1) {
                    var maxLineWidth = 0f
                    maxLineWidth = paint.measureText(text, 0, text.length)
                    if (high - low < precision) {
                        low
                    } else if (maxLineWidth > targetWidth) {
                        getAutoFitTextSize(
                            text,
                            paint,
                            targetWidth,
                            maxLines,
                            low,
                            mid,
                            precision,
                            displayMetrics
                        )
                    } else if (maxLineWidth < targetWidth) {
                        getAutoFitTextSize(
                            text,
                            paint,
                            targetWidth,
                            maxLines,
                            mid,
                            high,
                            precision,
                            displayMetrics
                        )
                    } else {
                        mid
                    }
                } else {
                    if (high - low < precision) {
                        low
                    } else {
                        getAutoFitTextSize(
                            text,
                            paint,
                            targetWidth,
                            maxLines,
                            mid,
                            high,
                            precision,
                            displayMetrics
                        )
                    }
                }
            }
        }
    }
}