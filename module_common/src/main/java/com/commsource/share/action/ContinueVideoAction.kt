package com.commsource.share.action

import com.commsource.share.NextAction
import com.commsource.util.ResourcesUtils
import com.meitu.lib_common.R

/**
 * @Description: 去拼图
 *
 * @Author: vinvince, @Time: 2023/7/14 16:28
 */
class ContinueVideoAction : NextAction() {

    override var drawableId: Int = R.string.edit_share_icon_new

    override var actionName: String? = ContinueVideo

    override var showName: String? = ResourcesUtils.getString(R.string.t_continue_video_edit)
}