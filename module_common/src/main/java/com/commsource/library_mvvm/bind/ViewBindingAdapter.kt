package com.commsource.library_mvvm.bind

import android.annotation.SuppressLint
import android.view.View
import androidx.databinding.BindingAdapter
import com.commsource.library_mvvm.bind.command.BindingCommand


/**
 * 通用的View bindingAdapter
 * <p>
 * Date: 2021-03-10
 * Company:
 * Updater:
 * Update Time:
 * Update Comments:
 *
 * Author:
 */
class ViewBindingAdapter

/**
 * requireAll 是意思是是否需要绑定全部参数, false为否
 * View的onClick事件绑定
 * onClickCommand 绑定的命令,
 * isThrottleFirst 是否开启防止过快点击
 */
@SuppressLint("CheckResult")
@BindingAdapter(value = ["onClickCommand", "isThrottleFirst"], requireAll = false)
fun View.onClickCommand(clickCommand: BindingCommand<*>?, isThrottleFirst: Boolean? = true) {
    //不拦截
    if (isThrottleFirst == false) {
        setOnClickListener {
            clickCommand?.execute()
        }
    } else {
        //1秒内只能点击一次
        setOnClickListener { clickCommand?.execute() }
    }
}


/**
 * 增加返回view对西那个
 * <p>
 * Author:
 * Date: 2021-03-30
 * @receiver View
 * @param clickCommand BindingCommand<*>?
 * @param isThrottleFirst Boolean?
 */
@SuppressLint("CheckResult")
@BindingAdapter(value = ["onClickWithViewCommand", "isThrottleFirst"], requireAll = false)
fun View.onClickWithViewCommand(
    clickCommand: BindingCommand<View>?,
    isThrottleFirst: Boolean? = true
) {
    //不拦截
    if (isThrottleFirst == false) {
        setOnClickListener {
            clickCommand?.execute(it)
        }
    } else {
        //1秒内只能点击一次
        setOnClickListener { v -> clickCommand?.execute(v) }
    }
}

