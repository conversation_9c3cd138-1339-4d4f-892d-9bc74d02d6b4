package com.commsource.util.common

import android.content.Context
import android.util.Log
import androidx.annotation.WorkerThread
import com.meitu.common.AppContext
import com.tencent.mmkv.MMKV

/**
 * MMKV 迁移配置。
 */
object MMKVMigrateHelper {
    @Retention(AnnotationRetention.SOURCE)
    annotation class MMKVMigrateState {
        companion object {
            const val NONE = -1
            const val Migrated = -2
            const val Migrating = 1
        }
    }


    // 迁移状态的表名字
    private const val MIGRATE_STATE_TABLE = "MIGRATE_STATE_TABLE"
    private const val MIGRATE_BOUNDS = "MIGRATE_BOUNDS"

    private const val CurVersionCanMigrated = "CurVersionCanMigrated"

    private val list = mutableListOf<List<String>>()

    private val hasMigrateSet = HashSet<String>()

    // 当前迁移的状态
    private var migrateState: Int? = null

    fun isCanUseMMKV(name: String): Boolean {
        val lastBound = fetchLastMigrateBound()
        if (lastBound == MMKVMigrateState.Migrated || isCurrentSpMigrated(name)) return true
        // 如果包含在还未迁移的名单中。那就还是使用SP
        list.forEachIndexed { index, strings ->
            if (index > lastBound && strings.contains(name)) {
               // Log.e("SPConfig", name + ">>无法使用MMKV 改为使用SP lastBound>${lastBound}>>当前Index>>>" + index)
                return false
            }
        }
        return true
    }


    @Synchronized
    private fun isCurrentSpMigrated(name: String): Boolean {
        return hasMigrateSet.contains(name)
    }

    @Synchronized
    private fun setCurrentSpMigrated(name: String) {
        hasMigrateSet.add(name)
    }

    fun setMigrateSpList(spNameList: List<List<String>>) {
        list.clear()
        list.addAll(spNameList)
    }


    val okTest = HashSet<String>()
    fun printSet(name: String) {
        if (!okTest.contains(name)) {
            okTest.add(name)
            Log.d("首轮", okTest.toTypedArray().contentToString())
        }
    }

    /**
     * 开启Sp ==> MMKV 的迁移
     */
    @WorkerThread
    fun startSp2MMKVMigrateInMultiTime(
        callback: MigrateCompleteCallback,
        excludeMap: Map<String, Set<String>>? = null
    ) {
        val lastBound = fetchLastMigrateBound()
        if (lastBound == MMKVMigrateState.Migrated) return
        // 获取下一轮的
        val curBounds = lastBound + 1
        for (bound in curBounds..list.lastIndex) {
            val startTime = System.currentTimeMillis()
            val spList = list.elementAtOrNull(bound) ?: emptyList()
            Log.d("MMKV_Migrate", ">>>>>startSp2MMKVMigrateIfNeed 开始迁移>>>当前迁移轮次>>${curBounds}>>>>${spList.size}")
            for (name in spList) {
                val tempTime1 = System.currentTimeMillis()
                val mmkv = MMKVProxy(MMKV.mmkvWithID(name, MMKV.SINGLE_PROCESS_MODE))
                val preferences = AppContext.context.getSharedPreferences(name, Context.MODE_PRIVATE)
                mmkv.importDataFromSp(name, preferences, excludeKeyMap = excludeMap?.get(name))
                // 一迁移完成。立刻通知SP 切换到MMKV
                setCurrentSpMigrated(name)
                SPConfig.transSp2MMKV(name, mmkv)
                Log.d("MMKV_Migrate_Cost", ">>>每个文件迁移用时>>${name}>${(System.currentTimeMillis() - tempTime1)}>>>>>")
            }
            if (bound >= list.lastIndex) fillMigrateBound(MMKVMigrateState.Migrated) else fillMigrateBound(bound)
            // 如果单次迁移时间超过1500 ms。那就下次在进行迁移。
            val useTime = System.currentTimeMillis() - startTime
            Log.d("MMKV_Migrate_Cost", ">>>>>当前轮次迁移使用时间>>>>>>>${useTime}>>${bound}>>>>")
            if (useTime > 1500) {
                break
            }
        }
        callback.onComplete()
    }


    private fun fillMigrateBound(bound: Int) {
        migrateState = bound
        MMKV.mmkvWithID(MIGRATE_STATE_TABLE, MMKV.SINGLE_PROCESS_MODE)?.encode(MIGRATE_BOUNDS, bound)
    }

    private fun fetchLastMigrateBound(): Int {
        return migrateState ?: kotlin.run {
            (MMKV.mmkvWithID(MIGRATE_STATE_TABLE, MMKV.SINGLE_PROCESS_MODE)?.decodeInt(MIGRATE_BOUNDS, -1) ?: -1).also {
                migrateState = it
            }
        }
    }


    fun fetchMigratedState(): Int {
        val bounds = fetchLastMigrateBound()
        Log.d("MMKV_Migrate", ">>fetchMigratedState>>轮次>>${bounds}>>listSize>>${list.size}")
        return when (bounds) {
            MMKVMigrateState.NONE -> MMKVMigrateState.NONE
            MMKVMigrateState.Migrated -> MMKVMigrateState.Migrated
            else -> MMKVMigrateState.Migrating
        }
    }

    fun setNoNeedMigrated() {
        fillMigrateBound(MMKVMigrateState.Migrated)
    }


    fun isCurVersionCanMigrated(versionCode: Int): Boolean {
        val mmkv = (MMKV.mmkvWithID(MIGRATE_STATE_TABLE, MMKV.SINGLE_PROCESS_MODE))
        val version = mmkv?.decodeInt(CurVersionCanMigrated)
        return versionCode > 0 && version == versionCode
    }

    fun setCurVersionCanMigratedIfNeed(versionCode: Int) {
        Log.d("MMKV_Migrate", ">>>>>>设置当前可以开始迁移了>>>>>${versionCode}")
        val mmkv = (MMKV.mmkvWithID(MIGRATE_STATE_TABLE, MMKV.SINGLE_PROCESS_MODE))
        val version = mmkv?.decodeInt(CurVersionCanMigrated)
        if (versionCode > 0 && version != versionCode) {
            mmkv.encode(CurVersionCanMigrated, versionCode)
        }
    }


    var callback: MigrateCompleteCallback? = null


    public interface MigrateCompleteCallback {
        fun onComplete()
    }
}