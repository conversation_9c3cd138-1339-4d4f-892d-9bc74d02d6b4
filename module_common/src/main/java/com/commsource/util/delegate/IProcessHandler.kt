package com.commsource.util.delegate

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.commsource.util.UIHelper
import java.lang.ref.WeakReference

/**
 * @Desc : 事件解析代理
 * <AUTHOR> Bear - 2020/4/27
 *
 * 使用fragment代理代理 activity发起跳转和接收result 控制在一个"流程"内
 * 目前[DelegateProcess]的实例为.
 */
class IProcessHandler {

    private final var TAG = "IProcessHandle"

    private var wFg: WeakReference<DelegateFragment>? = null

    /**
     * 构造
     */
    constructor(activity: FragmentActivity) {
        // 创建代理Fragment
        wFg = generateDelegateFragment(activity.supportFragmentManager);
    }

    /**
     * 创建代理Fragment
     *
     * @param fragmentManager
     * @return
     */
    private fun generateDelegateFragment(fragmentManager: FragmentManager): WeakReference<DelegateFragment> {
        var fg: Fragment? = fragmentManager.findFragmentByTag(TAG)
        val needCreate = fg == null
        if (needCreate) {
            fg = DelegateFragment()
            fragmentManager.beginTransaction().add(fg, TAG).commitNowAllowingStateLoss()
        }
        return WeakReference<DelegateFragment>(fg as DelegateFragment)
    }

    /**
     * 处理事件进程
     */
    fun execute(delegateProcess: DelegateProcess) {
        UIHelper.post {
            wFg?.get()?.takeIf { it.host != null }?.execute(delegateProcess)
        }
    }

}