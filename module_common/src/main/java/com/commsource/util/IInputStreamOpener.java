package com.commsource.util;

import android.content.Context;

import java.io.IOException;
import java.io.InputStream;

/**
 * 定义打开输入流方法的接口
 * (c) Zynga 2012
 *
 * <AUTHOR> <<EMAIL>>
 * @since 11:54:25 - 02.03.2012
 */
public interface IInputStreamOpener {
    // ===========================================================
    // Constants
    // ===========================================================

    // ===========================================================
    // Methods
    // ===========================================================

    /**
     * 打开一个输入流
     * @param context 上下文
     * @return 输入流
     * @throws IOException
     */
    InputStream open(Context context)
        throws IOException;
}
